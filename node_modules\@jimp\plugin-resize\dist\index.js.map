{"version": 3, "file": "index.js", "names": ["constants", "RESIZE_NEAREST_NEIGHBOR", "RESIZE_BILINEAR", "RESIZE_BICUBIC", "RESIZE_HERMITE", "RESIZE_BEZIER", "class", "resize", "w", "h", "mode", "cb", "throwError", "call", "constructor", "AUTO", "bitmap", "width", "height", "Math", "round", "Resize2", "dst", "data", "<PERSON><PERSON><PERSON>", "alloc", "image", "Resize", "buffer", "from", "isNodePattern"], "sources": ["../src/index.js"], "sourcesContent": ["import { throwError, isN<PERSON><PERSON>attern } from \"@jimp/utils\";\n\nimport Resize from \"./modules/resize\";\nimport Resize2 from \"./modules/resize2\";\n\nexport default () => ({\n  constants: {\n    RESIZE_NEAREST_NEIGHBOR: \"nearestNeighbor\",\n    RESIZE_BILINEAR: \"bilinearInterpolation\",\n    RESIZE_BICUBIC: \"bicubicInterpolation\",\n    RESIZE_HERMITE: \"hermiteInterpolation\",\n    RESIZE_BEZIER: \"bezierInterpolation\",\n  },\n\n  class: {\n    /**\n     * Resizes the image to a set width and height using a 2-pass bilinear algorithm\n     * @param {number} w the width to resize the image to (or Jimp.AUTO)\n     * @param {number} h the height to resize the image to (or Jimp.AUTO)\n     * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n     * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n     * @returns {Jim<PERSON>} this for chaining of methods\n     */\n    resize(w, h, mode, cb) {\n      if (typeof w !== \"number\" || typeof h !== \"number\") {\n        return throwError.call(this, \"w and h must be numbers\", cb);\n      }\n\n      if (typeof mode === \"function\" && typeof cb === \"undefined\") {\n        cb = mode;\n        mode = null;\n      }\n\n      if (w === this.constructor.AUTO && h === this.constructor.AUTO) {\n        return throwError.call(this, \"w and h cannot both be set to auto\", cb);\n      }\n\n      if (w === this.constructor.AUTO) {\n        w = this.bitmap.width * (h / this.bitmap.height);\n      }\n\n      if (h === this.constructor.AUTO) {\n        h = this.bitmap.height * (w / this.bitmap.width);\n      }\n\n      if (w < 0 || h < 0) {\n        return throwError.call(this, \"w and h must be positive numbers\", cb);\n      }\n\n      // round inputs\n      w = Math.round(w) || 1;\n      h = Math.round(h) || 1;\n\n      if (typeof Resize2[mode] === \"function\") {\n        const dst = {\n          data: Buffer.alloc(w * h * 4),\n          width: w,\n          height: h,\n        };\n        Resize2[mode](this.bitmap, dst);\n        this.bitmap = dst;\n      } else {\n        const image = this;\n        const resize = new Resize(\n          this.bitmap.width,\n          this.bitmap.height,\n          w,\n          h,\n          true,\n          true,\n          (buffer) => {\n            image.bitmap.data = Buffer.from(buffer);\n            image.bitmap.width = w;\n            image.bitmap.height = h;\n          }\n        );\n        resize.resize(this.bitmap.data);\n      }\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n  },\n});\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AAAwC;AAAA,eAEzB,OAAO;EACpBA,SAAS,EAAE;IACTC,uBAAuB,EAAE,iBAAiB;IAC1CC,eAAe,EAAE,uBAAuB;IACxCC,cAAc,EAAE,sBAAsB;IACtCC,cAAc,EAAE,sBAAsB;IACtCC,aAAa,EAAE;EACjB,CAAC;EAEDC,KAAK,EAAE;IACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,MAAM,CAACC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAEC,EAAE,EAAE;MACrB,IAAI,OAAOH,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;QAClD,OAAOG,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEF,EAAE,CAAC;MAC7D;MAEA,IAAI,OAAOD,IAAI,KAAK,UAAU,IAAI,OAAOC,EAAE,KAAK,WAAW,EAAE;QAC3DA,EAAE,GAAGD,IAAI;QACTA,IAAI,GAAG,IAAI;MACb;MAEA,IAAIF,CAAC,KAAK,IAAI,CAACM,WAAW,CAACC,IAAI,IAAIN,CAAC,KAAK,IAAI,CAACK,WAAW,CAACC,IAAI,EAAE;QAC9D,OAAOH,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,oCAAoC,EAAEF,EAAE,CAAC;MACxE;MAEA,IAAIH,CAAC,KAAK,IAAI,CAACM,WAAW,CAACC,IAAI,EAAE;QAC/BP,CAAC,GAAG,IAAI,CAACQ,MAAM,CAACC,KAAK,IAAIR,CAAC,GAAG,IAAI,CAACO,MAAM,CAACE,MAAM,CAAC;MAClD;MAEA,IAAIT,CAAC,KAAK,IAAI,CAACK,WAAW,CAACC,IAAI,EAAE;QAC/BN,CAAC,GAAG,IAAI,CAACO,MAAM,CAACE,MAAM,IAAIV,CAAC,GAAG,IAAI,CAACQ,MAAM,CAACC,KAAK,CAAC;MAClD;MAEA,IAAIT,CAAC,GAAG,CAAC,IAAIC,CAAC,GAAG,CAAC,EAAE;QAClB,OAAOG,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,kCAAkC,EAAEF,EAAE,CAAC;MACtE;;MAEA;MACAH,CAAC,GAAGW,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC,IAAI,CAAC;MACtBC,CAAC,GAAGU,IAAI,CAACC,KAAK,CAACX,CAAC,CAAC,IAAI,CAAC;MAEtB,IAAI,OAAOY,gBAAO,CAACX,IAAI,CAAC,KAAK,UAAU,EAAE;QACvC,MAAMY,GAAG,GAAG;UACVC,IAAI,EAAEC,MAAM,CAACC,KAAK,CAACjB,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC;UAC7BQ,KAAK,EAAET,CAAC;UACRU,MAAM,EAAET;QACV,CAAC;QACDY,gBAAO,CAACX,IAAI,CAAC,CAAC,IAAI,CAACM,MAAM,EAAEM,GAAG,CAAC;QAC/B,IAAI,CAACN,MAAM,GAAGM,GAAG;MACnB,CAAC,MAAM;QACL,MAAMI,KAAK,GAAG,IAAI;QAClB,MAAMnB,MAAM,GAAG,IAAIoB,eAAM,CACvB,IAAI,CAACX,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClBV,CAAC,EACDC,CAAC,EACD,IAAI,EACJ,IAAI,EACHmB,MAAM,IAAK;UACVF,KAAK,CAACV,MAAM,CAACO,IAAI,GAAGC,MAAM,CAACK,IAAI,CAACD,MAAM,CAAC;UACvCF,KAAK,CAACV,MAAM,CAACC,KAAK,GAAGT,CAAC;UACtBkB,KAAK,CAACV,MAAM,CAACE,MAAM,GAAGT,CAAC;QACzB,CAAC,CACF;QACDF,MAAM,CAACA,MAAM,CAAC,IAAI,CAACS,MAAM,CAACO,IAAI,CAAC;MACjC;MAEA,IAAI,IAAAO,oBAAa,EAACnB,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb;EACF;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}