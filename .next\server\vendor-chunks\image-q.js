"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/image-q";
exports.ids = ["vendor-chunks/image-q"];
exports.modules = {

/***/ "(rsc)/./node_modules/image-q/dist/cjs/image-q.cjs":
/*!***************************************************!*\
  !*** ./node_modules/image-q/dist/cjs/image-q.cjs ***!
  \***************************************************/
/***/ ((module) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value\n    }) : obj[key] = value;\nvar __markAsModule = (target)=>__defProp(target, \"__esModule\", {\n        value: true\n    });\nvar __export = (target, all)=>{\n    for(var name in all)__defProp(target, name, {\n        get: all[name],\n        enumerable: true\n    });\n};\nvar __reExport = (target, module2, copyDefault, desc)=>{\n    if (module2 && typeof module2 === \"object\" || typeof module2 === \"function\") {\n        for (let key of __getOwnPropNames(module2))if (!__hasOwnProp.call(target, key) && (copyDefault || key !== \"default\")) __defProp(target, key, {\n            get: ()=>module2[key],\n            enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable\n        });\n    }\n    return target;\n};\nvar __toCommonJS = /* @__PURE__ */ ((cache)=>{\n    return (module2, temp)=>{\n        return cache && cache.get(module2) || (temp = __reExport(__markAsModule({}), module2, 1), cache && cache.set(module2, temp), temp);\n    };\n})(typeof WeakMap !== \"undefined\" ? /* @__PURE__ */ new WeakMap() : 0);\nvar __publicField = (obj, key, value)=>{\n    __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n    return value;\n};\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n    applyPalette: ()=>applyPalette,\n    applyPaletteSync: ()=>applyPaletteSync,\n    buildPalette: ()=>buildPalette,\n    buildPaletteSync: ()=>buildPaletteSync,\n    constants: ()=>constants_exports,\n    conversion: ()=>conversion_exports,\n    distance: ()=>distance_exports,\n    image: ()=>image_exports,\n    palette: ()=>palette_exports,\n    quality: ()=>quality_exports,\n    utils: ()=>utils_exports\n});\n// src/constants/index.ts\nvar constants_exports = {};\n__export(constants_exports, {\n    bt709: ()=>bt709_exports\n});\n// src/constants/bt709.ts\nvar bt709_exports = {};\n__export(bt709_exports, {\n    Y: ()=>Y,\n    x: ()=>x,\n    y: ()=>y\n});\nvar Y = /* @__PURE__ */ ((Y2)=>{\n    Y2[Y2[\"RED\"] = 0.2126] = \"RED\";\n    Y2[Y2[\"GREEN\"] = 0.7152] = \"GREEN\";\n    Y2[Y2[\"BLUE\"] = 0.0722] = \"BLUE\";\n    Y2[Y2[\"WHITE\"] = 1] = \"WHITE\";\n    return Y2;\n})(Y || {});\nvar x = /* @__PURE__ */ ((x2)=>{\n    x2[x2[\"RED\"] = 0.64] = \"RED\";\n    x2[x2[\"GREEN\"] = 0.3] = \"GREEN\";\n    x2[x2[\"BLUE\"] = 0.15] = \"BLUE\";\n    x2[x2[\"WHITE\"] = 0.3127] = \"WHITE\";\n    return x2;\n})(x || {});\nvar y = /* @__PURE__ */ ((y2)=>{\n    y2[y2[\"RED\"] = 0.33] = \"RED\";\n    y2[y2[\"GREEN\"] = 0.6] = \"GREEN\";\n    y2[y2[\"BLUE\"] = 0.06] = \"BLUE\";\n    y2[y2[\"WHITE\"] = 0.329] = \"WHITE\";\n    return y2;\n})(y || {});\n// src/conversion/index.ts\nvar conversion_exports = {};\n__export(conversion_exports, {\n    lab2rgb: ()=>lab2rgb,\n    lab2xyz: ()=>lab2xyz,\n    rgb2hsl: ()=>rgb2hsl,\n    rgb2lab: ()=>rgb2lab,\n    rgb2xyz: ()=>rgb2xyz,\n    xyz2lab: ()=>xyz2lab,\n    xyz2rgb: ()=>xyz2rgb\n});\n// src/conversion/rgb2xyz.ts\nfunction correctGamma(n) {\n    return n > 0.04045 ? ((n + 0.055) / 1.055) ** 2.4 : n / 12.92;\n}\nfunction rgb2xyz(r, g, b) {\n    r = correctGamma(r / 255);\n    g = correctGamma(g / 255);\n    b = correctGamma(b / 255);\n    return {\n        x: r * 0.4124 + g * 0.3576 + b * 0.1805,\n        y: r * 0.2126 + g * 0.7152 + b * 0.0722,\n        z: r * 0.0193 + g * 0.1192 + b * 0.9505\n    };\n}\n// src/utils/arithmetic.ts\nvar arithmetic_exports = {};\n__export(arithmetic_exports, {\n    degrees2radians: ()=>degrees2radians,\n    inRange0to255: ()=>inRange0to255,\n    inRange0to255Rounded: ()=>inRange0to255Rounded,\n    intInRange: ()=>intInRange,\n    max3: ()=>max3,\n    min3: ()=>min3,\n    stableSort: ()=>stableSort\n});\nfunction degrees2radians(n) {\n    return n * (Math.PI / 180);\n}\nfunction max3(a, b, c) {\n    let m = a;\n    if (m < b) m = b;\n    if (m < c) m = c;\n    return m;\n}\nfunction min3(a, b, c) {\n    let m = a;\n    if (m > b) m = b;\n    if (m > c) m = c;\n    return m;\n}\nfunction intInRange(value, low, high) {\n    if (value > high) value = high;\n    if (value < low) value = low;\n    return value | 0;\n}\nfunction inRange0to255Rounded(n) {\n    n = Math.round(n);\n    if (n > 255) n = 255;\n    else if (n < 0) n = 0;\n    return n;\n}\nfunction inRange0to255(n) {\n    if (n > 255) n = 255;\n    else if (n < 0) n = 0;\n    return n;\n}\nfunction stableSort(arrayToSort, callback) {\n    const type = typeof arrayToSort[0];\n    let sorted;\n    if (type === \"number\" || type === \"string\") {\n        const ord = /* @__PURE__ */ Object.create(null);\n        for(let i = 0, l = arrayToSort.length; i < l; i++){\n            const val = arrayToSort[i];\n            if (ord[val] || ord[val] === 0) continue;\n            ord[val] = i;\n        }\n        sorted = arrayToSort.sort((a, b)=>callback(a, b) || ord[a] - ord[b]);\n    } else {\n        const ord2 = arrayToSort.slice(0);\n        sorted = arrayToSort.sort((a, b)=>callback(a, b) || ord2.indexOf(a) - ord2.indexOf(b));\n    }\n    return sorted;\n}\n// src/conversion/rgb2hsl.ts\nfunction rgb2hsl(r, g, b) {\n    const min = min3(r, g, b);\n    const max = max3(r, g, b);\n    const delta = max - min;\n    const l = (min + max) / 510;\n    let s = 0;\n    if (l > 0 && l < 1) s = delta / (l < 0.5 ? max + min : 510 - max - min);\n    let h = 0;\n    if (delta > 0) {\n        if (max === r) {\n            h = (g - b) / delta;\n        } else if (max === g) {\n            h = 2 + (b - r) / delta;\n        } else {\n            h = 4 + (r - g) / delta;\n        }\n        h *= 60;\n        if (h < 0) h += 360;\n    }\n    return {\n        h,\n        s,\n        l\n    };\n}\n// src/conversion/xyz2lab.ts\nvar refX = 0.95047;\nvar refY = 1;\nvar refZ = 1.08883;\nfunction pivot(n) {\n    return n > 8856e-6 ? n ** (1 / 3) : 7.787 * n + 16 / 116;\n}\nfunction xyz2lab(x2, y2, z) {\n    x2 = pivot(x2 / refX);\n    y2 = pivot(y2 / refY);\n    z = pivot(z / refZ);\n    if (116 * y2 - 16 < 0) throw new Error(\"xxx\");\n    return {\n        L: Math.max(0, 116 * y2 - 16),\n        a: 500 * (x2 - y2),\n        b: 200 * (y2 - z)\n    };\n}\n// src/conversion/rgb2lab.ts\nfunction rgb2lab(r, g, b) {\n    const xyz = rgb2xyz(r, g, b);\n    return xyz2lab(xyz.x, xyz.y, xyz.z);\n}\n// src/conversion/lab2xyz.ts\nvar refX2 = 0.95047;\nvar refY2 = 1;\nvar refZ2 = 1.08883;\nfunction pivot2(n) {\n    return n > 0.206893034 ? n ** 3 : (n - 16 / 116) / 7.787;\n}\nfunction lab2xyz(L, a, b) {\n    const y2 = (L + 16) / 116;\n    const x2 = a / 500 + y2;\n    const z = y2 - b / 200;\n    return {\n        x: refX2 * pivot2(x2),\n        y: refY2 * pivot2(y2),\n        z: refZ2 * pivot2(z)\n    };\n}\n// src/conversion/xyz2rgb.ts\nfunction correctGamma2(n) {\n    return n > 31308e-7 ? 1.055 * n ** (1 / 2.4) - 0.055 : 12.92 * n;\n}\nfunction xyz2rgb(x2, y2, z) {\n    const r = correctGamma2(x2 * 3.2406 + y2 * -1.5372 + z * -0.4986);\n    const g = correctGamma2(x2 * -0.9689 + y2 * 1.8758 + z * 0.0415);\n    const b = correctGamma2(x2 * 0.0557 + y2 * -0.204 + z * 1.057);\n    return {\n        r: inRange0to255Rounded(r * 255),\n        g: inRange0to255Rounded(g * 255),\n        b: inRange0to255Rounded(b * 255)\n    };\n}\n// src/conversion/lab2rgb.ts\nfunction lab2rgb(L, a, b) {\n    const xyz = lab2xyz(L, a, b);\n    return xyz2rgb(xyz.x, xyz.y, xyz.z);\n}\n// src/distance/index.ts\nvar distance_exports = {};\n__export(distance_exports, {\n    AbstractDistanceCalculator: ()=>AbstractDistanceCalculator,\n    AbstractEuclidean: ()=>AbstractEuclidean,\n    AbstractManhattan: ()=>AbstractManhattan,\n    CIE94GraphicArts: ()=>CIE94GraphicArts,\n    CIE94Textiles: ()=>CIE94Textiles,\n    CIEDE2000: ()=>CIEDE2000,\n    CMetric: ()=>CMetric,\n    Euclidean: ()=>Euclidean,\n    EuclideanBT709: ()=>EuclideanBT709,\n    EuclideanBT709NoAlpha: ()=>EuclideanBT709NoAlpha,\n    Manhattan: ()=>Manhattan,\n    ManhattanBT709: ()=>ManhattanBT709,\n    ManhattanNommyde: ()=>ManhattanNommyde,\n    PNGQuant: ()=>PNGQuant\n});\n// src/distance/distanceCalculator.ts\nvar AbstractDistanceCalculator = class {\n    constructor(){\n        __publicField(this, \"_maxDistance\");\n        __publicField(this, \"_whitePoint\");\n        this._setDefaults();\n        this.setWhitePoint(255, 255, 255, 255);\n    }\n    setWhitePoint(r, g, b, a) {\n        this._whitePoint = {\n            r: r > 0 ? 255 / r : 0,\n            g: g > 0 ? 255 / g : 0,\n            b: b > 0 ? 255 / b : 0,\n            a: a > 0 ? 255 / a : 0\n        };\n        this._maxDistance = this.calculateRaw(r, g, b, a, 0, 0, 0, 0);\n    }\n    calculateNormalized(colorA, colorB) {\n        return this.calculateRaw(colorA.r, colorA.g, colorA.b, colorA.a, colorB.r, colorB.g, colorB.b, colorB.a) / this._maxDistance;\n    }\n};\n// src/distance/cie94.ts\nvar AbstractCIE94 = class extends AbstractDistanceCalculator {\n    calculateRaw(r1, g1, b1, a1, r2, g2, b2, a2) {\n        const lab1 = rgb2lab(inRange0to255(r1 * this._whitePoint.r), inRange0to255(g1 * this._whitePoint.g), inRange0to255(b1 * this._whitePoint.b));\n        const lab2 = rgb2lab(inRange0to255(r2 * this._whitePoint.r), inRange0to255(g2 * this._whitePoint.g), inRange0to255(b2 * this._whitePoint.b));\n        const dL = lab1.L - lab2.L;\n        const dA = lab1.a - lab2.a;\n        const dB = lab1.b - lab2.b;\n        const c1 = Math.sqrt(lab1.a * lab1.a + lab1.b * lab1.b);\n        const c2 = Math.sqrt(lab2.a * lab2.a + lab2.b * lab2.b);\n        const dC = c1 - c2;\n        let deltaH = dA * dA + dB * dB - dC * dC;\n        deltaH = deltaH < 0 ? 0 : Math.sqrt(deltaH);\n        const dAlpha = (a2 - a1) * this._whitePoint.a * this._kA;\n        return Math.sqrt((dL / this._Kl) ** 2 + (dC / (1 + this._K1 * c1)) ** 2 + (deltaH / (1 + this._K2 * c1)) ** 2 + dAlpha ** 2);\n    }\n};\nvar CIE94Textiles = class extends AbstractCIE94 {\n    _setDefaults() {\n        this._Kl = 2;\n        this._K1 = 0.048;\n        this._K2 = 0.014;\n        this._kA = 0.25 * 50 / 255;\n    }\n};\nvar CIE94GraphicArts = class extends AbstractCIE94 {\n    _setDefaults() {\n        this._Kl = 1;\n        this._K1 = 0.045;\n        this._K2 = 0.015;\n        this._kA = 0.25 * 100 / 255;\n    }\n};\n// src/distance/ciede2000.ts\nvar _CIEDE2000 = class extends AbstractDistanceCalculator {\n    _setDefaults() {}\n    static _calculatehp(b, ap) {\n        const hp = Math.atan2(b, ap);\n        if (hp >= 0) return hp;\n        return hp + _CIEDE2000._deg360InRad;\n    }\n    static _calculateRT(ahp, aCp) {\n        const aCp_to_7 = aCp ** 7;\n        const R_C = 2 * Math.sqrt(aCp_to_7 / (aCp_to_7 + _CIEDE2000._pow25to7));\n        const delta_theta = _CIEDE2000._deg30InRad * Math.exp(-(((ahp - _CIEDE2000._deg275InRad) / _CIEDE2000._deg25InRad) ** 2));\n        return -Math.sin(2 * delta_theta) * R_C;\n    }\n    static _calculateT(ahp) {\n        return 1 - 0.17 * Math.cos(ahp - _CIEDE2000._deg30InRad) + 0.24 * Math.cos(ahp * 2) + 0.32 * Math.cos(ahp * 3 + _CIEDE2000._deg6InRad) - 0.2 * Math.cos(ahp * 4 - _CIEDE2000._deg63InRad);\n    }\n    static _calculate_ahp(C1pC2p, h_bar, h1p, h2p) {\n        const hpSum = h1p + h2p;\n        if (C1pC2p === 0) return hpSum;\n        if (h_bar <= _CIEDE2000._deg180InRad) return hpSum / 2;\n        if (hpSum < _CIEDE2000._deg360InRad) {\n            return (hpSum + _CIEDE2000._deg360InRad) / 2;\n        }\n        return (hpSum - _CIEDE2000._deg360InRad) / 2;\n    }\n    static _calculate_dHp(C1pC2p, h_bar, h2p, h1p) {\n        let dhp;\n        if (C1pC2p === 0) {\n            dhp = 0;\n        } else if (h_bar <= _CIEDE2000._deg180InRad) {\n            dhp = h2p - h1p;\n        } else if (h2p <= h1p) {\n            dhp = h2p - h1p + _CIEDE2000._deg360InRad;\n        } else {\n            dhp = h2p - h1p - _CIEDE2000._deg360InRad;\n        }\n        return 2 * Math.sqrt(C1pC2p) * Math.sin(dhp / 2);\n    }\n    calculateRaw(r1, g1, b1, a1, r2, g2, b2, a2) {\n        const lab1 = rgb2lab(inRange0to255(r1 * this._whitePoint.r), inRange0to255(g1 * this._whitePoint.g), inRange0to255(b1 * this._whitePoint.b));\n        const lab2 = rgb2lab(inRange0to255(r2 * this._whitePoint.r), inRange0to255(g2 * this._whitePoint.g), inRange0to255(b2 * this._whitePoint.b));\n        const dA = (a2 - a1) * this._whitePoint.a * _CIEDE2000._kA;\n        const dE2 = this.calculateRawInLab(lab1, lab2);\n        return Math.sqrt(dE2 + dA * dA);\n    }\n    calculateRawInLab(Lab1, Lab2) {\n        const L1 = Lab1.L;\n        const a1 = Lab1.a;\n        const b1 = Lab1.b;\n        const L2 = Lab2.L;\n        const a2 = Lab2.a;\n        const b2 = Lab2.b;\n        const C1 = Math.sqrt(a1 * a1 + b1 * b1);\n        const C2 = Math.sqrt(a2 * a2 + b2 * b2);\n        const pow_a_C1_C2_to_7 = ((C1 + C2) / 2) ** 7;\n        const G = 0.5 * (1 - Math.sqrt(pow_a_C1_C2_to_7 / (pow_a_C1_C2_to_7 + _CIEDE2000._pow25to7)));\n        const a1p = (1 + G) * a1;\n        const a2p = (1 + G) * a2;\n        const C1p = Math.sqrt(a1p * a1p + b1 * b1);\n        const C2p = Math.sqrt(a2p * a2p + b2 * b2);\n        const C1pC2p = C1p * C2p;\n        const h1p = _CIEDE2000._calculatehp(b1, a1p);\n        const h2p = _CIEDE2000._calculatehp(b2, a2p);\n        const h_bar = Math.abs(h1p - h2p);\n        const dLp = L2 - L1;\n        const dCp = C2p - C1p;\n        const dHp = _CIEDE2000._calculate_dHp(C1pC2p, h_bar, h2p, h1p);\n        const ahp = _CIEDE2000._calculate_ahp(C1pC2p, h_bar, h1p, h2p);\n        const T = _CIEDE2000._calculateT(ahp);\n        const aCp = (C1p + C2p) / 2;\n        const aLp_minus_50_square = ((L1 + L2) / 2 - 50) ** 2;\n        const S_L = 1 + 0.015 * aLp_minus_50_square / Math.sqrt(20 + aLp_minus_50_square);\n        const S_C = 1 + 0.045 * aCp;\n        const S_H = 1 + 0.015 * T * aCp;\n        const R_T = _CIEDE2000._calculateRT(ahp, aCp);\n        const dLpSL = dLp / S_L;\n        const dCpSC = dCp / S_C;\n        const dHpSH = dHp / S_H;\n        return dLpSL ** 2 + dCpSC ** 2 + dHpSH ** 2 + R_T * dCpSC * dHpSH;\n    }\n};\nvar CIEDE2000 = _CIEDE2000;\n__publicField(CIEDE2000, \"_kA\", 0.25 * 100 / 255);\n__publicField(CIEDE2000, \"_pow25to7\", 25 ** 7);\n__publicField(CIEDE2000, \"_deg360InRad\", degrees2radians(360));\n__publicField(CIEDE2000, \"_deg180InRad\", degrees2radians(180));\n__publicField(CIEDE2000, \"_deg30InRad\", degrees2radians(30));\n__publicField(CIEDE2000, \"_deg6InRad\", degrees2radians(6));\n__publicField(CIEDE2000, \"_deg63InRad\", degrees2radians(63));\n__publicField(CIEDE2000, \"_deg275InRad\", degrees2radians(275));\n__publicField(CIEDE2000, \"_deg25InRad\", degrees2radians(25));\n// src/distance/cmetric.ts\nvar CMetric = class extends AbstractDistanceCalculator {\n    calculateRaw(r1, g1, b1, a1, r2, g2, b2, a2) {\n        const rmean = (r1 + r2) / 2 * this._whitePoint.r;\n        const r = (r1 - r2) * this._whitePoint.r;\n        const g = (g1 - g2) * this._whitePoint.g;\n        const b = (b1 - b2) * this._whitePoint.b;\n        const dE = ((512 + rmean) * r * r >> 8) + 4 * g * g + ((767 - rmean) * b * b >> 8);\n        const dA = (a2 - a1) * this._whitePoint.a;\n        return Math.sqrt(dE + dA * dA);\n    }\n    _setDefaults() {}\n};\n// src/distance/euclidean.ts\nvar AbstractEuclidean = class extends AbstractDistanceCalculator {\n    calculateRaw(r1, g1, b1, a1, r2, g2, b2, a2) {\n        const dR = r2 - r1;\n        const dG = g2 - g1;\n        const dB = b2 - b1;\n        const dA = a2 - a1;\n        return Math.sqrt(this._kR * dR * dR + this._kG * dG * dG + this._kB * dB * dB + this._kA * dA * dA);\n    }\n};\nvar Euclidean = class extends AbstractEuclidean {\n    _setDefaults() {\n        this._kR = 1;\n        this._kG = 1;\n        this._kB = 1;\n        this._kA = 1;\n    }\n};\nvar EuclideanBT709 = class extends AbstractEuclidean {\n    _setDefaults() {\n        this._kR = 0.2126 /* RED */ ;\n        this._kG = 0.7152 /* GREEN */ ;\n        this._kB = 0.0722 /* BLUE */ ;\n        this._kA = 1;\n    }\n};\nvar EuclideanBT709NoAlpha = class extends AbstractEuclidean {\n    _setDefaults() {\n        this._kR = 0.2126 /* RED */ ;\n        this._kG = 0.7152 /* GREEN */ ;\n        this._kB = 0.0722 /* BLUE */ ;\n        this._kA = 0;\n    }\n};\n// src/distance/manhattan.ts\nvar AbstractManhattan = class extends AbstractDistanceCalculator {\n    calculateRaw(r1, g1, b1, a1, r2, g2, b2, a2) {\n        let dR = r2 - r1;\n        let dG = g2 - g1;\n        let dB = b2 - b1;\n        let dA = a2 - a1;\n        if (dR < 0) dR = 0 - dR;\n        if (dG < 0) dG = 0 - dG;\n        if (dB < 0) dB = 0 - dB;\n        if (dA < 0) dA = 0 - dA;\n        return this._kR * dR + this._kG * dG + this._kB * dB + this._kA * dA;\n    }\n};\nvar Manhattan = class extends AbstractManhattan {\n    _setDefaults() {\n        this._kR = 1;\n        this._kG = 1;\n        this._kB = 1;\n        this._kA = 1;\n    }\n};\nvar ManhattanNommyde = class extends AbstractManhattan {\n    _setDefaults() {\n        this._kR = 0.4984;\n        this._kG = 0.8625;\n        this._kB = 0.2979;\n        this._kA = 1;\n    }\n};\nvar ManhattanBT709 = class extends AbstractManhattan {\n    _setDefaults() {\n        this._kR = 0.2126 /* RED */ ;\n        this._kG = 0.7152 /* GREEN */ ;\n        this._kB = 0.0722 /* BLUE */ ;\n        this._kA = 1;\n    }\n};\n// src/distance/pngQuant.ts\nvar PNGQuant = class extends AbstractDistanceCalculator {\n    calculateRaw(r1, g1, b1, a1, r2, g2, b2, a2) {\n        const alphas = (a2 - a1) * this._whitePoint.a;\n        return this._colordifferenceCh(r1 * this._whitePoint.r, r2 * this._whitePoint.r, alphas) + this._colordifferenceCh(g1 * this._whitePoint.g, g2 * this._whitePoint.g, alphas) + this._colordifferenceCh(b1 * this._whitePoint.b, b2 * this._whitePoint.b, alphas);\n    }\n    _colordifferenceCh(x2, y2, alphas) {\n        const black = x2 - y2;\n        const white = black + alphas;\n        return black * black + white * white;\n    }\n    _setDefaults() {}\n};\n// src/palette/index.ts\nvar palette_exports = {};\n__export(palette_exports, {\n    AbstractPaletteQuantizer: ()=>AbstractPaletteQuantizer,\n    ColorHistogram: ()=>ColorHistogram,\n    NeuQuant: ()=>NeuQuant,\n    NeuQuantFloat: ()=>NeuQuantFloat,\n    RGBQuant: ()=>RGBQuant,\n    WuColorCube: ()=>WuColorCube,\n    WuQuant: ()=>WuQuant\n});\n// src/palette/paletteQuantizer.ts\nvar AbstractPaletteQuantizer = class {\n    quantizeSync() {\n        for (const value of this.quantize()){\n            if (value.palette) {\n                return value.palette;\n            }\n        }\n        throw new Error(\"unreachable\");\n    }\n};\n// src/utils/point.ts\nvar Point = class {\n    constructor(){\n        __publicField(this, \"r\");\n        __publicField(this, \"g\");\n        __publicField(this, \"b\");\n        __publicField(this, \"a\");\n        __publicField(this, \"uint32\");\n        __publicField(this, \"rgba\");\n        this.uint32 = -1 >>> 0;\n        this.r = this.g = this.b = this.a = 0;\n        this.rgba = new Array(4);\n        this.rgba[0] = 0;\n        this.rgba[1] = 0;\n        this.rgba[2] = 0;\n        this.rgba[3] = 0;\n    }\n    static createByQuadruplet(quadruplet) {\n        const point = new Point();\n        point.r = quadruplet[0] | 0;\n        point.g = quadruplet[1] | 0;\n        point.b = quadruplet[2] | 0;\n        point.a = quadruplet[3] | 0;\n        point._loadUINT32();\n        point._loadQuadruplet();\n        return point;\n    }\n    static createByRGBA(red, green, blue, alpha) {\n        const point = new Point();\n        point.r = red | 0;\n        point.g = green | 0;\n        point.b = blue | 0;\n        point.a = alpha | 0;\n        point._loadUINT32();\n        point._loadQuadruplet();\n        return point;\n    }\n    static createByUint32(uint32) {\n        const point = new Point();\n        point.uint32 = uint32 >>> 0;\n        point._loadRGBA();\n        point._loadQuadruplet();\n        return point;\n    }\n    from(point) {\n        this.r = point.r;\n        this.g = point.g;\n        this.b = point.b;\n        this.a = point.a;\n        this.uint32 = point.uint32;\n        this.rgba[0] = point.r;\n        this.rgba[1] = point.g;\n        this.rgba[2] = point.b;\n        this.rgba[3] = point.a;\n    }\n    getLuminosity(useAlphaChannel) {\n        let r = this.r;\n        let g = this.g;\n        let b = this.b;\n        if (useAlphaChannel) {\n            r = Math.min(255, 255 - this.a + this.a * r / 255);\n            g = Math.min(255, 255 - this.a + this.a * g / 255);\n            b = Math.min(255, 255 - this.a + this.a * b / 255);\n        }\n        return r * 0.2126 /* RED */  + g * 0.7152 /* GREEN */  + b * 0.0722 /* BLUE */ ;\n    }\n    _loadUINT32() {\n        this.uint32 = (this.a << 24 | this.b << 16 | this.g << 8 | this.r) >>> 0;\n    }\n    _loadRGBA() {\n        this.r = this.uint32 & 255;\n        this.g = this.uint32 >>> 8 & 255;\n        this.b = this.uint32 >>> 16 & 255;\n        this.a = this.uint32 >>> 24 & 255;\n    }\n    _loadQuadruplet() {\n        this.rgba[0] = this.r;\n        this.rgba[1] = this.g;\n        this.rgba[2] = this.b;\n        this.rgba[3] = this.a;\n    }\n};\n// src/utils/pointContainer.ts\nvar PointContainer = class {\n    constructor(){\n        __publicField(this, \"_pointArray\");\n        __publicField(this, \"_width\");\n        __publicField(this, \"_height\");\n        this._width = 0;\n        this._height = 0;\n        this._pointArray = [];\n    }\n    getWidth() {\n        return this._width;\n    }\n    getHeight() {\n        return this._height;\n    }\n    setWidth(width) {\n        this._width = width;\n    }\n    setHeight(height) {\n        this._height = height;\n    }\n    getPointArray() {\n        return this._pointArray;\n    }\n    clone() {\n        const clone = new PointContainer();\n        clone._width = this._width;\n        clone._height = this._height;\n        for(let i = 0, l = this._pointArray.length; i < l; i++){\n            clone._pointArray[i] = Point.createByUint32(this._pointArray[i].uint32 | 0);\n        }\n        return clone;\n    }\n    toUint32Array() {\n        const l = this._pointArray.length;\n        const uint32Array = new Uint32Array(l);\n        for(let i = 0; i < l; i++){\n            uint32Array[i] = this._pointArray[i].uint32;\n        }\n        return uint32Array;\n    }\n    toUint8Array() {\n        return new Uint8Array(this.toUint32Array().buffer);\n    }\n    static fromHTMLImageElement(img) {\n        const width = img.naturalWidth;\n        const height = img.naturalHeight;\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = width;\n        canvas.height = height;\n        const ctx = canvas.getContext(\"2d\");\n        ctx.drawImage(img, 0, 0, width, height, 0, 0, width, height);\n        return PointContainer.fromHTMLCanvasElement(canvas);\n    }\n    static fromHTMLCanvasElement(canvas) {\n        const width = canvas.width;\n        const height = canvas.height;\n        const ctx = canvas.getContext(\"2d\");\n        const imgData = ctx.getImageData(0, 0, width, height);\n        return PointContainer.fromImageData(imgData);\n    }\n    static fromImageData(imageData) {\n        const width = imageData.width;\n        const height = imageData.height;\n        return PointContainer.fromUint8Array(imageData.data, width, height);\n    }\n    static fromUint8Array(uint8Array, width, height) {\n        switch(Object.prototype.toString.call(uint8Array)){\n            case \"[object Uint8ClampedArray]\":\n            case \"[object Uint8Array]\":\n                break;\n            default:\n                uint8Array = new Uint8Array(uint8Array);\n        }\n        const uint32Array = new Uint32Array(uint8Array.buffer);\n        return PointContainer.fromUint32Array(uint32Array, width, height);\n    }\n    static fromUint32Array(uint32Array, width, height) {\n        const container = new PointContainer();\n        container._width = width;\n        container._height = height;\n        for(let i = 0, l = uint32Array.length; i < l; i++){\n            container._pointArray[i] = Point.createByUint32(uint32Array[i] | 0);\n        }\n        return container;\n    }\n    static fromBuffer(buffer, width, height) {\n        const uint32Array = new Uint32Array(buffer.buffer, buffer.byteOffset, buffer.byteLength / Uint32Array.BYTES_PER_ELEMENT);\n        return PointContainer.fromUint32Array(uint32Array, width, height);\n    }\n};\n// src/utils/palette.ts\nvar hueGroups = 10;\nfunction hueGroup(hue, segmentsNumber) {\n    const maxHue = 360;\n    const seg = maxHue / segmentsNumber;\n    const half = seg / 2;\n    for(let i = 1, mid = seg - half; i < segmentsNumber; i++, mid += seg){\n        if (hue >= mid && hue < mid + seg) return i;\n    }\n    return 0;\n}\nvar Palette = class {\n    constructor(){\n        __publicField(this, \"_pointContainer\");\n        __publicField(this, \"_pointArray\", []);\n        __publicField(this, \"_i32idx\", {});\n        this._pointContainer = new PointContainer();\n        this._pointContainer.setHeight(1);\n        this._pointArray = this._pointContainer.getPointArray();\n    }\n    add(color) {\n        this._pointArray.push(color);\n        this._pointContainer.setWidth(this._pointArray.length);\n    }\n    has(color) {\n        for(let i = this._pointArray.length - 1; i >= 0; i--){\n            if (color.uint32 === this._pointArray[i].uint32) return true;\n        }\n        return false;\n    }\n    getNearestColor(colorDistanceCalculator, color) {\n        return this._pointArray[this._getNearestIndex(colorDistanceCalculator, color) | 0];\n    }\n    getPointContainer() {\n        return this._pointContainer;\n    }\n    _nearestPointFromCache(key) {\n        return typeof this._i32idx[key] === \"number\" ? this._i32idx[key] : -1;\n    }\n    _getNearestIndex(colorDistanceCalculator, point) {\n        let idx = this._nearestPointFromCache(\"\" + point.uint32);\n        if (idx >= 0) return idx;\n        let minimalDistance = Number.MAX_VALUE;\n        idx = 0;\n        for(let i = 0, l = this._pointArray.length; i < l; i++){\n            const p = this._pointArray[i];\n            const distance1 = colorDistanceCalculator.calculateRaw(point.r, point.g, point.b, point.a, p.r, p.g, p.b, p.a);\n            if (distance1 < minimalDistance) {\n                minimalDistance = distance1;\n                idx = i;\n            }\n        }\n        this._i32idx[point.uint32] = idx;\n        return idx;\n    }\n    sort() {\n        this._i32idx = {};\n        this._pointArray.sort((a, b)=>{\n            const hslA = rgb2hsl(a.r, a.g, a.b);\n            const hslB = rgb2hsl(b.r, b.g, b.b);\n            const hueA = a.r === a.g && a.g === a.b ? 0 : 1 + hueGroup(hslA.h, hueGroups);\n            const hueB = b.r === b.g && b.g === b.b ? 0 : 1 + hueGroup(hslB.h, hueGroups);\n            const hueDiff = hueB - hueA;\n            if (hueDiff) return -hueDiff;\n            const lA = a.getLuminosity(true);\n            const lB = b.getLuminosity(true);\n            if (lB - lA !== 0) return lB - lA;\n            const satDiff = (hslB.s * 100 | 0) - (hslA.s * 100 | 0);\n            if (satDiff) return -satDiff;\n            return 0;\n        });\n    }\n};\n// src/utils/index.ts\nvar utils_exports = {};\n__export(utils_exports, {\n    HueStatistics: ()=>HueStatistics,\n    Palette: ()=>Palette,\n    Point: ()=>Point,\n    PointContainer: ()=>PointContainer,\n    ProgressTracker: ()=>ProgressTracker,\n    arithmetic: ()=>arithmetic_exports\n});\n// src/utils/hueStatistics.ts\nvar HueGroup = class {\n    constructor(){\n        __publicField(this, \"num\", 0);\n        __publicField(this, \"cols\", []);\n    }\n};\nvar HueStatistics = class {\n    constructor(numGroups, minCols){\n        __publicField(this, \"_numGroups\");\n        __publicField(this, \"_minCols\");\n        __publicField(this, \"_stats\");\n        __publicField(this, \"_groupsFull\");\n        this._numGroups = numGroups;\n        this._minCols = minCols;\n        this._stats = [];\n        for(let i = 0; i <= numGroups; i++){\n            this._stats[i] = new HueGroup();\n        }\n        this._groupsFull = 0;\n    }\n    check(i32) {\n        if (this._groupsFull === this._numGroups + 1) {\n            this.check = ()=>{};\n        }\n        const r = i32 & 255;\n        const g = i32 >>> 8 & 255;\n        const b = i32 >>> 16 & 255;\n        const hg = r === g && g === b ? 0 : 1 + hueGroup(rgb2hsl(r, g, b).h, this._numGroups);\n        const gr = this._stats[hg];\n        const min = this._minCols;\n        gr.num++;\n        if (gr.num > min) {\n            return;\n        }\n        if (gr.num === min) {\n            this._groupsFull++;\n        }\n        if (gr.num <= min) {\n            this._stats[hg].cols.push(i32);\n        }\n    }\n    injectIntoDictionary(histG) {\n        for(let i = 0; i <= this._numGroups; i++){\n            if (this._stats[i].num <= this._minCols) {\n                this._stats[i].cols.forEach((col)=>{\n                    if (!histG[col]) {\n                        histG[col] = 1;\n                    } else {\n                        histG[col]++;\n                    }\n                });\n            }\n        }\n    }\n    injectIntoArray(histG) {\n        for(let i = 0; i <= this._numGroups; i++){\n            if (this._stats[i].num <= this._minCols) {\n                this._stats[i].cols.forEach((col)=>{\n                    if (histG.indexOf(col) === -1) {\n                        histG.push(col);\n                    }\n                });\n            }\n        }\n    }\n};\n// src/utils/progressTracker.ts\nvar _ProgressTracker = class {\n    constructor(valueRange, progressRange){\n        __publicField(this, \"progress\");\n        __publicField(this, \"_step\");\n        __publicField(this, \"_range\");\n        __publicField(this, \"_last\");\n        __publicField(this, \"_progressRange\");\n        this._range = valueRange;\n        this._progressRange = progressRange;\n        this._step = Math.max(1, this._range / (_ProgressTracker.steps + 1) | 0);\n        this._last = -this._step;\n        this.progress = 0;\n    }\n    shouldNotify(current) {\n        if (current - this._last >= this._step) {\n            this._last = current;\n            this.progress = Math.min(this._progressRange * this._last / this._range, this._progressRange);\n            return true;\n        }\n        return false;\n    }\n};\nvar ProgressTracker = _ProgressTracker;\n__publicField(ProgressTracker, \"steps\", 100);\n// src/palette/neuquant/neuquant.ts\nvar networkBiasShift = 3;\nvar Neuron = class {\n    constructor(defaultValue){\n        __publicField(this, \"r\");\n        __publicField(this, \"g\");\n        __publicField(this, \"b\");\n        __publicField(this, \"a\");\n        this.r = this.g = this.b = this.a = defaultValue;\n    }\n    toPoint() {\n        return Point.createByRGBA(this.r >> networkBiasShift, this.g >> networkBiasShift, this.b >> networkBiasShift, this.a >> networkBiasShift);\n    }\n    subtract(r, g, b, a) {\n        this.r -= r | 0;\n        this.g -= g | 0;\n        this.b -= b | 0;\n        this.a -= a | 0;\n    }\n};\nvar _NeuQuant = class extends AbstractPaletteQuantizer {\n    constructor(colorDistanceCalculator, colors = 256){\n        super();\n        __publicField(this, \"_pointArray\");\n        __publicField(this, \"_networkSize\");\n        __publicField(this, \"_network\");\n        __publicField(this, \"_sampleFactor\");\n        __publicField(this, \"_radPower\");\n        __publicField(this, \"_freq\");\n        __publicField(this, \"_bias\");\n        __publicField(this, \"_distance\");\n        this._distance = colorDistanceCalculator;\n        this._pointArray = [];\n        this._sampleFactor = 1;\n        this._networkSize = colors;\n        this._distance.setWhitePoint(255 << networkBiasShift, 255 << networkBiasShift, 255 << networkBiasShift, 255 << networkBiasShift);\n    }\n    sample(pointContainer) {\n        this._pointArray = this._pointArray.concat(pointContainer.getPointArray());\n    }\n    *quantize() {\n        this._init();\n        yield* this._learn();\n        yield {\n            palette: this._buildPalette(),\n            progress: 100\n        };\n    }\n    _init() {\n        this._freq = [];\n        this._bias = [];\n        this._radPower = [];\n        this._network = [];\n        for(let i = 0; i < this._networkSize; i++){\n            this._network[i] = new Neuron((i << networkBiasShift + 8) / this._networkSize | 0);\n            this._freq[i] = _NeuQuant._initialBias / this._networkSize | 0;\n            this._bias[i] = 0;\n        }\n    }\n    *_learn() {\n        let sampleFactor = this._sampleFactor;\n        const pointsNumber = this._pointArray.length;\n        if (pointsNumber < _NeuQuant._minpicturebytes) sampleFactor = 1;\n        const alphadec = 30 + (sampleFactor - 1) / 3 | 0;\n        const pointsToSample = pointsNumber / sampleFactor | 0;\n        let delta = pointsToSample / _NeuQuant._nCycles | 0;\n        let alpha = _NeuQuant._initAlpha;\n        let radius = (this._networkSize >> 3) * _NeuQuant._radiusBias;\n        let rad = radius >> _NeuQuant._radiusBiasShift;\n        if (rad <= 1) rad = 0;\n        for(let i = 0; i < rad; i++){\n            this._radPower[i] = alpha * ((rad * rad - i * i) * _NeuQuant._radBias / (rad * rad)) >>> 0;\n        }\n        let step;\n        if (pointsNumber < _NeuQuant._minpicturebytes) {\n            step = 1;\n        } else if (pointsNumber % _NeuQuant._prime1 !== 0) {\n            step = _NeuQuant._prime1;\n        } else if (pointsNumber % _NeuQuant._prime2 !== 0) {\n            step = _NeuQuant._prime2;\n        } else if (pointsNumber % _NeuQuant._prime3 !== 0) {\n            step = _NeuQuant._prime3;\n        } else {\n            step = _NeuQuant._prime4;\n        }\n        const tracker = new ProgressTracker(pointsToSample, 99);\n        for(let i = 0, pointIndex = 0; i < pointsToSample;){\n            if (tracker.shouldNotify(i)) {\n                yield {\n                    progress: tracker.progress\n                };\n            }\n            const point = this._pointArray[pointIndex];\n            const b = point.b << networkBiasShift;\n            const g = point.g << networkBiasShift;\n            const r = point.r << networkBiasShift;\n            const a = point.a << networkBiasShift;\n            const neuronIndex = this._contest(b, g, r, a);\n            this._alterSingle(alpha, neuronIndex, b, g, r, a);\n            if (rad !== 0) this._alterNeighbour(rad, neuronIndex, b, g, r, a);\n            pointIndex += step;\n            if (pointIndex >= pointsNumber) pointIndex -= pointsNumber;\n            i++;\n            if (delta === 0) delta = 1;\n            if (i % delta === 0) {\n                alpha -= alpha / alphadec | 0;\n                radius -= radius / _NeuQuant._radiusDecrease | 0;\n                rad = radius >> _NeuQuant._radiusBiasShift;\n                if (rad <= 1) rad = 0;\n                for(let j = 0; j < rad; j++){\n                    this._radPower[j] = alpha * ((rad * rad - j * j) * _NeuQuant._radBias / (rad * rad)) >>> 0;\n                }\n            }\n        }\n    }\n    _buildPalette() {\n        const palette1 = new Palette();\n        this._network.forEach((neuron)=>{\n            palette1.add(neuron.toPoint());\n        });\n        palette1.sort();\n        return palette1;\n    }\n    _alterNeighbour(rad, i, b, g, r, al) {\n        let lo = i - rad;\n        if (lo < -1) lo = -1;\n        let hi = i + rad;\n        if (hi > this._networkSize) hi = this._networkSize;\n        let j = i + 1;\n        let k = i - 1;\n        let m = 1;\n        while(j < hi || k > lo){\n            const a = this._radPower[m++] / _NeuQuant._alphaRadBias;\n            if (j < hi) {\n                const p = this._network[j++];\n                p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n            }\n            if (k > lo) {\n                const p = this._network[k--];\n                p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n            }\n        }\n    }\n    _alterSingle(alpha, i, b, g, r, a) {\n        alpha /= _NeuQuant._initAlpha;\n        const n = this._network[i];\n        n.subtract(alpha * (n.r - r), alpha * (n.g - g), alpha * (n.b - b), alpha * (n.a - a));\n    }\n    _contest(b, g, r, a) {\n        const multiplier = 255 * 4 << networkBiasShift;\n        let bestd = ~(1 << 31);\n        let bestbiasd = bestd;\n        let bestpos = -1;\n        let bestbiaspos = bestpos;\n        for(let i = 0; i < this._networkSize; i++){\n            const n = this._network[i];\n            const dist = this._distance.calculateNormalized(n, {\n                r,\n                g,\n                b,\n                a\n            }) * multiplier | 0;\n            if (dist < bestd) {\n                bestd = dist;\n                bestpos = i;\n            }\n            const biasdist = dist - (this._bias[i] >> _NeuQuant._initialBiasShift - networkBiasShift);\n            if (biasdist < bestbiasd) {\n                bestbiasd = biasdist;\n                bestbiaspos = i;\n            }\n            const betafreq = this._freq[i] >> _NeuQuant._betaShift;\n            this._freq[i] -= betafreq;\n            this._bias[i] += betafreq << _NeuQuant._gammaShift;\n        }\n        this._freq[bestpos] += _NeuQuant._beta;\n        this._bias[bestpos] -= _NeuQuant._betaGamma;\n        return bestbiaspos;\n    }\n};\nvar NeuQuant = _NeuQuant;\n__publicField(NeuQuant, \"_prime1\", 499);\n__publicField(NeuQuant, \"_prime2\", 491);\n__publicField(NeuQuant, \"_prime3\", 487);\n__publicField(NeuQuant, \"_prime4\", 503);\n__publicField(NeuQuant, \"_minpicturebytes\", _NeuQuant._prime4);\n__publicField(NeuQuant, \"_nCycles\", 100);\n__publicField(NeuQuant, \"_initialBiasShift\", 16);\n__publicField(NeuQuant, \"_initialBias\", 1 << _NeuQuant._initialBiasShift);\n__publicField(NeuQuant, \"_gammaShift\", 10);\n__publicField(NeuQuant, \"_betaShift\", 10);\n__publicField(NeuQuant, \"_beta\", _NeuQuant._initialBias >> _NeuQuant._betaShift);\n__publicField(NeuQuant, \"_betaGamma\", _NeuQuant._initialBias << _NeuQuant._gammaShift - _NeuQuant._betaShift);\n__publicField(NeuQuant, \"_radiusBiasShift\", 6);\n__publicField(NeuQuant, \"_radiusBias\", 1 << _NeuQuant._radiusBiasShift);\n__publicField(NeuQuant, \"_radiusDecrease\", 30);\n__publicField(NeuQuant, \"_alphaBiasShift\", 10);\n__publicField(NeuQuant, \"_initAlpha\", 1 << _NeuQuant._alphaBiasShift);\n__publicField(NeuQuant, \"_radBiasShift\", 8);\n__publicField(NeuQuant, \"_radBias\", 1 << _NeuQuant._radBiasShift);\n__publicField(NeuQuant, \"_alphaRadBiasShift\", _NeuQuant._alphaBiasShift + _NeuQuant._radBiasShift);\n__publicField(NeuQuant, \"_alphaRadBias\", 1 << _NeuQuant._alphaRadBiasShift);\n// src/palette/neuquant/neuquantFloat.ts\nvar networkBiasShift2 = 3;\nvar NeuronFloat = class {\n    constructor(defaultValue){\n        __publicField(this, \"r\");\n        __publicField(this, \"g\");\n        __publicField(this, \"b\");\n        __publicField(this, \"a\");\n        this.r = this.g = this.b = this.a = defaultValue;\n    }\n    toPoint() {\n        return Point.createByRGBA(this.r >> networkBiasShift2, this.g >> networkBiasShift2, this.b >> networkBiasShift2, this.a >> networkBiasShift2);\n    }\n    subtract(r, g, b, a) {\n        this.r -= r;\n        this.g -= g;\n        this.b -= b;\n        this.a -= a;\n    }\n};\nvar _NeuQuantFloat = class extends AbstractPaletteQuantizer {\n    constructor(colorDistanceCalculator, colors = 256){\n        super();\n        __publicField(this, \"_pointArray\");\n        __publicField(this, \"_networkSize\");\n        __publicField(this, \"_network\");\n        __publicField(this, \"_sampleFactor\");\n        __publicField(this, \"_radPower\");\n        __publicField(this, \"_freq\");\n        __publicField(this, \"_bias\");\n        __publicField(this, \"_distance\");\n        this._distance = colorDistanceCalculator;\n        this._pointArray = [];\n        this._sampleFactor = 1;\n        this._networkSize = colors;\n        this._distance.setWhitePoint(255 << networkBiasShift2, 255 << networkBiasShift2, 255 << networkBiasShift2, 255 << networkBiasShift2);\n    }\n    sample(pointContainer) {\n        this._pointArray = this._pointArray.concat(pointContainer.getPointArray());\n    }\n    *quantize() {\n        this._init();\n        yield* this._learn();\n        yield {\n            palette: this._buildPalette(),\n            progress: 100\n        };\n    }\n    _init() {\n        this._freq = [];\n        this._bias = [];\n        this._radPower = [];\n        this._network = [];\n        for(let i = 0; i < this._networkSize; i++){\n            this._network[i] = new NeuronFloat((i << networkBiasShift2 + 8) / this._networkSize);\n            this._freq[i] = _NeuQuantFloat._initialBias / this._networkSize;\n            this._bias[i] = 0;\n        }\n    }\n    *_learn() {\n        let sampleFactor = this._sampleFactor;\n        const pointsNumber = this._pointArray.length;\n        if (pointsNumber < _NeuQuantFloat._minpicturebytes) sampleFactor = 1;\n        const alphadec = 30 + (sampleFactor - 1) / 3;\n        const pointsToSample = pointsNumber / sampleFactor;\n        let delta = pointsToSample / _NeuQuantFloat._nCycles | 0;\n        let alpha = _NeuQuantFloat._initAlpha;\n        let radius = (this._networkSize >> 3) * _NeuQuantFloat._radiusBias;\n        let rad = radius >> _NeuQuantFloat._radiusBiasShift;\n        if (rad <= 1) rad = 0;\n        for(let i = 0; i < rad; i++){\n            this._radPower[i] = alpha * ((rad * rad - i * i) * _NeuQuantFloat._radBias / (rad * rad));\n        }\n        let step;\n        if (pointsNumber < _NeuQuantFloat._minpicturebytes) {\n            step = 1;\n        } else if (pointsNumber % _NeuQuantFloat._prime1 !== 0) {\n            step = _NeuQuantFloat._prime1;\n        } else if (pointsNumber % _NeuQuantFloat._prime2 !== 0) {\n            step = _NeuQuantFloat._prime2;\n        } else if (pointsNumber % _NeuQuantFloat._prime3 !== 0) {\n            step = _NeuQuantFloat._prime3;\n        } else {\n            step = _NeuQuantFloat._prime4;\n        }\n        const tracker = new ProgressTracker(pointsToSample, 99);\n        for(let i = 0, pointIndex = 0; i < pointsToSample;){\n            if (tracker.shouldNotify(i)) {\n                yield {\n                    progress: tracker.progress\n                };\n            }\n            const point = this._pointArray[pointIndex];\n            const b = point.b << networkBiasShift2;\n            const g = point.g << networkBiasShift2;\n            const r = point.r << networkBiasShift2;\n            const a = point.a << networkBiasShift2;\n            const neuronIndex = this._contest(b, g, r, a);\n            this._alterSingle(alpha, neuronIndex, b, g, r, a);\n            if (rad !== 0) this._alterNeighbour(rad, neuronIndex, b, g, r, a);\n            pointIndex += step;\n            if (pointIndex >= pointsNumber) pointIndex -= pointsNumber;\n            i++;\n            if (delta === 0) delta = 1;\n            if (i % delta === 0) {\n                alpha -= alpha / alphadec;\n                radius -= radius / _NeuQuantFloat._radiusDecrease;\n                rad = radius >> _NeuQuantFloat._radiusBiasShift;\n                if (rad <= 1) rad = 0;\n                for(let j = 0; j < rad; j++){\n                    this._radPower[j] = alpha * ((rad * rad - j * j) * _NeuQuantFloat._radBias / (rad * rad));\n                }\n            }\n        }\n    }\n    _buildPalette() {\n        const palette1 = new Palette();\n        this._network.forEach((neuron)=>{\n            palette1.add(neuron.toPoint());\n        });\n        palette1.sort();\n        return palette1;\n    }\n    _alterNeighbour(rad, i, b, g, r, al) {\n        let lo = i - rad;\n        if (lo < -1) lo = -1;\n        let hi = i + rad;\n        if (hi > this._networkSize) hi = this._networkSize;\n        let j = i + 1;\n        let k = i - 1;\n        let m = 1;\n        while(j < hi || k > lo){\n            const a = this._radPower[m++] / _NeuQuantFloat._alphaRadBias;\n            if (j < hi) {\n                const p = this._network[j++];\n                p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n            }\n            if (k > lo) {\n                const p = this._network[k--];\n                p.subtract(a * (p.r - r), a * (p.g - g), a * (p.b - b), a * (p.a - al));\n            }\n        }\n    }\n    _alterSingle(alpha, i, b, g, r, a) {\n        alpha /= _NeuQuantFloat._initAlpha;\n        const n = this._network[i];\n        n.subtract(alpha * (n.r - r), alpha * (n.g - g), alpha * (n.b - b), alpha * (n.a - a));\n    }\n    _contest(b, g, r, al) {\n        const multiplier = 255 * 4 << networkBiasShift2;\n        let bestd = ~(1 << 31);\n        let bestbiasd = bestd;\n        let bestpos = -1;\n        let bestbiaspos = bestpos;\n        for(let i = 0; i < this._networkSize; i++){\n            const n = this._network[i];\n            const dist = this._distance.calculateNormalized(n, {\n                r,\n                g,\n                b,\n                a: al\n            }) * multiplier;\n            if (dist < bestd) {\n                bestd = dist;\n                bestpos = i;\n            }\n            const biasdist = dist - (this._bias[i] >> _NeuQuantFloat._initialBiasShift - networkBiasShift2);\n            if (biasdist < bestbiasd) {\n                bestbiasd = biasdist;\n                bestbiaspos = i;\n            }\n            const betafreq = this._freq[i] >> _NeuQuantFloat._betaShift;\n            this._freq[i] -= betafreq;\n            this._bias[i] += betafreq << _NeuQuantFloat._gammaShift;\n        }\n        this._freq[bestpos] += _NeuQuantFloat._beta;\n        this._bias[bestpos] -= _NeuQuantFloat._betaGamma;\n        return bestbiaspos;\n    }\n};\nvar NeuQuantFloat = _NeuQuantFloat;\n__publicField(NeuQuantFloat, \"_prime1\", 499);\n__publicField(NeuQuantFloat, \"_prime2\", 491);\n__publicField(NeuQuantFloat, \"_prime3\", 487);\n__publicField(NeuQuantFloat, \"_prime4\", 503);\n__publicField(NeuQuantFloat, \"_minpicturebytes\", _NeuQuantFloat._prime4);\n__publicField(NeuQuantFloat, \"_nCycles\", 100);\n__publicField(NeuQuantFloat, \"_initialBiasShift\", 16);\n__publicField(NeuQuantFloat, \"_initialBias\", 1 << _NeuQuantFloat._initialBiasShift);\n__publicField(NeuQuantFloat, \"_gammaShift\", 10);\n__publicField(NeuQuantFloat, \"_betaShift\", 10);\n__publicField(NeuQuantFloat, \"_beta\", _NeuQuantFloat._initialBias >> _NeuQuantFloat._betaShift);\n__publicField(NeuQuantFloat, \"_betaGamma\", _NeuQuantFloat._initialBias << _NeuQuantFloat._gammaShift - _NeuQuantFloat._betaShift);\n__publicField(NeuQuantFloat, \"_radiusBiasShift\", 6);\n__publicField(NeuQuantFloat, \"_radiusBias\", 1 << _NeuQuantFloat._radiusBiasShift);\n__publicField(NeuQuantFloat, \"_radiusDecrease\", 30);\n__publicField(NeuQuantFloat, \"_alphaBiasShift\", 10);\n__publicField(NeuQuantFloat, \"_initAlpha\", 1 << _NeuQuantFloat._alphaBiasShift);\n__publicField(NeuQuantFloat, \"_radBiasShift\", 8);\n__publicField(NeuQuantFloat, \"_radBias\", 1 << _NeuQuantFloat._radBiasShift);\n__publicField(NeuQuantFloat, \"_alphaRadBiasShift\", _NeuQuantFloat._alphaBiasShift + _NeuQuantFloat._radBiasShift);\n__publicField(NeuQuantFloat, \"_alphaRadBias\", 1 << _NeuQuantFloat._alphaRadBiasShift);\n// src/palette/rgbquant/colorHistogram.ts\nvar _ColorHistogram = class {\n    constructor(method, colors){\n        __publicField(this, \"_method\");\n        __publicField(this, \"_hueStats\");\n        __publicField(this, \"_histogram\");\n        __publicField(this, \"_initColors\");\n        __publicField(this, \"_minHueCols\");\n        this._method = method;\n        this._minHueCols = colors << 2;\n        this._initColors = colors << 2;\n        this._hueStats = new HueStatistics(_ColorHistogram._hueGroups, this._minHueCols);\n        this._histogram = /* @__PURE__ */ Object.create(null);\n    }\n    sample(pointContainer) {\n        switch(this._method){\n            case 1:\n                this._colorStats1D(pointContainer);\n                break;\n            case 2:\n                this._colorStats2D(pointContainer);\n                break;\n        }\n    }\n    getImportanceSortedColorsIDXI32() {\n        const sorted = stableSort(Object.keys(this._histogram), (a, b)=>this._histogram[b] - this._histogram[a]);\n        if (sorted.length === 0) {\n            return [];\n        }\n        let idxi32;\n        switch(this._method){\n            case 1:\n                const initialColorsLimit = Math.min(sorted.length, this._initColors);\n                const last = sorted[initialColorsLimit - 1];\n                const freq = this._histogram[last];\n                idxi32 = sorted.slice(0, initialColorsLimit);\n                let pos = initialColorsLimit;\n                const len = sorted.length;\n                while(pos < len && this._histogram[sorted[pos]] === freq){\n                    idxi32.push(sorted[pos++]);\n                }\n                this._hueStats.injectIntoArray(idxi32);\n                break;\n            case 2:\n                idxi32 = sorted;\n                break;\n            default:\n                throw new Error(\"Incorrect method\");\n        }\n        return idxi32.map((v)=>+v);\n    }\n    _colorStats1D(pointContainer) {\n        const histG = this._histogram;\n        const pointArray = pointContainer.getPointArray();\n        const len = pointArray.length;\n        for(let i = 0; i < len; i++){\n            const col = pointArray[i].uint32;\n            this._hueStats.check(col);\n            if (col in histG) {\n                histG[col]++;\n            } else {\n                histG[col] = 1;\n            }\n        }\n    }\n    _colorStats2D(pointContainer) {\n        const width = pointContainer.getWidth();\n        const height = pointContainer.getHeight();\n        const pointArray = pointContainer.getPointArray();\n        const boxW = _ColorHistogram._boxSize[0];\n        const boxH = _ColorHistogram._boxSize[1];\n        const area = boxW * boxH;\n        const boxes = this._makeBoxes(width, height, boxW, boxH);\n        const histG = this._histogram;\n        boxes.forEach((box)=>{\n            let effc = Math.round(box.w * box.h / area) * _ColorHistogram._boxPixels;\n            if (effc < 2) effc = 2;\n            const histL = {};\n            this._iterateBox(box, width, (i)=>{\n                const col = pointArray[i].uint32;\n                this._hueStats.check(col);\n                if (col in histG) {\n                    histG[col]++;\n                } else if (col in histL) {\n                    if (++histL[col] >= effc) {\n                        histG[col] = histL[col];\n                    }\n                } else {\n                    histL[col] = 1;\n                }\n            });\n        });\n        this._hueStats.injectIntoDictionary(histG);\n    }\n    _iterateBox(bbox, wid, fn) {\n        const b = bbox;\n        const i0 = b.y * wid + b.x;\n        const i1 = (b.y + b.h - 1) * wid + (b.x + b.w - 1);\n        const incr = wid - b.w + 1;\n        let cnt = 0;\n        let i = i0;\n        do {\n            fn.call(this, i);\n            i += ++cnt % b.w === 0 ? incr : 1;\n        }while (i <= i1);\n    }\n    _makeBoxes(width, height, stepX, stepY) {\n        const wrem = width % stepX;\n        const hrem = height % stepY;\n        const xend = width - wrem;\n        const yend = height - hrem;\n        const boxesArray = [];\n        for(let y2 = 0; y2 < height; y2 += stepY){\n            for(let x2 = 0; x2 < width; x2 += stepX){\n                boxesArray.push({\n                    x: x2,\n                    y: y2,\n                    w: x2 === xend ? wrem : stepX,\n                    h: y2 === yend ? hrem : stepY\n                });\n            }\n        }\n        return boxesArray;\n    }\n};\nvar ColorHistogram = _ColorHistogram;\n__publicField(ColorHistogram, \"_boxSize\", [\n    64,\n    64\n]);\n__publicField(ColorHistogram, \"_boxPixels\", 2);\n__publicField(ColorHistogram, \"_hueGroups\", 10);\n// src/palette/rgbquant/rgbquant.ts\nvar RemovedColor = class {\n    constructor(index, color, distance1){\n        __publicField(this, \"index\");\n        __publicField(this, \"color\");\n        __publicField(this, \"distance\");\n        this.index = index;\n        this.color = color;\n        this.distance = distance1;\n    }\n};\nvar RGBQuant = class extends AbstractPaletteQuantizer {\n    constructor(colorDistanceCalculator, colors = 256, method = 2){\n        super();\n        __publicField(this, \"_colors\");\n        __publicField(this, \"_initialDistance\");\n        __publicField(this, \"_distanceIncrement\");\n        __publicField(this, \"_histogram\");\n        __publicField(this, \"_distance\");\n        this._distance = colorDistanceCalculator;\n        this._colors = colors;\n        this._histogram = new ColorHistogram(method, colors);\n        this._initialDistance = 0.01;\n        this._distanceIncrement = 5e-3;\n    }\n    sample(image1) {\n        this._histogram.sample(image1);\n    }\n    *quantize() {\n        const idxi32 = this._histogram.getImportanceSortedColorsIDXI32();\n        if (idxi32.length === 0) {\n            throw new Error(\"No colors in image\");\n        }\n        yield* this._buildPalette(idxi32);\n    }\n    *_buildPalette(idxi32) {\n        const palette1 = new Palette();\n        const colorArray = palette1.getPointContainer().getPointArray();\n        const usageArray = new Array(idxi32.length);\n        for(let i = 0; i < idxi32.length; i++){\n            colorArray.push(Point.createByUint32(idxi32[i]));\n            usageArray[i] = 1;\n        }\n        const len = colorArray.length;\n        const memDist = [];\n        let palLen = len;\n        let thold = this._initialDistance;\n        const tracker = new ProgressTracker(palLen - this._colors, 99);\n        while(palLen > this._colors){\n            memDist.length = 0;\n            for(let i = 0; i < len; i++){\n                if (tracker.shouldNotify(len - palLen)) {\n                    yield {\n                        progress: tracker.progress\n                    };\n                }\n                if (usageArray[i] === 0) continue;\n                const pxi = colorArray[i];\n                for(let j = i + 1; j < len; j++){\n                    if (usageArray[j] === 0) continue;\n                    const pxj = colorArray[j];\n                    const dist = this._distance.calculateNormalized(pxi, pxj);\n                    if (dist < thold) {\n                        memDist.push(new RemovedColor(j, pxj, dist));\n                        usageArray[j] = 0;\n                        palLen--;\n                    }\n                }\n            }\n            thold += palLen > this._colors * 3 ? this._initialDistance : this._distanceIncrement;\n        }\n        if (palLen < this._colors) {\n            stableSort(memDist, (a, b)=>b.distance - a.distance);\n            let k = 0;\n            while(palLen < this._colors && k < memDist.length){\n                const removedColor = memDist[k];\n                usageArray[removedColor.index] = 1;\n                palLen++;\n                k++;\n            }\n        }\n        let colors = colorArray.length;\n        for(let colorIndex = colors - 1; colorIndex >= 0; colorIndex--){\n            if (usageArray[colorIndex] === 0) {\n                if (colorIndex !== colors - 1) {\n                    colorArray[colorIndex] = colorArray[colors - 1];\n                }\n                --colors;\n            }\n        }\n        colorArray.length = colors;\n        palette1.sort();\n        yield {\n            palette: palette1,\n            progress: 100\n        };\n    }\n};\n// src/palette/wu/wuQuant.ts\nfunction createArray1D(dimension1) {\n    const a = [];\n    for(let k = 0; k < dimension1; k++){\n        a[k] = 0;\n    }\n    return a;\n}\nfunction createArray4D(dimension1, dimension2, dimension3, dimension4) {\n    const a = new Array(dimension1);\n    for(let i = 0; i < dimension1; i++){\n        a[i] = new Array(dimension2);\n        for(let j = 0; j < dimension2; j++){\n            a[i][j] = new Array(dimension3);\n            for(let k = 0; k < dimension3; k++){\n                a[i][j][k] = new Array(dimension4);\n                for(let l = 0; l < dimension4; l++){\n                    a[i][j][k][l] = 0;\n                }\n            }\n        }\n    }\n    return a;\n}\nfunction createArray3D(dimension1, dimension2, dimension3) {\n    const a = new Array(dimension1);\n    for(let i = 0; i < dimension1; i++){\n        a[i] = new Array(dimension2);\n        for(let j = 0; j < dimension2; j++){\n            a[i][j] = new Array(dimension3);\n            for(let k = 0; k < dimension3; k++){\n                a[i][j][k] = 0;\n            }\n        }\n    }\n    return a;\n}\nfunction fillArray3D(a, dimension1, dimension2, dimension3, value) {\n    for(let i = 0; i < dimension1; i++){\n        a[i] = [];\n        for(let j = 0; j < dimension2; j++){\n            a[i][j] = [];\n            for(let k = 0; k < dimension3; k++){\n                a[i][j][k] = value;\n            }\n        }\n    }\n}\nfunction fillArray1D(a, dimension1, value) {\n    for(let i = 0; i < dimension1; i++){\n        a[i] = value;\n    }\n}\nvar WuColorCube = class {\n    constructor(){\n        __publicField(this, \"redMinimum\");\n        __publicField(this, \"redMaximum\");\n        __publicField(this, \"greenMinimum\");\n        __publicField(this, \"greenMaximum\");\n        __publicField(this, \"blueMinimum\");\n        __publicField(this, \"blueMaximum\");\n        __publicField(this, \"volume\");\n        __publicField(this, \"alphaMinimum\");\n        __publicField(this, \"alphaMaximum\");\n    }\n};\nvar _WuQuant = class extends AbstractPaletteQuantizer {\n    constructor(colorDistanceCalculator, colors = 256, significantBitsPerChannel = 5){\n        super();\n        __publicField(this, \"_reds\");\n        __publicField(this, \"_greens\");\n        __publicField(this, \"_blues\");\n        __publicField(this, \"_alphas\");\n        __publicField(this, \"_sums\");\n        __publicField(this, \"_weights\");\n        __publicField(this, \"_momentsRed\");\n        __publicField(this, \"_momentsGreen\");\n        __publicField(this, \"_momentsBlue\");\n        __publicField(this, \"_momentsAlpha\");\n        __publicField(this, \"_moments\");\n        __publicField(this, \"_table\");\n        __publicField(this, \"_pixels\");\n        __publicField(this, \"_cubes\");\n        __publicField(this, \"_colors\");\n        __publicField(this, \"_significantBitsPerChannel\");\n        __publicField(this, \"_maxSideIndex\");\n        __publicField(this, \"_alphaMaxSideIndex\");\n        __publicField(this, \"_sideSize\");\n        __publicField(this, \"_alphaSideSize\");\n        __publicField(this, \"_distance\");\n        this._distance = colorDistanceCalculator;\n        this._setQuality(significantBitsPerChannel);\n        this._initialize(colors);\n    }\n    sample(image1) {\n        const pointArray = image1.getPointArray();\n        for(let i = 0, l = pointArray.length; i < l; i++){\n            this._addColor(pointArray[i]);\n        }\n        this._pixels = this._pixels.concat(pointArray);\n    }\n    *quantize() {\n        yield* this._preparePalette();\n        const palette1 = new Palette();\n        for(let paletteIndex = 0; paletteIndex < this._colors; paletteIndex++){\n            if (this._sums[paletteIndex] > 0) {\n                const sum = this._sums[paletteIndex];\n                const r = this._reds[paletteIndex] / sum;\n                const g = this._greens[paletteIndex] / sum;\n                const b = this._blues[paletteIndex] / sum;\n                const a = this._alphas[paletteIndex] / sum;\n                const color = Point.createByRGBA(r | 0, g | 0, b | 0, a | 0);\n                palette1.add(color);\n            }\n        }\n        palette1.sort();\n        yield {\n            palette: palette1,\n            progress: 100\n        };\n    }\n    *_preparePalette() {\n        yield* this._calculateMoments();\n        let next = 0;\n        const volumeVariance = createArray1D(this._colors);\n        for(let cubeIndex = 1; cubeIndex < this._colors; ++cubeIndex){\n            if (this._cut(this._cubes[next], this._cubes[cubeIndex])) {\n                volumeVariance[next] = this._cubes[next].volume > 1 ? this._calculateVariance(this._cubes[next]) : 0;\n                volumeVariance[cubeIndex] = this._cubes[cubeIndex].volume > 1 ? this._calculateVariance(this._cubes[cubeIndex]) : 0;\n            } else {\n                volumeVariance[next] = 0;\n                cubeIndex--;\n            }\n            next = 0;\n            let temp = volumeVariance[0];\n            for(let index = 1; index <= cubeIndex; ++index){\n                if (volumeVariance[index] > temp) {\n                    temp = volumeVariance[index];\n                    next = index;\n                }\n            }\n            if (temp <= 0) {\n                this._colors = cubeIndex + 1;\n                break;\n            }\n        }\n        const lookupRed = [];\n        const lookupGreen = [];\n        const lookupBlue = [];\n        const lookupAlpha = [];\n        for(let k = 0; k < this._colors; ++k){\n            const weight = _WuQuant._volume(this._cubes[k], this._weights);\n            if (weight > 0) {\n                lookupRed[k] = _WuQuant._volume(this._cubes[k], this._momentsRed) / weight | 0;\n                lookupGreen[k] = _WuQuant._volume(this._cubes[k], this._momentsGreen) / weight | 0;\n                lookupBlue[k] = _WuQuant._volume(this._cubes[k], this._momentsBlue) / weight | 0;\n                lookupAlpha[k] = _WuQuant._volume(this._cubes[k], this._momentsAlpha) / weight | 0;\n            } else {\n                lookupRed[k] = 0;\n                lookupGreen[k] = 0;\n                lookupBlue[k] = 0;\n                lookupAlpha[k] = 0;\n            }\n        }\n        this._reds = createArray1D(this._colors + 1);\n        this._greens = createArray1D(this._colors + 1);\n        this._blues = createArray1D(this._colors + 1);\n        this._alphas = createArray1D(this._colors + 1);\n        this._sums = createArray1D(this._colors + 1);\n        for(let index = 0, l = this._pixels.length; index < l; index++){\n            const color = this._pixels[index];\n            const match = -1;\n            let bestMatch = match;\n            let bestDistance = Number.MAX_VALUE;\n            for(let lookup = 0; lookup < this._colors; lookup++){\n                const foundRed = lookupRed[lookup];\n                const foundGreen = lookupGreen[lookup];\n                const foundBlue = lookupBlue[lookup];\n                const foundAlpha = lookupAlpha[lookup];\n                const distance1 = this._distance.calculateRaw(foundRed, foundGreen, foundBlue, foundAlpha, color.r, color.g, color.b, color.a);\n                if (distance1 < bestDistance) {\n                    bestDistance = distance1;\n                    bestMatch = lookup;\n                }\n            }\n            this._reds[bestMatch] += color.r;\n            this._greens[bestMatch] += color.g;\n            this._blues[bestMatch] += color.b;\n            this._alphas[bestMatch] += color.a;\n            this._sums[bestMatch]++;\n        }\n    }\n    _addColor(color) {\n        const bitsToRemove = 8 - this._significantBitsPerChannel;\n        const indexRed = (color.r >> bitsToRemove) + 1;\n        const indexGreen = (color.g >> bitsToRemove) + 1;\n        const indexBlue = (color.b >> bitsToRemove) + 1;\n        const indexAlpha = (color.a >> bitsToRemove) + 1;\n        this._weights[indexAlpha][indexRed][indexGreen][indexBlue]++;\n        this._momentsRed[indexAlpha][indexRed][indexGreen][indexBlue] += color.r;\n        this._momentsGreen[indexAlpha][indexRed][indexGreen][indexBlue] += color.g;\n        this._momentsBlue[indexAlpha][indexRed][indexGreen][indexBlue] += color.b;\n        this._momentsAlpha[indexAlpha][indexRed][indexGreen][indexBlue] += color.a;\n        this._moments[indexAlpha][indexRed][indexGreen][indexBlue] += this._table[color.r] + this._table[color.g] + this._table[color.b] + this._table[color.a];\n    }\n    *_calculateMoments() {\n        const area = [];\n        const areaRed = [];\n        const areaGreen = [];\n        const areaBlue = [];\n        const areaAlpha = [];\n        const area2 = [];\n        const xarea = createArray3D(this._sideSize, this._sideSize, this._sideSize);\n        const xareaRed = createArray3D(this._sideSize, this._sideSize, this._sideSize);\n        const xareaGreen = createArray3D(this._sideSize, this._sideSize, this._sideSize);\n        const xareaBlue = createArray3D(this._sideSize, this._sideSize, this._sideSize);\n        const xareaAlpha = createArray3D(this._sideSize, this._sideSize, this._sideSize);\n        const xarea2 = createArray3D(this._sideSize, this._sideSize, this._sideSize);\n        let trackerProgress = 0;\n        const tracker = new ProgressTracker(this._alphaMaxSideIndex * this._maxSideIndex, 99);\n        for(let alphaIndex = 1; alphaIndex <= this._alphaMaxSideIndex; ++alphaIndex){\n            fillArray3D(xarea, this._sideSize, this._sideSize, this._sideSize, 0);\n            fillArray3D(xareaRed, this._sideSize, this._sideSize, this._sideSize, 0);\n            fillArray3D(xareaGreen, this._sideSize, this._sideSize, this._sideSize, 0);\n            fillArray3D(xareaBlue, this._sideSize, this._sideSize, this._sideSize, 0);\n            fillArray3D(xareaAlpha, this._sideSize, this._sideSize, this._sideSize, 0);\n            fillArray3D(xarea2, this._sideSize, this._sideSize, this._sideSize, 0);\n            for(let redIndex = 1; redIndex <= this._maxSideIndex; ++redIndex, ++trackerProgress){\n                if (tracker.shouldNotify(trackerProgress)) {\n                    yield {\n                        progress: tracker.progress\n                    };\n                }\n                fillArray1D(area, this._sideSize, 0);\n                fillArray1D(areaRed, this._sideSize, 0);\n                fillArray1D(areaGreen, this._sideSize, 0);\n                fillArray1D(areaBlue, this._sideSize, 0);\n                fillArray1D(areaAlpha, this._sideSize, 0);\n                fillArray1D(area2, this._sideSize, 0);\n                for(let greenIndex = 1; greenIndex <= this._maxSideIndex; ++greenIndex){\n                    let line = 0;\n                    let lineRed = 0;\n                    let lineGreen = 0;\n                    let lineBlue = 0;\n                    let lineAlpha = 0;\n                    let line2 = 0;\n                    for(let blueIndex = 1; blueIndex <= this._maxSideIndex; ++blueIndex){\n                        line += this._weights[alphaIndex][redIndex][greenIndex][blueIndex];\n                        lineRed += this._momentsRed[alphaIndex][redIndex][greenIndex][blueIndex];\n                        lineGreen += this._momentsGreen[alphaIndex][redIndex][greenIndex][blueIndex];\n                        lineBlue += this._momentsBlue[alphaIndex][redIndex][greenIndex][blueIndex];\n                        lineAlpha += this._momentsAlpha[alphaIndex][redIndex][greenIndex][blueIndex];\n                        line2 += this._moments[alphaIndex][redIndex][greenIndex][blueIndex];\n                        area[blueIndex] += line;\n                        areaRed[blueIndex] += lineRed;\n                        areaGreen[blueIndex] += lineGreen;\n                        areaBlue[blueIndex] += lineBlue;\n                        areaAlpha[blueIndex] += lineAlpha;\n                        area2[blueIndex] += line2;\n                        xarea[redIndex][greenIndex][blueIndex] = xarea[redIndex - 1][greenIndex][blueIndex] + area[blueIndex];\n                        xareaRed[redIndex][greenIndex][blueIndex] = xareaRed[redIndex - 1][greenIndex][blueIndex] + areaRed[blueIndex];\n                        xareaGreen[redIndex][greenIndex][blueIndex] = xareaGreen[redIndex - 1][greenIndex][blueIndex] + areaGreen[blueIndex];\n                        xareaBlue[redIndex][greenIndex][blueIndex] = xareaBlue[redIndex - 1][greenIndex][blueIndex] + areaBlue[blueIndex];\n                        xareaAlpha[redIndex][greenIndex][blueIndex] = xareaAlpha[redIndex - 1][greenIndex][blueIndex] + areaAlpha[blueIndex];\n                        xarea2[redIndex][greenIndex][blueIndex] = xarea2[redIndex - 1][greenIndex][blueIndex] + area2[blueIndex];\n                        this._weights[alphaIndex][redIndex][greenIndex][blueIndex] = this._weights[alphaIndex - 1][redIndex][greenIndex][blueIndex] + xarea[redIndex][greenIndex][blueIndex];\n                        this._momentsRed[alphaIndex][redIndex][greenIndex][blueIndex] = this._momentsRed[alphaIndex - 1][redIndex][greenIndex][blueIndex] + xareaRed[redIndex][greenIndex][blueIndex];\n                        this._momentsGreen[alphaIndex][redIndex][greenIndex][blueIndex] = this._momentsGreen[alphaIndex - 1][redIndex][greenIndex][blueIndex] + xareaGreen[redIndex][greenIndex][blueIndex];\n                        this._momentsBlue[alphaIndex][redIndex][greenIndex][blueIndex] = this._momentsBlue[alphaIndex - 1][redIndex][greenIndex][blueIndex] + xareaBlue[redIndex][greenIndex][blueIndex];\n                        this._momentsAlpha[alphaIndex][redIndex][greenIndex][blueIndex] = this._momentsAlpha[alphaIndex - 1][redIndex][greenIndex][blueIndex] + xareaAlpha[redIndex][greenIndex][blueIndex];\n                        this._moments[alphaIndex][redIndex][greenIndex][blueIndex] = this._moments[alphaIndex - 1][redIndex][greenIndex][blueIndex] + xarea2[redIndex][greenIndex][blueIndex];\n                    }\n                }\n            }\n        }\n    }\n    static _volumeFloat(cube, moment) {\n        return moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][cube.blueMaximum] - moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][cube.blueMaximum] - moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][cube.blueMaximum] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] - moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][cube.blueMaximum] + moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][cube.blueMaximum] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][cube.blueMaximum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] - (moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][cube.blueMinimum] - moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][cube.blueMinimum] - moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] - moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum]);\n    }\n    static _volume(cube, moment) {\n        return _WuQuant._volumeFloat(cube, moment) | 0;\n    }\n    static _top(cube, direction, position, moment) {\n        let result;\n        switch(direction){\n            case _WuQuant._alpha:\n                result = moment[position][cube.redMaximum][cube.greenMaximum][cube.blueMaximum] - moment[position][cube.redMaximum][cube.greenMinimum][cube.blueMaximum] - moment[position][cube.redMinimum][cube.greenMaximum][cube.blueMaximum] + moment[position][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] - (moment[position][cube.redMaximum][cube.greenMaximum][cube.blueMinimum] - moment[position][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] - moment[position][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] + moment[position][cube.redMinimum][cube.greenMinimum][cube.blueMinimum]);\n                break;\n            case _WuQuant._red:\n                result = moment[cube.alphaMaximum][position][cube.greenMaximum][cube.blueMaximum] - moment[cube.alphaMaximum][position][cube.greenMinimum][cube.blueMaximum] - moment[cube.alphaMinimum][position][cube.greenMaximum][cube.blueMaximum] + moment[cube.alphaMinimum][position][cube.greenMinimum][cube.blueMaximum] - (moment[cube.alphaMaximum][position][cube.greenMaximum][cube.blueMinimum] - moment[cube.alphaMaximum][position][cube.greenMinimum][cube.blueMinimum] - moment[cube.alphaMinimum][position][cube.greenMaximum][cube.blueMinimum] + moment[cube.alphaMinimum][position][cube.greenMinimum][cube.blueMinimum]);\n                break;\n            case _WuQuant._green:\n                result = moment[cube.alphaMaximum][cube.redMaximum][position][cube.blueMaximum] - moment[cube.alphaMaximum][cube.redMinimum][position][cube.blueMaximum] - moment[cube.alphaMinimum][cube.redMaximum][position][cube.blueMaximum] + moment[cube.alphaMinimum][cube.redMinimum][position][cube.blueMaximum] - (moment[cube.alphaMaximum][cube.redMaximum][position][cube.blueMinimum] - moment[cube.alphaMaximum][cube.redMinimum][position][cube.blueMinimum] - moment[cube.alphaMinimum][cube.redMaximum][position][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMinimum][position][cube.blueMinimum]);\n                break;\n            case _WuQuant._blue:\n                result = moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][position] - moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][position] - moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][position] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][position] - (moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][position] - moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][position] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][position] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][position]);\n                break;\n            default:\n                throw new Error(\"impossible\");\n        }\n        return result | 0;\n    }\n    static _bottom(cube, direction, moment) {\n        switch(direction){\n            case _WuQuant._alpha:\n                return -moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][cube.blueMaximum] + moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][cube.blueMaximum] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][cube.blueMaximum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] - (-moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum]);\n            case _WuQuant._red:\n                return -moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][cube.blueMaximum] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][cube.blueMaximum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] - (-moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum]);\n            case _WuQuant._green:\n                return -moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][cube.blueMaximum] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] + moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][cube.blueMaximum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMaximum] - (-moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum]);\n            case _WuQuant._blue:\n                return -moment[cube.alphaMaximum][cube.redMaximum][cube.greenMaximum][cube.blueMinimum] + moment[cube.alphaMaximum][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] + moment[cube.alphaMaximum][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] - moment[cube.alphaMaximum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum] - (-moment[cube.alphaMinimum][cube.redMaximum][cube.greenMaximum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMaximum][cube.greenMinimum][cube.blueMinimum] + moment[cube.alphaMinimum][cube.redMinimum][cube.greenMaximum][cube.blueMinimum] - moment[cube.alphaMinimum][cube.redMinimum][cube.greenMinimum][cube.blueMinimum]);\n            default:\n                return 0;\n        }\n    }\n    _calculateVariance(cube) {\n        const volumeRed = _WuQuant._volume(cube, this._momentsRed);\n        const volumeGreen = _WuQuant._volume(cube, this._momentsGreen);\n        const volumeBlue = _WuQuant._volume(cube, this._momentsBlue);\n        const volumeAlpha = _WuQuant._volume(cube, this._momentsAlpha);\n        const volumeMoment = _WuQuant._volumeFloat(cube, this._moments);\n        const volumeWeight = _WuQuant._volume(cube, this._weights);\n        const distance1 = volumeRed * volumeRed + volumeGreen * volumeGreen + volumeBlue * volumeBlue + volumeAlpha * volumeAlpha;\n        return volumeMoment - distance1 / volumeWeight;\n    }\n    _maximize(cube, direction, first, last, wholeRed, wholeGreen, wholeBlue, wholeAlpha, wholeWeight) {\n        const bottomRed = _WuQuant._bottom(cube, direction, this._momentsRed) | 0;\n        const bottomGreen = _WuQuant._bottom(cube, direction, this._momentsGreen) | 0;\n        const bottomBlue = _WuQuant._bottom(cube, direction, this._momentsBlue) | 0;\n        const bottomAlpha = _WuQuant._bottom(cube, direction, this._momentsAlpha) | 0;\n        const bottomWeight = _WuQuant._bottom(cube, direction, this._weights) | 0;\n        let result = 0;\n        let cutPosition = -1;\n        for(let position = first; position < last; ++position){\n            let halfRed = bottomRed + _WuQuant._top(cube, direction, position, this._momentsRed);\n            let halfGreen = bottomGreen + _WuQuant._top(cube, direction, position, this._momentsGreen);\n            let halfBlue = bottomBlue + _WuQuant._top(cube, direction, position, this._momentsBlue);\n            let halfAlpha = bottomAlpha + _WuQuant._top(cube, direction, position, this._momentsAlpha);\n            let halfWeight = bottomWeight + _WuQuant._top(cube, direction, position, this._weights);\n            if (halfWeight !== 0) {\n                let halfDistance = halfRed * halfRed + halfGreen * halfGreen + halfBlue * halfBlue + halfAlpha * halfAlpha;\n                let temp = halfDistance / halfWeight;\n                halfRed = wholeRed - halfRed;\n                halfGreen = wholeGreen - halfGreen;\n                halfBlue = wholeBlue - halfBlue;\n                halfAlpha = wholeAlpha - halfAlpha;\n                halfWeight = wholeWeight - halfWeight;\n                if (halfWeight !== 0) {\n                    halfDistance = halfRed * halfRed + halfGreen * halfGreen + halfBlue * halfBlue + halfAlpha * halfAlpha;\n                    temp += halfDistance / halfWeight;\n                    if (temp > result) {\n                        result = temp;\n                        cutPosition = position;\n                    }\n                }\n            }\n        }\n        return {\n            max: result,\n            position: cutPosition\n        };\n    }\n    _cut(first, second) {\n        let direction;\n        const wholeRed = _WuQuant._volume(first, this._momentsRed);\n        const wholeGreen = _WuQuant._volume(first, this._momentsGreen);\n        const wholeBlue = _WuQuant._volume(first, this._momentsBlue);\n        const wholeAlpha = _WuQuant._volume(first, this._momentsAlpha);\n        const wholeWeight = _WuQuant._volume(first, this._weights);\n        const red = this._maximize(first, _WuQuant._red, first.redMinimum + 1, first.redMaximum, wholeRed, wholeGreen, wholeBlue, wholeAlpha, wholeWeight);\n        const green = this._maximize(first, _WuQuant._green, first.greenMinimum + 1, first.greenMaximum, wholeRed, wholeGreen, wholeBlue, wholeAlpha, wholeWeight);\n        const blue = this._maximize(first, _WuQuant._blue, first.blueMinimum + 1, first.blueMaximum, wholeRed, wholeGreen, wholeBlue, wholeAlpha, wholeWeight);\n        const alpha = this._maximize(first, _WuQuant._alpha, first.alphaMinimum + 1, first.alphaMaximum, wholeRed, wholeGreen, wholeBlue, wholeAlpha, wholeWeight);\n        if (alpha.max >= red.max && alpha.max >= green.max && alpha.max >= blue.max) {\n            direction = _WuQuant._alpha;\n            if (alpha.position < 0) return false;\n        } else if (red.max >= alpha.max && red.max >= green.max && red.max >= blue.max) {\n            direction = _WuQuant._red;\n        } else if (green.max >= alpha.max && green.max >= red.max && green.max >= blue.max) {\n            direction = _WuQuant._green;\n        } else {\n            direction = _WuQuant._blue;\n        }\n        second.redMaximum = first.redMaximum;\n        second.greenMaximum = first.greenMaximum;\n        second.blueMaximum = first.blueMaximum;\n        second.alphaMaximum = first.alphaMaximum;\n        switch(direction){\n            case _WuQuant._red:\n                second.redMinimum = first.redMaximum = red.position;\n                second.greenMinimum = first.greenMinimum;\n                second.blueMinimum = first.blueMinimum;\n                second.alphaMinimum = first.alphaMinimum;\n                break;\n            case _WuQuant._green:\n                second.greenMinimum = first.greenMaximum = green.position;\n                second.redMinimum = first.redMinimum;\n                second.blueMinimum = first.blueMinimum;\n                second.alphaMinimum = first.alphaMinimum;\n                break;\n            case _WuQuant._blue:\n                second.blueMinimum = first.blueMaximum = blue.position;\n                second.redMinimum = first.redMinimum;\n                second.greenMinimum = first.greenMinimum;\n                second.alphaMinimum = first.alphaMinimum;\n                break;\n            case _WuQuant._alpha:\n                second.alphaMinimum = first.alphaMaximum = alpha.position;\n                second.blueMinimum = first.blueMinimum;\n                second.redMinimum = first.redMinimum;\n                second.greenMinimum = first.greenMinimum;\n                break;\n        }\n        first.volume = (first.redMaximum - first.redMinimum) * (first.greenMaximum - first.greenMinimum) * (first.blueMaximum - first.blueMinimum) * (first.alphaMaximum - first.alphaMinimum);\n        second.volume = (second.redMaximum - second.redMinimum) * (second.greenMaximum - second.greenMinimum) * (second.blueMaximum - second.blueMinimum) * (second.alphaMaximum - second.alphaMinimum);\n        return true;\n    }\n    _initialize(colors) {\n        this._colors = colors;\n        this._cubes = [];\n        for(let cubeIndex = 0; cubeIndex < colors; cubeIndex++){\n            this._cubes[cubeIndex] = new WuColorCube();\n        }\n        this._cubes[0].redMinimum = 0;\n        this._cubes[0].greenMinimum = 0;\n        this._cubes[0].blueMinimum = 0;\n        this._cubes[0].alphaMinimum = 0;\n        this._cubes[0].redMaximum = this._maxSideIndex;\n        this._cubes[0].greenMaximum = this._maxSideIndex;\n        this._cubes[0].blueMaximum = this._maxSideIndex;\n        this._cubes[0].alphaMaximum = this._alphaMaxSideIndex;\n        this._weights = createArray4D(this._alphaSideSize, this._sideSize, this._sideSize, this._sideSize);\n        this._momentsRed = createArray4D(this._alphaSideSize, this._sideSize, this._sideSize, this._sideSize);\n        this._momentsGreen = createArray4D(this._alphaSideSize, this._sideSize, this._sideSize, this._sideSize);\n        this._momentsBlue = createArray4D(this._alphaSideSize, this._sideSize, this._sideSize, this._sideSize);\n        this._momentsAlpha = createArray4D(this._alphaSideSize, this._sideSize, this._sideSize, this._sideSize);\n        this._moments = createArray4D(this._alphaSideSize, this._sideSize, this._sideSize, this._sideSize);\n        this._table = [];\n        for(let tableIndex = 0; tableIndex < 256; ++tableIndex){\n            this._table[tableIndex] = tableIndex * tableIndex;\n        }\n        this._pixels = [];\n    }\n    _setQuality(significantBitsPerChannel = 5) {\n        this._significantBitsPerChannel = significantBitsPerChannel;\n        this._maxSideIndex = 1 << this._significantBitsPerChannel;\n        this._alphaMaxSideIndex = this._maxSideIndex;\n        this._sideSize = this._maxSideIndex + 1;\n        this._alphaSideSize = this._alphaMaxSideIndex + 1;\n    }\n};\nvar WuQuant = _WuQuant;\n__publicField(WuQuant, \"_alpha\", 3);\n__publicField(WuQuant, \"_red\", 2);\n__publicField(WuQuant, \"_green\", 1);\n__publicField(WuQuant, \"_blue\", 0);\n// src/image/index.ts\nvar image_exports = {};\n__export(image_exports, {\n    AbstractImageQuantizer: ()=>AbstractImageQuantizer,\n    ErrorDiffusionArray: ()=>ErrorDiffusionArray,\n    ErrorDiffusionArrayKernel: ()=>ErrorDiffusionArrayKernel,\n    ErrorDiffusionRiemersma: ()=>ErrorDiffusionRiemersma,\n    NearestColor: ()=>NearestColor\n});\n// src/image/imageQuantizer.ts\nvar AbstractImageQuantizer = class {\n    quantizeSync(pointContainer, palette1) {\n        for (const value of this.quantize(pointContainer, palette1)){\n            if (value.pointContainer) {\n                return value.pointContainer;\n            }\n        }\n        throw new Error(\"unreachable\");\n    }\n};\n// src/image/nearestColor.ts\nvar NearestColor = class extends AbstractImageQuantizer {\n    constructor(colorDistanceCalculator){\n        super();\n        __publicField(this, \"_distance\");\n        this._distance = colorDistanceCalculator;\n    }\n    *quantize(pointContainer, palette1) {\n        const pointArray = pointContainer.getPointArray();\n        const width = pointContainer.getWidth();\n        const height = pointContainer.getHeight();\n        const tracker = new ProgressTracker(height, 99);\n        for(let y2 = 0; y2 < height; y2++){\n            if (tracker.shouldNotify(y2)) {\n                yield {\n                    progress: tracker.progress\n                };\n            }\n            for(let x2 = 0, idx = y2 * width; x2 < width; x2++, idx++){\n                const point = pointArray[idx];\n                point.from(palette1.getNearestColor(this._distance, point));\n            }\n        }\n        yield {\n            pointContainer,\n            progress: 100\n        };\n    }\n};\n// src/image/array.ts\nvar ErrorDiffusionArrayKernel = /* @__PURE__ */ ((ErrorDiffusionArrayKernel2)=>{\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"FloydSteinberg\"] = 0] = \"FloydSteinberg\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"FalseFloydSteinberg\"] = 1] = \"FalseFloydSteinberg\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"Stucki\"] = 2] = \"Stucki\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"Atkinson\"] = 3] = \"Atkinson\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"Jarvis\"] = 4] = \"Jarvis\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"Burkes\"] = 5] = \"Burkes\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"Sierra\"] = 6] = \"Sierra\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"TwoSierra\"] = 7] = \"TwoSierra\";\n    ErrorDiffusionArrayKernel2[ErrorDiffusionArrayKernel2[\"SierraLite\"] = 8] = \"SierraLite\";\n    return ErrorDiffusionArrayKernel2;\n})(ErrorDiffusionArrayKernel || {});\nvar ErrorDiffusionArray = class extends AbstractImageQuantizer {\n    constructor(colorDistanceCalculator, kernel, serpentine = true, minimumColorDistanceToDither = 0, calculateErrorLikeGIMP = false){\n        super();\n        __publicField(this, \"_minColorDistance\");\n        __publicField(this, \"_serpentine\");\n        __publicField(this, \"_kernel\");\n        __publicField(this, \"_calculateErrorLikeGIMP\");\n        __publicField(this, \"_distance\");\n        this._setKernel(kernel);\n        this._distance = colorDistanceCalculator;\n        this._minColorDistance = minimumColorDistanceToDither;\n        this._serpentine = serpentine;\n        this._calculateErrorLikeGIMP = calculateErrorLikeGIMP;\n    }\n    *quantize(pointContainer, palette1) {\n        const pointArray = pointContainer.getPointArray();\n        const originalPoint = new Point();\n        const width = pointContainer.getWidth();\n        const height = pointContainer.getHeight();\n        const errorLines = [];\n        let dir = 1;\n        let maxErrorLines = 1;\n        for (const kernel of this._kernel){\n            const kernelErrorLines = kernel[2] + 1;\n            if (maxErrorLines < kernelErrorLines) maxErrorLines = kernelErrorLines;\n        }\n        for(let i = 0; i < maxErrorLines; i++){\n            this._fillErrorLine(errorLines[i] = [], width);\n        }\n        const tracker = new ProgressTracker(height, 99);\n        for(let y2 = 0; y2 < height; y2++){\n            if (tracker.shouldNotify(y2)) {\n                yield {\n                    progress: tracker.progress\n                };\n            }\n            if (this._serpentine) dir *= -1;\n            const lni = y2 * width;\n            const xStart = dir === 1 ? 0 : width - 1;\n            const xEnd = dir === 1 ? width : -1;\n            this._fillErrorLine(errorLines[0], width);\n            errorLines.push(errorLines.shift());\n            const errorLine = errorLines[0];\n            for(let x2 = xStart, idx = lni + xStart; x2 !== xEnd; x2 += dir, idx += dir){\n                const point = pointArray[idx];\n                const error = errorLine[x2];\n                originalPoint.from(point);\n                const correctedPoint = Point.createByRGBA(inRange0to255Rounded(point.r + error[0]), inRange0to255Rounded(point.g + error[1]), inRange0to255Rounded(point.b + error[2]), inRange0to255Rounded(point.a + error[3]));\n                const palettePoint = palette1.getNearestColor(this._distance, correctedPoint);\n                point.from(palettePoint);\n                if (this._minColorDistance) {\n                    const dist = this._distance.calculateNormalized(originalPoint, palettePoint);\n                    if (dist < this._minColorDistance) continue;\n                }\n                let er;\n                let eg;\n                let eb;\n                let ea;\n                if (this._calculateErrorLikeGIMP) {\n                    er = correctedPoint.r - palettePoint.r;\n                    eg = correctedPoint.g - palettePoint.g;\n                    eb = correctedPoint.b - palettePoint.b;\n                    ea = correctedPoint.a - palettePoint.a;\n                } else {\n                    er = originalPoint.r - palettePoint.r;\n                    eg = originalPoint.g - palettePoint.g;\n                    eb = originalPoint.b - palettePoint.b;\n                    ea = originalPoint.a - palettePoint.a;\n                }\n                const dStart = dir === 1 ? 0 : this._kernel.length - 1;\n                const dEnd = dir === 1 ? this._kernel.length : -1;\n                for(let i = dStart; i !== dEnd; i += dir){\n                    const x1 = this._kernel[i][1] * dir;\n                    const y1 = this._kernel[i][2];\n                    if (x1 + x2 >= 0 && x1 + x2 < width && y1 + y2 >= 0 && y1 + y2 < height) {\n                        const d = this._kernel[i][0];\n                        const e = errorLines[y1][x1 + x2];\n                        e[0] += er * d;\n                        e[1] += eg * d;\n                        e[2] += eb * d;\n                        e[3] += ea * d;\n                    }\n                }\n            }\n        }\n        yield {\n            pointContainer,\n            progress: 100\n        };\n    }\n    _fillErrorLine(errorLine, width) {\n        if (errorLine.length > width) {\n            errorLine.length = width;\n        }\n        const l = errorLine.length;\n        for(let i = 0; i < l; i++){\n            const error = errorLine[i];\n            error[0] = error[1] = error[2] = error[3] = 0;\n        }\n        for(let i = l; i < width; i++){\n            errorLine[i] = [\n                0,\n                0,\n                0,\n                0\n            ];\n        }\n    }\n    _setKernel(kernel) {\n        switch(kernel){\n            case 0 /* FloydSteinberg */ :\n                this._kernel = [\n                    [\n                        7 / 16,\n                        1,\n                        0\n                    ],\n                    [\n                        3 / 16,\n                        -1,\n                        1\n                    ],\n                    [\n                        5 / 16,\n                        0,\n                        1\n                    ],\n                    [\n                        1 / 16,\n                        1,\n                        1\n                    ]\n                ];\n                break;\n            case 1 /* FalseFloydSteinberg */ :\n                this._kernel = [\n                    [\n                        3 / 8,\n                        1,\n                        0\n                    ],\n                    [\n                        3 / 8,\n                        0,\n                        1\n                    ],\n                    [\n                        2 / 8,\n                        1,\n                        1\n                    ]\n                ];\n                break;\n            case 2 /* Stucki */ :\n                this._kernel = [\n                    [\n                        8 / 42,\n                        1,\n                        0\n                    ],\n                    [\n                        4 / 42,\n                        2,\n                        0\n                    ],\n                    [\n                        2 / 42,\n                        -2,\n                        1\n                    ],\n                    [\n                        4 / 42,\n                        -1,\n                        1\n                    ],\n                    [\n                        8 / 42,\n                        0,\n                        1\n                    ],\n                    [\n                        4 / 42,\n                        1,\n                        1\n                    ],\n                    [\n                        2 / 42,\n                        2,\n                        1\n                    ],\n                    [\n                        1 / 42,\n                        -2,\n                        2\n                    ],\n                    [\n                        2 / 42,\n                        -1,\n                        2\n                    ],\n                    [\n                        4 / 42,\n                        0,\n                        2\n                    ],\n                    [\n                        2 / 42,\n                        1,\n                        2\n                    ],\n                    [\n                        1 / 42,\n                        2,\n                        2\n                    ]\n                ];\n                break;\n            case 3 /* Atkinson */ :\n                this._kernel = [\n                    [\n                        1 / 8,\n                        1,\n                        0\n                    ],\n                    [\n                        1 / 8,\n                        2,\n                        0\n                    ],\n                    [\n                        1 / 8,\n                        -1,\n                        1\n                    ],\n                    [\n                        1 / 8,\n                        0,\n                        1\n                    ],\n                    [\n                        1 / 8,\n                        1,\n                        1\n                    ],\n                    [\n                        1 / 8,\n                        0,\n                        2\n                    ]\n                ];\n                break;\n            case 4 /* Jarvis */ :\n                this._kernel = [\n                    [\n                        7 / 48,\n                        1,\n                        0\n                    ],\n                    [\n                        5 / 48,\n                        2,\n                        0\n                    ],\n                    [\n                        3 / 48,\n                        -2,\n                        1\n                    ],\n                    [\n                        5 / 48,\n                        -1,\n                        1\n                    ],\n                    [\n                        7 / 48,\n                        0,\n                        1\n                    ],\n                    [\n                        5 / 48,\n                        1,\n                        1\n                    ],\n                    [\n                        3 / 48,\n                        2,\n                        1\n                    ],\n                    [\n                        1 / 48,\n                        -2,\n                        2\n                    ],\n                    [\n                        3 / 48,\n                        -1,\n                        2\n                    ],\n                    [\n                        5 / 48,\n                        0,\n                        2\n                    ],\n                    [\n                        3 / 48,\n                        1,\n                        2\n                    ],\n                    [\n                        1 / 48,\n                        2,\n                        2\n                    ]\n                ];\n                break;\n            case 5 /* Burkes */ :\n                this._kernel = [\n                    [\n                        8 / 32,\n                        1,\n                        0\n                    ],\n                    [\n                        4 / 32,\n                        2,\n                        0\n                    ],\n                    [\n                        2 / 32,\n                        -2,\n                        1\n                    ],\n                    [\n                        4 / 32,\n                        -1,\n                        1\n                    ],\n                    [\n                        8 / 32,\n                        0,\n                        1\n                    ],\n                    [\n                        4 / 32,\n                        1,\n                        1\n                    ],\n                    [\n                        2 / 32,\n                        2,\n                        1\n                    ]\n                ];\n                break;\n            case 6 /* Sierra */ :\n                this._kernel = [\n                    [\n                        5 / 32,\n                        1,\n                        0\n                    ],\n                    [\n                        3 / 32,\n                        2,\n                        0\n                    ],\n                    [\n                        2 / 32,\n                        -2,\n                        1\n                    ],\n                    [\n                        4 / 32,\n                        -1,\n                        1\n                    ],\n                    [\n                        5 / 32,\n                        0,\n                        1\n                    ],\n                    [\n                        4 / 32,\n                        1,\n                        1\n                    ],\n                    [\n                        2 / 32,\n                        2,\n                        1\n                    ],\n                    [\n                        2 / 32,\n                        -1,\n                        2\n                    ],\n                    [\n                        3 / 32,\n                        0,\n                        2\n                    ],\n                    [\n                        2 / 32,\n                        1,\n                        2\n                    ]\n                ];\n                break;\n            case 7 /* TwoSierra */ :\n                this._kernel = [\n                    [\n                        4 / 16,\n                        1,\n                        0\n                    ],\n                    [\n                        3 / 16,\n                        2,\n                        0\n                    ],\n                    [\n                        1 / 16,\n                        -2,\n                        1\n                    ],\n                    [\n                        2 / 16,\n                        -1,\n                        1\n                    ],\n                    [\n                        3 / 16,\n                        0,\n                        1\n                    ],\n                    [\n                        2 / 16,\n                        1,\n                        1\n                    ],\n                    [\n                        1 / 16,\n                        2,\n                        1\n                    ]\n                ];\n                break;\n            case 8 /* SierraLite */ :\n                this._kernel = [\n                    [\n                        2 / 4,\n                        1,\n                        0\n                    ],\n                    [\n                        1 / 4,\n                        -1,\n                        1\n                    ],\n                    [\n                        1 / 4,\n                        0,\n                        1\n                    ]\n                ];\n                break;\n            default:\n                throw new Error(`ErrorDiffusionArray: unknown kernel = ${kernel}`);\n        }\n    }\n};\n// src/image/spaceFillingCurves/hilbertCurve.ts\nfunction* hilbertCurve(width, height, callback) {\n    const maxBound = Math.max(width, height);\n    const level = Math.floor(Math.log(maxBound) / Math.log(2) + 1);\n    const tracker = new ProgressTracker(width * height, 99);\n    const data = {\n        width,\n        height,\n        level,\n        callback,\n        tracker,\n        index: 0,\n        x: 0,\n        y: 0\n    };\n    yield* walkHilbert(data, 1 /* UP */ );\n    visit(data, 0 /* NONE */ );\n}\nfunction* walkHilbert(data, direction) {\n    if (data.level < 1) return;\n    if (data.tracker.shouldNotify(data.index)) {\n        yield {\n            progress: data.tracker.progress\n        };\n    }\n    data.level--;\n    switch(direction){\n        case 2 /* LEFT */ :\n            yield* walkHilbert(data, 1 /* UP */ );\n            visit(data, 3 /* RIGHT */ );\n            yield* walkHilbert(data, 2 /* LEFT */ );\n            visit(data, 4 /* DOWN */ );\n            yield* walkHilbert(data, 2 /* LEFT */ );\n            visit(data, 2 /* LEFT */ );\n            yield* walkHilbert(data, 4 /* DOWN */ );\n            break;\n        case 3 /* RIGHT */ :\n            yield* walkHilbert(data, 4 /* DOWN */ );\n            visit(data, 2 /* LEFT */ );\n            yield* walkHilbert(data, 3 /* RIGHT */ );\n            visit(data, 1 /* UP */ );\n            yield* walkHilbert(data, 3 /* RIGHT */ );\n            visit(data, 3 /* RIGHT */ );\n            yield* walkHilbert(data, 1 /* UP */ );\n            break;\n        case 1 /* UP */ :\n            yield* walkHilbert(data, 2 /* LEFT */ );\n            visit(data, 4 /* DOWN */ );\n            yield* walkHilbert(data, 1 /* UP */ );\n            visit(data, 3 /* RIGHT */ );\n            yield* walkHilbert(data, 1 /* UP */ );\n            visit(data, 1 /* UP */ );\n            yield* walkHilbert(data, 3 /* RIGHT */ );\n            break;\n        case 4 /* DOWN */ :\n            yield* walkHilbert(data, 3 /* RIGHT */ );\n            visit(data, 1 /* UP */ );\n            yield* walkHilbert(data, 4 /* DOWN */ );\n            visit(data, 2 /* LEFT */ );\n            yield* walkHilbert(data, 4 /* DOWN */ );\n            visit(data, 4 /* DOWN */ );\n            yield* walkHilbert(data, 2 /* LEFT */ );\n            break;\n        default:\n            break;\n    }\n    data.level++;\n}\nfunction visit(data, direction) {\n    if (data.x >= 0 && data.x < data.width && data.y >= 0 && data.y < data.height) {\n        data.callback(data.x, data.y);\n        data.index++;\n    }\n    switch(direction){\n        case 2 /* LEFT */ :\n            data.x--;\n            break;\n        case 3 /* RIGHT */ :\n            data.x++;\n            break;\n        case 1 /* UP */ :\n            data.y--;\n            break;\n        case 4 /* DOWN */ :\n            data.y++;\n            break;\n    }\n}\n// src/image/riemersma.ts\nvar ErrorDiffusionRiemersma = class extends AbstractImageQuantizer {\n    constructor(colorDistanceCalculator, errorQueueSize = 16, errorPropagation = 1){\n        super();\n        __publicField(this, \"_distance\");\n        __publicField(this, \"_weights\");\n        __publicField(this, \"_errorQueueSize\");\n        this._distance = colorDistanceCalculator;\n        this._errorQueueSize = errorQueueSize;\n        this._weights = ErrorDiffusionRiemersma._createWeights(errorPropagation, errorQueueSize);\n    }\n    *quantize(pointContainer, palette1) {\n        const pointArray = pointContainer.getPointArray();\n        const width = pointContainer.getWidth();\n        const height = pointContainer.getHeight();\n        const errorQueue = [];\n        let head = 0;\n        for(let i = 0; i < this._errorQueueSize; i++){\n            errorQueue[i] = {\n                r: 0,\n                g: 0,\n                b: 0,\n                a: 0\n            };\n        }\n        yield* hilbertCurve(width, height, (x2, y2)=>{\n            const p = pointArray[x2 + y2 * width];\n            let { r, g, b, a } = p;\n            for(let i = 0; i < this._errorQueueSize; i++){\n                const weight = this._weights[i];\n                const e = errorQueue[(i + head) % this._errorQueueSize];\n                r += e.r * weight;\n                g += e.g * weight;\n                b += e.b * weight;\n                a += e.a * weight;\n            }\n            const correctedPoint = Point.createByRGBA(inRange0to255Rounded(r), inRange0to255Rounded(g), inRange0to255Rounded(b), inRange0to255Rounded(a));\n            const quantizedPoint = palette1.getNearestColor(this._distance, correctedPoint);\n            head = (head + 1) % this._errorQueueSize;\n            const tail = (head + this._errorQueueSize - 1) % this._errorQueueSize;\n            errorQueue[tail].r = p.r - quantizedPoint.r;\n            errorQueue[tail].g = p.g - quantizedPoint.g;\n            errorQueue[tail].b = p.b - quantizedPoint.b;\n            errorQueue[tail].a = p.a - quantizedPoint.a;\n            p.from(quantizedPoint);\n        });\n        yield {\n            pointContainer,\n            progress: 100\n        };\n    }\n    static _createWeights(errorPropagation, errorQueueSize) {\n        const weights = [];\n        const multiplier = Math.exp(Math.log(errorQueueSize) / (errorQueueSize - 1));\n        for(let i = 0, next = 1; i < errorQueueSize; i++){\n            weights[i] = (next + 0.5 | 0) / errorQueueSize * errorPropagation;\n            next *= multiplier;\n        }\n        return weights;\n    }\n};\n// src/quality/index.ts\nvar quality_exports = {};\n__export(quality_exports, {\n    ssim: ()=>ssim\n});\n// src/quality/ssim.ts\nvar K1 = 0.01;\nvar K2 = 0.03;\nfunction ssim(image1, image2) {\n    if (image1.getHeight() !== image2.getHeight() || image1.getWidth() !== image2.getWidth()) {\n        throw new Error(\"Images have different sizes!\");\n    }\n    const bitsPerComponent = 8;\n    const L = (1 << bitsPerComponent) - 1;\n    const c1 = (K1 * L) ** 2;\n    const c2 = (K2 * L) ** 2;\n    let numWindows = 0;\n    let mssim = 0;\n    iterate(image1, image2, (lumaValues1, lumaValues2, averageLumaValue1, averageLumaValue2)=>{\n        let sigxy = 0;\n        let sigsqx = 0;\n        let sigsqy = 0;\n        for(let i = 0; i < lumaValues1.length; i++){\n            sigsqx += (lumaValues1[i] - averageLumaValue1) ** 2;\n            sigsqy += (lumaValues2[i] - averageLumaValue2) ** 2;\n            sigxy += (lumaValues1[i] - averageLumaValue1) * (lumaValues2[i] - averageLumaValue2);\n        }\n        const numPixelsInWin = lumaValues1.length - 1;\n        sigsqx /= numPixelsInWin;\n        sigsqy /= numPixelsInWin;\n        sigxy /= numPixelsInWin;\n        const numerator = (2 * averageLumaValue1 * averageLumaValue2 + c1) * (2 * sigxy + c2);\n        const denominator = (averageLumaValue1 ** 2 + averageLumaValue2 ** 2 + c1) * (sigsqx + sigsqy + c2);\n        const ssim2 = numerator / denominator;\n        mssim += ssim2;\n        numWindows++;\n    });\n    return mssim / numWindows;\n}\nfunction iterate(image1, image2, callback) {\n    const windowSize = 8;\n    const width = image1.getWidth();\n    const height = image1.getHeight();\n    for(let y2 = 0; y2 < height; y2 += windowSize){\n        for(let x2 = 0; x2 < width; x2 += windowSize){\n            const windowWidth = Math.min(windowSize, width - x2);\n            const windowHeight = Math.min(windowSize, height - y2);\n            const lumaValues1 = calculateLumaValuesForWindow(image1, x2, y2, windowWidth, windowHeight);\n            const lumaValues2 = calculateLumaValuesForWindow(image2, x2, y2, windowWidth, windowHeight);\n            const averageLuma1 = calculateAverageLuma(lumaValues1);\n            const averageLuma2 = calculateAverageLuma(lumaValues2);\n            callback(lumaValues1, lumaValues2, averageLuma1, averageLuma2);\n        }\n    }\n}\nfunction calculateLumaValuesForWindow(image1, x2, y2, width, height) {\n    const pointArray = image1.getPointArray();\n    const lumaValues = [];\n    let counter = 0;\n    for(let j = y2; j < y2 + height; j++){\n        const offset = j * image1.getWidth();\n        for(let i = x2; i < x2 + width; i++){\n            const point = pointArray[offset + i];\n            lumaValues[counter] = point.r * 0.2126 /* RED */  + point.g * 0.7152 /* GREEN */  + point.b * 0.0722 /* BLUE */ ;\n            counter++;\n        }\n    }\n    return lumaValues;\n}\nfunction calculateAverageLuma(lumaValues) {\n    let sumLuma = 0;\n    for (const luma of lumaValues){\n        sumLuma += luma;\n    }\n    return sumLuma / lumaValues.length;\n}\n// src/basicAPI.ts\nvar setImmediateImpl = typeof setImmediate === \"function\" ? setImmediate : typeof process !== \"undefined\" && typeof (process == null ? void 0 : process.nextTick) === \"function\" ? (callback)=>process.nextTick(callback) : (callback)=>setTimeout(callback, 0);\nfunction buildPaletteSync(images, { colorDistanceFormula, paletteQuantization, colors } = {}) {\n    const distanceCalculator = colorDistanceFormulaToColorDistance(colorDistanceFormula);\n    const paletteQuantizer = paletteQuantizationToPaletteQuantizer(distanceCalculator, paletteQuantization, colors);\n    images.forEach((image1)=>paletteQuantizer.sample(image1));\n    return paletteQuantizer.quantizeSync();\n}\nasync function buildPalette(images, { colorDistanceFormula, paletteQuantization, colors, onProgress } = {}) {\n    return new Promise((resolve, reject)=>{\n        const distanceCalculator = colorDistanceFormulaToColorDistance(colorDistanceFormula);\n        const paletteQuantizer = paletteQuantizationToPaletteQuantizer(distanceCalculator, paletteQuantization, colors);\n        images.forEach((image1)=>paletteQuantizer.sample(image1));\n        let palette1;\n        const iterator = paletteQuantizer.quantize();\n        const next = ()=>{\n            try {\n                const result = iterator.next();\n                if (result.done) {\n                    resolve(palette1);\n                } else {\n                    if (result.value.palette) palette1 = result.value.palette;\n                    if (onProgress) onProgress(result.value.progress);\n                    setImmediateImpl(next);\n                }\n            } catch (error) {\n                reject(error);\n            }\n        };\n        setImmediateImpl(next);\n    });\n}\nfunction applyPaletteSync(image1, palette1, { colorDistanceFormula, imageQuantization } = {}) {\n    const distanceCalculator = colorDistanceFormulaToColorDistance(colorDistanceFormula);\n    const imageQuantizer = imageQuantizationToImageQuantizer(distanceCalculator, imageQuantization);\n    return imageQuantizer.quantizeSync(image1, palette1);\n}\nasync function applyPalette(image1, palette1, { colorDistanceFormula, imageQuantization, onProgress } = {}) {\n    return new Promise((resolve, reject)=>{\n        const distanceCalculator = colorDistanceFormulaToColorDistance(colorDistanceFormula);\n        const imageQuantizer = imageQuantizationToImageQuantizer(distanceCalculator, imageQuantization);\n        let outPointContainer;\n        const iterator = imageQuantizer.quantize(image1, palette1);\n        const next = ()=>{\n            try {\n                const result = iterator.next();\n                if (result.done) {\n                    resolve(outPointContainer);\n                } else {\n                    if (result.value.pointContainer) {\n                        outPointContainer = result.value.pointContainer;\n                    }\n                    if (onProgress) onProgress(result.value.progress);\n                    setImmediateImpl(next);\n                }\n            } catch (error) {\n                reject(error);\n            }\n        };\n        setImmediateImpl(next);\n    });\n}\nfunction colorDistanceFormulaToColorDistance(colorDistanceFormula = \"euclidean-bt709\") {\n    switch(colorDistanceFormula){\n        case \"cie94-graphic-arts\":\n            return new CIE94GraphicArts();\n        case \"cie94-textiles\":\n            return new CIE94Textiles();\n        case \"ciede2000\":\n            return new CIEDE2000();\n        case \"color-metric\":\n            return new CMetric();\n        case \"euclidean\":\n            return new Euclidean();\n        case \"euclidean-bt709\":\n            return new EuclideanBT709();\n        case \"euclidean-bt709-noalpha\":\n            return new EuclideanBT709NoAlpha();\n        case \"manhattan\":\n            return new Manhattan();\n        case \"manhattan-bt709\":\n            return new ManhattanBT709();\n        case \"manhattan-nommyde\":\n            return new ManhattanNommyde();\n        case \"pngquant\":\n            return new PNGQuant();\n        default:\n            throw new Error(`Unknown colorDistanceFormula ${colorDistanceFormula}`);\n    }\n}\nfunction imageQuantizationToImageQuantizer(distanceCalculator, imageQuantization = \"floyd-steinberg\") {\n    switch(imageQuantization){\n        case \"nearest\":\n            return new NearestColor(distanceCalculator);\n        case \"riemersma\":\n            return new ErrorDiffusionRiemersma(distanceCalculator);\n        case \"floyd-steinberg\":\n            return new ErrorDiffusionArray(distanceCalculator, 0 /* FloydSteinberg */ );\n        case \"false-floyd-steinberg\":\n            return new ErrorDiffusionArray(distanceCalculator, 1 /* FalseFloydSteinberg */ );\n        case \"stucki\":\n            return new ErrorDiffusionArray(distanceCalculator, 2 /* Stucki */ );\n        case \"atkinson\":\n            return new ErrorDiffusionArray(distanceCalculator, 3 /* Atkinson */ );\n        case \"jarvis\":\n            return new ErrorDiffusionArray(distanceCalculator, 4 /* Jarvis */ );\n        case \"burkes\":\n            return new ErrorDiffusionArray(distanceCalculator, 5 /* Burkes */ );\n        case \"sierra\":\n            return new ErrorDiffusionArray(distanceCalculator, 6 /* Sierra */ );\n        case \"two-sierra\":\n            return new ErrorDiffusionArray(distanceCalculator, 7 /* TwoSierra */ );\n        case \"sierra-lite\":\n            return new ErrorDiffusionArray(distanceCalculator, 8 /* SierraLite */ );\n        default:\n            throw new Error(`Unknown imageQuantization ${imageQuantization}`);\n    }\n}\nfunction paletteQuantizationToPaletteQuantizer(distanceCalculator, paletteQuantization = \"wuquant\", colors = 256) {\n    switch(paletteQuantization){\n        case \"neuquant\":\n            return new NeuQuant(distanceCalculator, colors);\n        case \"rgbquant\":\n            return new RGBQuant(distanceCalculator, colors);\n        case \"wuquant\":\n            return new WuQuant(distanceCalculator, colors);\n        case \"neuquant-float\":\n            return new NeuQuantFloat(distanceCalculator, colors);\n        default:\n            throw new Error(`Unknown paletteQuantization ${paletteQuantization}`);\n    }\n}\nmodule.exports = __toCommonJS(src_exports);\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0); /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * cie94.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * ciede2000.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * cmetric.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * common.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * constants.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * ditherErrorDiffusionArray.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * euclidean.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * helper.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * hueStatistics.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * iq.ts - Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * lab2rgb.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * lab2xyz.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * manhattanNeuQuant.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * nearestColor.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * palette.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * pngQuant.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * point.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * pointContainer.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * rgb2hsl.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * rgb2lab.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * rgb2xyz.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * ssim.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * wuQuant.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * xyz2lab.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * xyz2rgb.ts - part of Image Quantization Library\n */  /**\n * @preserve\n * MIT License\n *\n * Copyright 2015-2018 Igor Bezkrovnyi\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to\n * deal in the Software without restriction, including without limitation the\n * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n * sell copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL\n * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n * IN THE SOFTWARE.\n *\n * riemersma.ts - part of Image Quantization Library\n */  /**\n * @preserve TypeScript port:\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * colorHistogram.ts - part of Image Quantization Library\n */  /**\n * @preserve TypeScript port:\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * neuquant.ts - part of Image Quantization Library\n */  /**\n * @preserve TypeScript port:\n * Copyright 2015-2018 Igor Bezkrovnyi\n * All rights reserved. (MIT Licensed)\n *\n * rgbquant.ts - part of Image Quantization Library\n */  //# sourceMappingURL=image-q.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/image-q/dist/cjs/image-q.cjs\n");

/***/ })

};
;