{"name": "xauusd-trading-analyzer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.6", "autoprefixer": "^10.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "eslint": "^8", "eslint-config-next": "14.0.0", "jimp": "^0.22.12", "lucide-react": "^0.292.0", "next": "14.0.0", "postcss": "^8", "react": "^18", "react-dom": "^18", "sharp": "^0.32.6", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "tesseract.js": "^5.1.1", "typescript": "^5", "uuid": "^9.0.1"}}