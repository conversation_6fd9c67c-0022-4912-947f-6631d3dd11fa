<!DOCTYPE html>
<html>
  <head>
    <title><PERSON>p browser example 1</title>
  </head>
  <body>
    <!-- Demonstrates loading a local file using <PERSON><PERSON> on the main thread -->
    <script src="../lib/jimp.js"></script>
    <script>
      function dropShadow(x, y, b, a) {
        var img = new Jimp(
          this.bitmap.width + Math.abs(x * 2) + b * 2,
          this.bitmap.height + Math.abs(y * 2) + b * 2
        );
        var orig = this.clone();
        this.scan(
          0,
          0,
          this.bitmap.width,
          this.bitmap.height,
          function (x, y, idx) {
            this.bitmap.data[idx + 0] = 0;
            this.bitmap.data[idx + 1] = 0;
            this.bitmap.data[idx + 2] = 0;
            this.bitmap.data[idx + 3] = this.bitmap.data[idx + 3] * a;
          }
        );
        // this.resize(this.bitmap.width + Math.abs(x) + b, this.bitmap.height + Math.abs(y) + b);

        var x1 = Math.max(x * -1, 0) + b;
        var y1 = Math.max(y * -1, 0) + b;
        img.composite(this, x1, y1);
        img.blur(b);
        img.composite(orig, x1 - x, y1 - y);
        //img.autocrop();
        return img;
      }
      Jimp.read("dice.png").then(function (img) {
        console.log(img.getMIME(), img.getExtension());
        var img = dropShadow.call(img, 20, 20, 20, 0.3);
        img.getBase64(Jimp.AUTO, function (err, src) {
          var img = document.createElement("img");
          img.setAttribute("src", src);
          document.body.appendChild(img);
        });
      });
    </script>
  </body>
</html>
