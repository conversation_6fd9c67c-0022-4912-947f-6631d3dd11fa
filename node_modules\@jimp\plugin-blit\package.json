{"name": "@jimp/plugin-blit", "version": "0.22.12", "description": "Blit an image.", "main": "dist/index.js", "module": "es/index.js", "repository": "jimp-dev/jimp", "types": "index.d.ts", "scripts": {"test": "cross-env BABEL_ENV=test mocha --require @babel/register --recursive test --extension js", "test:watch": "npm run test -- --reporter min --watch", "test:coverage": "nyc npm run test", "build": "npm run build:node:production && npm run build:module", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node"}, "author": "", "license": "MIT", "dependencies": {"@jimp/utils": "^0.22.12"}, "devDependencies": {"@jimp/custom": "^0.22.12", "@jimp/jpeg": "^0.22.12", "@jimp/test-utils": "^0.22.12"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "publishConfig": {"access": "public"}, "gitHead": "a4a8d6364bbf97629749e196f3b0a4c94c9a7abc"}