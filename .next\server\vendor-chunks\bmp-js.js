"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bmp-js";
exports.ids = ["vendor-chunks/bmp-js"];
exports.modules = {

/***/ "(rsc)/./node_modules/bmp-js/index.js":
/*!**************************************!*\
  !*** ./node_modules/bmp-js/index.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * <AUTHOR> *\n * support 1bit 4bit 8bit 24bit decode\n * encode with 24bit\n * \n */ \nvar encode = __webpack_require__(/*! ./lib/encoder */ \"(rsc)/./node_modules/bmp-js/lib/encoder.js\"), decode = __webpack_require__(/*! ./lib/decoder */ \"(rsc)/./node_modules/bmp-js/lib/decoder.js\");\nmodule.exports = {\n    encode: encode,\n    decode: decode\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYm1wLWpzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7Q0FNQztBQUVELElBQUlBLFNBQVNDLG1CQUFPQSxDQUFDLG9FQUNqQkMsU0FBU0QsbUJBQU9BLENBQUM7QUFFckJFLE9BQU9DLE9BQU8sR0FBRztJQUNmSixRQUFRQTtJQUNSRSxRQUFRQTtBQUNWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvLi9ub2RlX21vZHVsZXMvYm1wLWpzL2luZGV4LmpzPzg4NDYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAYXV0aG9yIHNoYW96aWxlZVxuICpcbiAqIHN1cHBvcnQgMWJpdCA0Yml0IDhiaXQgMjRiaXQgZGVjb2RlXG4gKiBlbmNvZGUgd2l0aCAyNGJpdFxuICogXG4gKi9cblxudmFyIGVuY29kZSA9IHJlcXVpcmUoJy4vbGliL2VuY29kZXInKSxcbiAgICBkZWNvZGUgPSByZXF1aXJlKCcuL2xpYi9kZWNvZGVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBlbmNvZGU6IGVuY29kZSxcbiAgZGVjb2RlOiBkZWNvZGVcbn07XG4iXSwibmFtZXMiOlsiZW5jb2RlIiwicmVxdWlyZSIsImRlY29kZSIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bmp-js/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/bmp-js/lib/decoder.js":
/*!********************************************!*\
  !*** ./node_modules/bmp-js/lib/decoder.js ***!
  \********************************************/
/***/ ((module) => {

eval("/**\n * <AUTHOR> *\n * Bmp format decoder,support 1bit 4bit 8bit 24bit bmp\n *\n */ \nfunction BmpDecoder(buffer, is_with_alpha) {\n    this.pos = 0;\n    this.buffer = buffer;\n    this.is_with_alpha = !!is_with_alpha;\n    this.bottom_up = true;\n    this.flag = this.buffer.toString(\"utf-8\", 0, this.pos += 2);\n    if (this.flag != \"BM\") throw new Error(\"Invalid BMP File\");\n    this.parseHeader();\n    this.parseRGBA();\n}\nBmpDecoder.prototype.parseHeader = function() {\n    this.fileSize = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.reserved = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.offset = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.headerSize = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.width = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.height = this.buffer.readInt32LE(this.pos);\n    this.pos += 4;\n    this.planes = this.buffer.readUInt16LE(this.pos);\n    this.pos += 2;\n    this.bitPP = this.buffer.readUInt16LE(this.pos);\n    this.pos += 2;\n    this.compress = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.rawSize = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.hr = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.vr = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.colors = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    this.importantColors = this.buffer.readUInt32LE(this.pos);\n    this.pos += 4;\n    if (this.bitPP === 16 && this.is_with_alpha) {\n        this.bitPP = 15;\n    }\n    if (this.bitPP < 15) {\n        var len = this.colors === 0 ? 1 << this.bitPP : this.colors;\n        this.palette = new Array(len);\n        for(var i = 0; i < len; i++){\n            var blue = this.buffer.readUInt8(this.pos++);\n            var green = this.buffer.readUInt8(this.pos++);\n            var red = this.buffer.readUInt8(this.pos++);\n            var quad = this.buffer.readUInt8(this.pos++);\n            this.palette[i] = {\n                red: red,\n                green: green,\n                blue: blue,\n                quad: quad\n            };\n        }\n    }\n    if (this.height < 0) {\n        this.height *= -1;\n        this.bottom_up = false;\n    }\n};\nBmpDecoder.prototype.parseRGBA = function() {\n    var bitn = \"bit\" + this.bitPP;\n    var len = this.width * this.height * 4;\n    this.data = new Buffer(len);\n    this[bitn]();\n};\nBmpDecoder.prototype.bit1 = function() {\n    var xlen = Math.ceil(this.width / 8);\n    var mode = xlen % 4;\n    var y = this.height >= 0 ? this.height - 1 : -this.height;\n    for(var y = this.height - 1; y >= 0; y--){\n        var line = this.bottom_up ? y : this.height - 1 - y;\n        for(var x = 0; x < xlen; x++){\n            var b = this.buffer.readUInt8(this.pos++);\n            var location = line * this.width * 4 + x * 8 * 4;\n            for(var i = 0; i < 8; i++){\n                if (x * 8 + i < this.width) {\n                    var rgb = this.palette[b >> 7 - i & 0x1];\n                    this.data[location + i * 4] = 0;\n                    this.data[location + i * 4 + 1] = rgb.blue;\n                    this.data[location + i * 4 + 2] = rgb.green;\n                    this.data[location + i * 4 + 3] = rgb.red;\n                } else {\n                    break;\n                }\n            }\n        }\n        if (mode != 0) {\n            this.pos += 4 - mode;\n        }\n    }\n};\nBmpDecoder.prototype.bit4 = function() {\n    //RLE-4\n    if (this.compress == 2) {\n        this.data.fill(0xff);\n        var location = 0;\n        var lines = this.bottom_up ? this.height - 1 : 0;\n        var low_nibble = false; //for all count of pixel\n        while(location < this.data.length){\n            var a = this.buffer.readUInt8(this.pos++);\n            var b = this.buffer.readUInt8(this.pos++);\n            //absolute mode\n            if (a == 0) {\n                if (b == 0) {\n                    if (this.bottom_up) {\n                        lines--;\n                    } else {\n                        lines++;\n                    }\n                    location = lines * this.width * 4;\n                    low_nibble = false;\n                    continue;\n                } else if (b == 1) {\n                    break;\n                } else if (b == 2) {\n                    //offset x,y\n                    var x = this.buffer.readUInt8(this.pos++);\n                    var y = this.buffer.readUInt8(this.pos++);\n                    if (this.bottom_up) {\n                        lines -= y;\n                    } else {\n                        lines += y;\n                    }\n                    location += y * this.width * 4 + x * 4;\n                } else {\n                    var c = this.buffer.readUInt8(this.pos++);\n                    for(var i = 0; i < b; i++){\n                        if (low_nibble) {\n                            setPixelData.call(this, c & 0x0f);\n                        } else {\n                            setPixelData.call(this, (c & 0xf0) >> 4);\n                        }\n                        if (i & 1 && i + 1 < b) {\n                            c = this.buffer.readUInt8(this.pos++);\n                        }\n                        low_nibble = !low_nibble;\n                    }\n                    if ((b + 1 >> 1 & 1) == 1) {\n                        this.pos++;\n                    }\n                }\n            } else {\n                for(var i = 0; i < a; i++){\n                    if (low_nibble) {\n                        setPixelData.call(this, b & 0x0f);\n                    } else {\n                        setPixelData.call(this, (b & 0xf0) >> 4);\n                    }\n                    low_nibble = !low_nibble;\n                }\n            }\n        }\n        function setPixelData(rgbIndex) {\n            var rgb = this.palette[rgbIndex];\n            this.data[location] = 0;\n            this.data[location + 1] = rgb.blue;\n            this.data[location + 2] = rgb.green;\n            this.data[location + 3] = rgb.red;\n            location += 4;\n        }\n    } else {\n        var xlen = Math.ceil(this.width / 2);\n        var mode = xlen % 4;\n        for(var y = this.height - 1; y >= 0; y--){\n            var line = this.bottom_up ? y : this.height - 1 - y;\n            for(var x = 0; x < xlen; x++){\n                var b = this.buffer.readUInt8(this.pos++);\n                var location = line * this.width * 4 + x * 2 * 4;\n                var before = b >> 4;\n                var after = b & 0x0F;\n                var rgb = this.palette[before];\n                this.data[location] = 0;\n                this.data[location + 1] = rgb.blue;\n                this.data[location + 2] = rgb.green;\n                this.data[location + 3] = rgb.red;\n                if (x * 2 + 1 >= this.width) break;\n                rgb = this.palette[after];\n                this.data[location + 4] = 0;\n                this.data[location + 4 + 1] = rgb.blue;\n                this.data[location + 4 + 2] = rgb.green;\n                this.data[location + 4 + 3] = rgb.red;\n            }\n            if (mode != 0) {\n                this.pos += 4 - mode;\n            }\n        }\n    }\n};\nBmpDecoder.prototype.bit8 = function() {\n    //RLE-8\n    if (this.compress == 1) {\n        this.data.fill(0xff);\n        var location = 0;\n        var lines = this.bottom_up ? this.height - 1 : 0;\n        while(location < this.data.length){\n            var a = this.buffer.readUInt8(this.pos++);\n            var b = this.buffer.readUInt8(this.pos++);\n            //absolute mode\n            if (a == 0) {\n                if (b == 0) {\n                    if (this.bottom_up) {\n                        lines--;\n                    } else {\n                        lines++;\n                    }\n                    location = lines * this.width * 4;\n                    continue;\n                } else if (b == 1) {\n                    break;\n                } else if (b == 2) {\n                    //offset x,y\n                    var x = this.buffer.readUInt8(this.pos++);\n                    var y = this.buffer.readUInt8(this.pos++);\n                    if (this.bottom_up) {\n                        lines -= y;\n                    } else {\n                        lines += y;\n                    }\n                    location += y * this.width * 4 + x * 4;\n                } else {\n                    for(var i = 0; i < b; i++){\n                        var c = this.buffer.readUInt8(this.pos++);\n                        setPixelData.call(this, c);\n                    }\n                    if (b & 1 == 1) {\n                        this.pos++;\n                    }\n                }\n            } else {\n                for(var i = 0; i < a; i++){\n                    setPixelData.call(this, b);\n                }\n            }\n        }\n        function setPixelData(rgbIndex) {\n            var rgb = this.palette[rgbIndex];\n            this.data[location] = 0;\n            this.data[location + 1] = rgb.blue;\n            this.data[location + 2] = rgb.green;\n            this.data[location + 3] = rgb.red;\n            location += 4;\n        }\n    } else {\n        var mode = this.width % 4;\n        for(var y = this.height - 1; y >= 0; y--){\n            var line = this.bottom_up ? y : this.height - 1 - y;\n            for(var x = 0; x < this.width; x++){\n                var b = this.buffer.readUInt8(this.pos++);\n                var location = line * this.width * 4 + x * 4;\n                if (b < this.palette.length) {\n                    var rgb = this.palette[b];\n                    this.data[location] = 0;\n                    this.data[location + 1] = rgb.blue;\n                    this.data[location + 2] = rgb.green;\n                    this.data[location + 3] = rgb.red;\n                } else {\n                    this.data[location] = 0;\n                    this.data[location + 1] = 0xFF;\n                    this.data[location + 2] = 0xFF;\n                    this.data[location + 3] = 0xFF;\n                }\n            }\n            if (mode != 0) {\n                this.pos += 4 - mode;\n            }\n        }\n    }\n};\nBmpDecoder.prototype.bit15 = function() {\n    var dif_w = this.width % 3;\n    var _11111 = parseInt(\"11111\", 2), _1_5 = _11111;\n    for(var y = this.height - 1; y >= 0; y--){\n        var line = this.bottom_up ? y : this.height - 1 - y;\n        for(var x = 0; x < this.width; x++){\n            var B = this.buffer.readUInt16LE(this.pos);\n            this.pos += 2;\n            var blue = (B & _1_5) / _1_5 * 255 | 0;\n            var green = (B >> 5 & _1_5) / _1_5 * 255 | 0;\n            var red = (B >> 10 & _1_5) / _1_5 * 255 | 0;\n            var alpha = B >> 15 ? 0xFF : 0x00;\n            var location = line * this.width * 4 + x * 4;\n            this.data[location] = alpha;\n            this.data[location + 1] = blue;\n            this.data[location + 2] = green;\n            this.data[location + 3] = red;\n        }\n        //skip extra bytes\n        this.pos += dif_w;\n    }\n};\nBmpDecoder.prototype.bit16 = function() {\n    var dif_w = this.width % 2 * 2;\n    //default xrgb555\n    this.maskRed = 0x7C00;\n    this.maskGreen = 0x3E0;\n    this.maskBlue = 0x1F;\n    this.mask0 = 0;\n    if (this.compress == 3) {\n        this.maskRed = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n        this.maskGreen = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n        this.maskBlue = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n        this.mask0 = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n    }\n    var ns = [\n        0,\n        0,\n        0\n    ];\n    for(var i = 0; i < 16; i++){\n        if (this.maskRed >> i & 0x01) ns[0]++;\n        if (this.maskGreen >> i & 0x01) ns[1]++;\n        if (this.maskBlue >> i & 0x01) ns[2]++;\n    }\n    ns[1] += ns[0];\n    ns[2] += ns[1];\n    ns[0] = 8 - ns[0];\n    ns[1] -= 8;\n    ns[2] -= 8;\n    for(var y = this.height - 1; y >= 0; y--){\n        var line = this.bottom_up ? y : this.height - 1 - y;\n        for(var x = 0; x < this.width; x++){\n            var B = this.buffer.readUInt16LE(this.pos);\n            this.pos += 2;\n            var blue = (B & this.maskBlue) << ns[0];\n            var green = (B & this.maskGreen) >> ns[1];\n            var red = (B & this.maskRed) >> ns[2];\n            var location = line * this.width * 4 + x * 4;\n            this.data[location] = 0;\n            this.data[location + 1] = blue;\n            this.data[location + 2] = green;\n            this.data[location + 3] = red;\n        }\n        //skip extra bytes\n        this.pos += dif_w;\n    }\n};\nBmpDecoder.prototype.bit24 = function() {\n    for(var y = this.height - 1; y >= 0; y--){\n        var line = this.bottom_up ? y : this.height - 1 - y;\n        for(var x = 0; x < this.width; x++){\n            //Little Endian rgb\n            var blue = this.buffer.readUInt8(this.pos++);\n            var green = this.buffer.readUInt8(this.pos++);\n            var red = this.buffer.readUInt8(this.pos++);\n            var location = line * this.width * 4 + x * 4;\n            this.data[location] = 0;\n            this.data[location + 1] = blue;\n            this.data[location + 2] = green;\n            this.data[location + 3] = red;\n        }\n        //skip extra bytes\n        this.pos += this.width % 4;\n    }\n};\n/**\n * add 32bit decode func\n * <AUTHOR> */ BmpDecoder.prototype.bit32 = function() {\n    //BI_BITFIELDS\n    if (this.compress == 3) {\n        this.maskRed = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n        this.maskGreen = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n        this.maskBlue = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n        this.mask0 = this.buffer.readUInt32LE(this.pos);\n        this.pos += 4;\n        for(var y = this.height - 1; y >= 0; y--){\n            var line = this.bottom_up ? y : this.height - 1 - y;\n            for(var x = 0; x < this.width; x++){\n                //Little Endian rgba\n                var alpha = this.buffer.readUInt8(this.pos++);\n                var blue = this.buffer.readUInt8(this.pos++);\n                var green = this.buffer.readUInt8(this.pos++);\n                var red = this.buffer.readUInt8(this.pos++);\n                var location = line * this.width * 4 + x * 4;\n                this.data[location] = alpha;\n                this.data[location + 1] = blue;\n                this.data[location + 2] = green;\n                this.data[location + 3] = red;\n            }\n        }\n    } else {\n        for(var y = this.height - 1; y >= 0; y--){\n            var line = this.bottom_up ? y : this.height - 1 - y;\n            for(var x = 0; x < this.width; x++){\n                //Little Endian argb\n                var blue = this.buffer.readUInt8(this.pos++);\n                var green = this.buffer.readUInt8(this.pos++);\n                var red = this.buffer.readUInt8(this.pos++);\n                var alpha = this.buffer.readUInt8(this.pos++);\n                var location = line * this.width * 4 + x * 4;\n                this.data[location] = alpha;\n                this.data[location + 1] = blue;\n                this.data[location + 2] = green;\n                this.data[location + 3] = red;\n            }\n        }\n    }\n};\nBmpDecoder.prototype.getData = function() {\n    return this.data;\n};\nmodule.exports = function(bmpData) {\n    var decoder = new BmpDecoder(bmpData);\n    return decoder;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bmp-js/lib/decoder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/bmp-js/lib/encoder.js":
/*!********************************************!*\
  !*** ./node_modules/bmp-js/lib/encoder.js ***!
  \********************************************/
/***/ ((module) => {

eval("/**\n * <AUTHOR> *\n * BMP format encoder,encode 24bit BMP\n * Not support quality compression\n *\n */ \nfunction BmpEncoder(imgData) {\n    this.buffer = imgData.data;\n    this.width = imgData.width;\n    this.height = imgData.height;\n    this.extraBytes = this.width % 4;\n    this.rgbSize = this.height * (3 * this.width + this.extraBytes);\n    this.headerInfoSize = 40;\n    this.data = [];\n    /******************header***********************/ this.flag = \"BM\";\n    this.reserved = 0;\n    this.offset = 54;\n    this.fileSize = this.rgbSize + this.offset;\n    this.planes = 1;\n    this.bitPP = 24;\n    this.compress = 0;\n    this.hr = 0;\n    this.vr = 0;\n    this.colors = 0;\n    this.importantColors = 0;\n}\nBmpEncoder.prototype.encode = function() {\n    var tempBuffer = new Buffer(this.offset + this.rgbSize);\n    this.pos = 0;\n    tempBuffer.write(this.flag, this.pos, 2);\n    this.pos += 2;\n    tempBuffer.writeUInt32LE(this.fileSize, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.reserved, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.offset, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.headerInfoSize, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.width, this.pos);\n    this.pos += 4;\n    tempBuffer.writeInt32LE(-this.height, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt16LE(this.planes, this.pos);\n    this.pos += 2;\n    tempBuffer.writeUInt16LE(this.bitPP, this.pos);\n    this.pos += 2;\n    tempBuffer.writeUInt32LE(this.compress, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.rgbSize, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.hr, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.vr, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.colors, this.pos);\n    this.pos += 4;\n    tempBuffer.writeUInt32LE(this.importantColors, this.pos);\n    this.pos += 4;\n    var i = 0;\n    var rowBytes = 3 * this.width + this.extraBytes;\n    for(var y = 0; y < this.height; y++){\n        for(var x = 0; x < this.width; x++){\n            var p = this.pos + y * rowBytes + x * 3;\n            i++; //a\n            tempBuffer[p] = this.buffer[i++]; //b\n            tempBuffer[p + 1] = this.buffer[i++]; //g\n            tempBuffer[p + 2] = this.buffer[i++]; //r\n        }\n        if (this.extraBytes > 0) {\n            var fillOffset = this.pos + y * rowBytes + this.width * 3;\n            tempBuffer.fill(0, fillOffset, fillOffset + this.extraBytes);\n        }\n    }\n    return tempBuffer;\n};\nmodule.exports = function(imgData, quality) {\n    if (typeof quality === \"undefined\") quality = 100;\n    var encoder = new BmpEncoder(imgData);\n    var data = encoder.encode();\n    return {\n        data: data,\n        width: imgData.width,\n        height: imgData.height\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bmp-js/lib/encoder.js\n");

/***/ })

};
;