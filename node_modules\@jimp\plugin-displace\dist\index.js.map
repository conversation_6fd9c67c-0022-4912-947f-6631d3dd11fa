{"version": 3, "file": "index.js", "names": ["displace", "map", "offset", "cb", "constructor", "throwError", "call", "source", "clone<PERSON>uiet", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "displacement", "data", "Math", "round", "ids", "getPixelIndex", "isNodePattern"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Displaces the image based on the provided displacement map\n * @param {object} map the source Jimp instance\n * @param {number} offset the maximum displacement value\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  displace(map, offset, cb) {\n    if (typeof map !== \"object\" || map.constructor !== this.constructor) {\n      return throwError.call(this, \"The source must be a Jimp image\", cb);\n    }\n\n    if (typeof offset !== \"number\") {\n      return throwError.call(this, \"factor must be a number\", cb);\n    }\n\n    const source = this.cloneQuiet();\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        let displacement = (map.bitmap.data[idx] / 256) * offset;\n        displacement = Math.round(displacement);\n\n        const ids = this.getPixelIndex(x + displacement, y);\n        this.bitmap.data[ids] = source.bitmap.data[idx];\n        this.bitmap.data[ids + 1] = source.bitmap.data[idx + 1];\n        this.bitmap.data[ids + 2] = source.bitmap.data[idx + 2];\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,eAOe,OAAO;EACpBA,QAAQ,CAACC,GAAG,EAAEC,MAAM,EAAEC,EAAE,EAAE;IACxB,IAAI,OAAOF,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACG,WAAW,KAAK,IAAI,CAACA,WAAW,EAAE;MACnE,OAAOC,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,iCAAiC,EAAEH,EAAE,CAAC;IACrE;IAEA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOG,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEH,EAAE,CAAC;IAC7D;IAEA,MAAMI,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;IAChC,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACC,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;MACnB,IAAIC,YAAY,GAAIf,GAAG,CAACS,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC,GAAG,GAAG,GAAIb,MAAM;MACxDc,YAAY,GAAGE,IAAI,CAACC,KAAK,CAACH,YAAY,CAAC;MAEvC,MAAMI,GAAG,GAAG,IAAI,CAACC,aAAa,CAACR,CAAC,GAAGG,YAAY,EAAEF,CAAC,CAAC;MACnD,IAAI,CAACJ,MAAM,CAACO,IAAI,CAACG,GAAG,CAAC,GAAGb,MAAM,CAACG,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC;MAC/C,IAAI,CAACL,MAAM,CAACO,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC,GAAGb,MAAM,CAACG,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC;MACvD,IAAI,CAACL,MAAM,CAACO,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC,GAAGb,MAAM,CAACG,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC;IACzD,CAAC,CACF;IAED,IAAI,IAAAO,oBAAa,EAACnB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACG,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}