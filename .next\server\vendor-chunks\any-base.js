"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/any-base";
exports.ids = ["vendor-chunks/any-base"];
exports.modules = {

/***/ "(rsc)/./node_modules/any-base/index.js":
/*!****************************************!*\
  !*** ./node_modules/any-base/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Converter = __webpack_require__(/*! ./src/converter */ \"(rsc)/./node_modules/any-base/src/converter.js\");\n/**\n * Function get source and destination alphabet and return convert function\n *\n * @param {string|Array} srcAlphabet\n * @param {string|Array} dstAlphabet\n *\n * @returns {function(number|Array)}\n */ function anyBase(srcAlphabet, dstAlphabet) {\n    var converter = new Converter(srcAlphabet, dstAlphabet);\n    /**\n     * Convert function\n     *\n     * @param {string|Array} number\n     *\n     * @return {string|Array} number\n     */ return function(number) {\n        return converter.convert(number);\n    };\n}\nanyBase.BIN = \"01\";\nanyBase.OCT = \"01234567\";\nanyBase.DEC = \"0123456789\";\nanyBase.HEX = \"0123456789abcdef\";\nmodule.exports = anyBase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYW55LWJhc2UvaW5kZXguanMiLCJtYXBwaW5ncyI6IjtBQUFBLElBQUlBLFlBQVlDLG1CQUFPQSxDQUFDO0FBRXhCOzs7Ozs7O0NBT0MsR0FDRCxTQUFTQyxRQUFRQyxXQUFXLEVBQUVDLFdBQVc7SUFDckMsSUFBSUMsWUFBWSxJQUFJTCxVQUFVRyxhQUFhQztJQUMzQzs7Ozs7O0tBTUMsR0FDRCxPQUFPLFNBQVVFLE1BQU07UUFDbkIsT0FBT0QsVUFBVUUsT0FBTyxDQUFDRDtJQUM3QjtBQUNKO0FBRUFKLFFBQVFNLEdBQUcsR0FBRztBQUNkTixRQUFRTyxHQUFHLEdBQUc7QUFDZFAsUUFBUVEsR0FBRyxHQUFHO0FBQ2RSLFFBQVFTLEdBQUcsR0FBRztBQUVkQyxPQUFPQyxPQUFPLEdBQUdYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvLi9ub2RlX21vZHVsZXMvYW55LWJhc2UvaW5kZXguanM/ZGExMSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgQ29udmVydGVyID0gcmVxdWlyZSgnLi9zcmMvY29udmVydGVyJyk7XG5cbi8qKlxuICogRnVuY3Rpb24gZ2V0IHNvdXJjZSBhbmQgZGVzdGluYXRpb24gYWxwaGFiZXQgYW5kIHJldHVybiBjb252ZXJ0IGZ1bmN0aW9uXG4gKlxuICogQHBhcmFtIHtzdHJpbmd8QXJyYXl9IHNyY0FscGhhYmV0XG4gKiBAcGFyYW0ge3N0cmluZ3xBcnJheX0gZHN0QWxwaGFiZXRcbiAqXG4gKiBAcmV0dXJucyB7ZnVuY3Rpb24obnVtYmVyfEFycmF5KX1cbiAqL1xuZnVuY3Rpb24gYW55QmFzZShzcmNBbHBoYWJldCwgZHN0QWxwaGFiZXQpIHtcbiAgICB2YXIgY29udmVydGVyID0gbmV3IENvbnZlcnRlcihzcmNBbHBoYWJldCwgZHN0QWxwaGFiZXQpO1xuICAgIC8qKlxuICAgICAqIENvbnZlcnQgZnVuY3Rpb25cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfEFycmF5fSBudW1iZXJcbiAgICAgKlxuICAgICAqIEByZXR1cm4ge3N0cmluZ3xBcnJheX0gbnVtYmVyXG4gICAgICovXG4gICAgcmV0dXJuIGZ1bmN0aW9uIChudW1iZXIpIHtcbiAgICAgICAgcmV0dXJuIGNvbnZlcnRlci5jb252ZXJ0KG51bWJlcik7XG4gICAgfVxufTtcblxuYW55QmFzZS5CSU4gPSAnMDEnO1xuYW55QmFzZS5PQ1QgPSAnMDEyMzQ1NjcnO1xuYW55QmFzZS5ERUMgPSAnMDEyMzQ1Njc4OSc7XG5hbnlCYXNlLkhFWCA9ICcwMTIzNDU2Nzg5YWJjZGVmJztcblxubW9kdWxlLmV4cG9ydHMgPSBhbnlCYXNlOyJdLCJuYW1lcyI6WyJDb252ZXJ0ZXIiLCJyZXF1aXJlIiwiYW55QmFzZSIsInNyY0FscGhhYmV0IiwiZHN0QWxwaGFiZXQiLCJjb252ZXJ0ZXIiLCJudW1iZXIiLCJjb252ZXJ0IiwiQklOIiwiT0NUIiwiREVDIiwiSEVYIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/any-base/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/any-base/src/converter.js":
/*!************************************************!*\
  !*** ./node_modules/any-base/src/converter.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n/**\n * Converter\n *\n * @param {string|Array} srcAlphabet\n * @param {string|Array} dstAlphabet\n * @constructor\n */ function Converter(srcAlphabet, dstAlphabet) {\n    if (!srcAlphabet || !dstAlphabet || !srcAlphabet.length || !dstAlphabet.length) {\n        throw new Error(\"Bad alphabet\");\n    }\n    this.srcAlphabet = srcAlphabet;\n    this.dstAlphabet = dstAlphabet;\n}\n/**\n * Convert number from source alphabet to destination alphabet\n *\n * @param {string|Array} number - number represented as a string or array of points\n *\n * @returns {string|Array}\n */ Converter.prototype.convert = function(number) {\n    var i, divide, newlen, numberMap = {}, fromBase = this.srcAlphabet.length, toBase = this.dstAlphabet.length, length = number.length, result = typeof number === \"string\" ? \"\" : [];\n    if (!this.isValid(number)) {\n        throw new Error('Number \"' + number + '\" contains of non-alphabetic digits (' + this.srcAlphabet + \")\");\n    }\n    if (this.srcAlphabet === this.dstAlphabet) {\n        return number;\n    }\n    for(i = 0; i < length; i++){\n        numberMap[i] = this.srcAlphabet.indexOf(number[i]);\n    }\n    do {\n        divide = 0;\n        newlen = 0;\n        for(i = 0; i < length; i++){\n            divide = divide * fromBase + numberMap[i];\n            if (divide >= toBase) {\n                numberMap[newlen++] = parseInt(divide / toBase, 10);\n                divide = divide % toBase;\n            } else if (newlen > 0) {\n                numberMap[newlen++] = 0;\n            }\n        }\n        length = newlen;\n        result = this.dstAlphabet.slice(divide, divide + 1).concat(result);\n    }while (newlen !== 0);\n    return result;\n};\n/**\n * Valid number with source alphabet\n *\n * @param {number} number\n *\n * @returns {boolean}\n */ Converter.prototype.isValid = function(number) {\n    var i = 0;\n    for(; i < number.length; ++i){\n        if (this.srcAlphabet.indexOf(number[i]) === -1) {\n            return false;\n        }\n    }\n    return true;\n};\nmodule.exports = Converter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/any-base/src/converter.js\n");

/***/ })

};
;