{"version": 3, "file": "index.js", "names": ["contain", "w", "h", "alignBits", "mode", "cb", "throwError", "call", "constructor", "HORIZONTAL_ALIGN_CENTER", "VERTICAL_ALIGN_MIDDLE", "hbits", "vbits", "alignH", "alignV", "f", "bitmap", "width", "height", "c", "clone<PERSON>uiet", "scale", "resize", "scanQuiet", "x", "y", "idx", "data", "writeUInt32BE", "_background", "blit", "isNodePattern"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Scale the image to the given width and height keeping the aspect ratio. Some parts of the image may be letter boxed.\n * @param {number} w the width to resize the image to\n * @param {number} h the height to resize the image to\n * @param {number} alignBits (optional) A bitmask for horizontal and vertical alignment\n * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  contain(w, h, alignBits, mode, cb) {\n    if (typeof w !== \"number\" || typeof h !== \"number\") {\n      return throwError.call(this, \"w and h must be numbers\", cb);\n    }\n\n    // permit any sort of optional parameters combination\n    if (typeof alignBits === \"string\") {\n      if (typeof mode === \"function\" && typeof cb === \"undefined\") cb = mode;\n      mode = alignBits;\n      alignBits = null;\n    }\n\n    if (typeof alignBits === \"function\") {\n      if (typeof cb === \"undefined\") cb = alignBits;\n      mode = null;\n      alignBits = null;\n    }\n\n    if (typeof mode === \"function\" && typeof cb === \"undefined\") {\n      cb = mode;\n      mode = null;\n    }\n\n    alignBits =\n      alignBits ||\n      this.constructor.HORIZONTAL_ALIGN_CENTER |\n        this.constructor.VERTICAL_ALIGN_MIDDLE;\n    const hbits = alignBits & ((1 << 3) - 1);\n    const vbits = alignBits >> 3;\n\n    // check if more flags than one is in the bit sets\n    if (\n      !(\n        (hbits !== 0 && !(hbits & (hbits - 1))) ||\n        (vbits !== 0 && !(vbits & (vbits - 1)))\n      )\n    ) {\n      return throwError.call(\n        this,\n        \"only use one flag per alignment direction\",\n        cb\n      );\n    }\n\n    const alignH = hbits >> 1; // 0, 1, 2\n    const alignV = vbits >> 1; // 0, 1, 2\n\n    const f =\n      w / h > this.bitmap.width / this.bitmap.height\n        ? h / this.bitmap.height\n        : w / this.bitmap.width;\n    const c = this.cloneQuiet().scale(f, mode);\n\n    this.resize(w, h, mode);\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data.writeUInt32BE(this._background, idx);\n      }\n    );\n    this.blit(\n      c,\n      ((this.bitmap.width - c.bitmap.width) / 2) * alignH,\n      ((this.bitmap.height - c.bitmap.height) / 2) * alignV\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,eASe,OAAO;EACpBA,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEC,SAAS,EAAEC,IAAI,EAAEC,EAAE,EAAE;IACjC,IAAI,OAAOJ,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAOI,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEF,EAAE,CAAC;IAC7D;;IAEA;IACA,IAAI,OAAOF,SAAS,KAAK,QAAQ,EAAE;MACjC,IAAI,OAAOC,IAAI,KAAK,UAAU,IAAI,OAAOC,EAAE,KAAK,WAAW,EAAEA,EAAE,GAAGD,IAAI;MACtEA,IAAI,GAAGD,SAAS;MAChBA,SAAS,GAAG,IAAI;IAClB;IAEA,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;MACnC,IAAI,OAAOE,EAAE,KAAK,WAAW,EAAEA,EAAE,GAAGF,SAAS;MAC7CC,IAAI,GAAG,IAAI;MACXD,SAAS,GAAG,IAAI;IAClB;IAEA,IAAI,OAAOC,IAAI,KAAK,UAAU,IAAI,OAAOC,EAAE,KAAK,WAAW,EAAE;MAC3DA,EAAE,GAAGD,IAAI;MACTA,IAAI,GAAG,IAAI;IACb;IAEAD,SAAS,GACPA,SAAS,IACT,IAAI,CAACK,WAAW,CAACC,uBAAuB,GACtC,IAAI,CAACD,WAAW,CAACE,qBAAqB;IAC1C,MAAMC,KAAK,GAAGR,SAAS,GAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAE;IACxC,MAAMS,KAAK,GAAGT,SAAS,IAAI,CAAC;;IAE5B;IACA,IACE,EACGQ,KAAK,KAAK,CAAC,IAAI,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAE,CAAC,IACrCC,KAAK,KAAK,CAAC,IAAI,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAE,CAAE,CACxC,EACD;MACA,OAAON,iBAAU,CAACC,IAAI,CACpB,IAAI,EACJ,2CAA2C,EAC3CF,EAAE,CACH;IACH;IAEA,MAAMQ,MAAM,GAAGF,KAAK,IAAI,CAAC,CAAC,CAAC;IAC3B,MAAMG,MAAM,GAAGF,KAAK,IAAI,CAAC,CAAC,CAAC;;IAE3B,MAAMG,CAAC,GACLd,CAAC,GAAGC,CAAC,GAAG,IAAI,CAACc,MAAM,CAACC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACE,MAAM,GAC1ChB,CAAC,GAAG,IAAI,CAACc,MAAM,CAACE,MAAM,GACtBjB,CAAC,GAAG,IAAI,CAACe,MAAM,CAACC,KAAK;IAC3B,MAAME,CAAC,GAAG,IAAI,CAACC,UAAU,EAAE,CAACC,KAAK,CAACN,CAAC,EAAEX,IAAI,CAAC;IAE1C,IAAI,CAACkB,MAAM,CAACrB,CAAC,EAAEC,CAAC,EAAEE,IAAI,CAAC;IACvB,IAAI,CAACmB,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACP,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUM,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;MACnB,IAAI,CAACV,MAAM,CAACW,IAAI,CAACC,aAAa,CAAC,IAAI,CAACC,WAAW,EAAEH,GAAG,CAAC;IACvD,CAAC,CACF;IACD,IAAI,CAACI,IAAI,CACPX,CAAC,EACA,CAAC,IAAI,CAACH,MAAM,CAACC,KAAK,GAAGE,CAAC,CAACH,MAAM,CAACC,KAAK,IAAI,CAAC,GAAIJ,MAAM,EAClD,CAAC,IAAI,CAACG,MAAM,CAACE,MAAM,GAAGC,CAAC,CAACH,MAAM,CAACE,MAAM,IAAI,CAAC,GAAIJ,MAAM,CACtD;IAED,IAAI,IAAAiB,oBAAa,EAAC1B,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}