/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUN4YW1wcCU1Q2h0ZG9jcyU1Q1RyYWRpbmclMjBBZ2VudCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q3hhbXBwJTVDaHRkb2NzJTVDVHJhZGluZyUyMEFnZW50JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixnSkFBeUY7QUFDaEg7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUEyRjtBQUNwSCxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvP2Q0OTciXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxceGFtcHBcXFxcaHRkb2NzXFxcXFRyYWRpbmcgQWdlbnRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSwgXCJDOlxcXFx4YW1wcFxcXFxodGRvY3NcXFxcVHJhZGluZyBBZ2VudFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxceGFtcHBcXFxcaHRkb2NzXFxcXFRyYWRpbmcgQWdlbnRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkM6XFxcXHhhbXBwXFxcXGh0ZG9jc1xcXFxUcmFkaW5nIEFnZW50XFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiQzpcXFxceGFtcHBcXFxcaHRkb2NzXFxcXFRyYWRpbmcgQWdlbnRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3hhbXBwJTVDaHRkb2NzJTVDVHJhZGluZyUyMEFnZW50JTVDc3JjJTVDYXBwJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvPzQ0OTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFx4YW1wcFxcXFxodGRvY3NcXFxcVHJhZGluZyBBZ2VudFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _components_ImageUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ImageUpload */ \"(ssr)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(ssr)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_TradingStrategySelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TradingStrategySelector */ \"(ssr)/./src/components/TradingStrategySelector.tsx\");\n/* harmony import */ var _components_StatsDashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StatsDashboard */ \"(ssr)/./src/components/StatsDashboard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"scalping\");\n    const [analysisResults, setAnalysisResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleImageUpload = async (file)=>{\n        setIsAnalyzing(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", file);\n            formData.append(\"strategy\", selectedStrategy);\n            const response = await fetch(\"/api/analyze\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(\"Analysis failed\");\n            }\n            const results = await response.json();\n            setAnalysisResults(results);\n        } catch (error) {\n            console.error(\"Analysis error:\", error);\n            alert(\"Analysis failed. Please try again.\");\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-6xl font-bold text-white mb-4\",\n                        children: [\n                            \"Professional \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gold-400\",\n                                children: \"XAUUSD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 24\n                            }, this),\n                            \" Analysis\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                        children: \"Upload your trading chart screenshots and receive detailed analysis using Smart Money Concepts and Inner Circle Trader methodologies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-4 gap-6 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Smart Money Concepts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Market structure, order blocks, and liquidity analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"ICT Methodology\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Institutional order flow and time-based analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Precise Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Exact entry points with detailed reasoning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Risk Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Stop loss and take profit calculations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradingStrategySelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                selectedStrategy: selectedStrategy,\n                onStrategyChange: setSelectedStrategy\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6 text-center\",\n                        children: \"Upload Your Chart Screenshot\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        onImageUpload: handleImageUpload,\n                        isAnalyzing: isAnalyzing\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            analysisResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                results: analysisResults\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StatsDashboard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"How It Works\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl\",\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Upload Chart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Upload a screenshot of your XAUUSD chart from any platform (MT4, MT5, TradingView)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"AI Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Our advanced algorithms analyze market structure, patterns, and institutional behavior\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Get Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Receive detailed trade setups with entry, stop loss, and take profit levels\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AnalysisResults.tsx":
/*!********************************************!*\
  !*** ./src/components/AnalysisResults.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalysisResults)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _FeedbackModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FeedbackModal */ \"(ssr)/./src/components/FeedbackModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AnalysisResults({ results }) {\n    const [showFeedbackModal, setShowFeedbackModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getDirectionColor = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return \"text-green-500\";\n            case \"SELL\":\n                return \"text-red-500\";\n            default:\n                return \"text-yellow-500\";\n        }\n    };\n    const getDirectionIcon = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 26\n                }, this);\n            case \"SELL\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatPrice = (price)=>{\n        return price.toFixed(2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white\",\n                        children: \"Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex items-center space-x-2 ${getDirectionColor(results.direction)}`,\n                                children: [\n                                    getDirectionIcon(results.direction),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: results.direction\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            results.confidence,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 ml-1\",\n                                        children: \"confidence\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Trade Setup\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Entry Price:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.entry)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Stop Loss:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.stopLoss)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP1:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP2:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP3:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp3)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Risk/Reward Ratios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP1 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp1\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP2 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp2\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP3 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp3\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Strategy:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white capitalize\",\n                                                        children: results.strategy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Timeframe:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: results.timeframe\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold text-white\",\n                        children: \"Detailed Analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Market Structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.marketStructure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Risk Assessment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.riskAssessment\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    results.reasoning.orderBlocks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Order Blocks Identified\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.orderBlocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            block\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.fairValueGaps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Fair Value Gaps\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.fairValueGaps.map((gap, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this),\n                                            gap\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.ictConcepts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"ICT Concepts Applied\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.ictConcepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            concept\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white\",\n                            children: \"Help Improve Our Analysis\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Your feedback helps our AI learn and provide better trading analysis\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowFeedbackModal(true),\n                            className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2 mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Provide Feedback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500 text-sm flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    \"Analysis completed: \",\n                    new Date(results.timestamp).toLocaleString()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeedbackModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: showFeedbackModal,\n                onClose: ()=>setShowFeedbackModal(false),\n                analysisId: results.analysisId || \"unknown\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AnalysisResults.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FeedbackModal.tsx":
/*!******************************************!*\
  !*** ./src/components/FeedbackModal.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedbackModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FeedbackModal({ isOpen, onClose, analysisId }) {\n    const [accuracy, setAccuracy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [profitability, setProfitability] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [actualOutcome, setActualOutcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        entryHit: false,\n        stopLossHit: false,\n        takeProfitsHit: {\n            tp1: false,\n            tp2: false,\n            tp3: false\n        }\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (!isOpen) return null;\n    const handleSubmit = async ()=>{\n        if (accuracy === 0 || profitability === 0) {\n            alert(\"Please provide both accuracy and profitability ratings\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/feedback\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    analysisId,\n                    accuracy,\n                    profitability,\n                    comments,\n                    actualOutcome\n                })\n            });\n            if (response.ok) {\n                alert(\"Thank you for your feedback! This helps improve our analysis.\");\n                onClose();\n                // Reset form\n                setAccuracy(0);\n                setProfitability(0);\n                setComments(\"\");\n                setActualOutcome({\n                    entryHit: false,\n                    stopLossHit: false,\n                    takeProfitsHit: {\n                        tp1: false,\n                        tp2: false,\n                        tp3: false\n                    }\n                });\n            } else {\n                throw new Error(\"Failed to submit feedback\");\n            }\n        } catch (error) {\n            console.error(\"Feedback submission error:\", error);\n            alert(\"Failed to submit feedback. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const StarRating = ({ rating, setRating, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"text-white font-medium\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5\n                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setRating(star),\n                            className: `p-1 transition-colors ${star <= rating ? \"text-gold-400\" : \"text-gray-600 hover:text-gold-300\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-6 h-6 fill-current\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        }, star, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n            lineNumber: 71,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900 rounded-xl border border-gold-500/20 max-w-md w-full max-h-[90vh] overflow-y-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Analysis Feedback\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                rating: accuracy,\n                                setRating: setAccuracy,\n                                label: \"Analysis Accuracy (1-5 stars)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                rating: profitability,\n                                setRating: setProfitability,\n                                label: \"Trade Profitability (1-5 stars)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white font-medium\",\n                                children: \"Actual Trade Outcome (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: actualOutcome.entryHit,\n                                                onChange: (e)=>setActualOutcome((prev)=>({\n                                                            ...prev,\n                                                            entryHit: e.target.checked\n                                                        })),\n                                                className: \"rounded border-gray-600 bg-gray-800 text-gold-500 focus:ring-gold-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Entry price was hit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: actualOutcome.stopLossHit,\n                                                onChange: (e)=>setActualOutcome((prev)=>({\n                                                            ...prev,\n                                                            stopLossHit: e.target.checked\n                                                        })),\n                                                className: \"rounded border-gray-600 bg-gray-800 text-red-500 focus:ring-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Stop loss was hit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: actualOutcome.takeProfitsHit.tp1,\n                                                        onChange: (e)=>setActualOutcome((prev)=>({\n                                                                    ...prev,\n                                                                    takeProfitsHit: {\n                                                                        ...prev.takeProfitsHit,\n                                                                        tp1: e.target.checked\n                                                                    }\n                                                                })),\n                                                        className: \"rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"TP1 was hit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: actualOutcome.takeProfitsHit.tp2,\n                                                        onChange: (e)=>setActualOutcome((prev)=>({\n                                                                    ...prev,\n                                                                    takeProfitsHit: {\n                                                                        ...prev.takeProfitsHit,\n                                                                        tp2: e.target.checked\n                                                                    }\n                                                                })),\n                                                        className: \"rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"TP2 was hit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: actualOutcome.takeProfitsHit.tp3,\n                                                        onChange: (e)=>setActualOutcome((prev)=>({\n                                                                    ...prev,\n                                                                    takeProfitsHit: {\n                                                                        ...prev.takeProfitsHit,\n                                                                        tp3: e.target.checked\n                                                                    }\n                                                                })),\n                                                        className: \"rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"TP3 was hit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-white font-medium\",\n                                children: \"Additional Comments (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: comments,\n                                onChange: (e)=>setComments(e.target.value),\n                                placeholder: \"Share your thoughts about the analysis quality, accuracy, or suggestions for improvement...\",\n                                className: \"w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-gold-500 focus:ring-1 focus:ring-gold-500 resize-none\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSubmit,\n                        disabled: isSubmitting || accuracy === 0 || profitability === 0,\n                        className: \"w-full bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-500 disabled:to-gray-600 text-black font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2\",\n                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Submitting...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Submit Feedback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400 text-center\",\n                        children: \"Your feedback helps our AI learn and improve future analysis accuracy\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FeedbackModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Loader2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Loader2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Loader2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Loader2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ImageUpload({ onImageUpload, isAnalyzing }) {\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === \"dragenter\" || e.type === \"dragover\") {\n            setDragActive(true);\n        } else if (e.type === \"dragleave\") {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        const files = e.dataTransfer.files;\n        if (files && files[0]) {\n            handleFileSelection(files[0]);\n        }\n    };\n    const handleFileSelection = (file)=>{\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select an image file\");\n            return;\n        }\n        // Validate file size (max 10MB)\n        if (file.size > 10 * 1024 * 1024) {\n            alert(\"File size must be less than 10MB\");\n            return;\n        }\n        setSelectedFile(file);\n        // Create preview URL\n        const url = URL.createObjectURL(file);\n        setPreviewUrl(url);\n    };\n    const handleFileInputChange = (e)=>{\n        const files = e.target.files;\n        if (files && files[0]) {\n            handleFileSelection(files[0]);\n        }\n    };\n    const handleAnalyze = ()=>{\n        if (selectedFile) {\n            onImageUpload(selectedFile);\n        }\n    };\n    const clearSelection = ()=>{\n        setSelectedFile(null);\n        if (previewUrl) {\n            URL.revokeObjectURL(previewUrl);\n            setPreviewUrl(null);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: !selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative border-2 border-dashed rounded-xl p-8 text-center transition-colors ${dragActive ? \"border-gold-400 bg-gold-400/10\" : \"border-gray-600 hover:border-gold-500\"}`,\n            onDragEnter: handleDrag,\n            onDragLeave: handleDrag,\n            onDragOver: handleDrag,\n            onDrop: handleDrop,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    ref: fileInputRef,\n                    type: \"file\",\n                    accept: \"image/*\",\n                    onChange: handleFileInputChange,\n                    className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-16 h-16 bg-gold-500/20 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-8 h-8 text-gold-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-2\",\n                                    children: \"Drop your chart screenshot here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"or click to browse files\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Supports: JPG, PNG, WebP (Max 10MB)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n            lineNumber: 85,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative bg-black/50 rounded-xl p-4 border border-gold-500/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: clearSelection,\n                            className: \"absolute top-2 right-2 p-2 bg-red-500 hover:bg-red-600 rounded-full transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gold-500/20 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-medium truncate\",\n                                            children: selectedFile.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: [\n                                                (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                \" MB\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this),\n                        previewUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: previewUrl,\n                                alt: \"Chart preview\",\n                                className: \"max-w-full h-auto max-h-64 rounded-lg border border-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleAnalyze,\n                    disabled: isAnalyzing,\n                    className: \"w-full bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-500 disabled:to-gray-600 text-black font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2\",\n                    children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Analyzing Chart...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Analyze Chart\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n            lineNumber: 122,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\ImageUpload.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ImageUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StatsDashboard.tsx":
/*!*******************************************!*\
  !*** ./src/components/StatsDashboard.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction StatsDashboard() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n    }, []);\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch(\"/api/analyze\");\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch stats:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black/30 backdrop-blur-sm rounded-xl p-6 border border-gold-500/20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-700 rounded w-1/3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-20 bg-gray-700 rounded\"\n                            }, i, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black/30 backdrop-blur-sm rounded-xl p-6 border border-gold-500/20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-3 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No analysis data available yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: \"Upload your first chart to see statistics\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this);\n    }\n    const getDirectionColor = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return \"text-green-500\";\n            case \"SELL\":\n                return \"text-red-500\";\n            case \"NEUTRAL\":\n                return \"text-yellow-500\";\n            default:\n                return \"text-gray-500\";\n        }\n    };\n    const getDirectionBg = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return \"bg-green-500/20 border-green-500/30\";\n            case \"SELL\":\n                return \"bg-red-500/20 border-red-500/30\";\n            case \"NEUTRAL\":\n                return \"bg-yellow-500/20 border-yellow-500/30\";\n            default:\n                return \"bg-gray-500/20 border-gray-500/30\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black/30 backdrop-blur-sm rounded-xl p-6 border border-gold-500/20 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-6 h-6 text-gold-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"Analysis Statistics\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-4 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Total Analyses\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: stats.totalAnalyses\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-4 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Avg Confidence\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: [\n                                    stats.averageConfidence.toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-4 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Buy Signals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-400\",\n                                children: stats.directionBreakdown.BUY || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-4 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-red-400 rotate-180\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Sell Signals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-red-400\",\n                                children: stats.directionBreakdown.SELL || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            Object.keys(stats.strategyBreakdown).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white\",\n                        children: \"Strategy Breakdown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-4\",\n                        children: Object.entries(stats.strategyBreakdown).map(([strategy, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-4 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300 capitalize\",\n                                                children: strategy\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 bg-gray-700 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gold-500 h-2 rounded-full transition-all duration-500\",\n                                            style: {\n                                                width: `${stats.totalAnalyses > 0 ? count / stats.totalAnalyses * 100 : 0}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, strategy, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white\",\n                        children: \"Signal Distribution\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4\",\n                        children: Object.entries(stats.directionBreakdown).map(([direction, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `rounded-lg p-4 border ${getDirectionBg(direction)}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `text-2xl font-bold ${getDirectionColor(direction)}`,\n                                            children: count\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300 mt-1\",\n                                            children: direction\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400 mt-1\",\n                                            children: stats.totalAnalyses > 0 ? `${(count / stats.totalAnalyses * 100).toFixed(1)}%` : \"0%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            }, direction, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-black/50 rounded-lg p-4 border border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white mb-3\",\n                        children: \"Performance Insights\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            stats.averageConfidence > 75 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-green-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"High confidence analysis - Strong signal quality\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            stats.totalAnalyses > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-blue-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sufficient data for pattern recognition\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            stats.directionBreakdown.BUY > stats.directionBreakdown.SELL && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-gold-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Bullish market bias detected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            stats.directionBreakdown.SELL > stats.directionBreakdown.BUY && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-gold-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 rotate-180\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Bearish market bias detected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            stats.totalAnalyses < 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Upload more charts to improve analysis accuracy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchStats,\n                    className: \"bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-semibold py-2 px-4 rounded-lg transition-all duration-200\",\n                    children: \"Refresh Statistics\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\StatsDashboard.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StatsDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TradingStrategySelector.tsx":
/*!****************************************************!*\
  !*** ./src/components/TradingStrategySelector.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingStrategySelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction TradingStrategySelector({ selectedStrategy, onStrategyChange }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black/30 backdrop-blur-sm rounded-xl p-6 border border-gold-500/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold text-white mb-6 text-center\",\n                children: \"Select Trading Strategy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onStrategyChange(\"scalping\"),\n                        className: `p-6 rounded-xl border-2 transition-all duration-200 ${selectedStrategy === \"scalping\" ? \"border-gold-500 bg-gold-500/10\" : \"border-gray-600 hover:border-gold-400 bg-black/20\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 rounded-full flex items-center justify-center ${selectedStrategy === \"scalping\" ? \"bg-gold-500\" : \"bg-gray-600\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        className: `w-6 h-6 ${selectedStrategy === \"scalping\" ? \"text-black\" : \"text-white\"}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"Scalping Strategy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mb-3\",\n                                            children: \"Quick trades, 1-15 minute timeframes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• Fast execution\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• Small profit targets\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• Tight stop losses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• High frequency trading\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onStrategyChange(\"swing\"),\n                        className: `p-6 rounded-xl border-2 transition-all duration-200 ${selectedStrategy === \"swing\" ? \"border-gold-500 bg-gold-500/10\" : \"border-gray-600 hover:border-gold-400 bg-black/20\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 rounded-full flex items-center justify-center ${selectedStrategy === \"swing\" ? \"bg-gold-500\" : \"bg-gray-600\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: `w-6 h-6 ${selectedStrategy === \"swing\" ? \"text-black\" : \"text-white\"}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"Swing Trading\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mb-3\",\n                                            children: \"Medium-term trades, 1H-1D timeframes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• Trend following\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• Larger profit targets\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• Wider stop losses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"• Lower frequency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-blue-400 font-medium mb-2\",\n                        children: selectedStrategy === \"scalping\" ? \"Scalping Focus\" : \"Swing Trading Focus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-300\",\n                        children: selectedStrategy === \"scalping\" ? \"Analysis will focus on short-term price action, order blocks, and quick reversal patterns suitable for fast entries and exits.\" : \"Analysis will focus on higher timeframe structure, major support/resistance levels, and trend continuation patterns for longer-term positions.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\TradingStrategySelector.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TradingStrategySelector.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"63ae203937c9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzhmYWUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2M2FlMjAzOTM3YzlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"XAUUSD Trading Analyzer - Smart Money Concepts & ICT Analysis\",\n    description: \"Advanced XAUUSD (Gold) trading analysis using Smart Money Concepts and Inner Circle Trader methodologies. Upload chart screenshots for detailed trade recommendations.\",\n    keywords: \"XAUUSD, Gold trading, Smart Money Concepts, ICT, Inner Circle Trader, trading analysis, forex\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-black/50 backdrop-blur-sm border-b border-gold-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-gold-400 to-gold-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-black font-bold text-sm\",\n                                                    children: \"AU\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 27,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: \"XAUUSD Trading Analyzer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gold-400\",\n                                        children: \"Smart Money Concepts & ICT Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-black/30 border-t border-gold-500/20 mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 XAUUSD Trading Analyzer. Advanced market analysis for professional traders.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\Trading Agent\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5CTrading%20Agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();