"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-bmfont-xml";
exports.ids = ["vendor-chunks/parse-bmfont-xml"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse-bmfont-xml/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/parse-bmfont-xml/lib/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar xml2js = __webpack_require__(/*! xml2js */ \"(rsc)/./node_modules/xml2js/lib/xml2js.js\");\nvar parseAttributes = __webpack_require__(/*! ./parse-attribs */ \"(rsc)/./node_modules/parse-bmfont-xml/lib/parse-attribs.js\");\nmodule.exports = function parseBMFontXML(data) {\n    data = data.toString().trim();\n    var output = {\n        pages: [],\n        chars: [],\n        kernings: []\n    };\n    xml2js.parseString(data, function(err, result) {\n        if (err) throw err;\n        if (!result.font) throw \"XML bitmap font doesn't have <font> root\";\n        result = result.font;\n        output.common = parseAttributes(result.common[0].$);\n        output.info = parseAttributes(result.info[0].$);\n        for(var i = 0; i < result.pages.length; i++){\n            var p = result.pages[i].page[0].$;\n            if (typeof p.id === \"undefined\") throw new Error(\"malformed file -- needs page id=N\");\n            if (typeof p.file !== \"string\") throw new Error('malformed file -- needs page file=\"path\"');\n            output.pages[parseInt(p.id, 10)] = p.file;\n        }\n        if (result.chars) {\n            var chrArray = result.chars[0][\"char\"] || [];\n            for(var i = 0; i < chrArray.length; i++){\n                output.chars.push(parseAttributes(chrArray[i].$));\n            }\n        }\n        if (result.kernings) {\n            var kernArray = result.kernings[0][\"kerning\"] || [];\n            for(var i = 0; i < kernArray.length; i++){\n                output.kernings.push(parseAttributes(kernArray[i].$));\n            }\n        }\n    });\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse-bmfont-xml/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/parse-bmfont-xml/lib/parse-attribs.js":
/*!************************************************************!*\
  !*** ./node_modules/parse-bmfont-xml/lib/parse-attribs.js ***!
  \************************************************************/
/***/ ((module) => {

eval("//Some versions of GlyphDesigner have a typo\n//that causes some bugs with parsing. \n//Need to confirm with recent version of the software\n//to see whether this is still an issue or not.\n\nvar GLYPH_DESIGNER_ERROR = \"chasrset\";\nmodule.exports = function parseAttributes(obj) {\n    obj = Object.assign({}, obj);\n    if (GLYPH_DESIGNER_ERROR in obj) {\n        obj[\"charset\"] = obj[GLYPH_DESIGNER_ERROR];\n        delete obj[GLYPH_DESIGNER_ERROR];\n    }\n    for(var k in obj){\n        if (k === \"face\" || k === \"charset\") continue;\n        else if (k === \"padding\" || k === \"spacing\") obj[k] = parseIntList(obj[k]);\n        else obj[k] = parseInt(obj[k], 10);\n    }\n    return obj;\n};\nfunction parseIntList(data) {\n    return data.split(\",\").map(function(val) {\n        return parseInt(val, 10);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse-bmfont-xml/lib/parse-attribs.js\n");

/***/ })

};
;