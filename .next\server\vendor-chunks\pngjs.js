"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pngjs";
exports.ids = ["vendor-chunks/pngjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/pngjs/lib/bitmapper.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitmapper.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(rsc)/./node_modules/pngjs/lib/interlace.js\");\nlet pixelBppMapper = [\n    // 0 - dummy entry\n    function() {},\n    // 1 - L\n    // 0: 0, 1: 0, 2: 0, 3: 0xff\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos === data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        let pixel = data[rawPos];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = 0xff;\n    },\n    // 2 - LA\n    // 0: 0, 1: 0, 2: 0, 3: 1\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos + 1 >= data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        let pixel = data[rawPos];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = data[rawPos + 1];\n    },\n    // 3 - RGB\n    // 0: 0, 1: 1, 2: 2, 3: 0xff\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos + 2 >= data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        pxData[pxPos] = data[rawPos];\n        pxData[pxPos + 1] = data[rawPos + 1];\n        pxData[pxPos + 2] = data[rawPos + 2];\n        pxData[pxPos + 3] = 0xff;\n    },\n    // 4 - RGBA\n    // 0: 0, 1: 1, 2: 2, 3: 3\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos + 3 >= data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        pxData[pxPos] = data[rawPos];\n        pxData[pxPos + 1] = data[rawPos + 1];\n        pxData[pxPos + 2] = data[rawPos + 2];\n        pxData[pxPos + 3] = data[rawPos + 3];\n    }\n];\nlet pixelBppCustomMapper = [\n    // 0 - dummy entry\n    function() {},\n    // 1 - L\n    // 0: 0, 1: 0, 2: 0, 3: 0xff\n    function(pxData, pixelData, pxPos, maxBit) {\n        let pixel = pixelData[0];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = maxBit;\n    },\n    // 2 - LA\n    // 0: 0, 1: 0, 2: 0, 3: 1\n    function(pxData, pixelData, pxPos) {\n        let pixel = pixelData[0];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = pixelData[1];\n    },\n    // 3 - RGB\n    // 0: 0, 1: 1, 2: 2, 3: 0xff\n    function(pxData, pixelData, pxPos, maxBit) {\n        pxData[pxPos] = pixelData[0];\n        pxData[pxPos + 1] = pixelData[1];\n        pxData[pxPos + 2] = pixelData[2];\n        pxData[pxPos + 3] = maxBit;\n    },\n    // 4 - RGBA\n    // 0: 0, 1: 1, 2: 2, 3: 3\n    function(pxData, pixelData, pxPos) {\n        pxData[pxPos] = pixelData[0];\n        pxData[pxPos + 1] = pixelData[1];\n        pxData[pxPos + 2] = pixelData[2];\n        pxData[pxPos + 3] = pixelData[3];\n    }\n];\nfunction bitRetriever(data, depth) {\n    let leftOver = [];\n    let i = 0;\n    function split() {\n        if (i === data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        let byte = data[i];\n        i++;\n        let byte8, byte7, byte6, byte5, byte4, byte3, byte2, byte1;\n        switch(depth){\n            default:\n                throw new Error(\"unrecognised depth\");\n            case 16:\n                byte2 = data[i];\n                i++;\n                leftOver.push((byte << 8) + byte2);\n                break;\n            case 4:\n                byte2 = byte & 0x0f;\n                byte1 = byte >> 4;\n                leftOver.push(byte1, byte2);\n                break;\n            case 2:\n                byte4 = byte & 3;\n                byte3 = byte >> 2 & 3;\n                byte2 = byte >> 4 & 3;\n                byte1 = byte >> 6 & 3;\n                leftOver.push(byte1, byte2, byte3, byte4);\n                break;\n            case 1:\n                byte8 = byte & 1;\n                byte7 = byte >> 1 & 1;\n                byte6 = byte >> 2 & 1;\n                byte5 = byte >> 3 & 1;\n                byte4 = byte >> 4 & 1;\n                byte3 = byte >> 5 & 1;\n                byte2 = byte >> 6 & 1;\n                byte1 = byte >> 7 & 1;\n                leftOver.push(byte1, byte2, byte3, byte4, byte5, byte6, byte7, byte8);\n                break;\n        }\n    }\n    return {\n        get: function(count) {\n            while(leftOver.length < count){\n                split();\n            }\n            let returner = leftOver.slice(0, count);\n            leftOver = leftOver.slice(count);\n            return returner;\n        },\n        resetAfterLine: function() {\n            leftOver.length = 0;\n        },\n        end: function() {\n            if (i !== data.length) {\n                throw new Error(\"extra data found\");\n            }\n        }\n    };\n}\nfunction mapImage8Bit(image, pxData, getPxPos, bpp, data, rawPos) {\n    // eslint-disable-line max-params\n    let imageWidth = image.width;\n    let imageHeight = image.height;\n    let imagePass = image.index;\n    for(let y = 0; y < imageHeight; y++){\n        for(let x = 0; x < imageWidth; x++){\n            let pxPos = getPxPos(x, y, imagePass);\n            pixelBppMapper[bpp](pxData, data, pxPos, rawPos);\n            rawPos += bpp; //eslint-disable-line no-param-reassign\n        }\n    }\n    return rawPos;\n}\nfunction mapImageCustomBit(image, pxData, getPxPos, bpp, bits, maxBit) {\n    // eslint-disable-line max-params\n    let imageWidth = image.width;\n    let imageHeight = image.height;\n    let imagePass = image.index;\n    for(let y = 0; y < imageHeight; y++){\n        for(let x = 0; x < imageWidth; x++){\n            let pixelData = bits.get(bpp);\n            let pxPos = getPxPos(x, y, imagePass);\n            pixelBppCustomMapper[bpp](pxData, pixelData, pxPos, maxBit);\n        }\n        bits.resetAfterLine();\n    }\n}\nexports.dataToBitMap = function(data, bitmapInfo) {\n    let width = bitmapInfo.width;\n    let height = bitmapInfo.height;\n    let depth = bitmapInfo.depth;\n    let bpp = bitmapInfo.bpp;\n    let interlace = bitmapInfo.interlace;\n    let bits;\n    if (depth !== 8) {\n        bits = bitRetriever(data, depth);\n    }\n    let pxData;\n    if (depth <= 8) {\n        pxData = Buffer.alloc(width * height * 4);\n    } else {\n        pxData = new Uint16Array(width * height * 4);\n    }\n    let maxBit = Math.pow(2, depth) - 1;\n    let rawPos = 0;\n    let images;\n    let getPxPos;\n    if (interlace) {\n        images = interlaceUtils.getImagePasses(width, height);\n        getPxPos = interlaceUtils.getInterlaceIterator(width, height);\n    } else {\n        let nonInterlacedPxPos = 0;\n        getPxPos = function() {\n            let returner = nonInterlacedPxPos;\n            nonInterlacedPxPos += 4;\n            return returner;\n        };\n        images = [\n            {\n                width: width,\n                height: height\n            }\n        ];\n    }\n    for(let imageIndex = 0; imageIndex < images.length; imageIndex++){\n        if (depth === 8) {\n            rawPos = mapImage8Bit(images[imageIndex], pxData, getPxPos, bpp, data, rawPos);\n        } else {\n            mapImageCustomBit(images[imageIndex], pxData, getPxPos, bpp, bits, maxBit);\n        }\n    }\n    if (depth === 8) {\n        if (rawPos !== data.length) {\n            throw new Error(\"extra data found\");\n        }\n    } else {\n        bits.end();\n    }\n    return pxData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/bitmapper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/bitpacker.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitpacker.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet constants = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pngjs/lib/constants.js\");\nmodule.exports = function(dataIn, width, height, options) {\n    let outHasAlpha = [\n        constants.COLORTYPE_COLOR_ALPHA,\n        constants.COLORTYPE_ALPHA\n    ].indexOf(options.colorType) !== -1;\n    if (options.colorType === options.inputColorType) {\n        let bigEndian = function() {\n            let buffer = new ArrayBuffer(2);\n            new DataView(buffer).setInt16(0, 256, true);\n            // Int16Array uses the platform's endianness.\n            return new Int16Array(buffer)[0] !== 256;\n        }();\n        // If no need to convert to grayscale and alpha is present/absent in both, take a fast route\n        if (options.bitDepth === 8 || options.bitDepth === 16 && bigEndian) {\n            return dataIn;\n        }\n    }\n    // map to a UInt16 array if data is 16bit, fix endianness below\n    let data = options.bitDepth !== 16 ? dataIn : new Uint16Array(dataIn.buffer);\n    let maxValue = 255;\n    let inBpp = constants.COLORTYPE_TO_BPP_MAP[options.inputColorType];\n    if (inBpp === 4 && !options.inputHasAlpha) {\n        inBpp = 3;\n    }\n    let outBpp = constants.COLORTYPE_TO_BPP_MAP[options.colorType];\n    if (options.bitDepth === 16) {\n        maxValue = 65535;\n        outBpp *= 2;\n    }\n    let outData = Buffer.alloc(width * height * outBpp);\n    let inIndex = 0;\n    let outIndex = 0;\n    let bgColor = options.bgColor || {};\n    if (bgColor.red === undefined) {\n        bgColor.red = maxValue;\n    }\n    if (bgColor.green === undefined) {\n        bgColor.green = maxValue;\n    }\n    if (bgColor.blue === undefined) {\n        bgColor.blue = maxValue;\n    }\n    function getRGBA() {\n        let red;\n        let green;\n        let blue;\n        let alpha = maxValue;\n        switch(options.inputColorType){\n            case constants.COLORTYPE_COLOR_ALPHA:\n                alpha = data[inIndex + 3];\n                red = data[inIndex];\n                green = data[inIndex + 1];\n                blue = data[inIndex + 2];\n                break;\n            case constants.COLORTYPE_COLOR:\n                red = data[inIndex];\n                green = data[inIndex + 1];\n                blue = data[inIndex + 2];\n                break;\n            case constants.COLORTYPE_ALPHA:\n                alpha = data[inIndex + 1];\n                red = data[inIndex];\n                green = red;\n                blue = red;\n                break;\n            case constants.COLORTYPE_GRAYSCALE:\n                red = data[inIndex];\n                green = red;\n                blue = red;\n                break;\n            default:\n                throw new Error(\"input color type:\" + options.inputColorType + \" is not supported at present\");\n        }\n        if (options.inputHasAlpha) {\n            if (!outHasAlpha) {\n                alpha /= maxValue;\n                red = Math.min(Math.max(Math.round((1 - alpha) * bgColor.red + alpha * red), 0), maxValue);\n                green = Math.min(Math.max(Math.round((1 - alpha) * bgColor.green + alpha * green), 0), maxValue);\n                blue = Math.min(Math.max(Math.round((1 - alpha) * bgColor.blue + alpha * blue), 0), maxValue);\n            }\n        }\n        return {\n            red: red,\n            green: green,\n            blue: blue,\n            alpha: alpha\n        };\n    }\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            let rgba = getRGBA(data, inIndex);\n            switch(options.colorType){\n                case constants.COLORTYPE_COLOR_ALPHA:\n                case constants.COLORTYPE_COLOR:\n                    if (options.bitDepth === 8) {\n                        outData[outIndex] = rgba.red;\n                        outData[outIndex + 1] = rgba.green;\n                        outData[outIndex + 2] = rgba.blue;\n                        if (outHasAlpha) {\n                            outData[outIndex + 3] = rgba.alpha;\n                        }\n                    } else {\n                        outData.writeUInt16BE(rgba.red, outIndex);\n                        outData.writeUInt16BE(rgba.green, outIndex + 2);\n                        outData.writeUInt16BE(rgba.blue, outIndex + 4);\n                        if (outHasAlpha) {\n                            outData.writeUInt16BE(rgba.alpha, outIndex + 6);\n                        }\n                    }\n                    break;\n                case constants.COLORTYPE_ALPHA:\n                case constants.COLORTYPE_GRAYSCALE:\n                    {\n                        // Convert to grayscale and alpha\n                        let grayscale = (rgba.red + rgba.green + rgba.blue) / 3;\n                        if (options.bitDepth === 8) {\n                            outData[outIndex] = grayscale;\n                            if (outHasAlpha) {\n                                outData[outIndex + 1] = rgba.alpha;\n                            }\n                        } else {\n                            outData.writeUInt16BE(grayscale, outIndex);\n                            if (outHasAlpha) {\n                                outData.writeUInt16BE(rgba.alpha, outIndex + 2);\n                            }\n                        }\n                        break;\n                    }\n                default:\n                    throw new Error(\"unrecognised color Type \" + options.colorType);\n            }\n            inIndex += inBpp;\n            outIndex += outBpp;\n        }\n    }\n    return outData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/bitpacker.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/chunkstream.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/chunkstream.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet ChunkStream = module.exports = function() {\n    Stream.call(this);\n    this._buffers = [];\n    this._buffered = 0;\n    this._reads = [];\n    this._paused = false;\n    this._encoding = \"utf8\";\n    this.writable = true;\n};\nutil.inherits(ChunkStream, Stream);\nChunkStream.prototype.read = function(length, callback) {\n    this._reads.push({\n        length: Math.abs(length),\n        allowLess: length < 0,\n        func: callback\n    });\n    process.nextTick((function() {\n        this._process();\n        // its paused and there is not enought data then ask for more\n        if (this._paused && this._reads && this._reads.length > 0) {\n            this._paused = false;\n            this.emit(\"drain\");\n        }\n    }).bind(this));\n};\nChunkStream.prototype.write = function(data, encoding) {\n    if (!this.writable) {\n        this.emit(\"error\", new Error(\"Stream not writable\"));\n        return false;\n    }\n    let dataBuffer;\n    if (Buffer.isBuffer(data)) {\n        dataBuffer = data;\n    } else {\n        dataBuffer = Buffer.from(data, encoding || this._encoding);\n    }\n    this._buffers.push(dataBuffer);\n    this._buffered += dataBuffer.length;\n    this._process();\n    // ok if there are no more read requests\n    if (this._reads && this._reads.length === 0) {\n        this._paused = true;\n    }\n    return this.writable && !this._paused;\n};\nChunkStream.prototype.end = function(data, encoding) {\n    if (data) {\n        this.write(data, encoding);\n    }\n    this.writable = false;\n    // already destroyed\n    if (!this._buffers) {\n        return;\n    }\n    // enqueue or handle end\n    if (this._buffers.length === 0) {\n        this._end();\n    } else {\n        this._buffers.push(null);\n        this._process();\n    }\n};\nChunkStream.prototype.destroySoon = ChunkStream.prototype.end;\nChunkStream.prototype._end = function() {\n    if (this._reads.length > 0) {\n        this.emit(\"error\", new Error(\"Unexpected end of input\"));\n    }\n    this.destroy();\n};\nChunkStream.prototype.destroy = function() {\n    if (!this._buffers) {\n        return;\n    }\n    this.writable = false;\n    this._reads = null;\n    this._buffers = null;\n    this.emit(\"close\");\n};\nChunkStream.prototype._processReadAllowingLess = function(read) {\n    // ok there is any data so that we can satisfy this request\n    this._reads.shift(); // == read\n    // first we need to peek into first buffer\n    let smallerBuf = this._buffers[0];\n    // ok there is more data than we need\n    if (smallerBuf.length > read.length) {\n        this._buffered -= read.length;\n        this._buffers[0] = smallerBuf.slice(read.length);\n        read.func.call(this, smallerBuf.slice(0, read.length));\n    } else {\n        // ok this is less than maximum length so use it all\n        this._buffered -= smallerBuf.length;\n        this._buffers.shift(); // == smallerBuf\n        read.func.call(this, smallerBuf);\n    }\n};\nChunkStream.prototype._processRead = function(read) {\n    this._reads.shift(); // == read\n    let pos = 0;\n    let count = 0;\n    let data = Buffer.alloc(read.length);\n    // create buffer for all data\n    while(pos < read.length){\n        let buf = this._buffers[count++];\n        let len = Math.min(buf.length, read.length - pos);\n        buf.copy(data, pos, 0, len);\n        pos += len;\n        // last buffer wasn't used all so just slice it and leave\n        if (len !== buf.length) {\n            this._buffers[--count] = buf.slice(len);\n        }\n    }\n    // remove all used buffers\n    if (count > 0) {\n        this._buffers.splice(0, count);\n    }\n    this._buffered -= read.length;\n    read.func.call(this, data);\n};\nChunkStream.prototype._process = function() {\n    try {\n        // as long as there is any data and read requests\n        while(this._buffered > 0 && this._reads && this._reads.length > 0){\n            let read = this._reads[0];\n            // read any data (but no more than length)\n            if (read.allowLess) {\n                this._processReadAllowingLess(read);\n            } else if (this._buffered >= read.length) {\n                // ok we can meet some expectations\n                this._processRead(read);\n            } else {\n                break;\n            }\n        }\n        if (this._buffers && !this.writable) {\n            this._end();\n        }\n    } catch (ex) {\n        this.emit(\"error\", ex);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/chunkstream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/constants.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/constants.js ***!
  \*********************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    PNG_SIGNATURE: [\n        0x89,\n        0x50,\n        0x4e,\n        0x47,\n        0x0d,\n        0x0a,\n        0x1a,\n        0x0a\n    ],\n    TYPE_IHDR: 0x49484452,\n    TYPE_IEND: 0x49454e44,\n    TYPE_IDAT: 0x49444154,\n    TYPE_PLTE: 0x504c5445,\n    TYPE_tRNS: 0x74524e53,\n    TYPE_gAMA: 0x67414d41,\n    // color-type bits\n    COLORTYPE_GRAYSCALE: 0,\n    COLORTYPE_PALETTE: 1,\n    COLORTYPE_COLOR: 2,\n    COLORTYPE_ALPHA: 4,\n    // color-type combinations\n    COLORTYPE_PALETTE_COLOR: 3,\n    COLORTYPE_COLOR_ALPHA: 6,\n    COLORTYPE_TO_BPP_MAP: {\n        0: 1,\n        2: 3,\n        3: 1,\n        4: 2,\n        6: 4\n    },\n    GAMMA_DIVISION: 100000\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxPQUFPQyxPQUFPLEdBQUc7SUFDZkMsZUFBZTtRQUFDO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtRQUFNO1FBQU07S0FBSztJQUUvREMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLFdBQVc7SUFDWEMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLFdBQVc7SUFFWCxrQkFBa0I7SUFDbEJDLHFCQUFxQjtJQUNyQkMsbUJBQW1CO0lBQ25CQyxpQkFBaUI7SUFDakJDLGlCQUFpQjtJQUVqQiwwQkFBMEI7SUFDMUJDLHlCQUF5QjtJQUN6QkMsdUJBQXVCO0lBRXZCQyxzQkFBc0I7UUFDcEIsR0FBRztRQUNILEdBQUc7UUFDSCxHQUFHO1FBQ0gsR0FBRztRQUNILEdBQUc7SUFDTDtJQUVBQyxnQkFBZ0I7QUFDbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly94YXV1c2QtdHJhZGluZy1hbmFseXplci8uL25vZGVfbW9kdWxlcy9wbmdqcy9saWIvY29uc3RhbnRzLmpzPzU4ZjQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBQTkdfU0lHTkFUVVJFOiBbMHg4OSwgMHg1MCwgMHg0ZSwgMHg0NywgMHgwZCwgMHgwYSwgMHgxYSwgMHgwYV0sXG5cbiAgVFlQRV9JSERSOiAweDQ5NDg0NDUyLFxuICBUWVBFX0lFTkQ6IDB4NDk0NTRlNDQsXG4gIFRZUEVfSURBVDogMHg0OTQ0NDE1NCxcbiAgVFlQRV9QTFRFOiAweDUwNGM1NDQ1LFxuICBUWVBFX3RSTlM6IDB4NzQ1MjRlNTMsIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgY2FtZWxjYXNlXG4gIFRZUEVfZ0FNQTogMHg2NzQxNGQ0MSwgLy8gZXNsaW50LWRpc2FibGUtbGluZSBjYW1lbGNhc2VcblxuICAvLyBjb2xvci10eXBlIGJpdHNcbiAgQ09MT1JUWVBFX0dSQVlTQ0FMRTogMCxcbiAgQ09MT1JUWVBFX1BBTEVUVEU6IDEsXG4gIENPTE9SVFlQRV9DT0xPUjogMixcbiAgQ09MT1JUWVBFX0FMUEhBOiA0LCAvLyBlLmcuIGdyYXlzY2FsZSBhbmQgYWxwaGFcblxuICAvLyBjb2xvci10eXBlIGNvbWJpbmF0aW9uc1xuICBDT0xPUlRZUEVfUEFMRVRURV9DT0xPUjogMyxcbiAgQ09MT1JUWVBFX0NPTE9SX0FMUEhBOiA2LFxuXG4gIENPTE9SVFlQRV9UT19CUFBfTUFQOiB7XG4gICAgMDogMSxcbiAgICAyOiAzLFxuICAgIDM6IDEsXG4gICAgNDogMixcbiAgICA2OiA0LFxuICB9LFxuXG4gIEdBTU1BX0RJVklTSU9OOiAxMDAwMDAsXG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJQTkdfU0lHTkFUVVJFIiwiVFlQRV9JSERSIiwiVFlQRV9JRU5EIiwiVFlQRV9JREFUIiwiVFlQRV9QTFRFIiwiVFlQRV90Uk5TIiwiVFlQRV9nQU1BIiwiQ09MT1JUWVBFX0dSQVlTQ0FMRSIsIkNPTE9SVFlQRV9QQUxFVFRFIiwiQ09MT1JUWVBFX0NPTE9SIiwiQ09MT1JUWVBFX0FMUEhBIiwiQ09MT1JUWVBFX1BBTEVUVEVfQ09MT1IiLCJDT0xPUlRZUEVfQ09MT1JfQUxQSEEiLCJDT0xPUlRZUEVfVE9fQlBQX01BUCIsIkdBTU1BX0RJVklTSU9OIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/crc.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/crc.js ***!
  \***************************************/
/***/ ((module) => {

eval("\nlet crcTable = [];\n(function() {\n    for(let i = 0; i < 256; i++){\n        let currentCrc = i;\n        for(let j = 0; j < 8; j++){\n            if (currentCrc & 1) {\n                currentCrc = 0xedb88320 ^ currentCrc >>> 1;\n            } else {\n                currentCrc = currentCrc >>> 1;\n            }\n        }\n        crcTable[i] = currentCrc;\n    }\n})();\nlet CrcCalculator = module.exports = function() {\n    this._crc = -1;\n};\nCrcCalculator.prototype.write = function(data) {\n    for(let i = 0; i < data.length; i++){\n        this._crc = crcTable[(this._crc ^ data[i]) & 0xff] ^ this._crc >>> 8;\n    }\n    return true;\n};\nCrcCalculator.prototype.crc32 = function() {\n    return this._crc ^ -1;\n};\nCrcCalculator.crc32 = function(buf) {\n    let crc = -1;\n    for(let i = 0; i < buf.length; i++){\n        crc = crcTable[(crc ^ buf[i]) & 0xff] ^ crc >>> 8;\n    }\n    return crc ^ -1;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NyYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViLElBQUlBLFdBQVcsRUFBRTtBQUVoQjtJQUNDLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJLEtBQUtBLElBQUs7UUFDNUIsSUFBSUMsYUFBYUQ7UUFDakIsSUFBSyxJQUFJRSxJQUFJLEdBQUdBLElBQUksR0FBR0EsSUFBSztZQUMxQixJQUFJRCxhQUFhLEdBQUc7Z0JBQ2xCQSxhQUFhLGFBQWNBLGVBQWU7WUFDNUMsT0FBTztnQkFDTEEsYUFBYUEsZUFBZTtZQUM5QjtRQUNGO1FBQ0FGLFFBQVEsQ0FBQ0MsRUFBRSxHQUFHQztJQUNoQjtBQUNGO0FBRUEsSUFBSUUsZ0JBQWlCQyxPQUFPQyxPQUFPLEdBQUc7SUFDcEMsSUFBSSxDQUFDQyxJQUFJLEdBQUcsQ0FBQztBQUNmO0FBRUFILGNBQWNJLFNBQVMsQ0FBQ0MsS0FBSyxHQUFHLFNBQVVDLElBQUk7SUFDNUMsSUFBSyxJQUFJVCxJQUFJLEdBQUdBLElBQUlTLEtBQUtDLE1BQU0sRUFBRVYsSUFBSztRQUNwQyxJQUFJLENBQUNNLElBQUksR0FBR1AsUUFBUSxDQUFDLENBQUMsSUFBSSxDQUFDTyxJQUFJLEdBQUdHLElBQUksQ0FBQ1QsRUFBRSxJQUFJLEtBQUssR0FBSSxJQUFJLENBQUNNLElBQUksS0FBSztJQUN0RTtJQUNBLE9BQU87QUFDVDtBQUVBSCxjQUFjSSxTQUFTLENBQUNJLEtBQUssR0FBRztJQUM5QixPQUFPLElBQUksQ0FBQ0wsSUFBSSxHQUFHLENBQUM7QUFDdEI7QUFFQUgsY0FBY1EsS0FBSyxHQUFHLFNBQVVDLEdBQUc7SUFDakMsSUFBSUMsTUFBTSxDQUFDO0lBQ1gsSUFBSyxJQUFJYixJQUFJLEdBQUdBLElBQUlZLElBQUlGLE1BQU0sRUFBRVYsSUFBSztRQUNuQ2EsTUFBTWQsUUFBUSxDQUFDLENBQUNjLE1BQU1ELEdBQUcsQ0FBQ1osRUFBRSxJQUFJLEtBQUssR0FBSWEsUUFBUTtJQUNuRDtJQUNBLE9BQU9BLE1BQU0sQ0FBQztBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL3hhdXVzZC10cmFkaW5nLWFuYWx5emVyLy4vbm9kZV9tb2R1bGVzL3BuZ2pzL2xpYi9jcmMuanM/NzBmYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IGNyY1RhYmxlID0gW107XG5cbihmdW5jdGlvbiAoKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgMjU2OyBpKyspIHtcbiAgICBsZXQgY3VycmVudENyYyA9IGk7XG4gICAgZm9yIChsZXQgaiA9IDA7IGogPCA4OyBqKyspIHtcbiAgICAgIGlmIChjdXJyZW50Q3JjICYgMSkge1xuICAgICAgICBjdXJyZW50Q3JjID0gMHhlZGI4ODMyMCBeIChjdXJyZW50Q3JjID4+PiAxKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGN1cnJlbnRDcmMgPSBjdXJyZW50Q3JjID4+PiAxO1xuICAgICAgfVxuICAgIH1cbiAgICBjcmNUYWJsZVtpXSA9IGN1cnJlbnRDcmM7XG4gIH1cbn0pKCk7XG5cbmxldCBDcmNDYWxjdWxhdG9yID0gKG1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKCkge1xuICB0aGlzLl9jcmMgPSAtMTtcbn0pO1xuXG5DcmNDYWxjdWxhdG9yLnByb3RvdHlwZS53cml0ZSA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKykge1xuICAgIHRoaXMuX2NyYyA9IGNyY1RhYmxlWyh0aGlzLl9jcmMgXiBkYXRhW2ldKSAmIDB4ZmZdIF4gKHRoaXMuX2NyYyA+Pj4gOCk7XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59O1xuXG5DcmNDYWxjdWxhdG9yLnByb3RvdHlwZS5jcmMzMiA9IGZ1bmN0aW9uICgpIHtcbiAgcmV0dXJuIHRoaXMuX2NyYyBeIC0xO1xufTtcblxuQ3JjQ2FsY3VsYXRvci5jcmMzMiA9IGZ1bmN0aW9uIChidWYpIHtcbiAgbGV0IGNyYyA9IC0xO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGJ1Zi5sZW5ndGg7IGkrKykge1xuICAgIGNyYyA9IGNyY1RhYmxlWyhjcmMgXiBidWZbaV0pICYgMHhmZl0gXiAoY3JjID4+PiA4KTtcbiAgfVxuICByZXR1cm4gY3JjIF4gLTE7XG59O1xuIl0sIm5hbWVzIjpbImNyY1RhYmxlIiwiaSIsImN1cnJlbnRDcmMiLCJqIiwiQ3JjQ2FsY3VsYXRvciIsIm1vZHVsZSIsImV4cG9ydHMiLCJfY3JjIiwicHJvdG90eXBlIiwid3JpdGUiLCJkYXRhIiwibGVuZ3RoIiwiY3JjMzIiLCJidWYiLCJjcmMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/crc.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/filter-pack.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/filter-pack.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(rsc)/./node_modules/pngjs/lib/paeth-predictor.js\");\nfunction filterNone(pxData, pxPos, byteWidth, rawData, rawPos) {\n    for(let x = 0; x < byteWidth; x++){\n        rawData[rawPos + x] = pxData[pxPos + x];\n    }\n}\nfunction filterSumNone(pxData, pxPos, byteWidth) {\n    let sum = 0;\n    let length = pxPos + byteWidth;\n    for(let i = pxPos; i < length; i++){\n        sum += Math.abs(pxData[i]);\n    }\n    return sum;\n}\nfunction filterSub(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let val = pxData[pxPos + x] - left;\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumSub(pxData, pxPos, byteWidth, bpp) {\n    let sum = 0;\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let val = pxData[pxPos + x] - left;\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nfunction filterUp(pxData, pxPos, byteWidth, rawData, rawPos) {\n    for(let x = 0; x < byteWidth; x++){\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let val = pxData[pxPos + x] - up;\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumUp(pxData, pxPos, byteWidth) {\n    let sum = 0;\n    let length = pxPos + byteWidth;\n    for(let x = pxPos; x < length; x++){\n        let up = pxPos > 0 ? pxData[x - byteWidth] : 0;\n        let val = pxData[x] - up;\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nfunction filterAvg(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let val = pxData[pxPos + x] - (left + up >> 1);\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumAvg(pxData, pxPos, byteWidth, bpp) {\n    let sum = 0;\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let val = pxData[pxPos + x] - (left + up >> 1);\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nfunction filterPaeth(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let upleft = pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n        let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumPaeth(pxData, pxPos, byteWidth, bpp) {\n    let sum = 0;\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let upleft = pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n        let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nlet filters = {\n    0: filterNone,\n    1: filterSub,\n    2: filterUp,\n    3: filterAvg,\n    4: filterPaeth\n};\nlet filterSums = {\n    0: filterSumNone,\n    1: filterSumSub,\n    2: filterSumUp,\n    3: filterSumAvg,\n    4: filterSumPaeth\n};\nmodule.exports = function(pxData, width, height, options, bpp) {\n    let filterTypes;\n    if (!(\"filterType\" in options) || options.filterType === -1) {\n        filterTypes = [\n            0,\n            1,\n            2,\n            3,\n            4\n        ];\n    } else if (typeof options.filterType === \"number\") {\n        filterTypes = [\n            options.filterType\n        ];\n    } else {\n        throw new Error(\"unrecognised filter types\");\n    }\n    if (options.bitDepth === 16) {\n        bpp *= 2;\n    }\n    let byteWidth = width * bpp;\n    let rawPos = 0;\n    let pxPos = 0;\n    let rawData = Buffer.alloc((byteWidth + 1) * height);\n    let sel = filterTypes[0];\n    for(let y = 0; y < height; y++){\n        if (filterTypes.length > 1) {\n            // find best filter for this line (with lowest sum of values)\n            let min = Infinity;\n            for(let i = 0; i < filterTypes.length; i++){\n                let sum = filterSums[filterTypes[i]](pxData, pxPos, byteWidth, bpp);\n                if (sum < min) {\n                    sel = filterTypes[i];\n                    min = sum;\n                }\n            }\n        }\n        rawData[rawPos] = sel;\n        rawPos++;\n        filters[sel](pxData, pxPos, byteWidth, rawData, rawPos, bpp);\n        rawPos += byteWidth;\n        pxPos += byteWidth;\n    }\n    return rawData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/filter-pack.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/filter-parse-async.js":
/*!******************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-async.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(rsc)/./node_modules/pngjs/lib/chunkstream.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(rsc)/./node_modules/pngjs/lib/filter-parse.js\");\nlet FilterAsync = module.exports = function(bitmapInfo) {\n    ChunkStream.call(this);\n    let buffers = [];\n    let that = this;\n    this._filter = new Filter(bitmapInfo, {\n        read: this.read.bind(this),\n        write: function(buffer) {\n            buffers.push(buffer);\n        },\n        complete: function() {\n            that.emit(\"complete\", Buffer.concat(buffers));\n        }\n    });\n    this._filter.start();\n};\nutil.inherits(FilterAsync, ChunkStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/filter-parse-async.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/filter-parse-sync.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-sync.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(rsc)/./node_modules/pngjs/lib/sync-reader.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(rsc)/./node_modules/pngjs/lib/filter-parse.js\");\nexports.process = function(inBuffer, bitmapInfo) {\n    let outBuffers = [];\n    let reader = new SyncReader(inBuffer);\n    let filter = new Filter(bitmapInfo, {\n        read: reader.read.bind(reader),\n        write: function(bufferPart) {\n            outBuffers.push(bufferPart);\n        },\n        complete: function() {}\n    });\n    filter.start();\n    reader.process();\n    return Buffer.concat(outBuffers);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWIsSUFBSUEsYUFBYUMsbUJBQU9BLENBQUMsb0VBQWU7QUFDeEMsSUFBSUMsU0FBU0QsbUJBQU9BLENBQUMsc0VBQWdCO0FBRXJDRSxlQUFlLEdBQUcsU0FBVUUsUUFBUSxFQUFFQyxVQUFVO0lBQzlDLElBQUlDLGFBQWEsRUFBRTtJQUNuQixJQUFJQyxTQUFTLElBQUlSLFdBQVdLO0lBQzVCLElBQUlJLFNBQVMsSUFBSVAsT0FBT0ksWUFBWTtRQUNsQ0ksTUFBTUYsT0FBT0UsSUFBSSxDQUFDQyxJQUFJLENBQUNIO1FBQ3ZCSSxPQUFPLFNBQVVDLFVBQVU7WUFDekJOLFdBQVdPLElBQUksQ0FBQ0Q7UUFDbEI7UUFDQUUsVUFBVSxZQUFhO0lBQ3pCO0lBRUFOLE9BQU9PLEtBQUs7SUFDWlIsT0FBT0osT0FBTztJQUVkLE9BQU9hLE9BQU9DLE1BQU0sQ0FBQ1g7QUFDdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly94YXV1c2QtdHJhZGluZy1hbmFseXplci8uL25vZGVfbW9kdWxlcy9wbmdqcy9saWIvZmlsdGVyLXBhcnNlLXN5bmMuanM/NDFkYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IFN5bmNSZWFkZXIgPSByZXF1aXJlKFwiLi9zeW5jLXJlYWRlclwiKTtcbmxldCBGaWx0ZXIgPSByZXF1aXJlKFwiLi9maWx0ZXItcGFyc2VcIik7XG5cbmV4cG9ydHMucHJvY2VzcyA9IGZ1bmN0aW9uIChpbkJ1ZmZlciwgYml0bWFwSW5mbykge1xuICBsZXQgb3V0QnVmZmVycyA9IFtdO1xuICBsZXQgcmVhZGVyID0gbmV3IFN5bmNSZWFkZXIoaW5CdWZmZXIpO1xuICBsZXQgZmlsdGVyID0gbmV3IEZpbHRlcihiaXRtYXBJbmZvLCB7XG4gICAgcmVhZDogcmVhZGVyLnJlYWQuYmluZChyZWFkZXIpLFxuICAgIHdyaXRlOiBmdW5jdGlvbiAoYnVmZmVyUGFydCkge1xuICAgICAgb3V0QnVmZmVycy5wdXNoKGJ1ZmZlclBhcnQpO1xuICAgIH0sXG4gICAgY29tcGxldGU6IGZ1bmN0aW9uICgpIHt9LFxuICB9KTtcblxuICBmaWx0ZXIuc3RhcnQoKTtcbiAgcmVhZGVyLnByb2Nlc3MoKTtcblxuICByZXR1cm4gQnVmZmVyLmNvbmNhdChvdXRCdWZmZXJzKTtcbn07XG4iXSwibmFtZXMiOlsiU3luY1JlYWRlciIsInJlcXVpcmUiLCJGaWx0ZXIiLCJleHBvcnRzIiwicHJvY2VzcyIsImluQnVmZmVyIiwiYml0bWFwSW5mbyIsIm91dEJ1ZmZlcnMiLCJyZWFkZXIiLCJmaWx0ZXIiLCJyZWFkIiwiYmluZCIsIndyaXRlIiwiYnVmZmVyUGFydCIsInB1c2giLCJjb21wbGV0ZSIsInN0YXJ0IiwiQnVmZmVyIiwiY29uY2F0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/filter-parse-sync.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/filter-parse.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(rsc)/./node_modules/pngjs/lib/interlace.js\");\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(rsc)/./node_modules/pngjs/lib/paeth-predictor.js\");\nfunction getByteWidth(width, bpp, depth) {\n    let byteWidth = width * bpp;\n    if (depth !== 8) {\n        byteWidth = Math.ceil(byteWidth / (8 / depth));\n    }\n    return byteWidth;\n}\nlet Filter = module.exports = function(bitmapInfo, dependencies) {\n    let width = bitmapInfo.width;\n    let height = bitmapInfo.height;\n    let interlace = bitmapInfo.interlace;\n    let bpp = bitmapInfo.bpp;\n    let depth = bitmapInfo.depth;\n    this.read = dependencies.read;\n    this.write = dependencies.write;\n    this.complete = dependencies.complete;\n    this._imageIndex = 0;\n    this._images = [];\n    if (interlace) {\n        let passes = interlaceUtils.getImagePasses(width, height);\n        for(let i = 0; i < passes.length; i++){\n            this._images.push({\n                byteWidth: getByteWidth(passes[i].width, bpp, depth),\n                height: passes[i].height,\n                lineIndex: 0\n            });\n        }\n    } else {\n        this._images.push({\n            byteWidth: getByteWidth(width, bpp, depth),\n            height: height,\n            lineIndex: 0\n        });\n    }\n    // when filtering the line we look at the pixel to the left\n    // the spec also says it is done on a byte level regardless of the number of pixels\n    // so if the depth is byte compatible (8 or 16) we subtract the bpp in order to compare back\n    // a pixel rather than just a different byte part. However if we are sub byte, we ignore.\n    if (depth === 8) {\n        this._xComparison = bpp;\n    } else if (depth === 16) {\n        this._xComparison = bpp * 2;\n    } else {\n        this._xComparison = 1;\n    }\n};\nFilter.prototype.start = function() {\n    this.read(this._images[this._imageIndex].byteWidth + 1, this._reverseFilterLine.bind(this));\n};\nFilter.prototype._unFilterType1 = function(rawData, unfilteredLine, byteWidth) {\n    let xComparison = this._xComparison;\n    let xBiggerThan = xComparison - 1;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f1Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n        unfilteredLine[x] = rawByte + f1Left;\n    }\n};\nFilter.prototype._unFilterType2 = function(rawData, unfilteredLine, byteWidth) {\n    let lastLine = this._lastLine;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f2Up = lastLine ? lastLine[x] : 0;\n        unfilteredLine[x] = rawByte + f2Up;\n    }\n};\nFilter.prototype._unFilterType3 = function(rawData, unfilteredLine, byteWidth) {\n    let xComparison = this._xComparison;\n    let xBiggerThan = xComparison - 1;\n    let lastLine = this._lastLine;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f3Up = lastLine ? lastLine[x] : 0;\n        let f3Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n        let f3Add = Math.floor((f3Left + f3Up) / 2);\n        unfilteredLine[x] = rawByte + f3Add;\n    }\n};\nFilter.prototype._unFilterType4 = function(rawData, unfilteredLine, byteWidth) {\n    let xComparison = this._xComparison;\n    let xBiggerThan = xComparison - 1;\n    let lastLine = this._lastLine;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f4Up = lastLine ? lastLine[x] : 0;\n        let f4Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n        let f4UpLeft = x > xBiggerThan && lastLine ? lastLine[x - xComparison] : 0;\n        let f4Add = paethPredictor(f4Left, f4Up, f4UpLeft);\n        unfilteredLine[x] = rawByte + f4Add;\n    }\n};\nFilter.prototype._reverseFilterLine = function(rawData) {\n    let filter = rawData[0];\n    let unfilteredLine;\n    let currentImage = this._images[this._imageIndex];\n    let byteWidth = currentImage.byteWidth;\n    if (filter === 0) {\n        unfilteredLine = rawData.slice(1, byteWidth + 1);\n    } else {\n        unfilteredLine = Buffer.alloc(byteWidth);\n        switch(filter){\n            case 1:\n                this._unFilterType1(rawData, unfilteredLine, byteWidth);\n                break;\n            case 2:\n                this._unFilterType2(rawData, unfilteredLine, byteWidth);\n                break;\n            case 3:\n                this._unFilterType3(rawData, unfilteredLine, byteWidth);\n                break;\n            case 4:\n                this._unFilterType4(rawData, unfilteredLine, byteWidth);\n                break;\n            default:\n                throw new Error(\"Unrecognised filter type - \" + filter);\n        }\n    }\n    this.write(unfilteredLine);\n    currentImage.lineIndex++;\n    if (currentImage.lineIndex >= currentImage.height) {\n        this._lastLine = null;\n        this._imageIndex++;\n        currentImage = this._images[this._imageIndex];\n    } else {\n        this._lastLine = unfilteredLine;\n    }\n    if (currentImage) {\n        // read, using the byte width that may be from the new current image\n        this.read(currentImage.byteWidth + 1, this._reverseFilterLine.bind(this));\n    } else {\n        this._lastLine = null;\n        this.complete();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViLElBQUlBLGlCQUFpQkMsbUJBQU9BLENBQUMsZ0VBQWE7QUFDMUMsSUFBSUMsaUJBQWlCRCxtQkFBT0EsQ0FBQyw0RUFBbUI7QUFFaEQsU0FBU0UsYUFBYUMsS0FBSyxFQUFFQyxHQUFHLEVBQUVDLEtBQUs7SUFDckMsSUFBSUMsWUFBWUgsUUFBUUM7SUFDeEIsSUFBSUMsVUFBVSxHQUFHO1FBQ2ZDLFlBQVlDLEtBQUtDLElBQUksQ0FBQ0YsWUFBYSxLQUFJRCxLQUFJO0lBQzdDO0lBQ0EsT0FBT0M7QUFDVDtBQUVBLElBQUlHLFNBQVVDLE9BQU9DLE9BQU8sR0FBRyxTQUFVQyxVQUFVLEVBQUVDLFlBQVk7SUFDL0QsSUFBSVYsUUFBUVMsV0FBV1QsS0FBSztJQUM1QixJQUFJVyxTQUFTRixXQUFXRSxNQUFNO0lBQzlCLElBQUlDLFlBQVlILFdBQVdHLFNBQVM7SUFDcEMsSUFBSVgsTUFBTVEsV0FBV1IsR0FBRztJQUN4QixJQUFJQyxRQUFRTyxXQUFXUCxLQUFLO0lBRTVCLElBQUksQ0FBQ1csSUFBSSxHQUFHSCxhQUFhRyxJQUFJO0lBQzdCLElBQUksQ0FBQ0MsS0FBSyxHQUFHSixhQUFhSSxLQUFLO0lBQy9CLElBQUksQ0FBQ0MsUUFBUSxHQUFHTCxhQUFhSyxRQUFRO0lBRXJDLElBQUksQ0FBQ0MsV0FBVyxHQUFHO0lBQ25CLElBQUksQ0FBQ0MsT0FBTyxHQUFHLEVBQUU7SUFDakIsSUFBSUwsV0FBVztRQUNiLElBQUlNLFNBQVN0QixlQUFldUIsY0FBYyxDQUFDbkIsT0FBT1c7UUFDbEQsSUFBSyxJQUFJUyxJQUFJLEdBQUdBLElBQUlGLE9BQU9HLE1BQU0sRUFBRUQsSUFBSztZQUN0QyxJQUFJLENBQUNILE9BQU8sQ0FBQ0ssSUFBSSxDQUFDO2dCQUNoQm5CLFdBQVdKLGFBQWFtQixNQUFNLENBQUNFLEVBQUUsQ0FBQ3BCLEtBQUssRUFBRUMsS0FBS0M7Z0JBQzlDUyxRQUFRTyxNQUFNLENBQUNFLEVBQUUsQ0FBQ1QsTUFBTTtnQkFDeEJZLFdBQVc7WUFDYjtRQUNGO0lBQ0YsT0FBTztRQUNMLElBQUksQ0FBQ04sT0FBTyxDQUFDSyxJQUFJLENBQUM7WUFDaEJuQixXQUFXSixhQUFhQyxPQUFPQyxLQUFLQztZQUNwQ1MsUUFBUUE7WUFDUlksV0FBVztRQUNiO0lBQ0Y7SUFFQSwyREFBMkQ7SUFDM0QsbUZBQW1GO0lBQ25GLDRGQUE0RjtJQUM1Rix5RkFBeUY7SUFDekYsSUFBSXJCLFVBQVUsR0FBRztRQUNmLElBQUksQ0FBQ3NCLFlBQVksR0FBR3ZCO0lBQ3RCLE9BQU8sSUFBSUMsVUFBVSxJQUFJO1FBQ3ZCLElBQUksQ0FBQ3NCLFlBQVksR0FBR3ZCLE1BQU07SUFDNUIsT0FBTztRQUNMLElBQUksQ0FBQ3VCLFlBQVksR0FBRztJQUN0QjtBQUNGO0FBRUFsQixPQUFPbUIsU0FBUyxDQUFDQyxLQUFLLEdBQUc7SUFDdkIsSUFBSSxDQUFDYixJQUFJLENBQ1AsSUFBSSxDQUFDSSxPQUFPLENBQUMsSUFBSSxDQUFDRCxXQUFXLENBQUMsQ0FBQ2IsU0FBUyxHQUFHLEdBQzNDLElBQUksQ0FBQ3dCLGtCQUFrQixDQUFDQyxJQUFJLENBQUMsSUFBSTtBQUVyQztBQUVBdEIsT0FBT21CLFNBQVMsQ0FBQ0ksY0FBYyxHQUFHLFNBQ2hDQyxPQUFPLEVBQ1BDLGNBQWMsRUFDZDVCLFNBQVM7SUFFVCxJQUFJNkIsY0FBYyxJQUFJLENBQUNSLFlBQVk7SUFDbkMsSUFBSVMsY0FBY0QsY0FBYztJQUVoQyxJQUFLLElBQUlFLElBQUksR0FBR0EsSUFBSS9CLFdBQVcrQixJQUFLO1FBQ2xDLElBQUlDLFVBQVVMLE9BQU8sQ0FBQyxJQUFJSSxFQUFFO1FBQzVCLElBQUlFLFNBQVNGLElBQUlELGNBQWNGLGNBQWMsQ0FBQ0csSUFBSUYsWUFBWSxHQUFHO1FBQ2pFRCxjQUFjLENBQUNHLEVBQUUsR0FBR0MsVUFBVUM7SUFDaEM7QUFDRjtBQUVBOUIsT0FBT21CLFNBQVMsQ0FBQ1ksY0FBYyxHQUFHLFNBQ2hDUCxPQUFPLEVBQ1BDLGNBQWMsRUFDZDVCLFNBQVM7SUFFVCxJQUFJbUMsV0FBVyxJQUFJLENBQUNDLFNBQVM7SUFFN0IsSUFBSyxJQUFJTCxJQUFJLEdBQUdBLElBQUkvQixXQUFXK0IsSUFBSztRQUNsQyxJQUFJQyxVQUFVTCxPQUFPLENBQUMsSUFBSUksRUFBRTtRQUM1QixJQUFJTSxPQUFPRixXQUFXQSxRQUFRLENBQUNKLEVBQUUsR0FBRztRQUNwQ0gsY0FBYyxDQUFDRyxFQUFFLEdBQUdDLFVBQVVLO0lBQ2hDO0FBQ0Y7QUFFQWxDLE9BQU9tQixTQUFTLENBQUNnQixjQUFjLEdBQUcsU0FDaENYLE9BQU8sRUFDUEMsY0FBYyxFQUNkNUIsU0FBUztJQUVULElBQUk2QixjQUFjLElBQUksQ0FBQ1IsWUFBWTtJQUNuQyxJQUFJUyxjQUFjRCxjQUFjO0lBQ2hDLElBQUlNLFdBQVcsSUFBSSxDQUFDQyxTQUFTO0lBRTdCLElBQUssSUFBSUwsSUFBSSxHQUFHQSxJQUFJL0IsV0FBVytCLElBQUs7UUFDbEMsSUFBSUMsVUFBVUwsT0FBTyxDQUFDLElBQUlJLEVBQUU7UUFDNUIsSUFBSVEsT0FBT0osV0FBV0EsUUFBUSxDQUFDSixFQUFFLEdBQUc7UUFDcEMsSUFBSVMsU0FBU1QsSUFBSUQsY0FBY0YsY0FBYyxDQUFDRyxJQUFJRixZQUFZLEdBQUc7UUFDakUsSUFBSVksUUFBUXhDLEtBQUt5QyxLQUFLLENBQUMsQ0FBQ0YsU0FBU0QsSUFBRyxJQUFLO1FBQ3pDWCxjQUFjLENBQUNHLEVBQUUsR0FBR0MsVUFBVVM7SUFDaEM7QUFDRjtBQUVBdEMsT0FBT21CLFNBQVMsQ0FBQ3FCLGNBQWMsR0FBRyxTQUNoQ2hCLE9BQU8sRUFDUEMsY0FBYyxFQUNkNUIsU0FBUztJQUVULElBQUk2QixjQUFjLElBQUksQ0FBQ1IsWUFBWTtJQUNuQyxJQUFJUyxjQUFjRCxjQUFjO0lBQ2hDLElBQUlNLFdBQVcsSUFBSSxDQUFDQyxTQUFTO0lBRTdCLElBQUssSUFBSUwsSUFBSSxHQUFHQSxJQUFJL0IsV0FBVytCLElBQUs7UUFDbEMsSUFBSUMsVUFBVUwsT0FBTyxDQUFDLElBQUlJLEVBQUU7UUFDNUIsSUFBSWEsT0FBT1QsV0FBV0EsUUFBUSxDQUFDSixFQUFFLEdBQUc7UUFDcEMsSUFBSWMsU0FBU2QsSUFBSUQsY0FBY0YsY0FBYyxDQUFDRyxJQUFJRixZQUFZLEdBQUc7UUFDakUsSUFBSWlCLFdBQVdmLElBQUlELGVBQWVLLFdBQVdBLFFBQVEsQ0FBQ0osSUFBSUYsWUFBWSxHQUFHO1FBQ3pFLElBQUlrQixRQUFRcEQsZUFBZWtELFFBQVFELE1BQU1FO1FBQ3pDbEIsY0FBYyxDQUFDRyxFQUFFLEdBQUdDLFVBQVVlO0lBQ2hDO0FBQ0Y7QUFFQTVDLE9BQU9tQixTQUFTLENBQUNFLGtCQUFrQixHQUFHLFNBQVVHLE9BQU87SUFDckQsSUFBSXFCLFNBQVNyQixPQUFPLENBQUMsRUFBRTtJQUN2QixJQUFJQztJQUNKLElBQUlxQixlQUFlLElBQUksQ0FBQ25DLE9BQU8sQ0FBQyxJQUFJLENBQUNELFdBQVcsQ0FBQztJQUNqRCxJQUFJYixZQUFZaUQsYUFBYWpELFNBQVM7SUFFdEMsSUFBSWdELFdBQVcsR0FBRztRQUNoQnBCLGlCQUFpQkQsUUFBUXVCLEtBQUssQ0FBQyxHQUFHbEQsWUFBWTtJQUNoRCxPQUFPO1FBQ0w0QixpQkFBaUJ1QixPQUFPQyxLQUFLLENBQUNwRDtRQUU5QixPQUFRZ0Q7WUFDTixLQUFLO2dCQUNILElBQUksQ0FBQ3RCLGNBQWMsQ0FBQ0MsU0FBU0MsZ0JBQWdCNUI7Z0JBQzdDO1lBQ0YsS0FBSztnQkFDSCxJQUFJLENBQUNrQyxjQUFjLENBQUNQLFNBQVNDLGdCQUFnQjVCO2dCQUM3QztZQUNGLEtBQUs7Z0JBQ0gsSUFBSSxDQUFDc0MsY0FBYyxDQUFDWCxTQUFTQyxnQkFBZ0I1QjtnQkFDN0M7WUFDRixLQUFLO2dCQUNILElBQUksQ0FBQzJDLGNBQWMsQ0FBQ2hCLFNBQVNDLGdCQUFnQjVCO2dCQUM3QztZQUNGO2dCQUNFLE1BQU0sSUFBSXFELE1BQU0sZ0NBQWdDTDtRQUNwRDtJQUNGO0lBRUEsSUFBSSxDQUFDckMsS0FBSyxDQUFDaUI7SUFFWHFCLGFBQWE3QixTQUFTO0lBQ3RCLElBQUk2QixhQUFhN0IsU0FBUyxJQUFJNkIsYUFBYXpDLE1BQU0sRUFBRTtRQUNqRCxJQUFJLENBQUM0QixTQUFTLEdBQUc7UUFDakIsSUFBSSxDQUFDdkIsV0FBVztRQUNoQm9DLGVBQWUsSUFBSSxDQUFDbkMsT0FBTyxDQUFDLElBQUksQ0FBQ0QsV0FBVyxDQUFDO0lBQy9DLE9BQU87UUFDTCxJQUFJLENBQUN1QixTQUFTLEdBQUdSO0lBQ25CO0lBRUEsSUFBSXFCLGNBQWM7UUFDaEIsb0VBQW9FO1FBQ3BFLElBQUksQ0FBQ3ZDLElBQUksQ0FBQ3VDLGFBQWFqRCxTQUFTLEdBQUcsR0FBRyxJQUFJLENBQUN3QixrQkFBa0IsQ0FBQ0MsSUFBSSxDQUFDLElBQUk7SUFDekUsT0FBTztRQUNMLElBQUksQ0FBQ1csU0FBUyxHQUFHO1FBQ2pCLElBQUksQ0FBQ3hCLFFBQVE7SUFDZjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS5qcz84N2MxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5sZXQgaW50ZXJsYWNlVXRpbHMgPSByZXF1aXJlKFwiLi9pbnRlcmxhY2VcIik7XG5sZXQgcGFldGhQcmVkaWN0b3IgPSByZXF1aXJlKFwiLi9wYWV0aC1wcmVkaWN0b3JcIik7XG5cbmZ1bmN0aW9uIGdldEJ5dGVXaWR0aCh3aWR0aCwgYnBwLCBkZXB0aCkge1xuICBsZXQgYnl0ZVdpZHRoID0gd2lkdGggKiBicHA7XG4gIGlmIChkZXB0aCAhPT0gOCkge1xuICAgIGJ5dGVXaWR0aCA9IE1hdGguY2VpbChieXRlV2lkdGggLyAoOCAvIGRlcHRoKSk7XG4gIH1cbiAgcmV0dXJuIGJ5dGVXaWR0aDtcbn1cblxubGV0IEZpbHRlciA9IChtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIChiaXRtYXBJbmZvLCBkZXBlbmRlbmNpZXMpIHtcbiAgbGV0IHdpZHRoID0gYml0bWFwSW5mby53aWR0aDtcbiAgbGV0IGhlaWdodCA9IGJpdG1hcEluZm8uaGVpZ2h0O1xuICBsZXQgaW50ZXJsYWNlID0gYml0bWFwSW5mby5pbnRlcmxhY2U7XG4gIGxldCBicHAgPSBiaXRtYXBJbmZvLmJwcDtcbiAgbGV0IGRlcHRoID0gYml0bWFwSW5mby5kZXB0aDtcblxuICB0aGlzLnJlYWQgPSBkZXBlbmRlbmNpZXMucmVhZDtcbiAgdGhpcy53cml0ZSA9IGRlcGVuZGVuY2llcy53cml0ZTtcbiAgdGhpcy5jb21wbGV0ZSA9IGRlcGVuZGVuY2llcy5jb21wbGV0ZTtcblxuICB0aGlzLl9pbWFnZUluZGV4ID0gMDtcbiAgdGhpcy5faW1hZ2VzID0gW107XG4gIGlmIChpbnRlcmxhY2UpIHtcbiAgICBsZXQgcGFzc2VzID0gaW50ZXJsYWNlVXRpbHMuZ2V0SW1hZ2VQYXNzZXMod2lkdGgsIGhlaWdodCk7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwYXNzZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIHRoaXMuX2ltYWdlcy5wdXNoKHtcbiAgICAgICAgYnl0ZVdpZHRoOiBnZXRCeXRlV2lkdGgocGFzc2VzW2ldLndpZHRoLCBicHAsIGRlcHRoKSxcbiAgICAgICAgaGVpZ2h0OiBwYXNzZXNbaV0uaGVpZ2h0LFxuICAgICAgICBsaW5lSW5kZXg6IDAsXG4gICAgICB9KTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgdGhpcy5faW1hZ2VzLnB1c2goe1xuICAgICAgYnl0ZVdpZHRoOiBnZXRCeXRlV2lkdGgod2lkdGgsIGJwcCwgZGVwdGgpLFxuICAgICAgaGVpZ2h0OiBoZWlnaHQsXG4gICAgICBsaW5lSW5kZXg6IDAsXG4gICAgfSk7XG4gIH1cblxuICAvLyB3aGVuIGZpbHRlcmluZyB0aGUgbGluZSB3ZSBsb29rIGF0IHRoZSBwaXhlbCB0byB0aGUgbGVmdFxuICAvLyB0aGUgc3BlYyBhbHNvIHNheXMgaXQgaXMgZG9uZSBvbiBhIGJ5dGUgbGV2ZWwgcmVnYXJkbGVzcyBvZiB0aGUgbnVtYmVyIG9mIHBpeGVsc1xuICAvLyBzbyBpZiB0aGUgZGVwdGggaXMgYnl0ZSBjb21wYXRpYmxlICg4IG9yIDE2KSB3ZSBzdWJ0cmFjdCB0aGUgYnBwIGluIG9yZGVyIHRvIGNvbXBhcmUgYmFja1xuICAvLyBhIHBpeGVsIHJhdGhlciB0aGFuIGp1c3QgYSBkaWZmZXJlbnQgYnl0ZSBwYXJ0LiBIb3dldmVyIGlmIHdlIGFyZSBzdWIgYnl0ZSwgd2UgaWdub3JlLlxuICBpZiAoZGVwdGggPT09IDgpIHtcbiAgICB0aGlzLl94Q29tcGFyaXNvbiA9IGJwcDtcbiAgfSBlbHNlIGlmIChkZXB0aCA9PT0gMTYpIHtcbiAgICB0aGlzLl94Q29tcGFyaXNvbiA9IGJwcCAqIDI7XG4gIH0gZWxzZSB7XG4gICAgdGhpcy5feENvbXBhcmlzb24gPSAxO1xuICB9XG59KTtcblxuRmlsdGVyLnByb3RvdHlwZS5zdGFydCA9IGZ1bmN0aW9uICgpIHtcbiAgdGhpcy5yZWFkKFxuICAgIHRoaXMuX2ltYWdlc1t0aGlzLl9pbWFnZUluZGV4XS5ieXRlV2lkdGggKyAxLFxuICAgIHRoaXMuX3JldmVyc2VGaWx0ZXJMaW5lLmJpbmQodGhpcylcbiAgKTtcbn07XG5cbkZpbHRlci5wcm90b3R5cGUuX3VuRmlsdGVyVHlwZTEgPSBmdW5jdGlvbiAoXG4gIHJhd0RhdGEsXG4gIHVuZmlsdGVyZWRMaW5lLFxuICBieXRlV2lkdGhcbikge1xuICBsZXQgeENvbXBhcmlzb24gPSB0aGlzLl94Q29tcGFyaXNvbjtcbiAgbGV0IHhCaWdnZXJUaGFuID0geENvbXBhcmlzb24gLSAxO1xuXG4gIGZvciAobGV0IHggPSAwOyB4IDwgYnl0ZVdpZHRoOyB4KyspIHtcbiAgICBsZXQgcmF3Qnl0ZSA9IHJhd0RhdGFbMSArIHhdO1xuICAgIGxldCBmMUxlZnQgPSB4ID4geEJpZ2dlclRoYW4gPyB1bmZpbHRlcmVkTGluZVt4IC0geENvbXBhcmlzb25dIDogMDtcbiAgICB1bmZpbHRlcmVkTGluZVt4XSA9IHJhd0J5dGUgKyBmMUxlZnQ7XG4gIH1cbn07XG5cbkZpbHRlci5wcm90b3R5cGUuX3VuRmlsdGVyVHlwZTIgPSBmdW5jdGlvbiAoXG4gIHJhd0RhdGEsXG4gIHVuZmlsdGVyZWRMaW5lLFxuICBieXRlV2lkdGhcbikge1xuICBsZXQgbGFzdExpbmUgPSB0aGlzLl9sYXN0TGluZTtcblxuICBmb3IgKGxldCB4ID0gMDsgeCA8IGJ5dGVXaWR0aDsgeCsrKSB7XG4gICAgbGV0IHJhd0J5dGUgPSByYXdEYXRhWzEgKyB4XTtcbiAgICBsZXQgZjJVcCA9IGxhc3RMaW5lID8gbGFzdExpbmVbeF0gOiAwO1xuICAgIHVuZmlsdGVyZWRMaW5lW3hdID0gcmF3Qnl0ZSArIGYyVXA7XG4gIH1cbn07XG5cbkZpbHRlci5wcm90b3R5cGUuX3VuRmlsdGVyVHlwZTMgPSBmdW5jdGlvbiAoXG4gIHJhd0RhdGEsXG4gIHVuZmlsdGVyZWRMaW5lLFxuICBieXRlV2lkdGhcbikge1xuICBsZXQgeENvbXBhcmlzb24gPSB0aGlzLl94Q29tcGFyaXNvbjtcbiAgbGV0IHhCaWdnZXJUaGFuID0geENvbXBhcmlzb24gLSAxO1xuICBsZXQgbGFzdExpbmUgPSB0aGlzLl9sYXN0TGluZTtcblxuICBmb3IgKGxldCB4ID0gMDsgeCA8IGJ5dGVXaWR0aDsgeCsrKSB7XG4gICAgbGV0IHJhd0J5dGUgPSByYXdEYXRhWzEgKyB4XTtcbiAgICBsZXQgZjNVcCA9IGxhc3RMaW5lID8gbGFzdExpbmVbeF0gOiAwO1xuICAgIGxldCBmM0xlZnQgPSB4ID4geEJpZ2dlclRoYW4gPyB1bmZpbHRlcmVkTGluZVt4IC0geENvbXBhcmlzb25dIDogMDtcbiAgICBsZXQgZjNBZGQgPSBNYXRoLmZsb29yKChmM0xlZnQgKyBmM1VwKSAvIDIpO1xuICAgIHVuZmlsdGVyZWRMaW5lW3hdID0gcmF3Qnl0ZSArIGYzQWRkO1xuICB9XG59O1xuXG5GaWx0ZXIucHJvdG90eXBlLl91bkZpbHRlclR5cGU0ID0gZnVuY3Rpb24gKFxuICByYXdEYXRhLFxuICB1bmZpbHRlcmVkTGluZSxcbiAgYnl0ZVdpZHRoXG4pIHtcbiAgbGV0IHhDb21wYXJpc29uID0gdGhpcy5feENvbXBhcmlzb247XG4gIGxldCB4QmlnZ2VyVGhhbiA9IHhDb21wYXJpc29uIC0gMTtcbiAgbGV0IGxhc3RMaW5lID0gdGhpcy5fbGFzdExpbmU7XG5cbiAgZm9yIChsZXQgeCA9IDA7IHggPCBieXRlV2lkdGg7IHgrKykge1xuICAgIGxldCByYXdCeXRlID0gcmF3RGF0YVsxICsgeF07XG4gICAgbGV0IGY0VXAgPSBsYXN0TGluZSA/IGxhc3RMaW5lW3hdIDogMDtcbiAgICBsZXQgZjRMZWZ0ID0geCA+IHhCaWdnZXJUaGFuID8gdW5maWx0ZXJlZExpbmVbeCAtIHhDb21wYXJpc29uXSA6IDA7XG4gICAgbGV0IGY0VXBMZWZ0ID0geCA+IHhCaWdnZXJUaGFuICYmIGxhc3RMaW5lID8gbGFzdExpbmVbeCAtIHhDb21wYXJpc29uXSA6IDA7XG4gICAgbGV0IGY0QWRkID0gcGFldGhQcmVkaWN0b3IoZjRMZWZ0LCBmNFVwLCBmNFVwTGVmdCk7XG4gICAgdW5maWx0ZXJlZExpbmVbeF0gPSByYXdCeXRlICsgZjRBZGQ7XG4gIH1cbn07XG5cbkZpbHRlci5wcm90b3R5cGUuX3JldmVyc2VGaWx0ZXJMaW5lID0gZnVuY3Rpb24gKHJhd0RhdGEpIHtcbiAgbGV0IGZpbHRlciA9IHJhd0RhdGFbMF07XG4gIGxldCB1bmZpbHRlcmVkTGluZTtcbiAgbGV0IGN1cnJlbnRJbWFnZSA9IHRoaXMuX2ltYWdlc1t0aGlzLl9pbWFnZUluZGV4XTtcbiAgbGV0IGJ5dGVXaWR0aCA9IGN1cnJlbnRJbWFnZS5ieXRlV2lkdGg7XG5cbiAgaWYgKGZpbHRlciA9PT0gMCkge1xuICAgIHVuZmlsdGVyZWRMaW5lID0gcmF3RGF0YS5zbGljZSgxLCBieXRlV2lkdGggKyAxKTtcbiAgfSBlbHNlIHtcbiAgICB1bmZpbHRlcmVkTGluZSA9IEJ1ZmZlci5hbGxvYyhieXRlV2lkdGgpO1xuXG4gICAgc3dpdGNoIChmaWx0ZXIpIHtcbiAgICAgIGNhc2UgMTpcbiAgICAgICAgdGhpcy5fdW5GaWx0ZXJUeXBlMShyYXdEYXRhLCB1bmZpbHRlcmVkTGluZSwgYnl0ZVdpZHRoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDI6XG4gICAgICAgIHRoaXMuX3VuRmlsdGVyVHlwZTIocmF3RGF0YSwgdW5maWx0ZXJlZExpbmUsIGJ5dGVXaWR0aCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAzOlxuICAgICAgICB0aGlzLl91bkZpbHRlclR5cGUzKHJhd0RhdGEsIHVuZmlsdGVyZWRMaW5lLCBieXRlV2lkdGgpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgNDpcbiAgICAgICAgdGhpcy5fdW5GaWx0ZXJUeXBlNChyYXdEYXRhLCB1bmZpbHRlcmVkTGluZSwgYnl0ZVdpZHRoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJVbnJlY29nbmlzZWQgZmlsdGVyIHR5cGUgLSBcIiArIGZpbHRlcik7XG4gICAgfVxuICB9XG5cbiAgdGhpcy53cml0ZSh1bmZpbHRlcmVkTGluZSk7XG5cbiAgY3VycmVudEltYWdlLmxpbmVJbmRleCsrO1xuICBpZiAoY3VycmVudEltYWdlLmxpbmVJbmRleCA+PSBjdXJyZW50SW1hZ2UuaGVpZ2h0KSB7XG4gICAgdGhpcy5fbGFzdExpbmUgPSBudWxsO1xuICAgIHRoaXMuX2ltYWdlSW5kZXgrKztcbiAgICBjdXJyZW50SW1hZ2UgPSB0aGlzLl9pbWFnZXNbdGhpcy5faW1hZ2VJbmRleF07XG4gIH0gZWxzZSB7XG4gICAgdGhpcy5fbGFzdExpbmUgPSB1bmZpbHRlcmVkTGluZTtcbiAgfVxuXG4gIGlmIChjdXJyZW50SW1hZ2UpIHtcbiAgICAvLyByZWFkLCB1c2luZyB0aGUgYnl0ZSB3aWR0aCB0aGF0IG1heSBiZSBmcm9tIHRoZSBuZXcgY3VycmVudCBpbWFnZVxuICAgIHRoaXMucmVhZChjdXJyZW50SW1hZ2UuYnl0ZVdpZHRoICsgMSwgdGhpcy5fcmV2ZXJzZUZpbHRlckxpbmUuYmluZCh0aGlzKSk7XG4gIH0gZWxzZSB7XG4gICAgdGhpcy5fbGFzdExpbmUgPSBudWxsO1xuICAgIHRoaXMuY29tcGxldGUoKTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6WyJpbnRlcmxhY2VVdGlscyIsInJlcXVpcmUiLCJwYWV0aFByZWRpY3RvciIsImdldEJ5dGVXaWR0aCIsIndpZHRoIiwiYnBwIiwiZGVwdGgiLCJieXRlV2lkdGgiLCJNYXRoIiwiY2VpbCIsIkZpbHRlciIsIm1vZHVsZSIsImV4cG9ydHMiLCJiaXRtYXBJbmZvIiwiZGVwZW5kZW5jaWVzIiwiaGVpZ2h0IiwiaW50ZXJsYWNlIiwicmVhZCIsIndyaXRlIiwiY29tcGxldGUiLCJfaW1hZ2VJbmRleCIsIl9pbWFnZXMiLCJwYXNzZXMiLCJnZXRJbWFnZVBhc3NlcyIsImkiLCJsZW5ndGgiLCJwdXNoIiwibGluZUluZGV4IiwiX3hDb21wYXJpc29uIiwicHJvdG90eXBlIiwic3RhcnQiLCJfcmV2ZXJzZUZpbHRlckxpbmUiLCJiaW5kIiwiX3VuRmlsdGVyVHlwZTEiLCJyYXdEYXRhIiwidW5maWx0ZXJlZExpbmUiLCJ4Q29tcGFyaXNvbiIsInhCaWdnZXJUaGFuIiwieCIsInJhd0J5dGUiLCJmMUxlZnQiLCJfdW5GaWx0ZXJUeXBlMiIsImxhc3RMaW5lIiwiX2xhc3RMaW5lIiwiZjJVcCIsIl91bkZpbHRlclR5cGUzIiwiZjNVcCIsImYzTGVmdCIsImYzQWRkIiwiZmxvb3IiLCJfdW5GaWx0ZXJUeXBlNCIsImY0VXAiLCJmNExlZnQiLCJmNFVwTGVmdCIsImY0QWRkIiwiZmlsdGVyIiwiY3VycmVudEltYWdlIiwic2xpY2UiLCJCdWZmZXIiLCJhbGxvYyIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/filter-parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/format-normaliser.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/format-normaliser.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("\nfunction dePalette(indata, outdata, width, height, palette) {\n    let pxPos = 0;\n    // use values from palette\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            let color = palette[indata[pxPos]];\n            if (!color) {\n                throw new Error(\"index \" + indata[pxPos] + \" not in palette\");\n            }\n            for(let i = 0; i < 4; i++){\n                outdata[pxPos + i] = color[i];\n            }\n            pxPos += 4;\n        }\n    }\n}\nfunction replaceTransparentColor(indata, outdata, width, height, transColor) {\n    let pxPos = 0;\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            let makeTrans = false;\n            if (transColor.length === 1) {\n                if (transColor[0] === indata[pxPos]) {\n                    makeTrans = true;\n                }\n            } else if (transColor[0] === indata[pxPos] && transColor[1] === indata[pxPos + 1] && transColor[2] === indata[pxPos + 2]) {\n                makeTrans = true;\n            }\n            if (makeTrans) {\n                for(let i = 0; i < 4; i++){\n                    outdata[pxPos + i] = 0;\n                }\n            }\n            pxPos += 4;\n        }\n    }\n}\nfunction scaleDepth(indata, outdata, width, height, depth) {\n    let maxOutSample = 255;\n    let maxInSample = Math.pow(2, depth) - 1;\n    let pxPos = 0;\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            for(let i = 0; i < 4; i++){\n                outdata[pxPos + i] = Math.floor(indata[pxPos + i] * maxOutSample / maxInSample + 0.5);\n            }\n            pxPos += 4;\n        }\n    }\n}\nmodule.exports = function(indata, imageData, skipRescale = false) {\n    let depth = imageData.depth;\n    let width = imageData.width;\n    let height = imageData.height;\n    let colorType = imageData.colorType;\n    let transColor = imageData.transColor;\n    let palette = imageData.palette;\n    let outdata = indata; // only different for 16 bits\n    if (colorType === 3) {\n        // paletted\n        dePalette(indata, outdata, width, height, palette);\n    } else {\n        if (transColor) {\n            replaceTransparentColor(indata, outdata, width, height, transColor);\n        }\n        // if it needs scaling\n        if (depth !== 8 && !skipRescale) {\n            // if we need to change the buffer size\n            if (depth === 16) {\n                outdata = Buffer.alloc(width * height * 4);\n            }\n            scaleDepth(indata, outdata, width, height, depth);\n        }\n    }\n    return outdata;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/format-normaliser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/interlace.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/interlace.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Adam 7\n//   0 1 2 3 4 5 6 7\n// 0 x 6 4 6 x 6 4 6\n// 1 7 7 7 7 7 7 7 7\n// 2 5 6 5 6 5 6 5 6\n// 3 7 7 7 7 7 7 7 7\n// 4 3 6 4 6 3 6 4 6\n// 5 7 7 7 7 7 7 7 7\n// 6 5 6 5 6 5 6 5 6\n// 7 7 7 7 7 7 7 7 7\nlet imagePasses = [\n    {\n        // pass 1 - 1px\n        x: [\n            0\n        ],\n        y: [\n            0\n        ]\n    },\n    {\n        // pass 2 - 1px\n        x: [\n            4\n        ],\n        y: [\n            0\n        ]\n    },\n    {\n        // pass 3 - 2px\n        x: [\n            0,\n            4\n        ],\n        y: [\n            4\n        ]\n    },\n    {\n        // pass 4 - 4px\n        x: [\n            2,\n            6\n        ],\n        y: [\n            0,\n            4\n        ]\n    },\n    {\n        // pass 5 - 8px\n        x: [\n            0,\n            2,\n            4,\n            6\n        ],\n        y: [\n            2,\n            6\n        ]\n    },\n    {\n        // pass 6 - 16px\n        x: [\n            1,\n            3,\n            5,\n            7\n        ],\n        y: [\n            0,\n            2,\n            4,\n            6\n        ]\n    },\n    {\n        // pass 7 - 32px\n        x: [\n            0,\n            1,\n            2,\n            3,\n            4,\n            5,\n            6,\n            7\n        ],\n        y: [\n            1,\n            3,\n            5,\n            7\n        ]\n    }\n];\nexports.getImagePasses = function(width, height) {\n    let images = [];\n    let xLeftOver = width % 8;\n    let yLeftOver = height % 8;\n    let xRepeats = (width - xLeftOver) / 8;\n    let yRepeats = (height - yLeftOver) / 8;\n    for(let i = 0; i < imagePasses.length; i++){\n        let pass = imagePasses[i];\n        let passWidth = xRepeats * pass.x.length;\n        let passHeight = yRepeats * pass.y.length;\n        for(let j = 0; j < pass.x.length; j++){\n            if (pass.x[j] < xLeftOver) {\n                passWidth++;\n            } else {\n                break;\n            }\n        }\n        for(let j = 0; j < pass.y.length; j++){\n            if (pass.y[j] < yLeftOver) {\n                passHeight++;\n            } else {\n                break;\n            }\n        }\n        if (passWidth > 0 && passHeight > 0) {\n            images.push({\n                width: passWidth,\n                height: passHeight,\n                index: i\n            });\n        }\n    }\n    return images;\n};\nexports.getInterlaceIterator = function(width) {\n    return function(x, y, pass) {\n        let outerXLeftOver = x % imagePasses[pass].x.length;\n        let outerX = (x - outerXLeftOver) / imagePasses[pass].x.length * 8 + imagePasses[pass].x[outerXLeftOver];\n        let outerYLeftOver = y % imagePasses[pass].y.length;\n        let outerY = (y - outerYLeftOver) / imagePasses[pass].y.length * 8 + imagePasses[pass].y[outerYLeftOver];\n        return outerX * 4 + outerY * width * 4;\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/interlace.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/packer-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/packer-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet constants = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(rsc)/./node_modules/pngjs/lib/packer.js\");\nlet PackerAsync = module.exports = function(opt) {\n    Stream.call(this);\n    let options = opt || {};\n    this._packer = new Packer(options);\n    this._deflate = this._packer.createDeflate();\n    this.readable = true;\n};\nutil.inherits(PackerAsync, Stream);\nPackerAsync.prototype.pack = function(data, width, height, gamma) {\n    // Signature\n    this.emit(\"data\", Buffer.from(constants.PNG_SIGNATURE));\n    this.emit(\"data\", this._packer.packIHDR(width, height));\n    if (gamma) {\n        this.emit(\"data\", this._packer.packGAMA(gamma));\n    }\n    let filteredData = this._packer.filterData(data, width, height);\n    // compress it\n    this._deflate.on(\"error\", this.emit.bind(this, \"error\"));\n    this._deflate.on(\"data\", (function(compressedData) {\n        this.emit(\"data\", this._packer.packIDAT(compressedData));\n    }).bind(this));\n    this._deflate.on(\"end\", (function() {\n        this.emit(\"data\", this._packer.packIEND());\n        this.emit(\"end\");\n    }).bind(this));\n    this._deflate.end(filteredData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/packer-async.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/packer-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/packer-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nif (!zlib.deflateSync) {\n    hasSyncZlib = false;\n}\nlet constants = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(rsc)/./node_modules/pngjs/lib/packer.js\");\nmodule.exports = function(metaData, opt) {\n    if (!hasSyncZlib) {\n        throw new Error(\"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\");\n    }\n    let options = opt || {};\n    let packer = new Packer(options);\n    let chunks = [];\n    // Signature\n    chunks.push(Buffer.from(constants.PNG_SIGNATURE));\n    // Header\n    chunks.push(packer.packIHDR(metaData.width, metaData.height));\n    if (metaData.gamma) {\n        chunks.push(packer.packGAMA(metaData.gamma));\n    }\n    let filteredData = packer.filterData(metaData.data, metaData.width, metaData.height);\n    // compress it\n    let compressedData = zlib.deflateSync(filteredData, packer.getDeflateOptions());\n    filteredData = null;\n    if (!compressedData || !compressedData.length) {\n        throw new Error(\"bad png - invalid compressed data response\");\n    }\n    chunks.push(packer.packIDAT(compressedData));\n    // End\n    chunks.push(packer.packIEND());\n    return Buffer.concat(chunks);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/packer-sync.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/packer.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/packer.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet constants = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pngjs/lib/constants.js\");\nlet CrcStream = __webpack_require__(/*! ./crc */ \"(rsc)/./node_modules/pngjs/lib/crc.js\");\nlet bitPacker = __webpack_require__(/*! ./bitpacker */ \"(rsc)/./node_modules/pngjs/lib/bitpacker.js\");\nlet filter = __webpack_require__(/*! ./filter-pack */ \"(rsc)/./node_modules/pngjs/lib/filter-pack.js\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet Packer = module.exports = function(options) {\n    this._options = options;\n    options.deflateChunkSize = options.deflateChunkSize || 32 * 1024;\n    options.deflateLevel = options.deflateLevel != null ? options.deflateLevel : 9;\n    options.deflateStrategy = options.deflateStrategy != null ? options.deflateStrategy : 3;\n    options.inputHasAlpha = options.inputHasAlpha != null ? options.inputHasAlpha : true;\n    options.deflateFactory = options.deflateFactory || zlib.createDeflate;\n    options.bitDepth = options.bitDepth || 8;\n    // This is outputColorType\n    options.colorType = typeof options.colorType === \"number\" ? options.colorType : constants.COLORTYPE_COLOR_ALPHA;\n    options.inputColorType = typeof options.inputColorType === \"number\" ? options.inputColorType : constants.COLORTYPE_COLOR_ALPHA;\n    if ([\n        constants.COLORTYPE_GRAYSCALE,\n        constants.COLORTYPE_COLOR,\n        constants.COLORTYPE_COLOR_ALPHA,\n        constants.COLORTYPE_ALPHA\n    ].indexOf(options.colorType) === -1) {\n        throw new Error(\"option color type:\" + options.colorType + \" is not supported at present\");\n    }\n    if ([\n        constants.COLORTYPE_GRAYSCALE,\n        constants.COLORTYPE_COLOR,\n        constants.COLORTYPE_COLOR_ALPHA,\n        constants.COLORTYPE_ALPHA\n    ].indexOf(options.inputColorType) === -1) {\n        throw new Error(\"option input color type:\" + options.inputColorType + \" is not supported at present\");\n    }\n    if (options.bitDepth !== 8 && options.bitDepth !== 16) {\n        throw new Error(\"option bit depth:\" + options.bitDepth + \" is not supported at present\");\n    }\n};\nPacker.prototype.getDeflateOptions = function() {\n    return {\n        chunkSize: this._options.deflateChunkSize,\n        level: this._options.deflateLevel,\n        strategy: this._options.deflateStrategy\n    };\n};\nPacker.prototype.createDeflate = function() {\n    return this._options.deflateFactory(this.getDeflateOptions());\n};\nPacker.prototype.filterData = function(data, width, height) {\n    // convert to correct format for filtering (e.g. right bpp and bit depth)\n    let packedData = bitPacker(data, width, height, this._options);\n    // filter pixel data\n    let bpp = constants.COLORTYPE_TO_BPP_MAP[this._options.colorType];\n    let filteredData = filter(packedData, width, height, this._options, bpp);\n    return filteredData;\n};\nPacker.prototype._packChunk = function(type, data) {\n    let len = data ? data.length : 0;\n    let buf = Buffer.alloc(len + 12);\n    buf.writeUInt32BE(len, 0);\n    buf.writeUInt32BE(type, 4);\n    if (data) {\n        data.copy(buf, 8);\n    }\n    buf.writeInt32BE(CrcStream.crc32(buf.slice(4, buf.length - 4)), buf.length - 4);\n    return buf;\n};\nPacker.prototype.packGAMA = function(gamma) {\n    let buf = Buffer.alloc(4);\n    buf.writeUInt32BE(Math.floor(gamma * constants.GAMMA_DIVISION), 0);\n    return this._packChunk(constants.TYPE_gAMA, buf);\n};\nPacker.prototype.packIHDR = function(width, height) {\n    let buf = Buffer.alloc(13);\n    buf.writeUInt32BE(width, 0);\n    buf.writeUInt32BE(height, 4);\n    buf[8] = this._options.bitDepth; // Bit depth\n    buf[9] = this._options.colorType; // colorType\n    buf[10] = 0; // compression\n    buf[11] = 0; // filter\n    buf[12] = 0; // interlace\n    return this._packChunk(constants.TYPE_IHDR, buf);\n};\nPacker.prototype.packIDAT = function(data) {\n    return this._packChunk(constants.TYPE_IDAT, data);\n};\nPacker.prototype.packIEND = function() {\n    return this._packChunk(constants.TYPE_IEND, null);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/packer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/paeth-predictor.js":
/*!***************************************************!*\
  !*** ./node_modules/pngjs/lib/paeth-predictor.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\nmodule.exports = function paethPredictor(left, above, upLeft) {\n    let paeth = left + above - upLeft;\n    let pLeft = Math.abs(paeth - left);\n    let pAbove = Math.abs(paeth - above);\n    let pUpLeft = Math.abs(paeth - upLeft);\n    if (pLeft <= pAbove && pLeft <= pUpLeft) {\n        return left;\n    }\n    if (pAbove <= pUpLeft) {\n        return above;\n    }\n    return upLeft;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BhZXRoLXByZWRpY3Rvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxPQUFPQyxPQUFPLEdBQUcsU0FBU0MsZUFBZUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLE1BQU07SUFDMUQsSUFBSUMsUUFBUUgsT0FBT0MsUUFBUUM7SUFDM0IsSUFBSUUsUUFBUUMsS0FBS0MsR0FBRyxDQUFDSCxRQUFRSDtJQUM3QixJQUFJTyxTQUFTRixLQUFLQyxHQUFHLENBQUNILFFBQVFGO0lBQzlCLElBQUlPLFVBQVVILEtBQUtDLEdBQUcsQ0FBQ0gsUUFBUUQ7SUFFL0IsSUFBSUUsU0FBU0csVUFBVUgsU0FBU0ksU0FBUztRQUN2QyxPQUFPUjtJQUNUO0lBQ0EsSUFBSU8sVUFBVUMsU0FBUztRQUNyQixPQUFPUDtJQUNUO0lBQ0EsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3hhdXVzZC10cmFkaW5nLWFuYWx5emVyLy4vbm9kZV9tb2R1bGVzL3BuZ2pzL2xpYi9wYWV0aC1wcmVkaWN0b3IuanM/ZjRjMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBwYWV0aFByZWRpY3RvcihsZWZ0LCBhYm92ZSwgdXBMZWZ0KSB7XG4gIGxldCBwYWV0aCA9IGxlZnQgKyBhYm92ZSAtIHVwTGVmdDtcbiAgbGV0IHBMZWZ0ID0gTWF0aC5hYnMocGFldGggLSBsZWZ0KTtcbiAgbGV0IHBBYm92ZSA9IE1hdGguYWJzKHBhZXRoIC0gYWJvdmUpO1xuICBsZXQgcFVwTGVmdCA9IE1hdGguYWJzKHBhZXRoIC0gdXBMZWZ0KTtcblxuICBpZiAocExlZnQgPD0gcEFib3ZlICYmIHBMZWZ0IDw9IHBVcExlZnQpIHtcbiAgICByZXR1cm4gbGVmdDtcbiAgfVxuICBpZiAocEFib3ZlIDw9IHBVcExlZnQpIHtcbiAgICByZXR1cm4gYWJvdmU7XG4gIH1cbiAgcmV0dXJuIHVwTGVmdDtcbn07XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInBhZXRoUHJlZGljdG9yIiwibGVmdCIsImFib3ZlIiwidXBMZWZ0IiwicGFldGgiLCJwTGVmdCIsIk1hdGgiLCJhYnMiLCJwQWJvdmUiLCJwVXBMZWZ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/paeth-predictor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/parser-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/parser-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(rsc)/./node_modules/pngjs/lib/chunkstream.js\");\nlet FilterAsync = __webpack_require__(/*! ./filter-parse-async */ \"(rsc)/./node_modules/pngjs/lib/filter-parse-async.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(rsc)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(rsc)/./node_modules/pngjs/lib/format-normaliser.js\");\nlet ParserAsync = module.exports = function(options) {\n    ChunkStream.call(this);\n    this._parser = new Parser(options, {\n        read: this.read.bind(this),\n        error: this._handleError.bind(this),\n        metadata: this._handleMetaData.bind(this),\n        gamma: this.emit.bind(this, \"gamma\"),\n        palette: this._handlePalette.bind(this),\n        transColor: this._handleTransColor.bind(this),\n        finished: this._finished.bind(this),\n        inflateData: this._inflateData.bind(this),\n        simpleTransparency: this._simpleTransparency.bind(this),\n        headersFinished: this._headersFinished.bind(this)\n    });\n    this._options = options;\n    this.writable = true;\n    this._parser.start();\n};\nutil.inherits(ParserAsync, ChunkStream);\nParserAsync.prototype._handleError = function(err) {\n    this.emit(\"error\", err);\n    this.writable = false;\n    this.destroy();\n    if (this._inflate && this._inflate.destroy) {\n        this._inflate.destroy();\n    }\n    if (this._filter) {\n        this._filter.destroy();\n        // For backward compatibility with Node 7 and below.\n        // Suppress errors due to _inflate calling write() even after\n        // it's destroy()'ed.\n        this._filter.on(\"error\", function() {});\n    }\n    this.errord = true;\n};\nParserAsync.prototype._inflateData = function(data) {\n    if (!this._inflate) {\n        if (this._bitmapInfo.interlace) {\n            this._inflate = zlib.createInflate();\n            this._inflate.on(\"error\", this.emit.bind(this, \"error\"));\n            this._filter.on(\"complete\", this._complete.bind(this));\n            this._inflate.pipe(this._filter);\n        } else {\n            let rowSize = (this._bitmapInfo.width * this._bitmapInfo.bpp * this._bitmapInfo.depth + 7 >> 3) + 1;\n            let imageSize = rowSize * this._bitmapInfo.height;\n            let chunkSize = Math.max(imageSize, zlib.Z_MIN_CHUNK);\n            this._inflate = zlib.createInflate({\n                chunkSize: chunkSize\n            });\n            let leftToInflate = imageSize;\n            let emitError = this.emit.bind(this, \"error\");\n            this._inflate.on(\"error\", function(err) {\n                if (!leftToInflate) {\n                    return;\n                }\n                emitError(err);\n            });\n            this._filter.on(\"complete\", this._complete.bind(this));\n            let filterWrite = this._filter.write.bind(this._filter);\n            this._inflate.on(\"data\", function(chunk) {\n                if (!leftToInflate) {\n                    return;\n                }\n                if (chunk.length > leftToInflate) {\n                    chunk = chunk.slice(0, leftToInflate);\n                }\n                leftToInflate -= chunk.length;\n                filterWrite(chunk);\n            });\n            this._inflate.on(\"end\", this._filter.end.bind(this._filter));\n        }\n    }\n    this._inflate.write(data);\n};\nParserAsync.prototype._handleMetaData = function(metaData) {\n    this._metaData = metaData;\n    this._bitmapInfo = Object.create(metaData);\n    this._filter = new FilterAsync(this._bitmapInfo);\n};\nParserAsync.prototype._handleTransColor = function(transColor) {\n    this._bitmapInfo.transColor = transColor;\n};\nParserAsync.prototype._handlePalette = function(palette) {\n    this._bitmapInfo.palette = palette;\n};\nParserAsync.prototype._simpleTransparency = function() {\n    this._metaData.alpha = true;\n};\nParserAsync.prototype._headersFinished = function() {\n    // Up until this point, we don't know if we have a tRNS chunk (alpha)\n    // so we can't emit metadata any earlier\n    this.emit(\"metadata\", this._metaData);\n};\nParserAsync.prototype._finished = function() {\n    if (this.errord) {\n        return;\n    }\n    if (!this._inflate) {\n        this.emit(\"error\", \"No Inflate block\");\n    } else {\n        // no more data to inflate\n        this._inflate.end();\n    }\n};\nParserAsync.prototype._complete = function(filteredData) {\n    if (this.errord) {\n        return;\n    }\n    let normalisedBitmapData;\n    try {\n        let bitmapData = bitmapper.dataToBitMap(filteredData, this._bitmapInfo);\n        normalisedBitmapData = formatNormaliser(bitmapData, this._bitmapInfo, this._options.skipRescale);\n        bitmapData = null;\n    } catch (ex) {\n        this._handleError(ex);\n        return;\n    }\n    this.emit(\"parsed\", normalisedBitmapData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/parser-async.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/parser-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/parser-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet inflateSync = __webpack_require__(/*! ./sync-inflate */ \"(rsc)/./node_modules/pngjs/lib/sync-inflate.js\");\nif (!zlib.deflateSync) {\n    hasSyncZlib = false;\n}\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(rsc)/./node_modules/pngjs/lib/sync-reader.js\");\nlet FilterSync = __webpack_require__(/*! ./filter-parse-sync */ \"(rsc)/./node_modules/pngjs/lib/filter-parse-sync.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(rsc)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(rsc)/./node_modules/pngjs/lib/format-normaliser.js\");\nmodule.exports = function(buffer, options) {\n    if (!hasSyncZlib) {\n        throw new Error(\"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\");\n    }\n    let err;\n    function handleError(_err_) {\n        err = _err_;\n    }\n    let metaData;\n    function handleMetaData(_metaData_) {\n        metaData = _metaData_;\n    }\n    function handleTransColor(transColor) {\n        metaData.transColor = transColor;\n    }\n    function handlePalette(palette) {\n        metaData.palette = palette;\n    }\n    function handleSimpleTransparency() {\n        metaData.alpha = true;\n    }\n    let gamma;\n    function handleGamma(_gamma_) {\n        gamma = _gamma_;\n    }\n    let inflateDataList = [];\n    function handleInflateData(inflatedData) {\n        inflateDataList.push(inflatedData);\n    }\n    let reader = new SyncReader(buffer);\n    let parser = new Parser(options, {\n        read: reader.read.bind(reader),\n        error: handleError,\n        metadata: handleMetaData,\n        gamma: handleGamma,\n        palette: handlePalette,\n        transColor: handleTransColor,\n        inflateData: handleInflateData,\n        simpleTransparency: handleSimpleTransparency\n    });\n    parser.start();\n    reader.process();\n    if (err) {\n        throw err;\n    }\n    //join together the inflate datas\n    let inflateData = Buffer.concat(inflateDataList);\n    inflateDataList.length = 0;\n    let inflatedData;\n    if (metaData.interlace) {\n        inflatedData = zlib.inflateSync(inflateData);\n    } else {\n        let rowSize = (metaData.width * metaData.bpp * metaData.depth + 7 >> 3) + 1;\n        let imageSize = rowSize * metaData.height;\n        inflatedData = inflateSync(inflateData, {\n            chunkSize: imageSize,\n            maxLength: imageSize\n        });\n    }\n    inflateData = null;\n    if (!inflatedData || !inflatedData.length) {\n        throw new Error(\"bad png - invalid inflate data response\");\n    }\n    let unfilteredData = FilterSync.process(inflatedData, metaData);\n    inflateData = null;\n    let bitmapData = bitmapper.dataToBitMap(unfilteredData, metaData);\n    unfilteredData = null;\n    let normalisedBitmapData = formatNormaliser(bitmapData, metaData, options.skipRescale);\n    metaData.data = normalisedBitmapData;\n    metaData.gamma = gamma || 0;\n    return metaData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/parser-sync.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/parser.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/parser.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet constants = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pngjs/lib/constants.js\");\nlet CrcCalculator = __webpack_require__(/*! ./crc */ \"(rsc)/./node_modules/pngjs/lib/crc.js\");\nlet Parser = module.exports = function(options, dependencies) {\n    this._options = options;\n    options.checkCRC = options.checkCRC !== false;\n    this._hasIHDR = false;\n    this._hasIEND = false;\n    this._emittedHeadersFinished = false;\n    // input flags/metadata\n    this._palette = [];\n    this._colorType = 0;\n    this._chunks = {};\n    this._chunks[constants.TYPE_IHDR] = this._handleIHDR.bind(this);\n    this._chunks[constants.TYPE_IEND] = this._handleIEND.bind(this);\n    this._chunks[constants.TYPE_IDAT] = this._handleIDAT.bind(this);\n    this._chunks[constants.TYPE_PLTE] = this._handlePLTE.bind(this);\n    this._chunks[constants.TYPE_tRNS] = this._handleTRNS.bind(this);\n    this._chunks[constants.TYPE_gAMA] = this._handleGAMA.bind(this);\n    this.read = dependencies.read;\n    this.error = dependencies.error;\n    this.metadata = dependencies.metadata;\n    this.gamma = dependencies.gamma;\n    this.transColor = dependencies.transColor;\n    this.palette = dependencies.palette;\n    this.parsed = dependencies.parsed;\n    this.inflateData = dependencies.inflateData;\n    this.finished = dependencies.finished;\n    this.simpleTransparency = dependencies.simpleTransparency;\n    this.headersFinished = dependencies.headersFinished || function() {};\n};\nParser.prototype.start = function() {\n    this.read(constants.PNG_SIGNATURE.length, this._parseSignature.bind(this));\n};\nParser.prototype._parseSignature = function(data) {\n    let signature = constants.PNG_SIGNATURE;\n    for(let i = 0; i < signature.length; i++){\n        if (data[i] !== signature[i]) {\n            this.error(new Error(\"Invalid file signature\"));\n            return;\n        }\n    }\n    this.read(8, this._parseChunkBegin.bind(this));\n};\nParser.prototype._parseChunkBegin = function(data) {\n    // chunk content length\n    let length = data.readUInt32BE(0);\n    // chunk type\n    let type = data.readUInt32BE(4);\n    let name = \"\";\n    for(let i = 4; i < 8; i++){\n        name += String.fromCharCode(data[i]);\n    }\n    //console.log('chunk ', name, length);\n    // chunk flags\n    let ancillary = Boolean(data[4] & 0x20); // or critical\n    //    priv = Boolean(data[5] & 0x20), // or public\n    //    safeToCopy = Boolean(data[7] & 0x20); // or unsafe\n    if (!this._hasIHDR && type !== constants.TYPE_IHDR) {\n        this.error(new Error(\"Expected IHDR on beggining\"));\n        return;\n    }\n    this._crc = new CrcCalculator();\n    this._crc.write(Buffer.from(name));\n    if (this._chunks[type]) {\n        return this._chunks[type](length);\n    }\n    if (!ancillary) {\n        this.error(new Error(\"Unsupported critical chunk type \" + name));\n        return;\n    }\n    this.read(length + 4, this._skipChunk.bind(this));\n};\nParser.prototype._skipChunk = function() {\n    this.read(8, this._parseChunkBegin.bind(this));\n};\nParser.prototype._handleChunkEnd = function() {\n    this.read(4, this._parseChunkEnd.bind(this));\n};\nParser.prototype._parseChunkEnd = function(data) {\n    let fileCrc = data.readInt32BE(0);\n    let calcCrc = this._crc.crc32();\n    // check CRC\n    if (this._options.checkCRC && calcCrc !== fileCrc) {\n        this.error(new Error(\"Crc error - \" + fileCrc + \" - \" + calcCrc));\n        return;\n    }\n    if (!this._hasIEND) {\n        this.read(8, this._parseChunkBegin.bind(this));\n    }\n};\nParser.prototype._handleIHDR = function(length) {\n    this.read(length, this._parseIHDR.bind(this));\n};\nParser.prototype._parseIHDR = function(data) {\n    this._crc.write(data);\n    let width = data.readUInt32BE(0);\n    let height = data.readUInt32BE(4);\n    let depth = data[8];\n    let colorType = data[9]; // bits: 1 palette, 2 color, 4 alpha\n    let compr = data[10];\n    let filter = data[11];\n    let interlace = data[12];\n    // console.log('    width', width, 'height', height,\n    //     'depth', depth, 'colorType', colorType,\n    //     'compr', compr, 'filter', filter, 'interlace', interlace\n    // );\n    if (depth !== 8 && depth !== 4 && depth !== 2 && depth !== 1 && depth !== 16) {\n        this.error(new Error(\"Unsupported bit depth \" + depth));\n        return;\n    }\n    if (!(colorType in constants.COLORTYPE_TO_BPP_MAP)) {\n        this.error(new Error(\"Unsupported color type\"));\n        return;\n    }\n    if (compr !== 0) {\n        this.error(new Error(\"Unsupported compression method\"));\n        return;\n    }\n    if (filter !== 0) {\n        this.error(new Error(\"Unsupported filter method\"));\n        return;\n    }\n    if (interlace !== 0 && interlace !== 1) {\n        this.error(new Error(\"Unsupported interlace method\"));\n        return;\n    }\n    this._colorType = colorType;\n    let bpp = constants.COLORTYPE_TO_BPP_MAP[this._colorType];\n    this._hasIHDR = true;\n    this.metadata({\n        width: width,\n        height: height,\n        depth: depth,\n        interlace: Boolean(interlace),\n        palette: Boolean(colorType & constants.COLORTYPE_PALETTE),\n        color: Boolean(colorType & constants.COLORTYPE_COLOR),\n        alpha: Boolean(colorType & constants.COLORTYPE_ALPHA),\n        bpp: bpp,\n        colorType: colorType\n    });\n    this._handleChunkEnd();\n};\nParser.prototype._handlePLTE = function(length) {\n    this.read(length, this._parsePLTE.bind(this));\n};\nParser.prototype._parsePLTE = function(data) {\n    this._crc.write(data);\n    let entries = Math.floor(data.length / 3);\n    // console.log('Palette:', entries);\n    for(let i = 0; i < entries; i++){\n        this._palette.push([\n            data[i * 3],\n            data[i * 3 + 1],\n            data[i * 3 + 2],\n            0xff\n        ]);\n    }\n    this.palette(this._palette);\n    this._handleChunkEnd();\n};\nParser.prototype._handleTRNS = function(length) {\n    this.simpleTransparency();\n    this.read(length, this._parseTRNS.bind(this));\n};\nParser.prototype._parseTRNS = function(data) {\n    this._crc.write(data);\n    // palette\n    if (this._colorType === constants.COLORTYPE_PALETTE_COLOR) {\n        if (this._palette.length === 0) {\n            this.error(new Error(\"Transparency chunk must be after palette\"));\n            return;\n        }\n        if (data.length > this._palette.length) {\n            this.error(new Error(\"More transparent colors than palette size\"));\n            return;\n        }\n        for(let i = 0; i < data.length; i++){\n            this._palette[i][3] = data[i];\n        }\n        this.palette(this._palette);\n    }\n    // for colorType 0 (grayscale) and 2 (rgb)\n    // there might be one gray/color defined as transparent\n    if (this._colorType === constants.COLORTYPE_GRAYSCALE) {\n        // grey, 2 bytes\n        this.transColor([\n            data.readUInt16BE(0)\n        ]);\n    }\n    if (this._colorType === constants.COLORTYPE_COLOR) {\n        this.transColor([\n            data.readUInt16BE(0),\n            data.readUInt16BE(2),\n            data.readUInt16BE(4)\n        ]);\n    }\n    this._handleChunkEnd();\n};\nParser.prototype._handleGAMA = function(length) {\n    this.read(length, this._parseGAMA.bind(this));\n};\nParser.prototype._parseGAMA = function(data) {\n    this._crc.write(data);\n    this.gamma(data.readUInt32BE(0) / constants.GAMMA_DIVISION);\n    this._handleChunkEnd();\n};\nParser.prototype._handleIDAT = function(length) {\n    if (!this._emittedHeadersFinished) {\n        this._emittedHeadersFinished = true;\n        this.headersFinished();\n    }\n    this.read(-length, this._parseIDAT.bind(this, length));\n};\nParser.prototype._parseIDAT = function(length, data) {\n    this._crc.write(data);\n    if (this._colorType === constants.COLORTYPE_PALETTE_COLOR && this._palette.length === 0) {\n        throw new Error(\"Expected palette not found\");\n    }\n    this.inflateData(data);\n    let leftOverLength = length - data.length;\n    if (leftOverLength > 0) {\n        this._handleIDAT(leftOverLength);\n    } else {\n        this._handleChunkEnd();\n    }\n};\nParser.prototype._handleIEND = function(length) {\n    this.read(length, this._parseIEND.bind(this));\n};\nParser.prototype._parseIEND = function(data) {\n    this._crc.write(data);\n    this._hasIEND = true;\n    this._handleChunkEnd();\n    if (this.finished) {\n        this.finished();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/png-sync.js":
/*!********************************************!*\
  !*** ./node_modules/pngjs/lib/png-sync.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet parse = __webpack_require__(/*! ./parser-sync */ \"(rsc)/./node_modules/pngjs/lib/parser-sync.js\");\nlet pack = __webpack_require__(/*! ./packer-sync */ \"(rsc)/./node_modules/pngjs/lib/packer-sync.js\");\nexports.read = function(buffer, options) {\n    return parse(buffer, options || {});\n};\nexports.write = function(png, options) {\n    return pack(png, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWIsSUFBSUEsUUFBUUMsbUJBQU9BLENBQUMsb0VBQWU7QUFDbkMsSUFBSUMsT0FBT0QsbUJBQU9BLENBQUMsb0VBQWU7QUFFbENFLFlBQVksR0FBRyxTQUFVRSxNQUFNLEVBQUVDLE9BQU87SUFDdEMsT0FBT04sTUFBTUssUUFBUUMsV0FBVyxDQUFDO0FBQ25DO0FBRUFILGFBQWEsR0FBRyxTQUFVSyxHQUFHLEVBQUVGLE9BQU87SUFDcEMsT0FBT0osS0FBS00sS0FBS0Y7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly94YXV1c2QtdHJhZGluZy1hbmFseXplci8uL25vZGVfbW9kdWxlcy9wbmdqcy9saWIvcG5nLXN5bmMuanM/OGFlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IHBhcnNlID0gcmVxdWlyZShcIi4vcGFyc2VyLXN5bmNcIik7XG5sZXQgcGFjayA9IHJlcXVpcmUoXCIuL3BhY2tlci1zeW5jXCIpO1xuXG5leHBvcnRzLnJlYWQgPSBmdW5jdGlvbiAoYnVmZmVyLCBvcHRpb25zKSB7XG4gIHJldHVybiBwYXJzZShidWZmZXIsIG9wdGlvbnMgfHwge30pO1xufTtcblxuZXhwb3J0cy53cml0ZSA9IGZ1bmN0aW9uIChwbmcsIG9wdGlvbnMpIHtcbiAgcmV0dXJuIHBhY2socG5nLCBvcHRpb25zKTtcbn07XG4iXSwibmFtZXMiOlsicGFyc2UiLCJyZXF1aXJlIiwicGFjayIsImV4cG9ydHMiLCJyZWFkIiwiYnVmZmVyIiwib3B0aW9ucyIsIndyaXRlIiwicG5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/png-sync.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/png.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/png.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet Parser = __webpack_require__(/*! ./parser-async */ \"(rsc)/./node_modules/pngjs/lib/parser-async.js\");\nlet Packer = __webpack_require__(/*! ./packer-async */ \"(rsc)/./node_modules/pngjs/lib/packer-async.js\");\nlet PNGSync = __webpack_require__(/*! ./png-sync */ \"(rsc)/./node_modules/pngjs/lib/png-sync.js\");\nlet PNG = exports.PNG = function(options) {\n    Stream.call(this);\n    options = options || {}; // eslint-disable-line no-param-reassign\n    // coerce pixel dimensions to integers (also coerces undefined -> 0):\n    this.width = options.width | 0;\n    this.height = options.height | 0;\n    this.data = this.width > 0 && this.height > 0 ? Buffer.alloc(4 * this.width * this.height) : null;\n    if (options.fill && this.data) {\n        this.data.fill(0);\n    }\n    this.gamma = 0;\n    this.readable = this.writable = true;\n    this._parser = new Parser(options);\n    this._parser.on(\"error\", this.emit.bind(this, \"error\"));\n    this._parser.on(\"close\", this._handleClose.bind(this));\n    this._parser.on(\"metadata\", this._metadata.bind(this));\n    this._parser.on(\"gamma\", this._gamma.bind(this));\n    this._parser.on(\"parsed\", (function(data) {\n        this.data = data;\n        this.emit(\"parsed\", data);\n    }).bind(this));\n    this._packer = new Packer(options);\n    this._packer.on(\"data\", this.emit.bind(this, \"data\"));\n    this._packer.on(\"end\", this.emit.bind(this, \"end\"));\n    this._parser.on(\"close\", this._handleClose.bind(this));\n    this._packer.on(\"error\", this.emit.bind(this, \"error\"));\n};\nutil.inherits(PNG, Stream);\nPNG.sync = PNGSync;\nPNG.prototype.pack = function() {\n    if (!this.data || !this.data.length) {\n        this.emit(\"error\", \"No data provided\");\n        return this;\n    }\n    process.nextTick((function() {\n        this._packer.pack(this.data, this.width, this.height, this.gamma);\n    }).bind(this));\n    return this;\n};\nPNG.prototype.parse = function(data, callback) {\n    if (callback) {\n        let onParsed, onError;\n        onParsed = (function(parsedData) {\n            this.removeListener(\"error\", onError);\n            this.data = parsedData;\n            callback(null, this);\n        }).bind(this);\n        onError = (function(err) {\n            this.removeListener(\"parsed\", onParsed);\n            callback(err, null);\n        }).bind(this);\n        this.once(\"parsed\", onParsed);\n        this.once(\"error\", onError);\n    }\n    this.end(data);\n    return this;\n};\nPNG.prototype.write = function(data) {\n    this._parser.write(data);\n    return true;\n};\nPNG.prototype.end = function(data) {\n    this._parser.end(data);\n};\nPNG.prototype._metadata = function(metadata) {\n    this.width = metadata.width;\n    this.height = metadata.height;\n    this.emit(\"metadata\", metadata);\n};\nPNG.prototype._gamma = function(gamma) {\n    this.gamma = gamma;\n};\nPNG.prototype._handleClose = function() {\n    if (!this._parser.writable && !this._packer.readable) {\n        this.emit(\"close\");\n    }\n};\nPNG.bitblt = function(src, dst, srcX, srcY, width, height, deltaX, deltaY) {\n    // eslint-disable-line max-params\n    // coerce pixel dimensions to integers (also coerces undefined -> 0):\n    /* eslint-disable no-param-reassign */ srcX |= 0;\n    srcY |= 0;\n    width |= 0;\n    height |= 0;\n    deltaX |= 0;\n    deltaY |= 0;\n    /* eslint-enable no-param-reassign */ if (srcX > src.width || srcY > src.height || srcX + width > src.width || srcY + height > src.height) {\n        throw new Error(\"bitblt reading outside image\");\n    }\n    if (deltaX > dst.width || deltaY > dst.height || deltaX + width > dst.width || deltaY + height > dst.height) {\n        throw new Error(\"bitblt writing outside image\");\n    }\n    for(let y = 0; y < height; y++){\n        src.data.copy(dst.data, (deltaY + y) * dst.width + deltaX << 2, (srcY + y) * src.width + srcX << 2, (srcY + y) * src.width + srcX + width << 2);\n    }\n};\nPNG.prototype.bitblt = function(dst, srcX, srcY, width, height, deltaX, deltaY) {\n    // eslint-disable-line max-params\n    PNG.bitblt(this, dst, srcX, srcY, width, height, deltaX, deltaY);\n    return this;\n};\nPNG.adjustGamma = function(src) {\n    if (src.gamma) {\n        for(let y = 0; y < src.height; y++){\n            for(let x = 0; x < src.width; x++){\n                let idx = src.width * y + x << 2;\n                for(let i = 0; i < 3; i++){\n                    let sample = src.data[idx + i] / 255;\n                    sample = Math.pow(sample, 1 / 2.2 / src.gamma);\n                    src.data[idx + i] = Math.round(sample * 255);\n                }\n            }\n        }\n        src.gamma = 0;\n    }\n};\nPNG.prototype.adjustGamma = function() {\n    PNG.adjustGamma(this);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/png.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/sync-inflate.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/sync-inflate.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nlet assert = (__webpack_require__(/*! assert */ \"assert\").ok);\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet util = __webpack_require__(/*! util */ \"util\");\nlet kMaxLength = (__webpack_require__(/*! buffer */ \"buffer\").kMaxLength);\nfunction Inflate(opts) {\n    if (!(this instanceof Inflate)) {\n        return new Inflate(opts);\n    }\n    if (opts && opts.chunkSize < zlib.Z_MIN_CHUNK) {\n        opts.chunkSize = zlib.Z_MIN_CHUNK;\n    }\n    zlib.Inflate.call(this, opts);\n    // Node 8 --> 9 compatibility check\n    this._offset = this._offset === undefined ? this._outOffset : this._offset;\n    this._buffer = this._buffer || this._outBuffer;\n    if (opts && opts.maxLength != null) {\n        this._maxLength = opts.maxLength;\n    }\n}\nfunction createInflate(opts) {\n    return new Inflate(opts);\n}\nfunction _close(engine, callback) {\n    if (callback) {\n        process.nextTick(callback);\n    }\n    // Caller may invoke .close after a zlib error (which will null _handle).\n    if (!engine._handle) {\n        return;\n    }\n    engine._handle.close();\n    engine._handle = null;\n}\nInflate.prototype._processChunk = function(chunk, flushFlag, asyncCb) {\n    if (typeof asyncCb === \"function\") {\n        return zlib.Inflate._processChunk.call(this, chunk, flushFlag, asyncCb);\n    }\n    let self = this;\n    let availInBefore = chunk && chunk.length;\n    let availOutBefore = this._chunkSize - this._offset;\n    let leftToInflate = this._maxLength;\n    let inOff = 0;\n    let buffers = [];\n    let nread = 0;\n    let error;\n    this.on(\"error\", function(err) {\n        error = err;\n    });\n    function handleChunk(availInAfter, availOutAfter) {\n        if (self._hadError) {\n            return;\n        }\n        let have = availOutBefore - availOutAfter;\n        assert(have >= 0, \"have should not go down\");\n        if (have > 0) {\n            let out = self._buffer.slice(self._offset, self._offset + have);\n            self._offset += have;\n            if (out.length > leftToInflate) {\n                out = out.slice(0, leftToInflate);\n            }\n            buffers.push(out);\n            nread += out.length;\n            leftToInflate -= out.length;\n            if (leftToInflate === 0) {\n                return false;\n            }\n        }\n        if (availOutAfter === 0 || self._offset >= self._chunkSize) {\n            availOutBefore = self._chunkSize;\n            self._offset = 0;\n            self._buffer = Buffer.allocUnsafe(self._chunkSize);\n        }\n        if (availOutAfter === 0) {\n            inOff += availInBefore - availInAfter;\n            availInBefore = availInAfter;\n            return true;\n        }\n        return false;\n    }\n    assert(this._handle, \"zlib binding closed\");\n    let res;\n    do {\n        res = this._handle.writeSync(flushFlag, chunk, inOff, availInBefore, this._buffer, this._offset, availOutBefore); // out_len\n        // Node 8 --> 9 compatibility check\n        res = res || this._writeState;\n    }while (!this._hadError && handleChunk(res[0], res[1]));\n    if (this._hadError) {\n        throw error;\n    }\n    if (nread >= kMaxLength) {\n        _close(this);\n        throw new RangeError(\"Cannot create final Buffer. It would be larger than 0x\" + kMaxLength.toString(16) + \" bytes\");\n    }\n    let buf = Buffer.concat(buffers, nread);\n    _close(this);\n    return buf;\n};\nutil.inherits(Inflate, zlib.Inflate);\nfunction zlibBufferSync(engine, buffer) {\n    if (typeof buffer === \"string\") {\n        buffer = Buffer.from(buffer);\n    }\n    if (!(buffer instanceof Buffer)) {\n        throw new TypeError(\"Not a string or buffer\");\n    }\n    let flushFlag = engine._finishFlushFlag;\n    if (flushFlag == null) {\n        flushFlag = zlib.Z_FINISH;\n    }\n    return engine._processChunk(buffer, flushFlag);\n}\nfunction inflateSync(buffer, opts) {\n    return zlibBufferSync(new Inflate(opts), buffer);\n}\nmodule.exports = exports = inflateSync;\nexports.Inflate = Inflate;\nexports.createInflate = createInflate;\nexports.inflateSync = inflateSync;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/sync-inflate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pngjs/lib/sync-reader.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/sync-reader.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\nlet SyncReader = module.exports = function(buffer) {\n    this._buffer = buffer;\n    this._reads = [];\n};\nSyncReader.prototype.read = function(length, callback) {\n    this._reads.push({\n        length: Math.abs(length),\n        allowLess: length < 0,\n        func: callback\n    });\n};\nSyncReader.prototype.process = function() {\n    // as long as there is any data and read requests\n    while(this._reads.length > 0 && this._buffer.length){\n        let read = this._reads[0];\n        if (this._buffer.length && (this._buffer.length >= read.length || read.allowLess)) {\n            // ok there is any data so that we can satisfy this request\n            this._reads.shift(); // == read\n            let buf = this._buffer;\n            this._buffer = buf.slice(read.length);\n            read.func.call(this, buf.slice(0, read.length));\n        } else {\n            break;\n        }\n    }\n    if (this._reads.length > 0) {\n        throw new Error(\"There are some read requests waitng on finished stream\");\n    }\n    if (this._buffer.length > 0) {\n        throw new Error(\"unrecognised content at end of stream\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pngjs/lib/sync-reader.js\n");

/***/ })

};
;