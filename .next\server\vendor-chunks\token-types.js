"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/token-types";
exports.ids = ["vendor-chunks/token-types"];
exports.modules = {

/***/ "(rsc)/./node_modules/token-types/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/token-types/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.AnsiStringType = exports.StringType = exports.BufferType = exports.Uint8ArrayType = exports.IgnoreType = exports.Float80_LE = exports.Float80_BE = exports.Float64_LE = exports.Float64_BE = exports.Float32_LE = exports.Float32_BE = exports.Float16_LE = exports.Float16_BE = exports.INT64_BE = exports.UINT64_BE = exports.INT64_LE = exports.UINT64_LE = exports.INT32_LE = exports.INT32_BE = exports.INT24_BE = exports.INT24_LE = exports.INT16_LE = exports.INT16_BE = exports.INT8 = exports.UINT32_BE = exports.UINT32_LE = exports.UINT24_BE = exports.UINT24_LE = exports.UINT16_BE = exports.UINT16_LE = exports.UINT8 = void 0;\nconst ieee754 = __webpack_require__(/*! ieee754 */ \"(rsc)/./node_modules/ieee754/index.js\");\n// Primitive types\nfunction dv(array) {\n    return new DataView(array.buffer, array.byteOffset);\n}\n/**\n * 8-bit unsigned integer\n */ exports.UINT8 = {\n    len: 1,\n    get (array, offset) {\n        return dv(array).getUint8(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setUint8(offset, value);\n        return offset + 1;\n    }\n};\n/**\n * 16-bit unsigned integer, Little Endian byte order\n */ exports.UINT16_LE = {\n    len: 2,\n    get (array, offset) {\n        return dv(array).getUint16(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setUint16(offset, value, true);\n        return offset + 2;\n    }\n};\n/**\n * 16-bit unsigned integer, Big Endian byte order\n */ exports.UINT16_BE = {\n    len: 2,\n    get (array, offset) {\n        return dv(array).getUint16(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setUint16(offset, value);\n        return offset + 2;\n    }\n};\n/**\n * 24-bit unsigned integer, Little Endian byte order\n */ exports.UINT24_LE = {\n    len: 3,\n    get (array, offset) {\n        const dataView = dv(array);\n        return dataView.getUint8(offset) + (dataView.getUint16(offset + 1, true) << 8);\n    },\n    put (array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint8(offset, value & 0xff);\n        dataView.setUint16(offset + 1, value >> 8, true);\n        return offset + 3;\n    }\n};\n/**\n * 24-bit unsigned integer, Big Endian byte order\n */ exports.UINT24_BE = {\n    len: 3,\n    get (array, offset) {\n        const dataView = dv(array);\n        return (dataView.getUint16(offset) << 8) + dataView.getUint8(offset + 2);\n    },\n    put (array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint16(offset, value >> 8);\n        dataView.setUint8(offset + 2, value & 0xff);\n        return offset + 3;\n    }\n};\n/**\n * 32-bit unsigned integer, Little Endian byte order\n */ exports.UINT32_LE = {\n    len: 4,\n    get (array, offset) {\n        return dv(array).getUint32(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setUint32(offset, value, true);\n        return offset + 4;\n    }\n};\n/**\n * 32-bit unsigned integer, Big Endian byte order\n */ exports.UINT32_BE = {\n    len: 4,\n    get (array, offset) {\n        return dv(array).getUint32(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setUint32(offset, value);\n        return offset + 4;\n    }\n};\n/**\n * 8-bit signed integer\n */ exports.INT8 = {\n    len: 1,\n    get (array, offset) {\n        return dv(array).getInt8(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setInt8(offset, value);\n        return offset + 1;\n    }\n};\n/**\n * 16-bit signed integer, Big Endian byte order\n */ exports.INT16_BE = {\n    len: 2,\n    get (array, offset) {\n        return dv(array).getInt16(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setInt16(offset, value);\n        return offset + 2;\n    }\n};\n/**\n * 16-bit signed integer, Little Endian byte order\n */ exports.INT16_LE = {\n    len: 2,\n    get (array, offset) {\n        return dv(array).getInt16(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setInt16(offset, value, true);\n        return offset + 2;\n    }\n};\n/**\n * 24-bit signed integer, Little Endian byte order\n */ exports.INT24_LE = {\n    len: 3,\n    get (array, offset) {\n        const unsigned = exports.UINT24_LE.get(array, offset);\n        return unsigned > 0x7fffff ? unsigned - 0x1000000 : unsigned;\n    },\n    put (array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint8(offset, value & 0xff);\n        dataView.setUint16(offset + 1, value >> 8, true);\n        return offset + 3;\n    }\n};\n/**\n * 24-bit signed integer, Big Endian byte order\n */ exports.INT24_BE = {\n    len: 3,\n    get (array, offset) {\n        const unsigned = exports.UINT24_BE.get(array, offset);\n        return unsigned > 0x7fffff ? unsigned - 0x1000000 : unsigned;\n    },\n    put (array, offset, value) {\n        const dataView = dv(array);\n        dataView.setUint16(offset, value >> 8);\n        dataView.setUint8(offset + 2, value & 0xff);\n        return offset + 3;\n    }\n};\n/**\n * 32-bit signed integer, Big Endian byte order\n */ exports.INT32_BE = {\n    len: 4,\n    get (array, offset) {\n        return dv(array).getInt32(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setInt32(offset, value);\n        return offset + 4;\n    }\n};\n/**\n * 32-bit signed integer, Big Endian byte order\n */ exports.INT32_LE = {\n    len: 4,\n    get (array, offset) {\n        return dv(array).getInt32(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setInt32(offset, value, true);\n        return offset + 4;\n    }\n};\n/**\n * 64-bit unsigned integer, Little Endian byte order\n */ exports.UINT64_LE = {\n    len: 8,\n    get (array, offset) {\n        return dv(array).getBigUint64(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setBigUint64(offset, value, true);\n        return offset + 8;\n    }\n};\n/**\n * 64-bit signed integer, Little Endian byte order\n */ exports.INT64_LE = {\n    len: 8,\n    get (array, offset) {\n        return dv(array).getBigInt64(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setBigInt64(offset, value, true);\n        return offset + 8;\n    }\n};\n/**\n * 64-bit unsigned integer, Big Endian byte order\n */ exports.UINT64_BE = {\n    len: 8,\n    get (array, offset) {\n        return dv(array).getBigUint64(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setBigUint64(offset, value);\n        return offset + 8;\n    }\n};\n/**\n * 64-bit signed integer, Big Endian byte order\n */ exports.INT64_BE = {\n    len: 8,\n    get (array, offset) {\n        return dv(array).getBigInt64(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setBigInt64(offset, value);\n        return offset + 8;\n    }\n};\n/**\n * IEEE 754 16-bit (half precision) float, big endian\n */ exports.Float16_BE = {\n    len: 2,\n    get (dataView, offset) {\n        return ieee754.read(dataView, offset, false, 10, this.len);\n    },\n    put (dataView, offset, value) {\n        ieee754.write(dataView, value, offset, false, 10, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * IEEE 754 16-bit (half precision) float, little endian\n */ exports.Float16_LE = {\n    len: 2,\n    get (array, offset) {\n        return ieee754.read(array, offset, true, 10, this.len);\n    },\n    put (array, offset, value) {\n        ieee754.write(array, value, offset, true, 10, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * IEEE 754 32-bit (single precision) float, big endian\n */ exports.Float32_BE = {\n    len: 4,\n    get (array, offset) {\n        return dv(array).getFloat32(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setFloat32(offset, value);\n        return offset + 4;\n    }\n};\n/**\n * IEEE 754 32-bit (single precision) float, little endian\n */ exports.Float32_LE = {\n    len: 4,\n    get (array, offset) {\n        return dv(array).getFloat32(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setFloat32(offset, value, true);\n        return offset + 4;\n    }\n};\n/**\n * IEEE 754 64-bit (double precision) float, big endian\n */ exports.Float64_BE = {\n    len: 8,\n    get (array, offset) {\n        return dv(array).getFloat64(offset);\n    },\n    put (array, offset, value) {\n        dv(array).setFloat64(offset, value);\n        return offset + 8;\n    }\n};\n/**\n * IEEE 754 64-bit (double precision) float, little endian\n */ exports.Float64_LE = {\n    len: 8,\n    get (array, offset) {\n        return dv(array).getFloat64(offset, true);\n    },\n    put (array, offset, value) {\n        dv(array).setFloat64(offset, value, true);\n        return offset + 8;\n    }\n};\n/**\n * IEEE 754 80-bit (extended precision) float, big endian\n */ exports.Float80_BE = {\n    len: 10,\n    get (array, offset) {\n        return ieee754.read(array, offset, false, 63, this.len);\n    },\n    put (array, offset, value) {\n        ieee754.write(array, value, offset, false, 63, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * IEEE 754 80-bit (extended precision) float, little endian\n */ exports.Float80_LE = {\n    len: 10,\n    get (array, offset) {\n        return ieee754.read(array, offset, true, 63, this.len);\n    },\n    put (array, offset, value) {\n        ieee754.write(array, value, offset, true, 63, this.len);\n        return offset + this.len;\n    }\n};\n/**\n * Ignore a given number of bytes\n */ class IgnoreType {\n    /**\n     * @param len number of bytes to ignore\n     */ constructor(len){\n        this.len = len;\n    }\n    // ToDo: don't read, but skip data\n    get(array, off) {}\n}\nexports.IgnoreType = IgnoreType;\nclass Uint8ArrayType {\n    constructor(len){\n        this.len = len;\n    }\n    get(array, offset) {\n        return array.subarray(offset, offset + this.len);\n    }\n}\nexports.Uint8ArrayType = Uint8ArrayType;\nclass BufferType {\n    constructor(len){\n        this.len = len;\n    }\n    get(uint8Array, off) {\n        return Buffer.from(uint8Array.subarray(off, off + this.len));\n    }\n}\nexports.BufferType = BufferType;\n/**\n * Consume a fixed number of bytes from the stream and return a string with a specified encoding.\n */ class StringType {\n    constructor(len, encoding){\n        this.len = len;\n        this.encoding = encoding;\n    }\n    get(uint8Array, offset) {\n        return Buffer.from(uint8Array).toString(this.encoding, offset, offset + this.len);\n    }\n}\nexports.StringType = StringType;\n/**\n * ANSI Latin 1 String\n * Using windows-1252 / ISO 8859-1 decoding\n */ class AnsiStringType {\n    constructor(len){\n        this.len = len;\n    }\n    static decode(buffer, offset, until) {\n        let str = \"\";\n        for(let i = offset; i < until; ++i){\n            str += AnsiStringType.codePointToString(AnsiStringType.singleByteDecoder(buffer[i]));\n        }\n        return str;\n    }\n    static inRange(a, min, max) {\n        return min <= a && a <= max;\n    }\n    static codePointToString(cp) {\n        if (cp <= 0xFFFF) {\n            return String.fromCharCode(cp);\n        } else {\n            cp -= 0x10000;\n            return String.fromCharCode((cp >> 10) + 0xD800, (cp & 0x3FF) + 0xDC00);\n        }\n    }\n    static singleByteDecoder(bite) {\n        if (AnsiStringType.inRange(bite, 0x00, 0x7F)) {\n            return bite;\n        }\n        const codePoint = AnsiStringType.windows1252[bite - 0x80];\n        if (codePoint === null) {\n            throw Error(\"invaliding encoding\");\n        }\n        return codePoint;\n    }\n    get(buffer, offset = 0) {\n        return AnsiStringType.decode(buffer, offset, offset + this.len);\n    }\n}\nexports.AnsiStringType = AnsiStringType;\nAnsiStringType.windows1252 = [\n    8364,\n    129,\n    8218,\n    402,\n    8222,\n    8230,\n    8224,\n    8225,\n    710,\n    8240,\n    352,\n    8249,\n    338,\n    141,\n    381,\n    143,\n    144,\n    8216,\n    8217,\n    8220,\n    8221,\n    8226,\n    8211,\n    8212,\n    732,\n    8482,\n    353,\n    8250,\n    339,\n    157,\n    382,\n    376,\n    160,\n    161,\n    162,\n    163,\n    164,\n    165,\n    166,\n    167,\n    168,\n    169,\n    170,\n    171,\n    172,\n    173,\n    174,\n    175,\n    176,\n    177,\n    178,\n    179,\n    180,\n    181,\n    182,\n    183,\n    184,\n    185,\n    186,\n    187,\n    188,\n    189,\n    190,\n    191,\n    192,\n    193,\n    194,\n    195,\n    196,\n    197,\n    198,\n    199,\n    200,\n    201,\n    202,\n    203,\n    204,\n    205,\n    206,\n    207,\n    208,\n    209,\n    210,\n    211,\n    212,\n    213,\n    214,\n    215,\n    216,\n    217,\n    218,\n    219,\n    220,\n    221,\n    222,\n    223,\n    224,\n    225,\n    226,\n    227,\n    228,\n    229,\n    230,\n    231,\n    232,\n    233,\n    234,\n    235,\n    236,\n    237,\n    238,\n    239,\n    240,\n    241,\n    242,\n    243,\n    244,\n    245,\n    246,\n    247,\n    248,\n    249,\n    250,\n    251,\n    252,\n    253,\n    254,\n    255\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/token-types/lib/index.js\n");

/***/ })

};
;