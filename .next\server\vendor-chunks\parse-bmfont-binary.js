"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-bmfont-binary";
exports.ids = ["vendor-chunks/parse-bmfont-binary"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse-bmfont-binary/index.js":
/*!***************************************************!*\
  !*** ./node_modules/parse-bmfont-binary/index.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\nvar HEADER = [\n    66,\n    77,\n    70\n];\nmodule.exports = function readBMFontBinary(buf) {\n    if (buf.length < 6) throw new Error(\"invalid buffer length for BMFont\");\n    var header = HEADER.every(function(byte, i) {\n        return buf.readUInt8(i) === byte;\n    });\n    if (!header) throw new Error(\"BMFont missing BMF byte header\");\n    var i = 3;\n    var vers = buf.readUInt8(i++);\n    if (vers > 3) throw new Error(\"Only supports BMFont Binary v3 (BMFont App v1.10)\");\n    var target = {\n        kernings: [],\n        chars: []\n    };\n    for(var b = 0; b < 5; b++)i += readBlock(target, buf, i);\n    return target;\n};\nfunction readBlock(target, buf, i) {\n    if (i > buf.length - 1) return 0;\n    var blockID = buf.readUInt8(i++);\n    var blockSize = buf.readInt32LE(i);\n    i += 4;\n    switch(blockID){\n        case 1:\n            target.info = readInfo(buf, i);\n            break;\n        case 2:\n            target.common = readCommon(buf, i);\n            break;\n        case 3:\n            target.pages = readPages(buf, i, blockSize);\n            break;\n        case 4:\n            target.chars = readChars(buf, i, blockSize);\n            break;\n        case 5:\n            target.kernings = readKernings(buf, i, blockSize);\n            break;\n    }\n    return 5 + blockSize;\n}\nfunction readInfo(buf, i) {\n    var info = {};\n    info.size = buf.readInt16LE(i);\n    var bitField = buf.readUInt8(i + 2);\n    info.smooth = bitField >> 7 & 1;\n    info.unicode = bitField >> 6 & 1;\n    info.italic = bitField >> 5 & 1;\n    info.bold = bitField >> 4 & 1;\n    //fixedHeight is only mentioned in binary spec \n    if (bitField >> 3 & 1) info.fixedHeight = 1;\n    info.charset = buf.readUInt8(i + 3) || \"\";\n    info.stretchH = buf.readUInt16LE(i + 4);\n    info.aa = buf.readUInt8(i + 6);\n    info.padding = [\n        buf.readInt8(i + 7),\n        buf.readInt8(i + 8),\n        buf.readInt8(i + 9),\n        buf.readInt8(i + 10)\n    ];\n    info.spacing = [\n        buf.readInt8(i + 11),\n        buf.readInt8(i + 12)\n    ];\n    info.outline = buf.readUInt8(i + 13);\n    info.face = readStringNT(buf, i + 14);\n    return info;\n}\nfunction readCommon(buf, i) {\n    var common = {};\n    common.lineHeight = buf.readUInt16LE(i);\n    common.base = buf.readUInt16LE(i + 2);\n    common.scaleW = buf.readUInt16LE(i + 4);\n    common.scaleH = buf.readUInt16LE(i + 6);\n    common.pages = buf.readUInt16LE(i + 8);\n    var bitField = buf.readUInt8(i + 10);\n    common.packed = 0;\n    common.alphaChnl = buf.readUInt8(i + 11);\n    common.redChnl = buf.readUInt8(i + 12);\n    common.greenChnl = buf.readUInt8(i + 13);\n    common.blueChnl = buf.readUInt8(i + 14);\n    return common;\n}\nfunction readPages(buf, i, size) {\n    var pages = [];\n    var text = readNameNT(buf, i);\n    var len = text.length + 1;\n    var count = size / len;\n    for(var c = 0; c < count; c++){\n        pages[c] = buf.slice(i, i + text.length).toString(\"utf8\");\n        i += len;\n    }\n    return pages;\n}\nfunction readChars(buf, i, blockSize) {\n    var chars = [];\n    var count = blockSize / 20;\n    for(var c = 0; c < count; c++){\n        var char = {};\n        var off = c * 20;\n        char.id = buf.readUInt32LE(i + 0 + off);\n        char.x = buf.readUInt16LE(i + 4 + off);\n        char.y = buf.readUInt16LE(i + 6 + off);\n        char.width = buf.readUInt16LE(i + 8 + off);\n        char.height = buf.readUInt16LE(i + 10 + off);\n        char.xoffset = buf.readInt16LE(i + 12 + off);\n        char.yoffset = buf.readInt16LE(i + 14 + off);\n        char.xadvance = buf.readInt16LE(i + 16 + off);\n        char.page = buf.readUInt8(i + 18 + off);\n        char.chnl = buf.readUInt8(i + 19 + off);\n        chars[c] = char;\n    }\n    return chars;\n}\nfunction readKernings(buf, i, blockSize) {\n    var kernings = [];\n    var count = blockSize / 10;\n    for(var c = 0; c < count; c++){\n        var kern = {};\n        var off = c * 10;\n        kern.first = buf.readUInt32LE(i + 0 + off);\n        kern.second = buf.readUInt32LE(i + 4 + off);\n        kern.amount = buf.readInt16LE(i + 8 + off);\n        kernings[c] = kern;\n    }\n    return kernings;\n}\nfunction readNameNT(buf, offset) {\n    var pos = offset;\n    for(; pos < buf.length; pos++){\n        if (buf[pos] === 0x00) break;\n    }\n    return buf.slice(offset, pos);\n}\nfunction readStringNT(buf, offset) {\n    return readNameNT(buf, offset).toString(\"utf8\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse-bmfont-binary/index.js\n");

/***/ })

};
;