{"version": 3, "file": "index.js", "names": ["isNodePattern", "invert", "cb", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "data", "call"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Inverts the image\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  invert(cb) {\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx] = 255 - this.bitmap.data[idx];\n        this.bitmap.data[idx + 1] = 255 - this.bitmap.data[idx + 1];\n        this.bitmap.data[idx + 2] = 255 - this.bitmap.data[idx + 2];\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,MAAM,CAACC,EAAE,EAAE;IACT,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACC,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;MACnB,IAAI,CAACL,MAAM,CAACM,IAAI,CAACD,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAACL,MAAM,CAACM,IAAI,CAACD,GAAG,CAAC;MACnD,IAAI,CAACL,MAAM,CAACM,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACL,MAAM,CAACM,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC;MAC3D,IAAI,CAACL,MAAM,CAACM,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACL,MAAM,CAACM,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC;IAC7D,CAAC,CACF;IAED,IAAIT,aAAa,CAACE,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}