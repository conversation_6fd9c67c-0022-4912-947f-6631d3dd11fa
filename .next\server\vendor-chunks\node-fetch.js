"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/node-fetch";
exports.ids = ["vendor-chunks/node-fetch"];
exports.modules = {

/***/ "(rsc)/./node_modules/node-fetch/lib/index.mjs":
/*!***********************************************!*\
  !*** ./node_modules/node-fetch/lib/index.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortError: () => (/* binding */ AbortError),\n/* harmony export */   FetchError: () => (/* binding */ FetchError),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   Response: () => (/* binding */ Response),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! http */ \"http\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var whatwg_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! whatwg-url */ \"(rsc)/./node_modules/whatwg-url/lib/public-api.js\");\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! https */ \"https\");\n/* harmony import */ var zlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zlib */ \"zlib\");\n\n\n\n\n\n\n// Based on https://github.com/tmpvar/jsdom/blob/aa85b2abf07766ff7bf5c1f6daafb3726f2f2db5/lib/jsdom/living/blob.js\n// fix for \"Readable\" isn't a named export issue\nconst Readable = stream__WEBPACK_IMPORTED_MODULE_0__.Readable;\nconst BUFFER = Symbol(\"buffer\");\nconst TYPE = Symbol(\"type\");\nclass Blob {\n    constructor(){\n        this[TYPE] = \"\";\n        const blobParts = arguments[0];\n        const options = arguments[1];\n        const buffers = [];\n        let size = 0;\n        if (blobParts) {\n            const a = blobParts;\n            const length = Number(a.length);\n            for(let i = 0; i < length; i++){\n                const element = a[i];\n                let buffer;\n                if (element instanceof Buffer) {\n                    buffer = element;\n                } else if (ArrayBuffer.isView(element)) {\n                    buffer = Buffer.from(element.buffer, element.byteOffset, element.byteLength);\n                } else if (element instanceof ArrayBuffer) {\n                    buffer = Buffer.from(element);\n                } else if (element instanceof Blob) {\n                    buffer = element[BUFFER];\n                } else {\n                    buffer = Buffer.from(typeof element === \"string\" ? element : String(element));\n                }\n                size += buffer.length;\n                buffers.push(buffer);\n            }\n        }\n        this[BUFFER] = Buffer.concat(buffers);\n        let type = options && options.type !== undefined && String(options.type).toLowerCase();\n        if (type && !/[^\\u0020-\\u007E]/.test(type)) {\n            this[TYPE] = type;\n        }\n    }\n    get size() {\n        return this[BUFFER].length;\n    }\n    get type() {\n        return this[TYPE];\n    }\n    text() {\n        return Promise.resolve(this[BUFFER].toString());\n    }\n    arrayBuffer() {\n        const buf = this[BUFFER];\n        const ab = buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n        return Promise.resolve(ab);\n    }\n    stream() {\n        const readable = new Readable();\n        readable._read = function() {};\n        readable.push(this[BUFFER]);\n        readable.push(null);\n        return readable;\n    }\n    toString() {\n        return \"[object Blob]\";\n    }\n    slice() {\n        const size = this.size;\n        const start = arguments[0];\n        const end = arguments[1];\n        let relativeStart, relativeEnd;\n        if (start === undefined) {\n            relativeStart = 0;\n        } else if (start < 0) {\n            relativeStart = Math.max(size + start, 0);\n        } else {\n            relativeStart = Math.min(start, size);\n        }\n        if (end === undefined) {\n            relativeEnd = size;\n        } else if (end < 0) {\n            relativeEnd = Math.max(size + end, 0);\n        } else {\n            relativeEnd = Math.min(end, size);\n        }\n        const span = Math.max(relativeEnd - relativeStart, 0);\n        const buffer = this[BUFFER];\n        const slicedBuffer = buffer.slice(relativeStart, relativeStart + span);\n        const blob = new Blob([], {\n            type: arguments[2]\n        });\n        blob[BUFFER] = slicedBuffer;\n        return blob;\n    }\n}\nObject.defineProperties(Blob.prototype, {\n    size: {\n        enumerable: true\n    },\n    type: {\n        enumerable: true\n    },\n    slice: {\n        enumerable: true\n    }\n});\nObject.defineProperty(Blob.prototype, Symbol.toStringTag, {\n    value: \"Blob\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\n/**\n * fetch-error.js\n *\n * FetchError interface for operational errors\n */ /**\n * Create FetchError instance\n *\n * @param   String      message      Error message for human\n * @param   String      type         Error type for machine\n * @param   String      systemError  For Node.js system error\n * @return  FetchError\n */ function FetchError(message, type, systemError) {\n    Error.call(this, message);\n    this.message = message;\n    this.type = type;\n    // when err.type is `system`, err.code contains system error code\n    if (systemError) {\n        this.code = this.errno = systemError.code;\n    }\n    // hide custom error implementation details from end-users\n    Error.captureStackTrace(this, this.constructor);\n}\nFetchError.prototype = Object.create(Error.prototype);\nFetchError.prototype.constructor = FetchError;\nFetchError.prototype.name = \"FetchError\";\nlet convert;\ntry {\n    convert = require(\"encoding\").convert;\n} catch (e) {}\nconst INTERNALS = Symbol(\"Body internals\");\n// fix an issue where \"PassThrough\" isn't a named export for node <10\nconst PassThrough = stream__WEBPACK_IMPORTED_MODULE_0__.PassThrough;\n/**\n * Body mixin\n *\n * Ref: https://fetch.spec.whatwg.org/#body\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */ function Body(body) {\n    var _this = this;\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}, _ref$size = _ref.size;\n    let size = _ref$size === undefined ? 0 : _ref$size;\n    var _ref$timeout = _ref.timeout;\n    let timeout = _ref$timeout === undefined ? 0 : _ref$timeout;\n    if (body == null) {\n        // body is undefined or null\n        body = null;\n    } else if (isURLSearchParams(body)) {\n        // body is a URLSearchParams\n        body = Buffer.from(body.toString());\n    } else if (isBlob(body)) ;\n    else if (Buffer.isBuffer(body)) ;\n    else if (Object.prototype.toString.call(body) === \"[object ArrayBuffer]\") {\n        // body is ArrayBuffer\n        body = Buffer.from(body);\n    } else if (ArrayBuffer.isView(body)) {\n        // body is ArrayBufferView\n        body = Buffer.from(body.buffer, body.byteOffset, body.byteLength);\n    } else if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__) ;\n    else {\n        // none of the above\n        // coerce to string then buffer\n        body = Buffer.from(String(body));\n    }\n    this[INTERNALS] = {\n        body,\n        disturbed: false,\n        error: null\n    };\n    this.size = size;\n    this.timeout = timeout;\n    if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__) {\n        body.on(\"error\", function(err) {\n            const error = err.name === \"AbortError\" ? err : new FetchError(`Invalid response body while trying to fetch ${_this.url}: ${err.message}`, \"system\", err);\n            _this[INTERNALS].error = error;\n        });\n    }\n}\nBody.prototype = {\n    get body () {\n        return this[INTERNALS].body;\n    },\n    get bodyUsed () {\n        return this[INTERNALS].disturbed;\n    },\n    /**\n  * Decode response as ArrayBuffer\n  *\n  * @return  Promise\n  */ arrayBuffer () {\n        return consumeBody.call(this).then(function(buf) {\n            return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n        });\n    },\n    /**\n  * Return raw response as Blob\n  *\n  * @return Promise\n  */ blob () {\n        let ct = this.headers && this.headers.get(\"content-type\") || \"\";\n        return consumeBody.call(this).then(function(buf) {\n            return Object.assign(// Prevent copying\n            new Blob([], {\n                type: ct.toLowerCase()\n            }), {\n                [BUFFER]: buf\n            });\n        });\n    },\n    /**\n  * Decode response as json\n  *\n  * @return  Promise\n  */ json () {\n        var _this2 = this;\n        return consumeBody.call(this).then(function(buffer) {\n            try {\n                return JSON.parse(buffer.toString());\n            } catch (err) {\n                return Body.Promise.reject(new FetchError(`invalid json response body at ${_this2.url} reason: ${err.message}`, \"invalid-json\"));\n            }\n        });\n    },\n    /**\n  * Decode response as text\n  *\n  * @return  Promise\n  */ text () {\n        return consumeBody.call(this).then(function(buffer) {\n            return buffer.toString();\n        });\n    },\n    /**\n  * Decode response as buffer (non-spec api)\n  *\n  * @return  Promise\n  */ buffer () {\n        return consumeBody.call(this);\n    },\n    /**\n  * Decode response as text, while automatically detecting the encoding and\n  * trying to decode to UTF-8 (non-spec api)\n  *\n  * @return  Promise\n  */ textConverted () {\n        var _this3 = this;\n        return consumeBody.call(this).then(function(buffer) {\n            return convertBody(buffer, _this3.headers);\n        });\n    }\n};\n// In browsers, all properties are enumerable.\nObject.defineProperties(Body.prototype, {\n    body: {\n        enumerable: true\n    },\n    bodyUsed: {\n        enumerable: true\n    },\n    arrayBuffer: {\n        enumerable: true\n    },\n    blob: {\n        enumerable: true\n    },\n    json: {\n        enumerable: true\n    },\n    text: {\n        enumerable: true\n    }\n});\nBody.mixIn = function(proto) {\n    for (const name of Object.getOwnPropertyNames(Body.prototype)){\n        // istanbul ignore else: future proof\n        if (!(name in proto)) {\n            const desc = Object.getOwnPropertyDescriptor(Body.prototype, name);\n            Object.defineProperty(proto, name, desc);\n        }\n    }\n};\n/**\n * Consume and convert an entire Body to a Buffer.\n *\n * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body\n *\n * @return  Promise\n */ function consumeBody() {\n    var _this4 = this;\n    if (this[INTERNALS].disturbed) {\n        return Body.Promise.reject(new TypeError(`body used already for: ${this.url}`));\n    }\n    this[INTERNALS].disturbed = true;\n    if (this[INTERNALS].error) {\n        return Body.Promise.reject(this[INTERNALS].error);\n    }\n    let body = this.body;\n    // body is null\n    if (body === null) {\n        return Body.Promise.resolve(Buffer.alloc(0));\n    }\n    // body is blob\n    if (isBlob(body)) {\n        body = body.stream();\n    }\n    // body is buffer\n    if (Buffer.isBuffer(body)) {\n        return Body.Promise.resolve(body);\n    }\n    // istanbul ignore if: should never happen\n    if (!(body instanceof stream__WEBPACK_IMPORTED_MODULE_0__)) {\n        return Body.Promise.resolve(Buffer.alloc(0));\n    }\n    // body is stream\n    // get ready to actually consume the body\n    let accum = [];\n    let accumBytes = 0;\n    let abort = false;\n    return new Body.Promise(function(resolve, reject) {\n        let resTimeout;\n        // allow timeout on slow response body\n        if (_this4.timeout) {\n            resTimeout = setTimeout(function() {\n                abort = true;\n                reject(new FetchError(`Response timeout while trying to fetch ${_this4.url} (over ${_this4.timeout}ms)`, \"body-timeout\"));\n            }, _this4.timeout);\n        }\n        // handle stream errors\n        body.on(\"error\", function(err) {\n            if (err.name === \"AbortError\") {\n                // if the request was aborted, reject with this Error\n                abort = true;\n                reject(err);\n            } else {\n                // other errors, such as incorrect content-encoding\n                reject(new FetchError(`Invalid response body while trying to fetch ${_this4.url}: ${err.message}`, \"system\", err));\n            }\n        });\n        body.on(\"data\", function(chunk) {\n            if (abort || chunk === null) {\n                return;\n            }\n            if (_this4.size && accumBytes + chunk.length > _this4.size) {\n                abort = true;\n                reject(new FetchError(`content size at ${_this4.url} over limit: ${_this4.size}`, \"max-size\"));\n                return;\n            }\n            accumBytes += chunk.length;\n            accum.push(chunk);\n        });\n        body.on(\"end\", function() {\n            if (abort) {\n                return;\n            }\n            clearTimeout(resTimeout);\n            try {\n                resolve(Buffer.concat(accum, accumBytes));\n            } catch (err) {\n                // handle streams that have accumulated too much data (issue #414)\n                reject(new FetchError(`Could not create Buffer from response body for ${_this4.url}: ${err.message}`, \"system\", err));\n            }\n        });\n    });\n}\n/**\n * Detect buffer encoding and convert to target encoding\n * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding\n *\n * @param   Buffer  buffer    Incoming buffer\n * @param   String  encoding  Target encoding\n * @return  String\n */ function convertBody(buffer, headers) {\n    if (typeof convert !== \"function\") {\n        throw new Error(\"The package `encoding` must be installed to use the textConverted() function\");\n    }\n    const ct = headers.get(\"content-type\");\n    let charset = \"utf-8\";\n    let res, str;\n    // header\n    if (ct) {\n        res = /charset=([^;]*)/i.exec(ct);\n    }\n    // no charset in content type, peek at response body for at most 1024 bytes\n    str = buffer.slice(0, 1024).toString();\n    // html5\n    if (!res && str) {\n        res = /<meta.+?charset=(['\"])(.+?)\\1/i.exec(str);\n    }\n    // html4\n    if (!res && str) {\n        res = /<meta[\\s]+?http-equiv=(['\"])content-type\\1[\\s]+?content=(['\"])(.+?)\\2/i.exec(str);\n        if (!res) {\n            res = /<meta[\\s]+?content=(['\"])(.+?)\\1[\\s]+?http-equiv=(['\"])content-type\\3/i.exec(str);\n            if (res) {\n                res.pop(); // drop last quote\n            }\n        }\n        if (res) {\n            res = /charset=(.*)/i.exec(res.pop());\n        }\n    }\n    // xml\n    if (!res && str) {\n        res = /<\\?xml.+?encoding=(['\"])(.+?)\\1/i.exec(str);\n    }\n    // found charset\n    if (res) {\n        charset = res.pop();\n        // prevent decode issues when sites use incorrect encoding\n        // ref: https://hsivonen.fi/encoding-menu/\n        if (charset === \"gb2312\" || charset === \"gbk\") {\n            charset = \"gb18030\";\n        }\n    }\n    // turn raw buffers into a single utf-8 buffer\n    return convert(buffer, \"UTF-8\", charset).toString();\n}\n/**\n * Detect a URLSearchParams object\n * ref: https://github.com/bitinn/node-fetch/issues/296#issuecomment-307598143\n *\n * @param   Object  obj     Object to detect by type or brand\n * @return  String\n */ function isURLSearchParams(obj) {\n    // Duck-typing as a necessary condition.\n    if (typeof obj !== \"object\" || typeof obj.append !== \"function\" || typeof obj.delete !== \"function\" || typeof obj.get !== \"function\" || typeof obj.getAll !== \"function\" || typeof obj.has !== \"function\" || typeof obj.set !== \"function\") {\n        return false;\n    }\n    // Brand-checking and more duck-typing as optional condition.\n    return obj.constructor.name === \"URLSearchParams\" || Object.prototype.toString.call(obj) === \"[object URLSearchParams]\" || typeof obj.sort === \"function\";\n}\n/**\n * Check if `obj` is a W3C `Blob` object (which `File` inherits from)\n * @param  {*} obj\n * @return {boolean}\n */ function isBlob(obj) {\n    return typeof obj === \"object\" && typeof obj.arrayBuffer === \"function\" && typeof obj.type === \"string\" && typeof obj.stream === \"function\" && typeof obj.constructor === \"function\" && typeof obj.constructor.name === \"string\" && /^(Blob|File)$/.test(obj.constructor.name) && /^(Blob|File)$/.test(obj[Symbol.toStringTag]);\n}\n/**\n * Clone body given Res/Req instance\n *\n * @param   Mixed  instance  Response or Request instance\n * @return  Mixed\n */ function clone(instance) {\n    let p1, p2;\n    let body = instance.body;\n    // don't allow cloning a used body\n    if (instance.bodyUsed) {\n        throw new Error(\"cannot clone body after it is used\");\n    }\n    // check that body is a stream and not form-data object\n    // note: we can't clone the form-data object without having it as a dependency\n    if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__ && typeof body.getBoundary !== \"function\") {\n        // tee instance body\n        p1 = new PassThrough();\n        p2 = new PassThrough();\n        body.pipe(p1);\n        body.pipe(p2);\n        // set instance body to teed body and return the other teed body\n        instance[INTERNALS].body = p1;\n        body = p2;\n    }\n    return body;\n}\n/**\n * Performs the operation \"extract a `Content-Type` value from |object|\" as\n * specified in the specification:\n * https://fetch.spec.whatwg.org/#concept-bodyinit-extract\n *\n * This function assumes that instance.body is present.\n *\n * @param   Mixed  instance  Any options.body input\n */ function extractContentType(body) {\n    if (body === null) {\n        // body is null\n        return null;\n    } else if (typeof body === \"string\") {\n        // body is string\n        return \"text/plain;charset=UTF-8\";\n    } else if (isURLSearchParams(body)) {\n        // body is a URLSearchParams\n        return \"application/x-www-form-urlencoded;charset=UTF-8\";\n    } else if (isBlob(body)) {\n        // body is blob\n        return body.type || null;\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        return null;\n    } else if (Object.prototype.toString.call(body) === \"[object ArrayBuffer]\") {\n        // body is ArrayBuffer\n        return null;\n    } else if (ArrayBuffer.isView(body)) {\n        // body is ArrayBufferView\n        return null;\n    } else if (typeof body.getBoundary === \"function\") {\n        // detect form data input from form-data module\n        return `multipart/form-data;boundary=${body.getBoundary()}`;\n    } else if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__) {\n        // body is stream\n        // can't really do much about this\n        return null;\n    } else {\n        // Body constructor defaults other things to string\n        return \"text/plain;charset=UTF-8\";\n    }\n}\n/**\n * The Fetch Standard treats this as if \"total bytes\" is a property on the body.\n * For us, we have to explicitly get it with a function.\n *\n * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes\n *\n * @param   Body    instance   Instance of Body\n * @return  Number?            Number of bytes, or null if not possible\n */ function getTotalBytes(instance) {\n    const body = instance.body;\n    if (body === null) {\n        // body is null\n        return 0;\n    } else if (isBlob(body)) {\n        return body.size;\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        return body.length;\n    } else if (body && typeof body.getLengthSync === \"function\") {\n        // detect form data input from form-data module\n        if (body._lengthRetrievers && body._lengthRetrievers.length == 0 || // 1.x\n        body.hasKnownLength && body.hasKnownLength()) {\n            // 2.x\n            return body.getLengthSync();\n        }\n        return null;\n    } else {\n        // body is stream\n        return null;\n    }\n}\n/**\n * Write a Body to a Node.js WritableStream (e.g. http.Request) object.\n *\n * @param   Body    instance   Instance of Body\n * @return  Void\n */ function writeToStream(dest, instance) {\n    const body = instance.body;\n    if (body === null) {\n        // body is null\n        dest.end();\n    } else if (isBlob(body)) {\n        body.stream().pipe(dest);\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        dest.write(body);\n        dest.end();\n    } else {\n        // body is stream\n        body.pipe(dest);\n    }\n}\n// expose Promise\nBody.Promise = global.Promise;\n/**\n * headers.js\n *\n * Headers class offers convenient helpers\n */ const invalidTokenRegex = /[^\\^_`a-zA-Z\\-0-9!#$%&'*+.|~]/;\nconst invalidHeaderCharRegex = /[^\\t\\x20-\\x7e\\x80-\\xff]/;\nfunction validateName(name) {\n    name = `${name}`;\n    if (invalidTokenRegex.test(name) || name === \"\") {\n        throw new TypeError(`${name} is not a legal HTTP header name`);\n    }\n}\nfunction validateValue(value) {\n    value = `${value}`;\n    if (invalidHeaderCharRegex.test(value)) {\n        throw new TypeError(`${value} is not a legal HTTP header value`);\n    }\n}\n/**\n * Find the key in the map object given a header name.\n *\n * Returns undefined if not found.\n *\n * @param   String  name  Header name\n * @return  String|Undefined\n */ function find(map, name) {\n    name = name.toLowerCase();\n    for(const key in map){\n        if (key.toLowerCase() === name) {\n            return key;\n        }\n    }\n    return undefined;\n}\nconst MAP = Symbol(\"map\");\nclass Headers {\n    /**\n  * Headers class\n  *\n  * @param   Object  headers  Response headers\n  * @return  Void\n  */ constructor(){\n        let init = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : undefined;\n        this[MAP] = Object.create(null);\n        if (init instanceof Headers) {\n            const rawHeaders = init.raw();\n            const headerNames = Object.keys(rawHeaders);\n            for (const headerName of headerNames){\n                for (const value of rawHeaders[headerName]){\n                    this.append(headerName, value);\n                }\n            }\n            return;\n        }\n        // We don't worry about converting prop to ByteString here as append()\n        // will handle it.\n        if (init == null) ;\n        else if (typeof init === \"object\") {\n            const method = init[Symbol.iterator];\n            if (method != null) {\n                if (typeof method !== \"function\") {\n                    throw new TypeError(\"Header pairs must be iterable\");\n                }\n                // sequence<sequence<ByteString>>\n                // Note: per spec we have to first exhaust the lists then process them\n                const pairs = [];\n                for (const pair of init){\n                    if (typeof pair !== \"object\" || typeof pair[Symbol.iterator] !== \"function\") {\n                        throw new TypeError(\"Each header pair must be iterable\");\n                    }\n                    pairs.push(Array.from(pair));\n                }\n                for (const pair of pairs){\n                    if (pair.length !== 2) {\n                        throw new TypeError(\"Each header pair must be a name/value tuple\");\n                    }\n                    this.append(pair[0], pair[1]);\n                }\n            } else {\n                // record<ByteString, ByteString>\n                for (const key of Object.keys(init)){\n                    const value = init[key];\n                    this.append(key, value);\n                }\n            }\n        } else {\n            throw new TypeError(\"Provided initializer must be an object\");\n        }\n    }\n    /**\n  * Return combined header value given name\n  *\n  * @param   String  name  Header name\n  * @return  Mixed\n  */ get(name) {\n        name = `${name}`;\n        validateName(name);\n        const key = find(this[MAP], name);\n        if (key === undefined) {\n            return null;\n        }\n        return this[MAP][key].join(\", \");\n    }\n    /**\n  * Iterate over all headers\n  *\n  * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)\n  * @param   Boolean   thisArg   `this` context for callback function\n  * @return  Void\n  */ forEach(callback) {\n        let thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n        let pairs = getHeaders(this);\n        let i = 0;\n        while(i < pairs.length){\n            var _pairs$i = pairs[i];\n            const name = _pairs$i[0], value = _pairs$i[1];\n            callback.call(thisArg, value, name, this);\n            pairs = getHeaders(this);\n            i++;\n        }\n    }\n    /**\n  * Overwrite header values given name\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */ set(name, value) {\n        name = `${name}`;\n        value = `${value}`;\n        validateName(name);\n        validateValue(value);\n        const key = find(this[MAP], name);\n        this[MAP][key !== undefined ? key : name] = [\n            value\n        ];\n    }\n    /**\n  * Append a value onto existing header\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */ append(name, value) {\n        name = `${name}`;\n        value = `${value}`;\n        validateName(name);\n        validateValue(value);\n        const key = find(this[MAP], name);\n        if (key !== undefined) {\n            this[MAP][key].push(value);\n        } else {\n            this[MAP][name] = [\n                value\n            ];\n        }\n    }\n    /**\n  * Check for header name existence\n  *\n  * @param   String   name  Header name\n  * @return  Boolean\n  */ has(name) {\n        name = `${name}`;\n        validateName(name);\n        return find(this[MAP], name) !== undefined;\n    }\n    /**\n  * Delete all header values given name\n  *\n  * @param   String  name  Header name\n  * @return  Void\n  */ delete(name) {\n        name = `${name}`;\n        validateName(name);\n        const key = find(this[MAP], name);\n        if (key !== undefined) {\n            delete this[MAP][key];\n        }\n    }\n    /**\n  * Return raw headers (non-spec api)\n  *\n  * @return  Object\n  */ raw() {\n        return this[MAP];\n    }\n    /**\n  * Get an iterator on keys.\n  *\n  * @return  Iterator\n  */ keys() {\n        return createHeadersIterator(this, \"key\");\n    }\n    /**\n  * Get an iterator on values.\n  *\n  * @return  Iterator\n  */ values() {\n        return createHeadersIterator(this, \"value\");\n    }\n    /**\n  * Get an iterator on entries.\n  *\n  * This is the default iterator of the Headers object.\n  *\n  * @return  Iterator\n  */ [Symbol.iterator]() {\n        return createHeadersIterator(this, \"key+value\");\n    }\n}\nHeaders.prototype.entries = Headers.prototype[Symbol.iterator];\nObject.defineProperty(Headers.prototype, Symbol.toStringTag, {\n    value: \"Headers\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nObject.defineProperties(Headers.prototype, {\n    get: {\n        enumerable: true\n    },\n    forEach: {\n        enumerable: true\n    },\n    set: {\n        enumerable: true\n    },\n    append: {\n        enumerable: true\n    },\n    has: {\n        enumerable: true\n    },\n    delete: {\n        enumerable: true\n    },\n    keys: {\n        enumerable: true\n    },\n    values: {\n        enumerable: true\n    },\n    entries: {\n        enumerable: true\n    }\n});\nfunction getHeaders(headers) {\n    let kind = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"key+value\";\n    const keys = Object.keys(headers[MAP]).sort();\n    return keys.map(kind === \"key\" ? function(k) {\n        return k.toLowerCase();\n    } : kind === \"value\" ? function(k) {\n        return headers[MAP][k].join(\", \");\n    } : function(k) {\n        return [\n            k.toLowerCase(),\n            headers[MAP][k].join(\", \")\n        ];\n    });\n}\nconst INTERNAL = Symbol(\"internal\");\nfunction createHeadersIterator(target, kind) {\n    const iterator = Object.create(HeadersIteratorPrototype);\n    iterator[INTERNAL] = {\n        target,\n        kind,\n        index: 0\n    };\n    return iterator;\n}\nconst HeadersIteratorPrototype = Object.setPrototypeOf({\n    next () {\n        // istanbul ignore if\n        if (!this || Object.getPrototypeOf(this) !== HeadersIteratorPrototype) {\n            throw new TypeError(\"Value of `this` is not a HeadersIterator\");\n        }\n        var _INTERNAL = this[INTERNAL];\n        const target = _INTERNAL.target, kind = _INTERNAL.kind, index = _INTERNAL.index;\n        const values = getHeaders(target, kind);\n        const len = values.length;\n        if (index >= len) {\n            return {\n                value: undefined,\n                done: true\n            };\n        }\n        this[INTERNAL].index = index + 1;\n        return {\n            value: values[index],\n            done: false\n        };\n    }\n}, Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));\nObject.defineProperty(HeadersIteratorPrototype, Symbol.toStringTag, {\n    value: \"HeadersIterator\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\n/**\n * Export the Headers object in a form that Node.js can consume.\n *\n * @param   Headers  headers\n * @return  Object\n */ function exportNodeCompatibleHeaders(headers) {\n    const obj = Object.assign({\n        __proto__: null\n    }, headers[MAP]);\n    // http.request() only supports string as Host header. This hack makes\n    // specifying custom Host header possible.\n    const hostHeaderKey = find(headers[MAP], \"Host\");\n    if (hostHeaderKey !== undefined) {\n        obj[hostHeaderKey] = obj[hostHeaderKey][0];\n    }\n    return obj;\n}\n/**\n * Create a Headers object from an object of headers, ignoring those that do\n * not conform to HTTP grammar productions.\n *\n * @param   Object  obj  Object of headers\n * @return  Headers\n */ function createHeadersLenient(obj) {\n    const headers = new Headers();\n    for (const name of Object.keys(obj)){\n        if (invalidTokenRegex.test(name)) {\n            continue;\n        }\n        if (Array.isArray(obj[name])) {\n            for (const val of obj[name]){\n                if (invalidHeaderCharRegex.test(val)) {\n                    continue;\n                }\n                if (headers[MAP][name] === undefined) {\n                    headers[MAP][name] = [\n                        val\n                    ];\n                } else {\n                    headers[MAP][name].push(val);\n                }\n            }\n        } else if (!invalidHeaderCharRegex.test(obj[name])) {\n            headers[MAP][name] = [\n                obj[name]\n            ];\n        }\n    }\n    return headers;\n}\nconst INTERNALS$1 = Symbol(\"Response internals\");\n// fix an issue where \"STATUS_CODES\" aren't a named export for node <10\nconst STATUS_CODES = http__WEBPACK_IMPORTED_MODULE_1__.STATUS_CODES;\n/**\n * Response class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */ class Response {\n    constructor(){\n        let body = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n        let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        Body.call(this, body, opts);\n        const status = opts.status || 200;\n        const headers = new Headers(opts.headers);\n        if (body != null && !headers.has(\"Content-Type\")) {\n            const contentType = extractContentType(body);\n            if (contentType) {\n                headers.append(\"Content-Type\", contentType);\n            }\n        }\n        this[INTERNALS$1] = {\n            url: opts.url,\n            status,\n            statusText: opts.statusText || STATUS_CODES[status],\n            headers,\n            counter: opts.counter\n        };\n    }\n    get url() {\n        return this[INTERNALS$1].url || \"\";\n    }\n    get status() {\n        return this[INTERNALS$1].status;\n    }\n    /**\n  * Convenience property representing if the request ended normally\n  */ get ok() {\n        return this[INTERNALS$1].status >= 200 && this[INTERNALS$1].status < 300;\n    }\n    get redirected() {\n        return this[INTERNALS$1].counter > 0;\n    }\n    get statusText() {\n        return this[INTERNALS$1].statusText;\n    }\n    get headers() {\n        return this[INTERNALS$1].headers;\n    }\n    /**\n  * Clone this response\n  *\n  * @return  Response\n  */ clone() {\n        return new Response(clone(this), {\n            url: this.url,\n            status: this.status,\n            statusText: this.statusText,\n            headers: this.headers,\n            ok: this.ok,\n            redirected: this.redirected\n        });\n    }\n}\nBody.mixIn(Response.prototype);\nObject.defineProperties(Response.prototype, {\n    url: {\n        enumerable: true\n    },\n    status: {\n        enumerable: true\n    },\n    ok: {\n        enumerable: true\n    },\n    redirected: {\n        enumerable: true\n    },\n    statusText: {\n        enumerable: true\n    },\n    headers: {\n        enumerable: true\n    },\n    clone: {\n        enumerable: true\n    }\n});\nObject.defineProperty(Response.prototype, Symbol.toStringTag, {\n    value: \"Response\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nconst INTERNALS$2 = Symbol(\"Request internals\");\nconst URL = url__WEBPACK_IMPORTED_MODULE_2__.URL || whatwg_url__WEBPACK_IMPORTED_MODULE_3__.URL;\n// fix an issue where \"format\", \"parse\" aren't a named export for node <10\nconst parse_url = url__WEBPACK_IMPORTED_MODULE_2__.parse;\nconst format_url = url__WEBPACK_IMPORTED_MODULE_2__.format;\n/**\n * Wrapper around `new URL` to handle arbitrary URLs\n *\n * @param  {string} urlStr\n * @return {void}\n */ function parseURL(urlStr) {\n    /*\n \tCheck whether the URL is absolute or not\n \t\tScheme: https://tools.ietf.org/html/rfc3986#section-3.1\n \tAbsolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\n */ if (/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.exec(urlStr)) {\n        urlStr = new URL(urlStr).toString();\n    }\n    // Fallback to old implementation for arbitrary URLs\n    return parse_url(urlStr);\n}\nconst streamDestructionSupported = \"destroy\" in stream__WEBPACK_IMPORTED_MODULE_0__.Readable.prototype;\n/**\n * Check if a value is an instance of Request.\n *\n * @param   Mixed   input\n * @return  Boolean\n */ function isRequest(input) {\n    return typeof input === \"object\" && typeof input[INTERNALS$2] === \"object\";\n}\nfunction isAbortSignal(signal) {\n    const proto = signal && typeof signal === \"object\" && Object.getPrototypeOf(signal);\n    return !!(proto && proto.constructor.name === \"AbortSignal\");\n}\n/**\n * Request class\n *\n * @param   Mixed   input  Url or Request instance\n * @param   Object  init   Custom options\n * @return  Void\n */ class Request {\n    constructor(input){\n        let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        let parsedURL;\n        // normalize input\n        if (!isRequest(input)) {\n            if (input && input.href) {\n                // in order to support Node.js' Url objects; though WHATWG's URL objects\n                // will fall into this branch also (since their `toString()` will return\n                // `href` property anyway)\n                parsedURL = parseURL(input.href);\n            } else {\n                // coerce input to a string before attempting to parse\n                parsedURL = parseURL(`${input}`);\n            }\n            input = {};\n        } else {\n            parsedURL = parseURL(input.url);\n        }\n        let method = init.method || input.method || \"GET\";\n        method = method.toUpperCase();\n        if ((init.body != null || isRequest(input) && input.body !== null) && (method === \"GET\" || method === \"HEAD\")) {\n            throw new TypeError(\"Request with GET/HEAD method cannot have body\");\n        }\n        let inputBody = init.body != null ? init.body : isRequest(input) && input.body !== null ? clone(input) : null;\n        Body.call(this, inputBody, {\n            timeout: init.timeout || input.timeout || 0,\n            size: init.size || input.size || 0\n        });\n        const headers = new Headers(init.headers || input.headers || {});\n        if (inputBody != null && !headers.has(\"Content-Type\")) {\n            const contentType = extractContentType(inputBody);\n            if (contentType) {\n                headers.append(\"Content-Type\", contentType);\n            }\n        }\n        let signal = isRequest(input) ? input.signal : null;\n        if (\"signal\" in init) signal = init.signal;\n        if (signal != null && !isAbortSignal(signal)) {\n            throw new TypeError(\"Expected signal to be an instanceof AbortSignal\");\n        }\n        this[INTERNALS$2] = {\n            method,\n            redirect: init.redirect || input.redirect || \"follow\",\n            headers,\n            parsedURL,\n            signal\n        };\n        // node-fetch-only options\n        this.follow = init.follow !== undefined ? init.follow : input.follow !== undefined ? input.follow : 20;\n        this.compress = init.compress !== undefined ? init.compress : input.compress !== undefined ? input.compress : true;\n        this.counter = init.counter || input.counter || 0;\n        this.agent = init.agent || input.agent;\n    }\n    get method() {\n        return this[INTERNALS$2].method;\n    }\n    get url() {\n        return format_url(this[INTERNALS$2].parsedURL);\n    }\n    get headers() {\n        return this[INTERNALS$2].headers;\n    }\n    get redirect() {\n        return this[INTERNALS$2].redirect;\n    }\n    get signal() {\n        return this[INTERNALS$2].signal;\n    }\n    /**\n  * Clone this request\n  *\n  * @return  Request\n  */ clone() {\n        return new Request(this);\n    }\n}\nBody.mixIn(Request.prototype);\nObject.defineProperty(Request.prototype, Symbol.toStringTag, {\n    value: \"Request\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nObject.defineProperties(Request.prototype, {\n    method: {\n        enumerable: true\n    },\n    url: {\n        enumerable: true\n    },\n    headers: {\n        enumerable: true\n    },\n    redirect: {\n        enumerable: true\n    },\n    clone: {\n        enumerable: true\n    },\n    signal: {\n        enumerable: true\n    }\n});\n/**\n * Convert a Request to Node.js http request options.\n *\n * @param   Request  A Request instance\n * @return  Object   The options object to be passed to http.request\n */ function getNodeRequestOptions(request) {\n    const parsedURL = request[INTERNALS$2].parsedURL;\n    const headers = new Headers(request[INTERNALS$2].headers);\n    // fetch step 1.3\n    if (!headers.has(\"Accept\")) {\n        headers.set(\"Accept\", \"*/*\");\n    }\n    // Basic fetch\n    if (!parsedURL.protocol || !parsedURL.hostname) {\n        throw new TypeError(\"Only absolute URLs are supported\");\n    }\n    if (!/^https?:$/.test(parsedURL.protocol)) {\n        throw new TypeError(\"Only HTTP(S) protocols are supported\");\n    }\n    if (request.signal && request.body instanceof stream__WEBPACK_IMPORTED_MODULE_0__.Readable && !streamDestructionSupported) {\n        throw new Error(\"Cancellation of streamed requests with AbortSignal is not supported in node < 8\");\n    }\n    // HTTP-network-or-cache fetch steps 2.4-2.7\n    let contentLengthValue = null;\n    if (request.body == null && /^(POST|PUT)$/i.test(request.method)) {\n        contentLengthValue = \"0\";\n    }\n    if (request.body != null) {\n        const totalBytes = getTotalBytes(request);\n        if (typeof totalBytes === \"number\") {\n            contentLengthValue = String(totalBytes);\n        }\n    }\n    if (contentLengthValue) {\n        headers.set(\"Content-Length\", contentLengthValue);\n    }\n    // HTTP-network-or-cache fetch step 2.11\n    if (!headers.has(\"User-Agent\")) {\n        headers.set(\"User-Agent\", \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\");\n    }\n    // HTTP-network-or-cache fetch step 2.15\n    if (request.compress && !headers.has(\"Accept-Encoding\")) {\n        headers.set(\"Accept-Encoding\", \"gzip,deflate\");\n    }\n    let agent = request.agent;\n    if (typeof agent === \"function\") {\n        agent = agent(parsedURL);\n    }\n    // HTTP-network fetch step 4.2\n    // chunked encoding is handled by Node.js\n    return Object.assign({}, parsedURL, {\n        method: request.method,\n        headers: exportNodeCompatibleHeaders(headers),\n        agent\n    });\n}\n/**\n * abort-error.js\n *\n * AbortError interface for cancelled requests\n */ /**\n * Create AbortError instance\n *\n * @param   String      message      Error message for human\n * @return  AbortError\n */ function AbortError(message) {\n    Error.call(this, message);\n    this.type = \"aborted\";\n    this.message = message;\n    // hide custom error implementation details from end-users\n    Error.captureStackTrace(this, this.constructor);\n}\nAbortError.prototype = Object.create(Error.prototype);\nAbortError.prototype.constructor = AbortError;\nAbortError.prototype.name = \"AbortError\";\nconst URL$1 = url__WEBPACK_IMPORTED_MODULE_2__.URL || whatwg_url__WEBPACK_IMPORTED_MODULE_3__.URL;\n// fix an issue where \"PassThrough\", \"resolve\" aren't a named export for node <10\nconst PassThrough$1 = stream__WEBPACK_IMPORTED_MODULE_0__.PassThrough;\nconst isDomainOrSubdomain = function isDomainOrSubdomain(destination, original) {\n    const orig = new URL$1(original).hostname;\n    const dest = new URL$1(destination).hostname;\n    return orig === dest || orig[orig.length - dest.length - 1] === \".\" && orig.endsWith(dest);\n};\n/**\n * isSameProtocol reports whether the two provided URLs use the same protocol.\n *\n * Both domains must already be in canonical form.\n * @param {string|URL} original\n * @param {string|URL} destination\n */ const isSameProtocol = function isSameProtocol(destination, original) {\n    const orig = new URL$1(original).protocol;\n    const dest = new URL$1(destination).protocol;\n    return orig === dest;\n};\n/**\n * Fetch function\n *\n * @param   Mixed    url   Absolute url or Request instance\n * @param   Object   opts  Fetch options\n * @return  Promise\n */ function fetch(url, opts) {\n    // allow custom promise\n    if (!fetch.Promise) {\n        throw new Error(\"native promise missing, set fetch.Promise to your favorite alternative\");\n    }\n    Body.Promise = fetch.Promise;\n    // wrap http.request into fetch\n    return new fetch.Promise(function(resolve, reject) {\n        // build request object\n        const request = new Request(url, opts);\n        const options = getNodeRequestOptions(request);\n        const send = (options.protocol === \"https:\" ? https__WEBPACK_IMPORTED_MODULE_4__ : http__WEBPACK_IMPORTED_MODULE_1__).request;\n        const signal = request.signal;\n        let response = null;\n        const abort = function abort() {\n            let error = new AbortError(\"The user aborted a request.\");\n            reject(error);\n            if (request.body && request.body instanceof stream__WEBPACK_IMPORTED_MODULE_0__.Readable) {\n                destroyStream(request.body, error);\n            }\n            if (!response || !response.body) return;\n            response.body.emit(\"error\", error);\n        };\n        if (signal && signal.aborted) {\n            abort();\n            return;\n        }\n        const abortAndFinalize = function abortAndFinalize() {\n            abort();\n            finalize();\n        };\n        // send request\n        const req = send(options);\n        let reqTimeout;\n        if (signal) {\n            signal.addEventListener(\"abort\", abortAndFinalize);\n        }\n        function finalize() {\n            req.abort();\n            if (signal) signal.removeEventListener(\"abort\", abortAndFinalize);\n            clearTimeout(reqTimeout);\n        }\n        if (request.timeout) {\n            req.once(\"socket\", function(socket) {\n                reqTimeout = setTimeout(function() {\n                    reject(new FetchError(`network timeout at: ${request.url}`, \"request-timeout\"));\n                    finalize();\n                }, request.timeout);\n            });\n        }\n        req.on(\"error\", function(err) {\n            reject(new FetchError(`request to ${request.url} failed, reason: ${err.message}`, \"system\", err));\n            if (response && response.body) {\n                destroyStream(response.body, err);\n            }\n            finalize();\n        });\n        fixResponseChunkedTransferBadEnding(req, function(err) {\n            if (signal && signal.aborted) {\n                return;\n            }\n            if (response && response.body) {\n                destroyStream(response.body, err);\n            }\n        });\n        /* c8 ignore next 18 */ if (parseInt(process.version.substring(1)) < 14) {\n            // Before Node.js 14, pipeline() does not fully support async iterators and does not always\n            // properly handle when the socket close/end events are out of order.\n            req.on(\"socket\", function(s) {\n                s.addListener(\"close\", function(hadError) {\n                    // if a data listener is still present we didn't end cleanly\n                    const hasDataListener = s.listenerCount(\"data\") > 0;\n                    // if end happened before close but the socket didn't emit an error, do it now\n                    if (response && hasDataListener && !hadError && !(signal && signal.aborted)) {\n                        const err = new Error(\"Premature close\");\n                        err.code = \"ERR_STREAM_PREMATURE_CLOSE\";\n                        response.body.emit(\"error\", err);\n                    }\n                });\n            });\n        }\n        req.on(\"response\", function(res) {\n            clearTimeout(reqTimeout);\n            const headers = createHeadersLenient(res.headers);\n            // HTTP fetch step 5\n            if (fetch.isRedirect(res.statusCode)) {\n                // HTTP fetch step 5.2\n                const location = headers.get(\"Location\");\n                // HTTP fetch step 5.3\n                let locationURL = null;\n                try {\n                    locationURL = location === null ? null : new URL$1(location, request.url).toString();\n                } catch (err) {\n                    // error here can only be invalid URL in Location: header\n                    // do not throw when options.redirect == manual\n                    // let the user extract the errorneous redirect URL\n                    if (request.redirect !== \"manual\") {\n                        reject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, \"invalid-redirect\"));\n                        finalize();\n                        return;\n                    }\n                }\n                // HTTP fetch step 5.5\n                switch(request.redirect){\n                    case \"error\":\n                        reject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, \"no-redirect\"));\n                        finalize();\n                        return;\n                    case \"manual\":\n                        // node-fetch-specific step: make manual redirect a bit easier to use by setting the Location header value to the resolved URL.\n                        if (locationURL !== null) {\n                            // handle corrupted header\n                            try {\n                                headers.set(\"Location\", locationURL);\n                            } catch (err) {\n                                // istanbul ignore next: nodejs server prevent invalid response headers, we can't test this through normal request\n                                reject(err);\n                            }\n                        }\n                        break;\n                    case \"follow\":\n                        // HTTP-redirect fetch step 2\n                        if (locationURL === null) {\n                            break;\n                        }\n                        // HTTP-redirect fetch step 5\n                        if (request.counter >= request.follow) {\n                            reject(new FetchError(`maximum redirect reached at: ${request.url}`, \"max-redirect\"));\n                            finalize();\n                            return;\n                        }\n                        // HTTP-redirect fetch step 6 (counter increment)\n                        // Create a new Request object.\n                        const requestOpts = {\n                            headers: new Headers(request.headers),\n                            follow: request.follow,\n                            counter: request.counter + 1,\n                            agent: request.agent,\n                            compress: request.compress,\n                            method: request.method,\n                            body: request.body,\n                            signal: request.signal,\n                            timeout: request.timeout,\n                            size: request.size\n                        };\n                        if (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {\n                            for (const name of [\n                                \"authorization\",\n                                \"www-authenticate\",\n                                \"cookie\",\n                                \"cookie2\"\n                            ]){\n                                requestOpts.headers.delete(name);\n                            }\n                        }\n                        // HTTP-redirect fetch step 9\n                        if (res.statusCode !== 303 && request.body && getTotalBytes(request) === null) {\n                            reject(new FetchError(\"Cannot follow redirect with body being a readable stream\", \"unsupported-redirect\"));\n                            finalize();\n                            return;\n                        }\n                        // HTTP-redirect fetch step 11\n                        if (res.statusCode === 303 || (res.statusCode === 301 || res.statusCode === 302) && request.method === \"POST\") {\n                            requestOpts.method = \"GET\";\n                            requestOpts.body = undefined;\n                            requestOpts.headers.delete(\"content-length\");\n                        }\n                        // HTTP-redirect fetch step 15\n                        resolve(fetch(new Request(locationURL, requestOpts)));\n                        finalize();\n                        return;\n                }\n            }\n            // prepare response\n            res.once(\"end\", function() {\n                if (signal) signal.removeEventListener(\"abort\", abortAndFinalize);\n            });\n            let body = res.pipe(new PassThrough$1());\n            const response_options = {\n                url: request.url,\n                status: res.statusCode,\n                statusText: res.statusMessage,\n                headers: headers,\n                size: request.size,\n                timeout: request.timeout,\n                counter: request.counter\n            };\n            // HTTP-network fetch step ********\n            const codings = headers.get(\"Content-Encoding\");\n            // HTTP-network fetch step ********: handle content codings\n            // in following scenarios we ignore compression support\n            // 1. compression support is disabled\n            // 2. HEAD request\n            // 3. no Content-Encoding header\n            // 4. no content response (204)\n            // 5. content not modified response (304)\n            if (!request.compress || request.method === \"HEAD\" || codings === null || res.statusCode === 204 || res.statusCode === 304) {\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // For Node v6+\n            // Be less strict when decoding compressed responses, since sometimes\n            // servers send slightly invalid responses that are still accepted\n            // by common browsers.\n            // Always using Z_SYNC_FLUSH is what cURL does.\n            const zlibOptions = {\n                flush: zlib__WEBPACK_IMPORTED_MODULE_5__.Z_SYNC_FLUSH,\n                finishFlush: zlib__WEBPACK_IMPORTED_MODULE_5__.Z_SYNC_FLUSH\n            };\n            // for gzip\n            if (codings == \"gzip\" || codings == \"x-gzip\") {\n                body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createGunzip(zlibOptions));\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // for deflate\n            if (codings == \"deflate\" || codings == \"x-deflate\") {\n                // handle the infamous raw deflate response from old servers\n                // a hack for old IIS and Apache servers\n                const raw = res.pipe(new PassThrough$1());\n                raw.once(\"data\", function(chunk) {\n                    // see http://stackoverflow.com/questions/37519828\n                    if ((chunk[0] & 0x0F) === 0x08) {\n                        body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createInflate());\n                    } else {\n                        body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createInflateRaw());\n                    }\n                    response = new Response(body, response_options);\n                    resolve(response);\n                });\n                raw.on(\"end\", function() {\n                    // some old IIS servers return zero-length OK deflate responses, so 'data' is never emitted.\n                    if (!response) {\n                        response = new Response(body, response_options);\n                        resolve(response);\n                    }\n                });\n                return;\n            }\n            // for br\n            if (codings == \"br\" && typeof zlib__WEBPACK_IMPORTED_MODULE_5__.createBrotliDecompress === \"function\") {\n                body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createBrotliDecompress());\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // otherwise, use response as-is\n            response = new Response(body, response_options);\n            resolve(response);\n        });\n        writeToStream(req, request);\n    });\n}\nfunction fixResponseChunkedTransferBadEnding(request, errorCallback) {\n    let socket;\n    request.on(\"socket\", function(s) {\n        socket = s;\n    });\n    request.on(\"response\", function(response) {\n        const headers = response.headers;\n        if (headers[\"transfer-encoding\"] === \"chunked\" && !headers[\"content-length\"]) {\n            response.once(\"close\", function(hadError) {\n                // tests for socket presence, as in some situations the\n                // the 'socket' event is not triggered for the request\n                // (happens in deno), avoids `TypeError`\n                // if a data listener is still present we didn't end cleanly\n                const hasDataListener = socket && socket.listenerCount(\"data\") > 0;\n                if (hasDataListener && !hadError) {\n                    const err = new Error(\"Premature close\");\n                    err.code = \"ERR_STREAM_PREMATURE_CLOSE\";\n                    errorCallback(err);\n                }\n            });\n        }\n    });\n}\nfunction destroyStream(stream, err) {\n    if (stream.destroy) {\n        stream.destroy(err);\n    } else {\n        // node < 8\n        stream.emit(\"error\", err);\n        stream.end();\n    }\n}\n/**\n * Redirect code matching\n *\n * @param   Number   code  Status code\n * @return  Boolean\n */ fetch.isRedirect = function(code) {\n    return code === 301 || code === 302 || code === 303 || code === 307 || code === 308;\n};\n// expose Promise\nfetch.Promise = global.Promise;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (fetch);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/node-fetch/lib/index.mjs\n");

/***/ })

};
;