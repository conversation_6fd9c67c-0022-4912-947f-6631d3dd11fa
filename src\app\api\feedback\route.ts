import { NextRequest, NextResponse } from 'next/server'
import { TradingAnalysisEngine } from '@/lib/analysisEngine'
import { database } from '@/lib/database'

const analysisEngine = new TradingAnalysisEngine()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { analysisId, accuracy, profitability, comments, actualOutcome } = body

    // Validate required fields
    if (!analysisId || typeof accuracy !== 'number' || typeof profitability !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields: analysisId, accuracy, profitability' },
        { status: 400 }
      )
    }

    // Validate rating ranges
    if (accuracy < 1 || accuracy > 5 || profitability < 1 || profitability > 5) {
      return NextResponse.json(
        { error: 'Accuracy and profitability ratings must be between 1 and 5' },
        { status: 400 }
      )
    }

    // Store feedback in database
    await database.storeFeedback({
      analysisId,
      accuracy,
      profitability,
      comments,
      actualOutcome
    })

    // Store feedback for learning
    analysisEngine.adaptAnalysisBasedOnFeedback(analysisId, {
      accuracy,
      profitability,
      actualOutcome
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Feedback received successfully. Thank you for helping improve our analysis!' 
    })

  } catch (error) {
    console.error('Feedback API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to process feedback',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Feedback endpoint is working. Use POST to submit feedback.',
    requiredFields: {
      analysisId: 'string',
      accuracy: 'number (1-5)',
      profitability: 'number (1-5)',
      comments: 'string (optional)',
      actualOutcome: {
        entryHit: 'boolean (optional)',
        stopLossHit: 'boolean (optional)',
        takeProfitsHit: {
          tp1: 'boolean (optional)',
          tp2: 'boolean (optional)',
          tp3: 'boolean (optional)'
        }
      }
    }
  })
}
