"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AnalysisResults.tsx":
/*!********************************************!*\
  !*** ./src/components/AnalysisResults.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnalysisResults; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AnalysisResults(param) {\n    let { results } = param;\n    _s();\n    const [showFeedbackModal, setShowFeedbackModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getDirectionColor = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return \"text-green-500\";\n            case \"SELL\":\n                return \"text-red-500\";\n            default:\n                return \"text-yellow-500\";\n        }\n    };\n    const getDirectionIcon = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 26\n                }, this);\n            case \"SELL\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatPrice = (price)=>{\n        return price.toFixed(2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white\",\n                        children: \"Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 \".concat(getDirectionColor(results.direction)),\n                                children: [\n                                    getDirectionIcon(results.direction),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: results.direction\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            results.confidence,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 ml-1\",\n                                        children: \"confidence\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Trade Setup\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Entry Price:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.entry)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Stop Loss:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.stopLoss)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP1:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP2:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP3:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp3)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Risk/Reward Ratios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP1 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp1\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP2 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp2\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP3 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp3\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Strategy:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white capitalize\",\n                                                        children: results.strategy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Timeframe:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: results.timeframe\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold text-white\",\n                        children: \"Detailed Analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Market Structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.marketStructure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Risk Assessment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.riskAssessment\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    results.reasoning.orderBlocks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Order Blocks Identified\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.orderBlocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            block\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.fairValueGaps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Fair Value Gaps\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.fairValueGaps.map((gap, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this),\n                                            gap\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.ictConcepts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"ICT Concepts Applied\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.ictConcepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            concept\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500 text-sm flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    \"Analysis completed: \",\n                    new Date(results.timestamp).toLocaleString()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalysisResults, \"sGCCH0LhJqWqr/mG3EidXhQdhzI=\");\n_c = AnalysisResults;\nvar _c;\n$RefreshReg$(_c, \"AnalysisResults\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AnalysisResults.tsx\n"));

/***/ })

});