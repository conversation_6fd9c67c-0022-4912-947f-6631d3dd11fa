"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/strtok3";
exports.ids = ["vendor-chunks/strtok3"];
exports.modules = {

/***/ "(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js":
/*!*******************************************************!*\
  !*** ./node_modules/strtok3/lib/AbstractTokenizer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.AbstractTokenizer = void 0;\nconst peek_readable_1 = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\n/**\r\n * Core tokenizer\r\n */ class AbstractTokenizer {\n    constructor(fileInfo){\n        /**\r\n         * Tokenizer-stream position\r\n         */ this.position = 0;\n        this.numBuffer = new Uint8Array(8);\n        this.fileInfo = fileInfo ? fileInfo : {};\n    }\n    /**\r\n     * Read a token from the tokenizer-stream\r\n     * @param token - The token to read\r\n     * @param position - If provided, the desired position in the tokenizer-stream\r\n     * @returns Promise with token data\r\n     */ async readToken(token, position = this.position) {\n        const uint8Array = Buffer.alloc(token.len);\n        const len = await this.readBuffer(uint8Array, {\n            position\n        });\n        if (len < token.len) throw new peek_readable_1.EndOfStreamError();\n        return token.get(uint8Array, 0);\n    }\n    /**\r\n     * Peek a token from the tokenizer-stream.\r\n     * @param token - Token to peek from the tokenizer-stream.\r\n     * @param position - Offset where to begin reading within the file. If position is null, data will be read from the current file position.\r\n     * @returns Promise with token data\r\n     */ async peekToken(token, position = this.position) {\n        const uint8Array = Buffer.alloc(token.len);\n        const len = await this.peekBuffer(uint8Array, {\n            position\n        });\n        if (len < token.len) throw new peek_readable_1.EndOfStreamError();\n        return token.get(uint8Array, 0);\n    }\n    /**\r\n     * Read a numeric token from the stream\r\n     * @param token - Numeric token\r\n     * @returns Promise with number\r\n     */ async readNumber(token) {\n        const len = await this.readBuffer(this.numBuffer, {\n            length: token.len\n        });\n        if (len < token.len) throw new peek_readable_1.EndOfStreamError();\n        return token.get(this.numBuffer, 0);\n    }\n    /**\r\n     * Read a numeric token from the stream\r\n     * @param token - Numeric token\r\n     * @returns Promise with number\r\n     */ async peekNumber(token) {\n        const len = await this.peekBuffer(this.numBuffer, {\n            length: token.len\n        });\n        if (len < token.len) throw new peek_readable_1.EndOfStreamError();\n        return token.get(this.numBuffer, 0);\n    }\n    /**\r\n     * Ignore number of bytes, advances the pointer in under tokenizer-stream.\r\n     * @param length - Number of bytes to ignore\r\n     * @return resolves the number of bytes ignored, equals length if this available, otherwise the number of bytes available\r\n     */ async ignore(length) {\n        if (this.fileInfo.size !== undefined) {\n            const bytesLeft = this.fileInfo.size - this.position;\n            if (length > bytesLeft) {\n                this.position += bytesLeft;\n                return bytesLeft;\n            }\n        }\n        this.position += length;\n        return length;\n    }\n    async close() {\n    // empty\n    }\n    normalizeOptions(uint8Array, options) {\n        if (options && options.position !== undefined && options.position < this.position) {\n            throw new Error(\"`options.position` must be equal or greater than `tokenizer.position`\");\n        }\n        if (options) {\n            return {\n                mayBeLess: options.mayBeLess === true,\n                offset: options.offset ? options.offset : 0,\n                length: options.length ? options.length : uint8Array.length - (options.offset ? options.offset : 0),\n                position: options.position ? options.position : this.position\n            };\n        }\n        return {\n            mayBeLess: false,\n            offset: 0,\n            length: uint8Array.length,\n            position: this.position\n        };\n    }\n}\nexports.AbstractTokenizer = AbstractTokenizer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/BufferTokenizer.js":
/*!*****************************************************!*\
  !*** ./node_modules/strtok3/lib/BufferTokenizer.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.BufferTokenizer = void 0;\nconst peek_readable_1 = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\nconst AbstractTokenizer_1 = __webpack_require__(/*! ./AbstractTokenizer */ \"(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\");\nclass BufferTokenizer extends AbstractTokenizer_1.AbstractTokenizer {\n    /**\r\n     * Construct BufferTokenizer\r\n     * @param uint8Array - Uint8Array to tokenize\r\n     * @param fileInfo - Pass additional file information to the tokenizer\r\n     */ constructor(uint8Array, fileInfo){\n        super(fileInfo);\n        this.uint8Array = uint8Array;\n        this.fileInfo.size = this.fileInfo.size ? this.fileInfo.size : uint8Array.length;\n    }\n    /**\r\n     * Read buffer from tokenizer\r\n     * @param uint8Array - Uint8Array to tokenize\r\n     * @param options - Read behaviour options\r\n     * @returns {Promise<number>}\r\n     */ async readBuffer(uint8Array, options) {\n        if (options && options.position) {\n            if (options.position < this.position) {\n                throw new Error(\"`options.position` must be equal or greater than `tokenizer.position`\");\n            }\n            this.position = options.position;\n        }\n        const bytesRead = await this.peekBuffer(uint8Array, options);\n        this.position += bytesRead;\n        return bytesRead;\n    }\n    /**\r\n     * Peek (read ahead) buffer from tokenizer\r\n     * @param uint8Array\r\n     * @param options - Read behaviour options\r\n     * @returns {Promise<number>}\r\n     */ async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const bytes2read = Math.min(this.uint8Array.length - normOptions.position, normOptions.length);\n        if (!normOptions.mayBeLess && bytes2read < normOptions.length) {\n            throw new peek_readable_1.EndOfStreamError();\n        } else {\n            uint8Array.set(this.uint8Array.subarray(normOptions.position, normOptions.position + bytes2read), normOptions.offset);\n            return bytes2read;\n        }\n    }\n    async close() {\n    // empty\n    }\n}\nexports.BufferTokenizer = BufferTokenizer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/BufferTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/FileTokenizer.js":
/*!***************************************************!*\
  !*** ./node_modules/strtok3/lib/FileTokenizer.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.fromFile = exports.FileTokenizer = void 0;\nconst AbstractTokenizer_1 = __webpack_require__(/*! ./AbstractTokenizer */ \"(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\");\nconst peek_readable_1 = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\nconst fs = __webpack_require__(/*! ./FsPromise */ \"(rsc)/./node_modules/strtok3/lib/FsPromise.js\");\nclass FileTokenizer extends AbstractTokenizer_1.AbstractTokenizer {\n    constructor(fd, fileInfo){\n        super(fileInfo);\n        this.fd = fd;\n    }\n    /**\r\n     * Read buffer from file\r\n     * @param uint8Array - Uint8Array to write result to\r\n     * @param options - Read behaviour options\r\n     * @returns Promise number of bytes read\r\n     */ async readBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        this.position = normOptions.position;\n        const res = await fs.read(this.fd, uint8Array, normOptions.offset, normOptions.length, normOptions.position);\n        this.position += res.bytesRead;\n        if (res.bytesRead < normOptions.length && (!options || !options.mayBeLess)) {\n            throw new peek_readable_1.EndOfStreamError();\n        }\n        return res.bytesRead;\n    }\n    /**\r\n     * Peek buffer from file\r\n     * @param uint8Array - Uint8Array (or Buffer) to write data to\r\n     * @param options - Read behaviour options\r\n     * @returns Promise number of bytes read\r\n     */ async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const res = await fs.read(this.fd, uint8Array, normOptions.offset, normOptions.length, normOptions.position);\n        if (!normOptions.mayBeLess && res.bytesRead < normOptions.length) {\n            throw new peek_readable_1.EndOfStreamError();\n        }\n        return res.bytesRead;\n    }\n    async close() {\n        return fs.close(this.fd);\n    }\n}\nexports.FileTokenizer = FileTokenizer;\nasync function fromFile(sourceFilePath) {\n    const stat = await fs.stat(sourceFilePath);\n    if (!stat.isFile) {\n        throw new Error(`File not a file: ${sourceFilePath}`);\n    }\n    const fd = await fs.open(sourceFilePath, \"r\");\n    return new FileTokenizer(fd, {\n        path: sourceFilePath,\n        size: stat.size\n    });\n}\nexports.fromFile = fromFile;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/FileTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/FsPromise.js":
/*!***********************************************!*\
  !*** ./node_modules/strtok3/lib/FsPromise.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\r\n * Module convert fs functions to promise based functions\r\n */ Object.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.readFile = exports.writeFileSync = exports.writeFile = exports.read = exports.open = exports.close = exports.stat = exports.createReadStream = exports.pathExists = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nexports.pathExists = fs.existsSync;\nexports.createReadStream = fs.createReadStream;\nasync function stat(path) {\n    return new Promise((resolve, reject)=>{\n        fs.stat(path, (err, stats)=>{\n            if (err) reject(err);\n            else resolve(stats);\n        });\n    });\n}\nexports.stat = stat;\nasync function close(fd) {\n    return new Promise((resolve, reject)=>{\n        fs.close(fd, (err)=>{\n            if (err) reject(err);\n            else resolve();\n        });\n    });\n}\nexports.close = close;\nasync function open(path, mode) {\n    return new Promise((resolve, reject)=>{\n        fs.open(path, mode, (err, fd)=>{\n            if (err) reject(err);\n            else resolve(fd);\n        });\n    });\n}\nexports.open = open;\nasync function read(fd, buffer, offset, length, position) {\n    return new Promise((resolve, reject)=>{\n        fs.read(fd, buffer, offset, length, position, (err, bytesRead, _buffer)=>{\n            if (err) reject(err);\n            else resolve({\n                bytesRead,\n                buffer: _buffer\n            });\n        });\n    });\n}\nexports.read = read;\nasync function writeFile(path, data) {\n    return new Promise((resolve, reject)=>{\n        fs.writeFile(path, data, (err)=>{\n            if (err) reject(err);\n            else resolve();\n        });\n    });\n}\nexports.writeFile = writeFile;\nfunction writeFileSync(path, data) {\n    fs.writeFileSync(path, data);\n}\nexports.writeFileSync = writeFileSync;\nasync function readFile(path) {\n    return new Promise((resolve, reject)=>{\n        fs.readFile(path, (err, buffer)=>{\n            if (err) reject(err);\n            else resolve(buffer);\n        });\n    });\n}\nexports.readFile = readFile;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/FsPromise.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/ReadStreamTokenizer.js":
/*!*********************************************************!*\
  !*** ./node_modules/strtok3/lib/ReadStreamTokenizer.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.ReadStreamTokenizer = void 0;\nconst AbstractTokenizer_1 = __webpack_require__(/*! ./AbstractTokenizer */ \"(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\");\nconst peek_readable_1 = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\nconst maxBufferSize = 256000;\nclass ReadStreamTokenizer extends AbstractTokenizer_1.AbstractTokenizer {\n    constructor(stream, fileInfo){\n        super(fileInfo);\n        this.streamReader = new peek_readable_1.StreamReader(stream);\n    }\n    /**\r\n     * Get file information, an HTTP-client may implement this doing a HEAD request\r\n     * @return Promise with file information\r\n     */ async getFileInfo() {\n        return this.fileInfo;\n    }\n    /**\r\n     * Read buffer from tokenizer\r\n     * @param uint8Array - Target Uint8Array to fill with data read from the tokenizer-stream\r\n     * @param options - Read behaviour options\r\n     * @returns Promise with number of bytes read\r\n     */ async readBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const skipBytes = normOptions.position - this.position;\n        if (skipBytes > 0) {\n            await this.ignore(skipBytes);\n            return this.readBuffer(uint8Array, options);\n        } else if (skipBytes < 0) {\n            throw new Error(\"`options.position` must be equal or greater than `tokenizer.position`\");\n        }\n        if (normOptions.length === 0) {\n            return 0;\n        }\n        const bytesRead = await this.streamReader.read(uint8Array, normOptions.offset, normOptions.length);\n        this.position += bytesRead;\n        if ((!options || !options.mayBeLess) && bytesRead < normOptions.length) {\n            throw new peek_readable_1.EndOfStreamError();\n        }\n        return bytesRead;\n    }\n    /**\r\n     * Peek (read ahead) buffer from tokenizer\r\n     * @param uint8Array - Uint8Array (or Buffer) to write data to\r\n     * @param options - Read behaviour options\r\n     * @returns Promise with number of bytes peeked\r\n     */ async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        let bytesRead = 0;\n        if (normOptions.position) {\n            const skipBytes = normOptions.position - this.position;\n            if (skipBytes > 0) {\n                const skipBuffer = new Uint8Array(normOptions.length + skipBytes);\n                bytesRead = await this.peekBuffer(skipBuffer, {\n                    mayBeLess: normOptions.mayBeLess\n                });\n                uint8Array.set(skipBuffer.subarray(skipBytes), normOptions.offset);\n                return bytesRead - skipBytes;\n            } else if (skipBytes < 0) {\n                throw new Error(\"Cannot peek from a negative offset in a stream\");\n            }\n        }\n        if (normOptions.length > 0) {\n            try {\n                bytesRead = await this.streamReader.peek(uint8Array, normOptions.offset, normOptions.length);\n            } catch (err) {\n                if (options && options.mayBeLess && err instanceof peek_readable_1.EndOfStreamError) {\n                    return 0;\n                }\n                throw err;\n            }\n            if (!normOptions.mayBeLess && bytesRead < normOptions.length) {\n                throw new peek_readable_1.EndOfStreamError();\n            }\n        }\n        return bytesRead;\n    }\n    async ignore(length) {\n        // debug(`ignore ${this.position}...${this.position + length - 1}`);\n        const bufSize = Math.min(maxBufferSize, length);\n        const buf = new Uint8Array(bufSize);\n        let totBytesRead = 0;\n        while(totBytesRead < length){\n            const remaining = length - totBytesRead;\n            const bytesRead = await this.readBuffer(buf, {\n                length: Math.min(bufSize, remaining)\n            });\n            if (bytesRead < 0) {\n                return bytesRead;\n            }\n            totBytesRead += bytesRead;\n        }\n        return totBytesRead;\n    }\n}\nexports.ReadStreamTokenizer = ReadStreamTokenizer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/ReadStreamTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/core.js":
/*!******************************************!*\
  !*** ./node_modules/strtok3/lib/core.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.fromBuffer = exports.fromStream = exports.EndOfStreamError = void 0;\nconst ReadStreamTokenizer_1 = __webpack_require__(/*! ./ReadStreamTokenizer */ \"(rsc)/./node_modules/strtok3/lib/ReadStreamTokenizer.js\");\nconst BufferTokenizer_1 = __webpack_require__(/*! ./BufferTokenizer */ \"(rsc)/./node_modules/strtok3/lib/BufferTokenizer.js\");\nvar peek_readable_1 = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\nObject.defineProperty(exports, \"EndOfStreamError\", ({\n    enumerable: true,\n    get: function() {\n        return peek_readable_1.EndOfStreamError;\n    }\n}));\n/**\r\n * Construct ReadStreamTokenizer from given Stream.\r\n * Will set fileSize, if provided given Stream has set the .path property/\r\n * @param stream - Read from Node.js Stream.Readable\r\n * @param fileInfo - Pass the file information, like size and MIME-type of the corresponding stream.\r\n * @returns ReadStreamTokenizer\r\n */ function fromStream(stream, fileInfo) {\n    fileInfo = fileInfo ? fileInfo : {};\n    return new ReadStreamTokenizer_1.ReadStreamTokenizer(stream, fileInfo);\n}\nexports.fromStream = fromStream;\n/**\r\n * Construct ReadStreamTokenizer from given Buffer.\r\n * @param uint8Array - Uint8Array to tokenize\r\n * @param fileInfo - Pass additional file information to the tokenizer\r\n * @returns BufferTokenizer\r\n */ function fromBuffer(uint8Array, fileInfo) {\n    return new BufferTokenizer_1.BufferTokenizer(uint8Array, fileInfo);\n}\nexports.fromBuffer = fromBuffer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/strtok3/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.fromStream = exports.fromBuffer = exports.EndOfStreamError = exports.fromFile = void 0;\nconst fs = __webpack_require__(/*! ./FsPromise */ \"(rsc)/./node_modules/strtok3/lib/FsPromise.js\");\nconst core = __webpack_require__(/*! ./core */ \"(rsc)/./node_modules/strtok3/lib/core.js\");\nvar FileTokenizer_1 = __webpack_require__(/*! ./FileTokenizer */ \"(rsc)/./node_modules/strtok3/lib/FileTokenizer.js\");\nObject.defineProperty(exports, \"fromFile\", ({\n    enumerable: true,\n    get: function() {\n        return FileTokenizer_1.fromFile;\n    }\n}));\nvar core_1 = __webpack_require__(/*! ./core */ \"(rsc)/./node_modules/strtok3/lib/core.js\");\nObject.defineProperty(exports, \"EndOfStreamError\", ({\n    enumerable: true,\n    get: function() {\n        return core_1.EndOfStreamError;\n    }\n}));\nObject.defineProperty(exports, \"fromBuffer\", ({\n    enumerable: true,\n    get: function() {\n        return core_1.fromBuffer;\n    }\n}));\n/**\r\n * Construct ReadStreamTokenizer from given Stream.\r\n * Will set fileSize, if provided given Stream has set the .path property.\r\n * @param stream - Node.js Stream.Readable\r\n * @param fileInfo - Pass additional file information to the tokenizer\r\n * @returns Tokenizer\r\n */ async function fromStream(stream, fileInfo) {\n    fileInfo = fileInfo ? fileInfo : {};\n    if (stream.path) {\n        const stat = await fs.stat(stream.path);\n        fileInfo.path = stream.path;\n        fileInfo.size = stat.size;\n    }\n    return core.fromStream(stream, fileInfo);\n}\nexports.fromStream = fromStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/index.js\n");

/***/ })

};
;