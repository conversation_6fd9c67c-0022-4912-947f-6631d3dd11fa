'use client'

import { useState } from 'react'
import { X, Star, Send } from 'lucide-react'

interface FeedbackModalProps {
  isOpen: boolean
  onClose: () => void
  analysisId: string
}

export default function FeedbackModal({ isOpen, onClose, analysisId }: FeedbackModalProps) {
  const [accuracy, setAccuracy] = useState(0)
  const [profitability, setProfitability] = useState(0)
  const [comments, setComments] = useState('')
  const [actualOutcome, setActualOutcome] = useState({
    entryHit: false,
    stopLossHit: false,
    takeProfitsHit: { tp1: false, tp2: false, tp3: false }
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  if (!isOpen) return null

  const handleSubmit = async () => {
    if (accuracy === 0 || profitability === 0) {
      alert('Please provide both accuracy and profitability ratings')
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          analysisId,
          accuracy,
          profitability,
          comments,
          actualOutcome
        })
      })

      if (response.ok) {
        alert('Thank you for your feedback! This helps improve our analysis.')
        onClose()
        // Reset form
        setAccuracy(0)
        setProfitability(0)
        setComments('')
        setActualOutcome({
          entryHit: false,
          stopLossHit: false,
          takeProfitsHit: { tp1: false, tp2: false, tp3: false }
        })
      } else {
        throw new Error('Failed to submit feedback')
      }
    } catch (error) {
      console.error('Feedback submission error:', error)
      alert('Failed to submit feedback. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const StarRating = ({ rating, setRating, label }: { rating: number; setRating: (rating: number) => void; label: string }) => (
    <div className="space-y-2">
      <label className="text-white font-medium">{label}</label>
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => setRating(star)}
            className={`p-1 transition-colors ${
              star <= rating ? 'text-gold-400' : 'text-gray-600 hover:text-gold-300'
            }`}
          >
            <Star className="w-6 h-6 fill-current" />
          </button>
        ))}
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-xl border border-gold-500/20 max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-white">Analysis Feedback</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          {/* Rating Section */}
          <div className="space-y-4">
            <StarRating
              rating={accuracy}
              setRating={setAccuracy}
              label="Analysis Accuracy (1-5 stars)"
            />
            
            <StarRating
              rating={profitability}
              setRating={setProfitability}
              label="Trade Profitability (1-5 stars)"
            />
          </div>

          {/* Actual Outcome */}
          <div className="space-y-3">
            <h3 className="text-white font-medium">Actual Trade Outcome (Optional)</h3>
            
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={actualOutcome.entryHit}
                  onChange={(e) => setActualOutcome(prev => ({ ...prev, entryHit: e.target.checked }))}
                  className="rounded border-gray-600 bg-gray-800 text-gold-500 focus:ring-gold-500"
                />
                <span className="text-gray-300">Entry price was hit</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={actualOutcome.stopLossHit}
                  onChange={(e) => setActualOutcome(prev => ({ ...prev, stopLossHit: e.target.checked }))}
                  className="rounded border-gray-600 bg-gray-800 text-red-500 focus:ring-red-500"
                />
                <span className="text-gray-300">Stop loss was hit</span>
              </label>
              
              <div className="ml-4 space-y-1">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={actualOutcome.takeProfitsHit.tp1}
                    onChange={(e) => setActualOutcome(prev => ({ 
                      ...prev, 
                      takeProfitsHit: { ...prev.takeProfitsHit, tp1: e.target.checked }
                    }))}
                    className="rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500"
                  />
                  <span className="text-gray-300">TP1 was hit</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={actualOutcome.takeProfitsHit.tp2}
                    onChange={(e) => setActualOutcome(prev => ({ 
                      ...prev, 
                      takeProfitsHit: { ...prev.takeProfitsHit, tp2: e.target.checked }
                    }))}
                    className="rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500"
                  />
                  <span className="text-gray-300">TP2 was hit</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={actualOutcome.takeProfitsHit.tp3}
                    onChange={(e) => setActualOutcome(prev => ({ 
                      ...prev, 
                      takeProfitsHit: { ...prev.takeProfitsHit, tp3: e.target.checked }
                    }))}
                    className="rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500"
                  />
                  <span className="text-gray-300">TP3 was hit</span>
                </label>
              </div>
            </div>
          </div>

          {/* Comments */}
          <div className="space-y-2">
            <label className="text-white font-medium">Additional Comments (Optional)</label>
            <textarea
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="Share your thoughts about the analysis quality, accuracy, or suggestions for improvement..."
              className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-gold-500 focus:ring-1 focus:ring-gold-500 resize-none"
              rows={4}
            />
          </div>

          {/* Submit Button */}
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || accuracy === 0 || profitability === 0}
            className="w-full bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-500 disabled:to-gray-600 text-black font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin" />
                <span>Submitting...</span>
              </>
            ) : (
              <>
                <Send className="w-5 h-5" />
                <span>Submit Feedback</span>
              </>
            )}
          </button>

          <p className="text-xs text-gray-400 text-center">
            Your feedback helps our AI learn and improve future analysis accuracy
          </p>
        </div>
      </div>
    </div>
  )
}
