{"version": 3, "file": "index.js", "names": ["xOffsetBasedOnAlignment", "constants", "font", "line", "max<PERSON><PERSON><PERSON>", "alignment", "HORIZONTAL_ALIGN_LEFT", "HORIZONTAL_ALIGN_CENTER", "measureText", "<PERSON><PERSON><PERSON><PERSON>", "image", "x", "y", "char", "width", "height", "characterPage", "pages", "page", "blit", "xoffset", "yoffset", "printText", "text", "defaultCharWidth", "i", "length", "chars", "test", "fontChar", "fontKerning", "kernings", "kerning", "xadvance", "loadPages", "<PERSON><PERSON>", "dir", "newPages", "map", "read", "Promise", "all", "process", "env", "DIRNAME", "__dirname", "measureTextHeight", "FONT_SANS_8_BLACK", "Path", "join", "FONT_SANS_10_BLACK", "FONT_SANS_12_BLACK", "FONT_SANS_14_BLACK", "FONT_SANS_16_BLACK", "FONT_SANS_32_BLACK", "FONT_SANS_64_BLACK", "FONT_SANS_128_BLACK", "FONT_SANS_8_WHITE", "FONT_SANS_16_WHITE", "FONT_SANS_32_WHITE", "FONT_SANS_64_WHITE", "FONT_SANS_128_WHITE", "loadFont", "file", "cb", "throwError", "call", "resolve", "reject", "err", "b<PERSON>ont", "String", "fromCharCode", "id", "firstString", "first", "second", "amount", "dirname", "then", "common", "info", "class", "print", "maxHeight", "Infinity", "alignmentX", "alignmentY", "undefined", "constructor", "VERTICAL_ALIGN_TOP", "toString", "VERTICAL_ALIGN_BOTTOM", "VERTICAL_ALIGN_MIDDLE", "Object", "entries", "lines", "longestLine", "splitLines", "for<PERSON>ach", "lineString", "alignmentWidth", "lineHeight", "isNodePattern"], "sources": ["../src/index.js"], "sourcesContent": ["import Path from \"path\";\nimport bMFont from \"load-bmfont\";\nimport { isNodePattern, throwError } from \"@jimp/utils\";\nimport { measureText, measureTextHeight, splitLines } from \"./measure-text\";\n\nfunction xOffsetBasedOnAlignment(constants, font, line, maxWidth, alignment) {\n  if (alignment === constants.HORIZONTAL_ALIGN_LEFT) {\n    return 0;\n  }\n\n  if (alignment === constants.HORIZONTAL_ALIGN_CENTER) {\n    return (maxWidth - measureText(font, line)) / 2;\n  }\n\n  return maxWidth - measureText(font, line);\n}\n\nfunction drawCharacter(image, font, x, y, char) {\n  if (char.width > 0 && char.height > 0) {\n    const characterPage = font.pages[char.page];\n\n    image.blit(\n      characterPage,\n      x + char.xoffset,\n      y + char.yoffset,\n      char.x,\n      char.y,\n      char.width,\n      char.height\n    );\n  }\n\n  return image;\n}\n\nfunction printText(font, x, y, text, defaultCharWidth) {\n  for (let i = 0; i < text.length; i++) {\n    let char;\n\n    if (font.chars[text[i]]) {\n      char = text[i];\n    } else if (/\\s/.test(text[i])) {\n      char = \"\";\n    } else {\n      char = \"?\";\n    }\n\n    const fontChar = font.chars[char] || {};\n    const fontKerning = font.kernings[char];\n\n    drawCharacter(this, font, x, y, fontChar || {});\n\n    const kerning =\n      fontKerning && fontKerning[text[i + 1]] ? fontKerning[text[i + 1]] : 0;\n\n    x += kerning + (fontChar.xadvance || defaultCharWidth);\n  }\n}\n\nfunction loadPages(Jimp, dir, pages) {\n  const newPages = pages.map((page) => {\n    return Jimp.read(dir + \"/\" + page);\n  });\n\n  return Promise.all(newPages);\n}\n\nconst dir = process.env.DIRNAME || `${__dirname}/../`;\n\nexport default () => ({\n  constants: {\n    measureText,\n    measureTextHeight,\n    FONT_SANS_8_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt\"\n    ),\n    FONT_SANS_10_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt\"\n    ),\n    FONT_SANS_12_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt\"\n    ),\n    FONT_SANS_14_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt\"\n    ),\n    FONT_SANS_16_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt\"\n    ),\n    FONT_SANS_32_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt\"\n    ),\n    FONT_SANS_64_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt\"\n    ),\n    FONT_SANS_128_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt\"\n    ),\n\n    FONT_SANS_8_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt\"\n    ),\n    FONT_SANS_16_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt\"\n    ),\n    FONT_SANS_32_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt\"\n    ),\n    FONT_SANS_64_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt\"\n    ),\n    FONT_SANS_128_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt\"\n    ),\n\n    /**\n     * Loads a bitmap font from a file\n     * @param {string} file the file path of a .fnt file\n     * @param {function(Error, Jimp)} cb (optional) a function to call when the font is loaded\n     * @returns {Promise} a promise\n     */\n    loadFont(file, cb) {\n      if (typeof file !== \"string\")\n        return throwError.call(this, \"file must be a string\", cb);\n\n      return new Promise((resolve, reject) => {\n        cb =\n          cb ||\n          function (err, font) {\n            if (err) reject(err);\n            else resolve(font);\n          };\n\n        bMFont(file, (err, font) => {\n          const chars = {};\n          const kernings = {};\n\n          if (err) {\n            return throwError.call(this, err, cb);\n          }\n\n          for (let i = 0; i < font.chars.length; i++) {\n            chars[String.fromCharCode(font.chars[i].id)] = font.chars[i];\n          }\n\n          for (let i = 0; i < font.kernings.length; i++) {\n            const firstString = String.fromCharCode(font.kernings[i].first);\n            kernings[firstString] = kernings[firstString] || {};\n            kernings[firstString][\n              String.fromCharCode(font.kernings[i].second)\n            ] = font.kernings[i].amount;\n          }\n\n          loadPages(this, Path.dirname(file), font.pages).then((pages) => {\n            cb(null, {\n              chars,\n              kernings,\n              pages,\n              common: font.common,\n              info: font.info,\n            });\n          });\n        });\n      });\n    },\n  },\n\n  class: {\n    /**\n     * Draws a text on a image on a given boundary\n     * @param {Jimp} font a bitmap font loaded from `Jimp.loadFont` command\n     * @param {number} x the x position to start drawing the text\n     * @param {number} y the y position to start drawing the text\n     * @param {any} text the text to draw (string or object with `text`, `alignmentX`, and/or `alignmentY`)\n     * @param {number} maxWidth (optional) the boundary width to draw in\n     * @param {number} maxHeight (optional) the boundary height to draw in\n     * @param {function(Error, Jimp)} cb (optional) a function to call when the text is written\n     * @returns {Jimp} this for chaining of methods\n     */\n    print(font, x, y, text, maxWidth, maxHeight, cb) {\n      if (typeof maxWidth === \"function\" && typeof cb === \"undefined\") {\n        cb = maxWidth;\n        maxWidth = Infinity;\n      }\n\n      if (typeof maxWidth === \"undefined\") {\n        maxWidth = Infinity;\n      }\n\n      if (typeof maxHeight === \"function\" && typeof cb === \"undefined\") {\n        cb = maxHeight;\n        maxHeight = Infinity;\n      }\n\n      if (typeof maxHeight === \"undefined\") {\n        maxHeight = Infinity;\n      }\n\n      if (typeof font !== \"object\") {\n        return throwError.call(this, \"font must be a Jimp loadFont\", cb);\n      }\n\n      if (\n        typeof x !== \"number\" ||\n        typeof y !== \"number\" ||\n        typeof maxWidth !== \"number\"\n      ) {\n        return throwError.call(this, \"x, y and maxWidth must be numbers\", cb);\n      }\n\n      if (typeof maxWidth !== \"number\") {\n        return throwError.call(this, \"maxWidth must be a number\", cb);\n      }\n\n      if (typeof maxHeight !== \"number\") {\n        return throwError.call(this, \"maxHeight must be a number\", cb);\n      }\n\n      let alignmentX;\n      let alignmentY;\n\n      if (\n        typeof text === \"object\" &&\n        text.text !== null &&\n        text.text !== undefined\n      ) {\n        alignmentX = text.alignmentX || this.constructor.HORIZONTAL_ALIGN_LEFT;\n        alignmentY = text.alignmentY || this.constructor.VERTICAL_ALIGN_TOP;\n        ({ text } = text);\n      } else {\n        alignmentX = this.constructor.HORIZONTAL_ALIGN_LEFT;\n        alignmentY = this.constructor.VERTICAL_ALIGN_TOP;\n        text = text.toString();\n      }\n\n      if (\n        maxHeight !== Infinity &&\n        alignmentY === this.constructor.VERTICAL_ALIGN_BOTTOM\n      ) {\n        y += maxHeight - measureTextHeight(font, text, maxWidth);\n      } else if (\n        maxHeight !== Infinity &&\n        alignmentY === this.constructor.VERTICAL_ALIGN_MIDDLE\n      ) {\n        y += maxHeight / 2 - measureTextHeight(font, text, maxWidth) / 2;\n      }\n\n      const defaultCharWidth = Object.entries(font.chars)[0][1].xadvance;\n      const { lines, longestLine } = splitLines(font, text, maxWidth);\n\n      lines.forEach((line) => {\n        const lineString = line.join(\" \");\n        const alignmentWidth = xOffsetBasedOnAlignment(\n          this.constructor,\n          font,\n          lineString,\n          maxWidth,\n          alignmentX\n        );\n\n        printText.call(\n          this,\n          font,\n          x + alignmentWidth,\n          y,\n          lineString,\n          defaultCharWidth\n        );\n\n        y += font.common.lineHeight;\n      });\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this, { x: x + longestLine, y });\n      }\n\n      return this;\n    },\n  },\n});\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAA4E;AAE5E,SAASA,uBAAuB,CAACC,SAAS,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EAC3E,IAAIA,SAAS,KAAKJ,SAAS,CAACK,qBAAqB,EAAE;IACjD,OAAO,CAAC;EACV;EAEA,IAAID,SAAS,KAAKJ,SAAS,CAACM,uBAAuB,EAAE;IACnD,OAAO,CAACH,QAAQ,GAAG,IAAAI,wBAAW,EAACN,IAAI,EAAEC,IAAI,CAAC,IAAI,CAAC;EACjD;EAEA,OAAOC,QAAQ,GAAG,IAAAI,wBAAW,EAACN,IAAI,EAAEC,IAAI,CAAC;AAC3C;AAEA,SAASM,aAAa,CAACC,KAAK,EAAER,IAAI,EAAES,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAE;EAC9C,IAAIA,IAAI,CAACC,KAAK,GAAG,CAAC,IAAID,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;IACrC,MAAMC,aAAa,GAAGd,IAAI,CAACe,KAAK,CAACJ,IAAI,CAACK,IAAI,CAAC;IAE3CR,KAAK,CAACS,IAAI,CACRH,aAAa,EACbL,CAAC,GAAGE,IAAI,CAACO,OAAO,EAChBR,CAAC,GAAGC,IAAI,CAACQ,OAAO,EAChBR,IAAI,CAACF,CAAC,EACNE,IAAI,CAACD,CAAC,EACNC,IAAI,CAACC,KAAK,EACVD,IAAI,CAACE,MAAM,CACZ;EACH;EAEA,OAAOL,KAAK;AACd;AAEA,SAASY,SAAS,CAACpB,IAAI,EAAES,CAAC,EAAEC,CAAC,EAAEW,IAAI,EAAEC,gBAAgB,EAAE;EACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIZ,IAAI;IAER,IAAIX,IAAI,CAACyB,KAAK,CAACJ,IAAI,CAACE,CAAC,CAAC,CAAC,EAAE;MACvBZ,IAAI,GAAGU,IAAI,CAACE,CAAC,CAAC;IAChB,CAAC,MAAM,IAAI,IAAI,CAACG,IAAI,CAACL,IAAI,CAACE,CAAC,CAAC,CAAC,EAAE;MAC7BZ,IAAI,GAAG,EAAE;IACX,CAAC,MAAM;MACLA,IAAI,GAAG,GAAG;IACZ;IAEA,MAAMgB,QAAQ,GAAG3B,IAAI,CAACyB,KAAK,CAACd,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,MAAMiB,WAAW,GAAG5B,IAAI,CAAC6B,QAAQ,CAAClB,IAAI,CAAC;IAEvCJ,aAAa,CAAC,IAAI,EAAEP,IAAI,EAAES,CAAC,EAAEC,CAAC,EAAEiB,QAAQ,IAAI,CAAC,CAAC,CAAC;IAE/C,MAAMG,OAAO,GACXF,WAAW,IAAIA,WAAW,CAACP,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGK,WAAW,CAACP,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAExEd,CAAC,IAAIqB,OAAO,IAAIH,QAAQ,CAACI,QAAQ,IAAIT,gBAAgB,CAAC;EACxD;AACF;AAEA,SAASU,SAAS,CAACC,IAAI,EAAEC,GAAG,EAAEnB,KAAK,EAAE;EACnC,MAAMoB,QAAQ,GAAGpB,KAAK,CAACqB,GAAG,CAAEpB,IAAI,IAAK;IACnC,OAAOiB,IAAI,CAACI,IAAI,CAACH,GAAG,GAAG,GAAG,GAAGlB,IAAI,CAAC;EACpC,CAAC,CAAC;EAEF,OAAOsB,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;AAC9B;AAEA,MAAMD,GAAG,GAAGM,OAAO,CAACC,GAAG,CAACC,OAAO,IAAK,GAAEC,SAAU,MAAK;AAAC,eAEvC,OAAO;EACpB5C,SAAS,EAAE;IACTO,WAAW,EAAXA,wBAAW;IACXsC,iBAAiB,EAAjBA,8BAAiB;IACjBC,iBAAiB,EAAEC,aAAI,CAACC,IAAI,CAC1Bb,GAAG,EACH,yDAAyD,CAC1D;IACDc,kBAAkB,EAAEF,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDe,kBAAkB,EAAEH,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDgB,kBAAkB,EAAEJ,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDiB,kBAAkB,EAAEL,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDkB,kBAAkB,EAAEN,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDmB,kBAAkB,EAAEP,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDoB,mBAAmB,EAAER,aAAI,CAACC,IAAI,CAC5Bb,GAAG,EACH,6DAA6D,CAC9D;IAEDqB,iBAAiB,EAAET,aAAI,CAACC,IAAI,CAC1Bb,GAAG,EACH,yDAAyD,CAC1D;IACDsB,kBAAkB,EAAEV,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDuB,kBAAkB,EAAEX,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDwB,kBAAkB,EAAEZ,aAAI,CAACC,IAAI,CAC3Bb,GAAG,EACH,2DAA2D,CAC5D;IACDyB,mBAAmB,EAAEb,aAAI,CAACC,IAAI,CAC5Bb,GAAG,EACH,6DAA6D,CAC9D;IAED;AACJ;AACA;AACA;AACA;AACA;IACI0B,QAAQ,CAACC,IAAI,EAAEC,EAAE,EAAE;MACjB,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAC1B,OAAOE,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEF,EAAE,CAAC;MAE3D,OAAO,IAAIxB,OAAO,CAAC,CAAC2B,OAAO,EAAEC,MAAM,KAAK;QACtCJ,EAAE,GACAA,EAAE,IACF,UAAUK,GAAG,EAAEnE,IAAI,EAAE;UACnB,IAAImE,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC,KAChBF,OAAO,CAACjE,IAAI,CAAC;QACpB,CAAC;QAEH,IAAAoE,mBAAM,EAACP,IAAI,EAAE,CAACM,GAAG,EAAEnE,IAAI,KAAK;UAC1B,MAAMyB,KAAK,GAAG,CAAC,CAAC;UAChB,MAAMI,QAAQ,GAAG,CAAC,CAAC;UAEnB,IAAIsC,GAAG,EAAE;YACP,OAAOJ,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAEG,GAAG,EAAEL,EAAE,CAAC;UACvC;UAEA,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,IAAI,CAACyB,KAAK,CAACD,MAAM,EAAED,CAAC,EAAE,EAAE;YAC1CE,KAAK,CAAC4C,MAAM,CAACC,YAAY,CAACtE,IAAI,CAACyB,KAAK,CAACF,CAAC,CAAC,CAACgD,EAAE,CAAC,CAAC,GAAGvE,IAAI,CAACyB,KAAK,CAACF,CAAC,CAAC;UAC9D;UAEA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,IAAI,CAAC6B,QAAQ,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;YAC7C,MAAMiD,WAAW,GAAGH,MAAM,CAACC,YAAY,CAACtE,IAAI,CAAC6B,QAAQ,CAACN,CAAC,CAAC,CAACkD,KAAK,CAAC;YAC/D5C,QAAQ,CAAC2C,WAAW,CAAC,GAAG3C,QAAQ,CAAC2C,WAAW,CAAC,IAAI,CAAC,CAAC;YACnD3C,QAAQ,CAAC2C,WAAW,CAAC,CACnBH,MAAM,CAACC,YAAY,CAACtE,IAAI,CAAC6B,QAAQ,CAACN,CAAC,CAAC,CAACmD,MAAM,CAAC,CAC7C,GAAG1E,IAAI,CAAC6B,QAAQ,CAACN,CAAC,CAAC,CAACoD,MAAM;UAC7B;UAEA3C,SAAS,CAAC,IAAI,EAAEc,aAAI,CAAC8B,OAAO,CAACf,IAAI,CAAC,EAAE7D,IAAI,CAACe,KAAK,CAAC,CAAC8D,IAAI,CAAE9D,KAAK,IAAK;YAC9D+C,EAAE,CAAC,IAAI,EAAE;cACPrC,KAAK;cACLI,QAAQ;cACRd,KAAK;cACL+D,MAAM,EAAE9E,IAAI,CAAC8E,MAAM;cACnBC,IAAI,EAAE/E,IAAI,CAAC+E;YACb,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EAEDC,KAAK,EAAE;IACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,KAAK,CAACjF,IAAI,EAAES,CAAC,EAAEC,CAAC,EAAEW,IAAI,EAAEnB,QAAQ,EAAEgF,SAAS,EAAEpB,EAAE,EAAE;MAC/C,IAAI,OAAO5D,QAAQ,KAAK,UAAU,IAAI,OAAO4D,EAAE,KAAK,WAAW,EAAE;QAC/DA,EAAE,GAAG5D,QAAQ;QACbA,QAAQ,GAAGiF,QAAQ;MACrB;MAEA,IAAI,OAAOjF,QAAQ,KAAK,WAAW,EAAE;QACnCA,QAAQ,GAAGiF,QAAQ;MACrB;MAEA,IAAI,OAAOD,SAAS,KAAK,UAAU,IAAI,OAAOpB,EAAE,KAAK,WAAW,EAAE;QAChEA,EAAE,GAAGoB,SAAS;QACdA,SAAS,GAAGC,QAAQ;MACtB;MAEA,IAAI,OAAOD,SAAS,KAAK,WAAW,EAAE;QACpCA,SAAS,GAAGC,QAAQ;MACtB;MAEA,IAAI,OAAOnF,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO+D,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,8BAA8B,EAAEF,EAAE,CAAC;MAClE;MAEA,IACE,OAAOrD,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,IACrB,OAAOR,QAAQ,KAAK,QAAQ,EAC5B;QACA,OAAO6D,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,mCAAmC,EAAEF,EAAE,CAAC;MACvE;MAEA,IAAI,OAAO5D,QAAQ,KAAK,QAAQ,EAAE;QAChC,OAAO6D,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,2BAA2B,EAAEF,EAAE,CAAC;MAC/D;MAEA,IAAI,OAAOoB,SAAS,KAAK,QAAQ,EAAE;QACjC,OAAOnB,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,4BAA4B,EAAEF,EAAE,CAAC;MAChE;MAEA,IAAIsB,UAAU;MACd,IAAIC,UAAU;MAEd,IACE,OAAOhE,IAAI,KAAK,QAAQ,IACxBA,IAAI,CAACA,IAAI,KAAK,IAAI,IAClBA,IAAI,CAACA,IAAI,KAAKiE,SAAS,EACvB;QACAF,UAAU,GAAG/D,IAAI,CAAC+D,UAAU,IAAI,IAAI,CAACG,WAAW,CAACnF,qBAAqB;QACtEiF,UAAU,GAAGhE,IAAI,CAACgE,UAAU,IAAI,IAAI,CAACE,WAAW,CAACC,kBAAkB;QACnE,CAAC;UAAEnE;QAAK,CAAC,GAAGA,IAAI;MAClB,CAAC,MAAM;QACL+D,UAAU,GAAG,IAAI,CAACG,WAAW,CAACnF,qBAAqB;QACnDiF,UAAU,GAAG,IAAI,CAACE,WAAW,CAACC,kBAAkB;QAChDnE,IAAI,GAAGA,IAAI,CAACoE,QAAQ,EAAE;MACxB;MAEA,IACEP,SAAS,KAAKC,QAAQ,IACtBE,UAAU,KAAK,IAAI,CAACE,WAAW,CAACG,qBAAqB,EACrD;QACAhF,CAAC,IAAIwE,SAAS,GAAG,IAAAtC,8BAAiB,EAAC5C,IAAI,EAAEqB,IAAI,EAAEnB,QAAQ,CAAC;MAC1D,CAAC,MAAM,IACLgF,SAAS,KAAKC,QAAQ,IACtBE,UAAU,KAAK,IAAI,CAACE,WAAW,CAACI,qBAAqB,EACrD;QACAjF,CAAC,IAAIwE,SAAS,GAAG,CAAC,GAAG,IAAAtC,8BAAiB,EAAC5C,IAAI,EAAEqB,IAAI,EAAEnB,QAAQ,CAAC,GAAG,CAAC;MAClE;MAEA,MAAMoB,gBAAgB,GAAGsE,MAAM,CAACC,OAAO,CAAC7F,IAAI,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ;MAClE,MAAM;QAAE+D,KAAK;QAAEC;MAAY,CAAC,GAAG,IAAAC,uBAAU,EAAChG,IAAI,EAAEqB,IAAI,EAAEnB,QAAQ,CAAC;MAE/D4F,KAAK,CAACG,OAAO,CAAEhG,IAAI,IAAK;QACtB,MAAMiG,UAAU,GAAGjG,IAAI,CAAC8C,IAAI,CAAC,GAAG,CAAC;QACjC,MAAMoD,cAAc,GAAGrG,uBAAuB,CAC5C,IAAI,CAACyF,WAAW,EAChBvF,IAAI,EACJkG,UAAU,EACVhG,QAAQ,EACRkF,UAAU,CACX;QAEDhE,SAAS,CAAC4C,IAAI,CACZ,IAAI,EACJhE,IAAI,EACJS,CAAC,GAAG0F,cAAc,EAClBzF,CAAC,EACDwF,UAAU,EACV5E,gBAAgB,CACjB;QAEDZ,CAAC,IAAIV,IAAI,CAAC8E,MAAM,CAACsB,UAAU;MAC7B,CAAC,CAAC;MAEF,IAAI,IAAAC,oBAAa,EAACvC,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;UAAEvD,CAAC,EAAEA,CAAC,GAAGsF,WAAW;UAAErF;QAAE,CAAC,CAAC;MACtD;MAEA,OAAO,IAAI;IACb;EACF;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}