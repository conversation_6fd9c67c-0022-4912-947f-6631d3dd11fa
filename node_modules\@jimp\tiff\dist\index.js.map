{"version": 3, "file": "index.js", "names": ["MIME_TYPE", "mime", "constants", "MIME_TIFF", "decoders", "data", "ifds", "utif", "decode", "page", "for<PERSON>ach", "ifd", "decodeImage", "rgba", "toRGBA8", "<PERSON><PERSON><PERSON>", "from", "width", "t256", "height", "t257", "encoders", "image", "tiff", "encodeImage", "bitmap"], "sources": ["../src/index.js"], "sourcesContent": ["import utif from \"utif2\";\n\nconst MIME_TYPE = \"image/tiff\";\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"tiff\", \"tif\"] },\n\n  constants: {\n    MIME_TIFF: MIME_TYPE,\n  },\n\n  decoders: {\n    [MIME_TYPE]: (data) => {\n      const ifds = utif.decode(data);\n      const page = ifds[0];\n\n      ifds.forEach((ifd) => {\n        utif.decodeImage(data, ifd);\n      });\n\n      const rgba = utif.toRGBA8(page);\n\n      return {\n        data: Buffer.from(rgba),\n        width: page.t256[0],\n        height: page.t257[0],\n      };\n    },\n  },\n\n  encoders: {\n    [MIME_TYPE]: (image) => {\n      const tiff = utif.encodeImage(\n        image.bitmap.data,\n        image.bitmap.width,\n        image.bitmap.height\n      );\n\n      return Buffer.from(tiff);\n    },\n  },\n});\n"], "mappings": ";;;;;;AAAA;AAAyB;AAEzB,MAAMA,SAAS,GAAG,YAAY;AAAC,eAEhB,OAAO;EACpBC,IAAI,EAAE;IAAE,CAACD,SAAS,GAAG,CAAC,MAAM,EAAE,KAAK;EAAE,CAAC;EAEtCE,SAAS,EAAE;IACTC,SAAS,EAAEH;EACb,CAAC;EAEDI,QAAQ,EAAE;IACR,CAACJ,SAAS,GAAIK,IAAI,IAAK;MACrB,MAAMC,IAAI,GAAGC,aAAI,CAACC,MAAM,CAACH,IAAI,CAAC;MAC9B,MAAMI,IAAI,GAAGH,IAAI,CAAC,CAAC,CAAC;MAEpBA,IAAI,CAACI,OAAO,CAAEC,GAAG,IAAK;QACpBJ,aAAI,CAACK,WAAW,CAACP,IAAI,EAAEM,GAAG,CAAC;MAC7B,CAAC,CAAC;MAEF,MAAME,IAAI,GAAGN,aAAI,CAACO,OAAO,CAACL,IAAI,CAAC;MAE/B,OAAO;QACLJ,IAAI,EAAEU,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;QACvBI,KAAK,EAAER,IAAI,CAACS,IAAI,CAAC,CAAC,CAAC;QACnBC,MAAM,EAAEV,IAAI,CAACW,IAAI,CAAC,CAAC;MACrB,CAAC;IACH;EACF,CAAC;EAEDC,QAAQ,EAAE;IACR,CAACrB,SAAS,GAAIsB,KAAK,IAAK;MACtB,MAAMC,IAAI,GAAGhB,aAAI,CAACiB,WAAW,CAC3BF,KAAK,CAACG,MAAM,CAACpB,IAAI,EACjBiB,KAAK,CAACG,MAAM,CAACR,KAAK,EAClBK,KAAK,CAACG,MAAM,CAACN,MAAM,CACpB;MAED,OAAOJ,MAAM,CAACC,IAAI,CAACO,IAAI,CAAC;IAC1B;EACF;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}