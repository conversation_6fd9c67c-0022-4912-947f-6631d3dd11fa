"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/message-square.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MessageSquare; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst MessageSquare = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MessageSquare\", [\n  [\n    \"path\",\n    {\n      d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n      key: \"1lielz\"\n    }\n  ]\n]);\n\n\n//# sourceMappingURL=message-square.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWVzc2FnZS1zcXVhcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxzQkFBc0IsZ0VBQWdCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9DO0FBQ3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWVzc2FnZS1zcXVhcmUuanM/NzA0MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBNZXNzYWdlU3F1YXJlID0gY3JlYXRlTHVjaWRlSWNvbihcIk1lc3NhZ2VTcXVhcmVcIiwgW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6XCIsXG4gICAgICBrZXk6IFwiMWxpZWx6XCJcbiAgICB9XG4gIF1cbl0pO1xuXG5leHBvcnQgeyBNZXNzYWdlU3F1YXJlIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2Utc3F1YXJlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/send.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Send; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst Send = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Send\", [\n  [\"path\", { d: \"m22 2-7 20-4-9-9-4Z\", key: \"1q3vgg\" }],\n  [\"path\", { d: \"M22 2 11 13\", key: \"nzbqef\" }]\n]);\n\n\n//# sourceMappingURL=send.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCLGFBQWEseUNBQXlDO0FBQ3RELGFBQWEsaUNBQWlDO0FBQzlDOztBQUUyQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3NlbmQuanM/OWQzOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBTZW5kID0gY3JlYXRlTHVjaWRlSWNvbihcIlNlbmRcIiwgW1xuICBbXCJwYXRoXCIsIHsgZDogXCJtMjIgMi03IDIwLTQtOS05LTRaXCIsIGtleTogXCIxcTN2Z2dcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTIyIDIgMTEgMTNcIiwga2V5OiBcIm56YnFlZlwiIH1dXG5dKTtcblxuZXhwb3J0IHsgU2VuZCBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZW5kLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Star; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst Star = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Star\", [\n  [\n    \"polygon\",\n    {\n      points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\",\n      key: \"8f66p6\"\n    }\n  ]\n]);\n\n\n//# sourceMappingURL=star.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc3Rhci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCO0FBQzNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc3Rhci5qcz9mZWFkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbHVjaWRlLXJlYWN0IHYwLjI5Mi4wIC0gSVNDXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IFN0YXIgPSBjcmVhdGVMdWNpZGVJY29uKFwiU3RhclwiLCBbXG4gIFtcbiAgICBcInBvbHlnb25cIixcbiAgICB7XG4gICAgICBwb2ludHM6IFwiMTIgMiAxNS4wOSA4LjI2IDIyIDkuMjcgMTcgMTQuMTQgMTguMTggMjEuMDIgMTIgMTcuNzcgNS44MiAyMS4wMiA3IDE0LjE0IDIgOS4yNyA4LjkxIDguMjYgMTIgMlwiLFxuICAgICAga2V5OiBcIjhmNjZwNlwiXG4gICAgfVxuICBdXG5dKTtcblxuZXhwb3J0IHsgU3RhciBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdGFyLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AnalysisResults.tsx":
/*!********************************************!*\
  !*** ./src/components/AnalysisResults.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnalysisResults; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,MessageSquare,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _FeedbackModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FeedbackModal */ \"(app-pages-browser)/./src/components/FeedbackModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AnalysisResults(param) {\n    let { results } = param;\n    _s();\n    const [showFeedbackModal, setShowFeedbackModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getDirectionColor = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return \"text-green-500\";\n            case \"SELL\":\n                return \"text-red-500\";\n            default:\n                return \"text-yellow-500\";\n        }\n    };\n    const getDirectionIcon = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 26\n                }, this);\n            case \"SELL\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatPrice = (price)=>{\n        return price.toFixed(2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white\",\n                        children: \"Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 \".concat(getDirectionColor(results.direction)),\n                                children: [\n                                    getDirectionIcon(results.direction),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: results.direction\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            results.confidence,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 ml-1\",\n                                        children: \"confidence\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Trade Setup\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Entry Price:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.entry)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Stop Loss:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.stopLoss)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP1:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP2:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP3:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp3)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Risk/Reward Ratios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP1 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp1\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP2 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp2\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP3 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp3\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Strategy:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white capitalize\",\n                                                        children: results.strategy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Timeframe:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: results.timeframe\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold text-white\",\n                        children: \"Detailed Analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Market Structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.marketStructure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Risk Assessment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.riskAssessment\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    results.reasoning.orderBlocks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Order Blocks Identified\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.orderBlocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            block\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.fairValueGaps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Fair Value Gaps\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.fairValueGaps.map((gap, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this),\n                                            gap\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.ictConcepts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"ICT Concepts Applied\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.ictConcepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            concept\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white\",\n                            children: \"Help Improve Our Analysis\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Your feedback helps our AI learn and provide better trading analysis\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowFeedbackModal(true),\n                            className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2 mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Provide Feedback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500 text-sm flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_MessageSquare_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    \"Analysis completed: \",\n                    new Date(results.timestamp).toLocaleString()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeedbackModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: showFeedbackModal,\n                onClose: ()=>setShowFeedbackModal(false),\n                analysisId: results.analysisId || \"unknown\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalysisResults, \"sGCCH0LhJqWqr/mG3EidXhQdhzI=\");\n_c = AnalysisResults;\nvar _c;\n$RefreshReg$(_c, \"AnalysisResults\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AnalysisResults.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FeedbackModal.tsx":
/*!******************************************!*\
  !*** ./src/components/FeedbackModal.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FeedbackModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Send,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction FeedbackModal(param) {\n    let { isOpen, onClose, analysisId } = param;\n    _s();\n    const [accuracy, setAccuracy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [profitability, setProfitability] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [actualOutcome, setActualOutcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        entryHit: false,\n        stopLossHit: false,\n        takeProfitsHit: {\n            tp1: false,\n            tp2: false,\n            tp3: false\n        }\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (!isOpen) return null;\n    const handleSubmit = async ()=>{\n        if (accuracy === 0 || profitability === 0) {\n            alert(\"Please provide both accuracy and profitability ratings\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/feedback\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    analysisId,\n                    accuracy,\n                    profitability,\n                    comments,\n                    actualOutcome\n                })\n            });\n            if (response.ok) {\n                alert(\"Thank you for your feedback! This helps improve our analysis.\");\n                onClose();\n                // Reset form\n                setAccuracy(0);\n                setProfitability(0);\n                setComments(\"\");\n                setActualOutcome({\n                    entryHit: false,\n                    stopLossHit: false,\n                    takeProfitsHit: {\n                        tp1: false,\n                        tp2: false,\n                        tp3: false\n                    }\n                });\n            } else {\n                throw new Error(\"Failed to submit feedback\");\n            }\n        } catch (error) {\n            console.error(\"Feedback submission error:\", error);\n            alert(\"Failed to submit feedback. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const StarRating = (param)=>/*#__PURE__*/ {\n        let { rating, setRating, label } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"text-white font-medium\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5\n                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setRating(star),\n                            className: \"p-1 transition-colors \".concat(star <= rating ? \"text-gold-400\" : \"text-gray-600 hover:text-gold-300\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-6 h-6 fill-current\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        }, star, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n            lineNumber: 71,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900 rounded-xl border border-gold-500/20 max-w-md w-full max-h-[90vh] overflow-y-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Analysis Feedback\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                rating: accuracy,\n                                setRating: setAccuracy,\n                                label: \"Analysis Accuracy (1-5 stars)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                rating: profitability,\n                                setRating: setProfitability,\n                                label: \"Trade Profitability (1-5 stars)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white font-medium\",\n                                children: \"Actual Trade Outcome (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: actualOutcome.entryHit,\n                                                onChange: (e)=>setActualOutcome((prev)=>({\n                                                            ...prev,\n                                                            entryHit: e.target.checked\n                                                        })),\n                                                className: \"rounded border-gray-600 bg-gray-800 text-gold-500 focus:ring-gold-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Entry price was hit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: actualOutcome.stopLossHit,\n                                                onChange: (e)=>setActualOutcome((prev)=>({\n                                                            ...prev,\n                                                            stopLossHit: e.target.checked\n                                                        })),\n                                                className: \"rounded border-gray-600 bg-gray-800 text-red-500 focus:ring-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Stop loss was hit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: actualOutcome.takeProfitsHit.tp1,\n                                                        onChange: (e)=>setActualOutcome((prev)=>({\n                                                                    ...prev,\n                                                                    takeProfitsHit: {\n                                                                        ...prev.takeProfitsHit,\n                                                                        tp1: e.target.checked\n                                                                    }\n                                                                })),\n                                                        className: \"rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"TP1 was hit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: actualOutcome.takeProfitsHit.tp2,\n                                                        onChange: (e)=>setActualOutcome((prev)=>({\n                                                                    ...prev,\n                                                                    takeProfitsHit: {\n                                                                        ...prev.takeProfitsHit,\n                                                                        tp2: e.target.checked\n                                                                    }\n                                                                })),\n                                                        className: \"rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"TP2 was hit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: actualOutcome.takeProfitsHit.tp3,\n                                                        onChange: (e)=>setActualOutcome((prev)=>({\n                                                                    ...prev,\n                                                                    takeProfitsHit: {\n                                                                        ...prev.takeProfitsHit,\n                                                                        tp3: e.target.checked\n                                                                    }\n                                                                })),\n                                                        className: \"rounded border-gray-600 bg-gray-800 text-green-500 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"TP3 was hit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-white font-medium\",\n                                children: \"Additional Comments (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: comments,\n                                onChange: (e)=>setComments(e.target.value),\n                                placeholder: \"Share your thoughts about the analysis quality, accuracy, or suggestions for improvement...\",\n                                className: \"w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-gold-500 focus:ring-1 focus:ring-gold-500 resize-none\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSubmit,\n                        disabled: isSubmitting || accuracy === 0 || profitability === 0,\n                        className: \"w-full bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-500 disabled:to-gray-600 text-black font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2\",\n                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Submitting...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Submit Feedback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400 text-center\",\n                        children: \"Your feedback helps our AI learn and improve future analysis accuracy\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\FeedbackModal.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedbackModal, \"HzPkidPpF1AGNgkoh2WBsh0asQQ=\");\n_c = FeedbackModal;\nvar _c;\n$RefreshReg$(_c, \"FeedbackModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FeedbackModal.tsx\n"));

/***/ })

});