import sqlite3 from 'sqlite3'
import { promisify } from 'util'
import path from 'path'

// Simple in-memory database for demo purposes
// In production, you'd use a proper database like PostgreSQL or MongoDB

class SimpleDatabase {
  private db: sqlite3.Database | null = null
  private isInitialized = false

  async initialize() {
    if (this.isInitialized) return

    try {
      // Create database in memory for demo
      this.db = new sqlite3.Database(':memory:')
      
      // Promisify database methods
      const run = promisify(this.db.run.bind(this.db))
      const get = promisify(this.db.get.bind(this.db))
      const all = promisify(this.db.all.bind(this.db))

      // Create tables
      await run(`
        CREATE TABLE IF NOT EXISTS analyses (
          id TEXT PRIMARY KEY,
          timestamp TEXT NOT NULL,
          strategy TEXT NOT NULL,
          timeframe TEXT NOT NULL,
          direction TEXT NOT NULL,
          confidence INTEGER NOT NULL,
          entry_price REAL NOT NULL,
          stop_loss REAL NOT NULL,
          tp1 REAL NOT NULL,
          tp2 REAL NOT NULL,
          tp3 REAL NOT NULL,
          market_structure TEXT,
          reasoning TEXT
        )
      `)

      await run(`
        CREATE TABLE IF NOT EXISTS feedback (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          analysis_id TEXT NOT NULL,
          accuracy INTEGER NOT NULL,
          profitability INTEGER NOT NULL,
          comments TEXT,
          entry_hit BOOLEAN,
          stop_loss_hit BOOLEAN,
          tp1_hit BOOLEAN,
          tp2_hit BOOLEAN,
          tp3_hit BOOLEAN,
          timestamp TEXT NOT NULL,
          FOREIGN KEY (analysis_id) REFERENCES analyses (id)
        )
      `)

      await run(`
        CREATE TABLE IF NOT EXISTS learning_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          strategy TEXT NOT NULL,
          timeframe TEXT NOT NULL,
          pattern_features TEXT NOT NULL,
          outcome TEXT NOT NULL,
          confidence INTEGER NOT NULL,
          timestamp TEXT NOT NULL
        )
      `)

      this.isInitialized = true
      console.log('Database initialized successfully')
    } catch (error) {
      console.error('Database initialization error:', error)
      throw error
    }
  }

  async storeAnalysis(analysis: {
    id: string
    timestamp: string
    strategy: string
    timeframe: string
    direction: string
    confidence: number
    entryPrice: number
    stopLoss: number
    tp1: number
    tp2: number
    tp3: number
    marketStructure: string
    reasoning: string
  }) {
    if (!this.db) await this.initialize()
    
    const run = promisify(this.db!.run.bind(this.db!))
    
    try {
      await run(`
        INSERT INTO analyses (
          id, timestamp, strategy, timeframe, direction, confidence,
          entry_price, stop_loss, tp1, tp2, tp3, market_structure, reasoning
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        analysis.id,
        analysis.timestamp,
        analysis.strategy,
        analysis.timeframe,
        analysis.direction,
        analysis.confidence,
        analysis.entryPrice,
        analysis.stopLoss,
        analysis.tp1,
        analysis.tp2,
        analysis.tp3,
        analysis.marketStructure,
        analysis.reasoning
      ])
    } catch (error) {
      console.error('Error storing analysis:', error)
      throw error
    }
  }

  async storeFeedback(feedback: {
    analysisId: string
    accuracy: number
    profitability: number
    comments?: string
    actualOutcome?: {
      entryHit: boolean
      stopLossHit: boolean
      takeProfitsHit: { tp1: boolean; tp2: boolean; tp3: boolean }
    }
  }) {
    if (!this.db) await this.initialize()
    
    const run = promisify(this.db!.run.bind(this.db!))
    
    try {
      await run(`
        INSERT INTO feedback (
          analysis_id, accuracy, profitability, comments,
          entry_hit, stop_loss_hit, tp1_hit, tp2_hit, tp3_hit, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        feedback.analysisId,
        feedback.accuracy,
        feedback.profitability,
        feedback.comments || null,
        feedback.actualOutcome?.entryHit || false,
        feedback.actualOutcome?.stopLossHit || false,
        feedback.actualOutcome?.takeProfitsHit.tp1 || false,
        feedback.actualOutcome?.takeProfitsHit.tp2 || false,
        feedback.actualOutcome?.takeProfitsHit.tp3 || false,
        new Date().toISOString()
      ])
    } catch (error) {
      console.error('Error storing feedback:', error)
      throw error
    }
  }

  async getAnalysisStats() {
    if (!this.db) await this.initialize()
    
    const all = promisify(this.db!.all.bind(this.db!))
    
    try {
      const totalAnalyses = await all('SELECT COUNT(*) as count FROM analyses')
      const strategyBreakdown = await all(`
        SELECT strategy, COUNT(*) as count 
        FROM analyses 
        GROUP BY strategy
      `)
      const directionBreakdown = await all(`
        SELECT direction, COUNT(*) as count 
        FROM analyses 
        GROUP BY direction
      `)
      const avgConfidence = await all('SELECT AVG(confidence) as avg FROM analyses')
      
      return {
        totalAnalyses: totalAnalyses[0]?.count || 0,
        strategyBreakdown: strategyBreakdown.reduce((acc: any, row: any) => {
          acc[row.strategy] = row.count
          return acc
        }, {}),
        directionBreakdown: directionBreakdown.reduce((acc: any, row: any) => {
          acc[row.direction] = row.count
          return acc
        }, {}),
        averageConfidence: avgConfidence[0]?.avg || 0
      }
    } catch (error) {
      console.error('Error getting analysis stats:', error)
      return {
        totalAnalyses: 0,
        strategyBreakdown: {},
        directionBreakdown: {},
        averageConfidence: 0
      }
    }
  }

  async getFeedbackStats() {
    if (!this.db) await this.initialize()
    
    const all = promisify(this.db!.all.bind(this.db!))
    
    try {
      const avgAccuracy = await all('SELECT AVG(accuracy) as avg FROM feedback')
      const avgProfitability = await all('SELECT AVG(profitability) as avg FROM feedback')
      const totalFeedback = await all('SELECT COUNT(*) as count FROM feedback')
      
      return {
        averageAccuracy: avgAccuracy[0]?.avg || 0,
        averageProfitability: avgProfitability[0]?.avg || 0,
        totalFeedback: totalFeedback[0]?.count || 0
      }
    } catch (error) {
      console.error('Error getting feedback stats:', error)
      return {
        averageAccuracy: 0,
        averageProfitability: 0,
        totalFeedback: 0
      }
    }
  }

  async storeLearningData(data: {
    strategy: string
    timeframe: string
    patternFeatures: string
    outcome: string
    confidence: number
  }) {
    if (!this.db) await this.initialize()
    
    const run = promisify(this.db!.run.bind(this.db!))
    
    try {
      await run(`
        INSERT INTO learning_data (
          strategy, timeframe, pattern_features, outcome, confidence, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?)
      `, [
        data.strategy,
        data.timeframe,
        data.patternFeatures,
        data.outcome,
        data.confidence,
        new Date().toISOString()
      ])
    } catch (error) {
      console.error('Error storing learning data:', error)
      throw error
    }
  }

  async close() {
    if (this.db) {
      const close = promisify(this.db.close.bind(this.db))
      await close()
      this.db = null
      this.isInitialized = false
    }
  }
}

// Export singleton instance
export const database = new SimpleDatabase()

// Initialize on import
database.initialize().catch(console.error)
