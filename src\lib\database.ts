import { promises as fs } from 'fs'
import path from 'path'

// Simple JSON-based storage for demo purposes
// In production, you'd use a proper database like PostgreSQL or MongoDB

interface StorageData {
  analyses: any[]
  feedback: any[]
  learningData: any[]
}

class SimpleDatabase {
  private data: StorageData = {
    analyses: [],
    feedback: [],
    learningData: []
  }
  private isInitialized = false
  private dataFile = path.join(process.cwd(), 'data', 'trading-data.json')

  async initialize() {
    if (this.isInitialized) return

    try {
      // Create data directory if it doesn't exist
      const dataDir = path.dirname(this.dataFile)
      try {
        await fs.access(dataDir)
      } catch {
        await fs.mkdir(dataDir, { recursive: true })
      }

      // Load existing data if file exists
      try {
        const fileContent = await fs.readFile(this.dataFile, 'utf-8')
        this.data = JSON.parse(fileContent)
      } catch {
        // File doesn't exist or is invalid, start with empty data
        await this.saveData()
      }

      this.isInitialized = true
      console.log('Database initialized successfully')
    } catch (error) {
      console.error('Database initialization error:', error)
      // Continue with in-memory storage if file operations fail
      this.isInitialized = true
    }
  }

  private async saveData() {
    try {
      await fs.writeFile(this.dataFile, JSON.stringify(this.data, null, 2))
    } catch (error) {
      console.error('Error saving data:', error)
      // Continue with in-memory storage if file operations fail
    }
  }

  async storeAnalysis(analysis: {
    id: string
    timestamp: string
    strategy: string
    timeframe: string
    direction: string
    confidence: number
    entryPrice: number
    stopLoss: number
    tp1: number
    tp2: number
    tp3: number
    marketStructure: string
    reasoning: string
  }) {
    if (!this.isInitialized) await this.initialize()

    try {
      this.data.analyses.push({
        id: analysis.id,
        timestamp: analysis.timestamp,
        strategy: analysis.strategy,
        timeframe: analysis.timeframe,
        direction: analysis.direction,
        confidence: analysis.confidence,
        entry_price: analysis.entryPrice,
        stop_loss: analysis.stopLoss,
        tp1: analysis.tp1,
        tp2: analysis.tp2,
        tp3: analysis.tp3,
        market_structure: analysis.marketStructure,
        reasoning: analysis.reasoning
      })

      await this.saveData()
    } catch (error) {
      console.error('Error storing analysis:', error)
      throw error
    }
  }

  async storeFeedback(feedback: {
    analysisId: string
    accuracy: number
    profitability: number
    comments?: string
    actualOutcome?: {
      entryHit: boolean
      stopLossHit: boolean
      takeProfitsHit: { tp1: boolean; tp2: boolean; tp3: boolean }
    }
  }) {
    if (!this.isInitialized) await this.initialize()

    try {
      this.data.feedback.push({
        id: Date.now(),
        analysis_id: feedback.analysisId,
        accuracy: feedback.accuracy,
        profitability: feedback.profitability,
        comments: feedback.comments || null,
        entry_hit: feedback.actualOutcome?.entryHit || false,
        stop_loss_hit: feedback.actualOutcome?.stopLossHit || false,
        tp1_hit: feedback.actualOutcome?.takeProfitsHit.tp1 || false,
        tp2_hit: feedback.actualOutcome?.takeProfitsHit.tp2 || false,
        tp3_hit: feedback.actualOutcome?.takeProfitsHit.tp3 || false,
        timestamp: new Date().toISOString()
      })

      await this.saveData()
    } catch (error) {
      console.error('Error storing feedback:', error)
      throw error
    }
  }

  async getAnalysisStats() {
    if (!this.isInitialized) await this.initialize()

    try {
      const analyses = this.data.analyses
      const totalAnalyses = analyses.length

      // Strategy breakdown
      const strategyBreakdown = analyses.reduce((acc: any, analysis: any) => {
        acc[analysis.strategy] = (acc[analysis.strategy] || 0) + 1
        return acc
      }, {})

      // Direction breakdown
      const directionBreakdown = analyses.reduce((acc: any, analysis: any) => {
        acc[analysis.direction] = (acc[analysis.direction] || 0) + 1
        return acc
      }, { BUY: 0, SELL: 0, NEUTRAL: 0 })

      // Average confidence
      const avgConfidence = analyses.length > 0
        ? analyses.reduce((sum: number, analysis: any) => sum + analysis.confidence, 0) / analyses.length
        : 0

      return {
        totalAnalyses,
        strategyBreakdown,
        directionBreakdown,
        averageConfidence: avgConfidence
      }
    } catch (error) {
      console.error('Error getting analysis stats:', error)
      return {
        totalAnalyses: 0,
        strategyBreakdown: {},
        directionBreakdown: { BUY: 0, SELL: 0, NEUTRAL: 0 },
        averageConfidence: 0
      }
    }
  }

  async getFeedbackStats() {
    if (!this.isInitialized) await this.initialize()

    try {
      const feedback = this.data.feedback
      const totalFeedback = feedback.length

      const avgAccuracy = feedback.length > 0
        ? feedback.reduce((sum: number, f: any) => sum + f.accuracy, 0) / feedback.length
        : 0

      const avgProfitability = feedback.length > 0
        ? feedback.reduce((sum: number, f: any) => sum + f.profitability, 0) / feedback.length
        : 0

      return {
        averageAccuracy: avgAccuracy,
        averageProfitability: avgProfitability,
        totalFeedback: totalFeedback
      }
    } catch (error) {
      console.error('Error getting feedback stats:', error)
      return {
        averageAccuracy: 0,
        averageProfitability: 0,
        totalFeedback: 0
      }
    }
  }

  async storeLearningData(data: {
    strategy: string
    timeframe: string
    patternFeatures: string
    outcome: string
    confidence: number
  }) {
    if (!this.isInitialized) await this.initialize()

    try {
      this.data.learningData.push({
        id: Date.now(),
        strategy: data.strategy,
        timeframe: data.timeframe,
        pattern_features: data.patternFeatures,
        outcome: data.outcome,
        confidence: data.confidence,
        timestamp: new Date().toISOString()
      })

      await this.saveData()
    } catch (error) {
      console.error('Error storing learning data:', error)
      throw error
    }
  }

  async close() {
    if (this.isInitialized) {
      await this.saveData()
      this.isInitialized = false
    }
  }
}

// Export singleton instance
export const database = new SimpleDatabase()

// Initialize on import
database.initialize().catch(console.error)
