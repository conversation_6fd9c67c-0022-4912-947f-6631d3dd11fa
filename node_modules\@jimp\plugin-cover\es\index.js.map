{"version": 3, "file": "index.js", "names": ["isNodePattern", "throwError", "cover", "w", "h", "alignBits", "mode", "cb", "call", "constructor", "HORIZONTAL_ALIGN_CENTER", "VERTICAL_ALIGN_MIDDLE", "hbits", "vbits", "alignH", "alignV", "f", "bitmap", "width", "height", "scale", "crop"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Scale the image so the given width and height keeping the aspect ratio. Some parts of the image may be clipped.\n * @param {number} w the width to resize the image to\n * @param {number} h the height to resize the image to\n * @param {number} alignBits (optional) A bitmask for horizontal and vertical alignment\n * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jim<PERSON>} this for chaining of methods\n */\nexport default () => ({\n  cover(w, h, alignBits, mode, cb) {\n    if (typeof w !== \"number\" || typeof h !== \"number\") {\n      return throwError.call(this, \"w and h must be numbers\", cb);\n    }\n\n    if (\n      alignBits &&\n      typeof alignBits === \"function\" &&\n      typeof cb === \"undefined\"\n    ) {\n      cb = alignBits;\n      alignBits = null;\n      mode = null;\n    } else if (typeof mode === \"function\" && typeof cb === \"undefined\") {\n      cb = mode;\n      mode = null;\n    }\n\n    alignBits =\n      alignBits ||\n      this.constructor.HORIZONTAL_ALIGN_CENTER |\n        this.constructor.VERTICAL_ALIGN_MIDDLE;\n    const hbits = alignBits & ((1 << 3) - 1);\n    const vbits = alignBits >> 3;\n\n    // check if more flags than one is in the bit sets\n    if (\n      !(\n        (hbits !== 0 && !(hbits & (hbits - 1))) ||\n        (vbits !== 0 && !(vbits & (vbits - 1)))\n      )\n    )\n      return throwError.call(\n        this,\n        \"only use one flag per alignment direction\",\n        cb\n      );\n\n    const alignH = hbits >> 1; // 0, 1, 2\n    const alignV = vbits >> 1; // 0, 1, 2\n\n    const f =\n      w / h > this.bitmap.width / this.bitmap.height\n        ? w / this.bitmap.width\n        : h / this.bitmap.height;\n    this.scale(f, mode);\n    this.crop(\n      ((this.bitmap.width - w) / 2) * alignH,\n      ((this.bitmap.height - h) / 2) * alignV,\n      w,\n      h\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,KAAK,CAACC,CAAC,EAAEC,CAAC,EAAEC,SAAS,EAAEC,IAAI,EAAEC,EAAE,EAAE;IAC/B,IAAI,OAAOJ,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAOH,UAAU,CAACO,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAED,EAAE,CAAC;IAC7D;IAEA,IACEF,SAAS,IACT,OAAOA,SAAS,KAAK,UAAU,IAC/B,OAAOE,EAAE,KAAK,WAAW,EACzB;MACAA,EAAE,GAAGF,SAAS;MACdA,SAAS,GAAG,IAAI;MAChBC,IAAI,GAAG,IAAI;IACb,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,IAAI,OAAOC,EAAE,KAAK,WAAW,EAAE;MAClEA,EAAE,GAAGD,IAAI;MACTA,IAAI,GAAG,IAAI;IACb;IAEAD,SAAS,GACPA,SAAS,IACT,IAAI,CAACI,WAAW,CAACC,uBAAuB,GACtC,IAAI,CAACD,WAAW,CAACE,qBAAqB;IAC1C,MAAMC,KAAK,GAAGP,SAAS,GAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAE;IACxC,MAAMQ,KAAK,GAAGR,SAAS,IAAI,CAAC;;IAE5B;IACA,IACE,EACGO,KAAK,KAAK,CAAC,IAAI,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAE,CAAC,IACrCC,KAAK,KAAK,CAAC,IAAI,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAE,CAAE,CACxC,EAED,OAAOZ,UAAU,CAACO,IAAI,CACpB,IAAI,EACJ,2CAA2C,EAC3CD,EAAE,CACH;IAEH,MAAMO,MAAM,GAAGF,KAAK,IAAI,CAAC,CAAC,CAAC;IAC3B,MAAMG,MAAM,GAAGF,KAAK,IAAI,CAAC,CAAC,CAAC;;IAE3B,MAAMG,CAAC,GACLb,CAAC,GAAGC,CAAC,GAAG,IAAI,CAACa,MAAM,CAACC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACE,MAAM,GAC1ChB,CAAC,GAAG,IAAI,CAACc,MAAM,CAACC,KAAK,GACrBd,CAAC,GAAG,IAAI,CAACa,MAAM,CAACE,MAAM;IAC5B,IAAI,CAACC,KAAK,CAACJ,CAAC,EAAEV,IAAI,CAAC;IACnB,IAAI,CAACe,IAAI,CACN,CAAC,IAAI,CAACJ,MAAM,CAACC,KAAK,GAAGf,CAAC,IAAI,CAAC,GAAIW,MAAM,EACrC,CAAC,IAAI,CAACG,MAAM,CAACE,MAAM,GAAGf,CAAC,IAAI,CAAC,GAAIW,MAAM,EACvCZ,CAAC,EACDC,CAAC,CACF;IAED,IAAIJ,aAAa,CAACO,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}