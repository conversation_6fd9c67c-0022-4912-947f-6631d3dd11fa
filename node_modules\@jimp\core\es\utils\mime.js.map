{"version": 3, "file": "mime.js", "names": ["mimeTypes", "findType", "extension", "Object", "entries", "find", "type", "includes", "addType", "mime", "extensions", "getType", "path", "pathParts", "split", "slice", "length", "pop", "getExtension", "toLowerCase"], "sources": ["../../src/utils/mime.js"], "sourcesContent": ["const mimeTypes = {};\n\nconst findType = (extension) =>\n  Object.entries(mimeTypes).find((type) => type[1].includes(extension)) || [];\n\nexport const addType = (mime, extensions) => {\n  mimeTypes[mime] = extensions;\n};\n\n/**\n * Lookup a mime type based on extension\n * @param {string} path path to find extension for\n * @returns {string} mime found mime type\n */\nexport const getType = (path) => {\n  const pathParts = path.split(\"/\").slice(-1);\n  const extension = pathParts[pathParts.length - 1].split(\".\").pop();\n  const type = findType(extension);\n\n  return type[0];\n};\n\n/**\n * Return file extension associated with a mime type\n * @param {string} type mime type to look up\n * @returns {string} extension file extension\n */\nexport const getExtension = (type) => (mimeTypes[type.toLowerCase()] || [])[0];\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,CAAC;AAEpB,MAAMC,QAAQ,GAAIC,SAAS,IACzBC,MAAM,CAACC,OAAO,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACL,SAAS,CAAC,CAAC,IAAI,EAAE;AAE7E,OAAO,MAAMM,OAAO,GAAG,CAACC,IAAI,EAAEC,UAAU,KAAK;EAC3CV,SAAS,CAACS,IAAI,CAAC,GAAGC,UAAU;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAIC,IAAI,IAAK;EAC/B,MAAMC,SAAS,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAMb,SAAS,GAAGW,SAAS,CAACA,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,EAAE;EAClE,MAAMX,IAAI,GAAGL,QAAQ,CAACC,SAAS,CAAC;EAEhC,OAAOI,IAAI,CAAC,CAAC,CAAC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMY,YAAY,GAAIZ,IAAI,IAAK,CAACN,SAAS,CAACM,IAAI,CAACa,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC"}