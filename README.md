# XAUUSD Trading Analyzer

A comprehensive trading analysis website for XAUUSD (Gold/USD) that uses Smart Money Concepts (SMC) and Inner Circle Trader (ICT) methodologies to provide detailed trade recommendations.

## 🚀 Features

### Core Functionality
- **Chart Screenshot Analysis**: Upload trading chart screenshots from any platform (MT4, MT5, TradingView)
- **Smart Money Concepts (SMC)**: Advanced market structure analysis, order blocks, fair value gaps, and liquidity analysis
- **Inner Circle Trader (ICT)**: Institutional order flow, market maker models, and time-based analysis
- **Dual Trading Strategies**: Scalping and Swing trading approaches with tailored analysis
- **Machine Learning Adaptation**: System learns from user feedback to improve analysis accuracy

### Trading Analysis Features
- **Market Structure Analysis**: Trend identification, higher highs/lows, key support/resistance levels
- **Order Block Detection**: Bullish and bearish institutional order blocks with strength ratings
- **Fair Value Gap Identification**: Unfilled price gaps for potential retracement targets
- **Liquidity Level Mapping**: Buy/sell liquidity pools and institutional levels
- **Technical Indicators**: RSI, MACD, Moving Averages, Bollinger Bands
- **Risk Management**: Precise stop loss and multiple take profit calculations

### Trade Recommendations
- **Entry Price**: Exact entry levels with detailed reasoning
- **Stop Loss**: Risk-managed stop loss placement
- **Take Profits**: TP1, TP2, TP3 with different risk-reward ratios
- **Confidence Scoring**: AI-powered confidence ratings for each analysis
- **Detailed Reasoning**: Comprehensive explanation of trade setup logic

## 🛠 Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Image Processing**: Sharp for image analysis and processing
- **Database**: SQLite (in-memory for demo, easily upgradeable to PostgreSQL/MongoDB)
- **Styling**: Tailwind CSS with custom gold/trading theme
- **Icons**: Lucide React for modern iconography

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd xauusd-trading-analyzer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## 🎯 Usage

### 1. Upload Chart Screenshot
- Select your trading strategy (Scalping or Swing Trading)
- Upload a clear screenshot of your XAUUSD chart
- Supported formats: JPG, PNG, WebP (max 10MB)

### 2. Receive Analysis
- Get detailed trade recommendations within seconds
- View entry price, stop loss, and take profit levels
- Read comprehensive reasoning based on SMC and ICT concepts

### 3. Provide Feedback
- Rate analysis accuracy and profitability
- Report actual trade outcomes
- Help the AI learn and improve future analysis

### 4. Track Statistics
- View analysis history and performance metrics
- Monitor signal distribution and confidence trends
- Access performance insights and recommendations

## 🧠 Analysis Methodology

### Smart Money Concepts (SMC)
- **Market Structure**: Identifies trend direction and key structural levels
- **Order Blocks**: Detects institutional buying/selling zones
- **Fair Value Gaps**: Finds price imbalances for potential fills
- **Liquidity Sweeps**: Identifies stop hunt areas and liquidity pools

### Inner Circle Trader (ICT)
- **Kill Zones**: London, New York, and Asian session timing
- **Optimal Trade Entry (OTE)**: Fibonacci retracement zones (61.8%-78.6%)
- **Market Maker Models**: Accumulation, Manipulation, Distribution phases
- **Institutional Order Flow**: Analysis of smart money movement

### Technical Analysis
- **Trend Analysis**: Moving averages and trend confirmation
- **Momentum**: RSI and MACD for entry timing
- **Volatility**: Bollinger Bands for market conditions
- **Support/Resistance**: Key price levels and psychological numbers

## 📊 API Endpoints

### POST /api/analyze
Analyze uploaded chart image
```json
{
  "image": "File",
  "strategy": "scalping" | "swing"
}
```

### POST /api/feedback
Submit analysis feedback
```json
{
  "analysisId": "string",
  "accuracy": 1-5,
  "profitability": 1-5,
  "comments": "string",
  "actualOutcome": {
    "entryHit": boolean,
    "stopLossHit": boolean,
    "takeProfitsHit": {
      "tp1": boolean,
      "tp2": boolean,
      "tp3": boolean
    }
  }
}
```

### GET /api/analyze
Get analysis statistics
```json
{
  "totalAnalyses": number,
  "strategyBreakdown": object,
  "averageConfidence": number,
  "directionBreakdown": object
}
```

## 🔧 Configuration

### Environment Variables
Create a `.env.local` file:
```env
# Add any required environment variables here
# Currently, the app runs without external dependencies
```

### Customization
- **Trading Pairs**: Modify `src/types/trading.ts` to support additional pairs
- **Strategies**: Add new strategies in `src/lib/tradingStrategies.ts`
- **Indicators**: Extend technical analysis in `src/lib/technicalIndicators.ts`
- **Database**: Upgrade from SQLite to PostgreSQL/MongoDB in `src/lib/database.ts`

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Deploy automatically

### Docker
```bash
# Build image
docker build -t xauusd-analyzer .

# Run container
docker run -p 3000:3000 xauusd-analyzer
```

### Manual Deployment
```bash
# Build for production
npm run build

# Start production server
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This trading analysis tool is for educational and informational purposes only. It does not constitute financial advice. Trading involves substantial risk of loss and is not suitable for all investors. Always conduct your own research and consider consulting with a qualified financial advisor before making trading decisions.

## 🆘 Support

For support, email <EMAIL> or create an issue in the GitHub repository.

## 🔮 Roadmap

- [ ] Real-time market data integration
- [ ] Multiple timeframe analysis
- [ ] Advanced pattern recognition
- [ ] Portfolio management features
- [ ] Mobile app development
- [ ] Social trading features
- [ ] Advanced backtesting capabilities
- [ ] Integration with popular trading platforms

---

Built with ❤️ for the trading community
