"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-equal";
exports.ids = ["vendor-chunks/buffer-equal"];
exports.modules = {

/***/ "(rsc)/./node_modules/buffer-equal/index.js":
/*!********************************************!*\
  !*** ./node_modules/buffer-equal/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Buffer = (__webpack_require__(/*! buffer */ \"buffer\").Buffer); // for use with browserify\nmodule.exports = function(a, b) {\n    if (!Buffer.isBuffer(a)) return undefined;\n    if (!Buffer.isBuffer(b)) return undefined;\n    if (typeof a.equals === \"function\") return a.equals(b);\n    if (a.length !== b.length) return false;\n    for(var i = 0; i < a.length; i++){\n        if (a[i] !== b[i]) return false;\n    }\n    return true;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWVxdWFsL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7QUFBQSxJQUFJQSxTQUFTQyxvREFBd0IsRUFBRSwwQkFBMEI7QUFFakVDLE9BQU9DLE9BQU8sR0FBRyxTQUFVQyxDQUFDLEVBQUVDLENBQUM7SUFDM0IsSUFBSSxDQUFDTCxPQUFPTSxRQUFRLENBQUNGLElBQUksT0FBT0c7SUFDaEMsSUFBSSxDQUFDUCxPQUFPTSxRQUFRLENBQUNELElBQUksT0FBT0U7SUFDaEMsSUFBSSxPQUFPSCxFQUFFSSxNQUFNLEtBQUssWUFBWSxPQUFPSixFQUFFSSxNQUFNLENBQUNIO0lBQ3BELElBQUlELEVBQUVLLE1BQU0sS0FBS0osRUFBRUksTUFBTSxFQUFFLE9BQU87SUFFbEMsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlOLEVBQUVLLE1BQU0sRUFBRUMsSUFBSztRQUMvQixJQUFJTixDQUFDLENBQUNNLEVBQUUsS0FBS0wsQ0FBQyxDQUFDSyxFQUFFLEVBQUUsT0FBTztJQUM5QjtJQUVBLE9BQU87QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL3hhdXVzZC10cmFkaW5nLWFuYWx5emVyLy4vbm9kZV9tb2R1bGVzL2J1ZmZlci1lcXVhbC9pbmRleC5qcz8wNWQwIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBCdWZmZXIgPSByZXF1aXJlKCdidWZmZXInKS5CdWZmZXI7IC8vIGZvciB1c2Ugd2l0aCBicm93c2VyaWZ5XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKGEsIGIpIHtcbiAgICBpZiAoIUJ1ZmZlci5pc0J1ZmZlcihhKSkgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICBpZiAoIUJ1ZmZlci5pc0J1ZmZlcihiKSkgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICBpZiAodHlwZW9mIGEuZXF1YWxzID09PSAnZnVuY3Rpb24nKSByZXR1cm4gYS5lcXVhbHMoYik7XG4gICAgaWYgKGEubGVuZ3RoICE9PSBiLmxlbmd0aCkgcmV0dXJuIGZhbHNlO1xuICAgIFxuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgYS5sZW5ndGg7IGkrKykge1xuICAgICAgICBpZiAoYVtpXSAhPT0gYltpXSkgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gdHJ1ZTtcbn07XG4iXSwibmFtZXMiOlsiQnVmZmVyIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJhIiwiYiIsImlzQnVmZmVyIiwidW5kZWZpbmVkIiwiZXF1YWxzIiwibGVuZ3RoIiwiaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/buffer-equal/index.js\n");

/***/ })

};
;