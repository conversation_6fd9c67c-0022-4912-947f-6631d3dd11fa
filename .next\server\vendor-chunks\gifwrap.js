"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gifwrap";
exports.ids = ["vendor-chunks/gifwrap"];
exports.modules = {

/***/ "(rsc)/./node_modules/gifwrap/src/bitmapimage.js":
/*!*************************************************!*\
  !*** ./node_modules/gifwrap/src/bitmapimage.js ***!
  \*************************************************/
/***/ ((module) => {

eval("\n/** @class BitmapImage */ class BitmapImage {\n    /**\n     * BitmapImage is a class that hold an RGBA (red, green, blue, alpha) representation of an image. It's shape is borrowed from the Jimp package to make it easy to transfer GIF image frames into Jimp and Jimp images into GIF image frames. Each instance has a `bitmap` property having the following properties:\n     * \n     * Property | Description\n     * --- | ---\n     * bitmap.width | width of image in pixels\n     * bitmap.height | height of image in pixels\n     * bitmap.data | a Buffer whose every four bytes represents a pixel, each sequential byte of a pixel corresponding to the red, green, blue, and alpha values of the pixel\n     *\n     * Its constructor supports the following signatures:\n     *\n     * * new BitmapImage(bitmap: { width: number, height: number, data: Buffer })\n     * * new BitmapImage(bitmapImage: BitmapImage)\n     * * new BitmapImage(width: number, height: number, buffer: Buffer)\n     * * new BitmapImage(width: number, height: number, backgroundRGBA?: number)\n     * \n     * When a `BitmapImage` is provided, the constructed `BitmapImage` is a deep clone of the provided one, so that each image's pixel data can subsequently be modified without affecting each other.\n     *\n     * `backgroundRGBA` is an optional parameter representing a pixel as a single number. In hex, the number is as follows: 0xRRGGBBAA, where RR is the red byte, GG the green byte, BB, the blue byte, and AA the alpha value. An AA of 0x00 is considered transparent, and all non-zero AA values are treated as opaque.\n     */ constructor(...args){\n        // don't confirm the number of args, because a subclass may have\n        // additional args and pass them all to the superclass\n        if (args.length === 0) {\n            throw new Error(\"constructor requires parameters\");\n        }\n        const firstArg = args[0];\n        if (firstArg !== null && typeof firstArg === \"object\") {\n            if (firstArg instanceof BitmapImage) {\n                // copy a provided BitmapImage\n                const sourceBitmap = firstArg.bitmap;\n                this.bitmap = {\n                    width: sourceBitmap.width,\n                    height: sourceBitmap.height,\n                    data: new Buffer(sourceBitmap.width * sourceBitmap.height * 4)\n                };\n                sourceBitmap.data.copy(this.bitmap.data);\n            } else if (firstArg.width && firstArg.height && firstArg.data) {\n                // share a provided bitmap\n                this.bitmap = firstArg;\n            } else {\n                throw new Error(\"unrecognized constructor parameters\");\n            }\n        } else if (typeof firstArg === \"number\" && typeof args[1] === \"number\") {\n            const width = firstArg;\n            const height = args[1];\n            const thirdArg = args[2];\n            this.bitmap = {\n                width,\n                height\n            };\n            if (Buffer.isBuffer(thirdArg)) {\n                this.bitmap.data = thirdArg;\n            } else {\n                this.bitmap.data = new Buffer(width * height * 4);\n                if (typeof thirdArg === \"number\") {\n                    this.fillRGBA(thirdArg);\n                }\n            }\n        } else {\n            throw new Error(\"unrecognized constructor parameters\");\n        }\n    }\n    /**\n     * Copy a square portion of this image into another image. \n     * \n     * @param {BitmapImage} toImage Image into which to copy the square\n     * @param {number} toX x-coord in toImage of upper-left corner of receiving square\n     * @param {number} toY y-coord in toImage of upper-left corner of receiving square\n     * @param {number} fromX x-coord in this image of upper-left corner of source square\n     * @param {number} fromY y-coord in this image of upper-left corner of source square\n     * @return {BitmapImage} The present image to allow for chaining.\n     */ blit(toImage, toX, toY, fromX, fromY, fromWidth, fromHeight) {\n        if (fromX + fromWidth > this.bitmap.width) {\n            throw new Error(\"copy exceeds width of source bitmap\");\n        }\n        if (toX + fromWidth > toImage.bitmap.width) {\n            throw new Error(\"copy exceeds width of target bitmap\");\n        }\n        if (fromY + fromHeight > this.bitmap.height) {\n            throw new Error(\"copy exceeds height of source bitmap\");\n        }\n        if (toY + fromHeight > toImage.bitmap.height) {\n            throw new Erro(\"copy exceeds height of target bitmap\");\n        }\n        const sourceBuf = this.bitmap.data;\n        const targetBuf = toImage.bitmap.data;\n        const sourceByteWidth = this.bitmap.width * 4;\n        const targetByteWidth = toImage.bitmap.width * 4;\n        const copyByteWidth = fromWidth * 4;\n        let si = fromY * sourceByteWidth + fromX * 4;\n        let ti = toY * targetByteWidth + toX * 4;\n        while(--fromHeight >= 0){\n            sourceBuf.copy(targetBuf, ti, si, si + copyByteWidth);\n            si += sourceByteWidth;\n            ti += targetByteWidth;\n        }\n        return this;\n    }\n    /**\n     * Fills the image with a single color.\n     * \n     * @param {number} rgba Color with which to fill image, expressed as a singlenumber in the form 0xRRGGBBAA, where AA is 0x00 for transparent and any other value for opaque.\n     * @return {BitmapImage} The present image to allow for chaining.\n     */ fillRGBA(rgba) {\n        const buf = this.bitmap.data;\n        const bufByteWidth = this.bitmap.height * 4;\n        let bi = 0;\n        while(bi < bufByteWidth){\n            buf.writeUInt32BE(rgba, bi);\n            bi += 4;\n        }\n        while(bi < buf.length){\n            buf.copy(buf, bi, 0, bufByteWidth);\n            bi += bufByteWidth;\n        }\n        return this;\n    }\n    /**\n     * Gets the RGBA number of the pixel at the given coordinate in the form 0xRRGGBBAA, where AA is the alpha value, with alpha 0x00 encoding to transparency in GIFs.\n     * \n     * @param {number} x x-coord of pixel\n     * @param {number} y y-coord of pixel\n     * @return {number} RGBA of pixel in 0xRRGGBBAA form\n     */ getRGBA(x, y) {\n        const bi = (y * this.bitmap.width + x) * 4;\n        return this.bitmap.data.readUInt32BE(bi);\n    }\n    /**\n     * Gets a set of all RGBA colors found within the image.\n     * \n     * @return {Set} Set of all RGBA colors that the image contains.\n     */ getRGBASet() {\n        const rgbaSet = new Set();\n        const buf = this.bitmap.data;\n        for(let bi = 0; bi < buf.length; bi += 4){\n            rgbaSet.add(buf.readUInt32BE(bi, true));\n        }\n        return rgbaSet;\n    }\n    /**\n     * Converts the image to greyscale using inferred Adobe metrics.\n     * \n     * @return {BitmapImage} The present image to allow for chaining.\n     */ greyscale() {\n        const buf = this.bitmap.data;\n        this.scan(0, 0, this.bitmap.width, this.bitmap.height, (x, y, idx)=>{\n            const grey = Math.round(0.299 * buf[idx] + 0.587 * buf[idx + 1] + 0.114 * buf[idx + 2]);\n            buf[idx] = grey;\n            buf[idx + 1] = grey;\n            buf[idx + 2] = grey;\n        });\n        return this;\n    }\n    /**\n     * Reframes the image as if placing a frame around the original image and replacing the original image with the newly framed image. When the new frame is strictly within the boundaries of the original image, this method crops the image. When any of the new boundaries exceed those of the original image, the `fillRGBA` must be provided to indicate the color with which to fill the extra space added to the image.\n     * \n     * @param {number} xOffset The x-coord offset of the upper-left pixel of the desired image relative to the present image.\n     * @param {number} yOffset The y-coord offset of the upper-left pixel of the desired image relative to the present image.\n     * @param {number} width The width of the new image after reframing\n     * @param {number} height The height of the new image after reframing\n     * @param {number} fillRGBA The color with which to fill space added to the image as a result of the reframing, in 0xRRGGBBAA format, where AA is 0x00 to indicate transparent and a non-zero value to indicate opaque. This parameter is only required when the reframing exceeds the original boundaries (i.e. does not simply perform a crop).\n     * @return {BitmapImage} The present image to allow for chaining.\n     */ reframe(xOffset, yOffset, width, height, fillRGBA) {\n        const cropX = xOffset < 0 ? 0 : xOffset;\n        const cropY = yOffset < 0 ? 0 : yOffset;\n        const cropWidth = width + cropX > this.bitmap.width ? this.bitmap.width - cropX : width;\n        const cropHeight = height + cropY > this.bitmap.height ? this.bitmap.height - cropY : height;\n        const newX = xOffset < 0 ? -xOffset : 0;\n        const newY = yOffset < 0 ? -yOffset : 0;\n        let image;\n        if (fillRGBA === undefined) {\n            if (cropX !== xOffset || cropY != yOffset || cropWidth !== width || cropHeight !== height) {\n                throw new GifError(`fillRGBA required for this reframing`);\n            }\n            image = new BitmapImage(width, height);\n        } else {\n            image = new BitmapImage(width, height, fillRGBA);\n        }\n        this.blit(image, newX, newY, cropX, cropY, cropWidth, cropHeight);\n        this.bitmap = image.bitmap;\n        return this;\n    }\n    /**\n     * Scales the image size up by an integer factor. Each pixel of the original image becomes a square of the same color in the new image having a size of `factor` x `factor` pixels.\n     * \n     * @param {number} factor The factor by which to scale up the image. Must be an integer >= 1.\n     * @return {BitmapImage} The present image to allow for chaining.\n     */ scale(factor) {\n        if (factor === 1) {\n            return;\n        }\n        if (!Number.isInteger(factor) || factor < 1) {\n            throw new Error(\"the scale must be an integer >= 1\");\n        }\n        const sourceWidth = this.bitmap.width;\n        const sourceHeight = this.bitmap.height;\n        const destByteWidth = sourceWidth * factor * 4;\n        const sourceBuf = this.bitmap.data;\n        const destBuf = new Buffer(sourceHeight * destByteWidth * factor);\n        let sourceIndex = 0;\n        let priorDestRowIndex;\n        let destIndex = 0;\n        for(let y = 0; y < sourceHeight; ++y){\n            priorDestRowIndex = destIndex;\n            for(let x = 0; x < sourceWidth; ++x){\n                const color = sourceBuf.readUInt32BE(sourceIndex, true);\n                for(let cx = 0; cx < factor; ++cx){\n                    destBuf.writeUInt32BE(color, destIndex);\n                    destIndex += 4;\n                }\n                sourceIndex += 4;\n            }\n            for(let cy = 1; cy < factor; ++cy){\n                destBuf.copy(destBuf, destIndex, priorDestRowIndex, destIndex);\n                destIndex += destByteWidth;\n                priorDestRowIndex += destByteWidth;\n            }\n        }\n        this.bitmap = {\n            width: sourceWidth * factor,\n            height: sourceHeight * factor,\n            data: destBuf\n        };\n        return this;\n    }\n    /**\n     * Scans all coordinates of the image, handing each in turn to the provided handler function.\n     *\n     * @param {function} scanHandler A function(x: number, y: number, bi: number) to be called for each pixel of the image with that pixel's x-coord, y-coord, and index into the `data` buffer. The function accesses the pixel at this coordinate by accessing the `this.data` at index `bi`.\n     * @see scanAllIndexes\n     */ scanAllCoords(scanHandler) {\n        const width = this.bitmap.width;\n        const bufferLength = this.bitmap.data.length;\n        let x = 0;\n        let y = 0;\n        for(let bi = 0; bi < bufferLength; bi += 4){\n            scanHandler(x, y, bi);\n            if (++x === width) {\n                x = 0;\n                ++y;\n            }\n        }\n    }\n    /**\n     * Scans all pixels of the image, handing the index of each in turn to the provided handler function. Runs a bit faster than `scanAllCoords()`, should the handler not need pixel coordinates.\n     *\n     * @param {function} scanHandler A function(bi: number) to be called for each pixel of the image with that pixel's index into the `data` buffer. The pixels is found at index 'bi' within `this.data`.\n     * @see scanAllCoords\n     */ scanAllIndexes(scanHandler) {\n        const bufferLength = this.bitmap.data.length;\n        for(let bi = 0; bi < bufferLength; bi += 4){\n            scanHandler(bi);\n        }\n    }\n}\nmodule.exports = BitmapImage;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2lmd3JhcC9zcmMvYml0bWFwaW1hZ2UuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSx1QkFBdUIsR0FFdkIsTUFBTUE7SUFFRjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztLQW1CQyxHQUVEQyxZQUFZLEdBQUdDLElBQUksQ0FBRTtRQUNqQixnRUFBZ0U7UUFDaEUsc0RBQXNEO1FBQ3RELElBQUlBLEtBQUtDLE1BQU0sS0FBSyxHQUFHO1lBQ25CLE1BQU0sSUFBSUMsTUFBTTtRQUNwQjtRQUNBLE1BQU1DLFdBQVdILElBQUksQ0FBQyxFQUFFO1FBQ3hCLElBQUlHLGFBQWEsUUFBUSxPQUFPQSxhQUFhLFVBQVU7WUFDbkQsSUFBSUEsb0JBQW9CTCxhQUFhO2dCQUNqQyw4QkFBOEI7Z0JBQzlCLE1BQU1NLGVBQWVELFNBQVNFLE1BQU07Z0JBQ3BDLElBQUksQ0FBQ0EsTUFBTSxHQUFHO29CQUNWQyxPQUFPRixhQUFhRSxLQUFLO29CQUN6QkMsUUFBUUgsYUFBYUcsTUFBTTtvQkFDM0JDLE1BQU0sSUFBSUMsT0FBT0wsYUFBYUUsS0FBSyxHQUFHRixhQUFhRyxNQUFNLEdBQUc7Z0JBQ2hFO2dCQUNBSCxhQUFhSSxJQUFJLENBQUNFLElBQUksQ0FBQyxJQUFJLENBQUNMLE1BQU0sQ0FBQ0csSUFBSTtZQUMzQyxPQUNLLElBQUlMLFNBQVNHLEtBQUssSUFBSUgsU0FBU0ksTUFBTSxJQUFJSixTQUFTSyxJQUFJLEVBQUU7Z0JBQ3pELDBCQUEwQjtnQkFDMUIsSUFBSSxDQUFDSCxNQUFNLEdBQUdGO1lBQ2xCLE9BQ0s7Z0JBQ0QsTUFBTSxJQUFJRCxNQUFNO1lBQ3BCO1FBQ0osT0FDSyxJQUFJLE9BQU9DLGFBQWEsWUFBWSxPQUFPSCxJQUFJLENBQUMsRUFBRSxLQUFLLFVBQzVEO1lBQ0ksTUFBTU0sUUFBUUg7WUFDZCxNQUFNSSxTQUFTUCxJQUFJLENBQUMsRUFBRTtZQUN0QixNQUFNVyxXQUFXWCxJQUFJLENBQUMsRUFBRTtZQUN4QixJQUFJLENBQUNLLE1BQU0sR0FBRztnQkFBRUM7Z0JBQU9DO1lBQU87WUFFOUIsSUFBSUUsT0FBT0csUUFBUSxDQUFDRCxXQUFXO2dCQUMzQixJQUFJLENBQUNOLE1BQU0sQ0FBQ0csSUFBSSxHQUFHRztZQUN2QixPQUNLO2dCQUNELElBQUksQ0FBQ04sTUFBTSxDQUFDRyxJQUFJLEdBQUcsSUFBSUMsT0FBT0gsUUFBUUMsU0FBUztnQkFDL0MsSUFBSSxPQUFPSSxhQUFhLFVBQVU7b0JBQzlCLElBQUksQ0FBQ0UsUUFBUSxDQUFDRjtnQkFDbEI7WUFDSjtRQUNKLE9BQ0s7WUFDRCxNQUFNLElBQUlULE1BQU07UUFDcEI7SUFDSjtJQUVBOzs7Ozs7Ozs7S0FTQyxHQUVEWSxLQUFLQyxPQUFPLEVBQUVDLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxLQUFLLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUU7UUFDekQsSUFBSUgsUUFBUUUsWUFBWSxJQUFJLENBQUNmLE1BQU0sQ0FBQ0MsS0FBSyxFQUFFO1lBQ3ZDLE1BQU0sSUFBSUosTUFBTTtRQUNwQjtRQUNBLElBQUljLE1BQU1JLFlBQVlMLFFBQVFWLE1BQU0sQ0FBQ0MsS0FBSyxFQUFFO1lBQ3hDLE1BQU0sSUFBSUosTUFBTTtRQUNwQjtRQUNBLElBQUlpQixRQUFRRSxhQUFhLElBQUksQ0FBQ2hCLE1BQU0sQ0FBQ0UsTUFBTSxFQUFFO1lBQ3pDLE1BQU0sSUFBSUwsTUFBTTtRQUNwQjtRQUNBLElBQUllLE1BQU1JLGFBQWFOLFFBQVFWLE1BQU0sQ0FBQ0UsTUFBTSxFQUFFO1lBQzFDLE1BQU0sSUFBSWUsS0FBSztRQUNuQjtRQUVBLE1BQU1DLFlBQVksSUFBSSxDQUFDbEIsTUFBTSxDQUFDRyxJQUFJO1FBQ2xDLE1BQU1nQixZQUFZVCxRQUFRVixNQUFNLENBQUNHLElBQUk7UUFDckMsTUFBTWlCLGtCQUFrQixJQUFJLENBQUNwQixNQUFNLENBQUNDLEtBQUssR0FBRztRQUM1QyxNQUFNb0Isa0JBQWtCWCxRQUFRVixNQUFNLENBQUNDLEtBQUssR0FBRztRQUMvQyxNQUFNcUIsZ0JBQWdCUCxZQUFZO1FBQ2xDLElBQUlRLEtBQUtULFFBQVFNLGtCQUFrQlAsUUFBUTtRQUMzQyxJQUFJVyxLQUFLWixNQUFNUyxrQkFBa0JWLE1BQU07UUFFdkMsTUFBTyxFQUFFSyxjQUFjLEVBQUc7WUFDdEJFLFVBQVViLElBQUksQ0FBQ2MsV0FBV0ssSUFBSUQsSUFBSUEsS0FBS0Q7WUFDdkNDLE1BQU1IO1lBQ05JLE1BQU1IO1FBQ1Y7UUFDQSxPQUFPLElBQUk7SUFDZjtJQUVBOzs7OztLQUtDLEdBRURiLFNBQVNpQixJQUFJLEVBQUU7UUFDWCxNQUFNQyxNQUFNLElBQUksQ0FBQzFCLE1BQU0sQ0FBQ0csSUFBSTtRQUM1QixNQUFNd0IsZUFBZSxJQUFJLENBQUMzQixNQUFNLENBQUNFLE1BQU0sR0FBRztRQUUxQyxJQUFJMEIsS0FBSztRQUNULE1BQU9BLEtBQUtELGFBQWM7WUFDdEJELElBQUlHLGFBQWEsQ0FBQ0osTUFBTUc7WUFDeEJBLE1BQU07UUFDVjtRQUNBLE1BQU9BLEtBQUtGLElBQUk5QixNQUFNLENBQUU7WUFDcEI4QixJQUFJckIsSUFBSSxDQUFDcUIsS0FBS0UsSUFBSSxHQUFHRDtZQUNyQkMsTUFBTUQ7UUFDVjtRQUNBLE9BQU8sSUFBSTtJQUNmO0lBRUE7Ozs7OztLQU1DLEdBRURHLFFBQVFDLENBQUMsRUFBRUMsQ0FBQyxFQUFFO1FBQ1YsTUFBTUosS0FBSyxDQUFDSSxJQUFJLElBQUksQ0FBQ2hDLE1BQU0sQ0FBQ0MsS0FBSyxHQUFHOEIsQ0FBQUEsSUFBSztRQUN6QyxPQUFPLElBQUksQ0FBQy9CLE1BQU0sQ0FBQ0csSUFBSSxDQUFDOEIsWUFBWSxDQUFDTDtJQUN6QztJQUVBOzs7O0tBSUMsR0FFRE0sYUFBYTtRQUNULE1BQU1DLFVBQVUsSUFBSUM7UUFDcEIsTUFBTVYsTUFBTSxJQUFJLENBQUMxQixNQUFNLENBQUNHLElBQUk7UUFDNUIsSUFBSyxJQUFJeUIsS0FBSyxHQUFHQSxLQUFLRixJQUFJOUIsTUFBTSxFQUFFZ0MsTUFBTSxFQUFHO1lBQ3ZDTyxRQUFRRSxHQUFHLENBQUNYLElBQUlPLFlBQVksQ0FBQ0wsSUFBSTtRQUNyQztRQUNBLE9BQU9PO0lBQ1g7SUFFQTs7OztLQUlDLEdBRURHLFlBQVk7UUFDUixNQUFNWixNQUFNLElBQUksQ0FBQzFCLE1BQU0sQ0FBQ0csSUFBSTtRQUM1QixJQUFJLENBQUNvQyxJQUFJLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQ3ZDLE1BQU0sQ0FBQ0MsS0FBSyxFQUFFLElBQUksQ0FBQ0QsTUFBTSxDQUFDRSxNQUFNLEVBQUUsQ0FBQzZCLEdBQUdDLEdBQUdRO1lBQzFELE1BQU1DLE9BQU9DLEtBQUtDLEtBQUssQ0FDbkIsUUFBUWpCLEdBQUcsQ0FBQ2MsSUFBSSxHQUNoQixRQUFRZCxHQUFHLENBQUNjLE1BQU0sRUFBRSxHQUNwQixRQUFRZCxHQUFHLENBQUNjLE1BQU0sRUFBRTtZQUV4QmQsR0FBRyxDQUFDYyxJQUFJLEdBQUdDO1lBQ1hmLEdBQUcsQ0FBQ2MsTUFBTSxFQUFFLEdBQUdDO1lBQ2ZmLEdBQUcsQ0FBQ2MsTUFBTSxFQUFFLEdBQUdDO1FBQ25CO1FBQ0EsT0FBTyxJQUFJO0lBQ2Y7SUFFQTs7Ozs7Ozs7O0tBU0MsR0FFREcsUUFBUUMsT0FBTyxFQUFFQyxPQUFPLEVBQUU3QyxLQUFLLEVBQUVDLE1BQU0sRUFBRU0sUUFBUSxFQUFFO1FBQy9DLE1BQU11QyxRQUFTRixVQUFVLElBQUksSUFBSUE7UUFDakMsTUFBTUcsUUFBU0YsVUFBVSxJQUFJLElBQUlBO1FBQ2pDLE1BQU1HLFlBQWFoRCxRQUFROEMsUUFBUSxJQUFJLENBQUMvQyxNQUFNLENBQUNDLEtBQUssR0FDNUMsSUFBSSxDQUFDRCxNQUFNLENBQUNDLEtBQUssR0FBRzhDLFFBQVE5QztRQUNwQyxNQUFNaUQsYUFBY2hELFNBQVM4QyxRQUFRLElBQUksQ0FBQ2hELE1BQU0sQ0FBQ0UsTUFBTSxHQUMvQyxJQUFJLENBQUNGLE1BQU0sQ0FBQ0UsTUFBTSxHQUFHOEMsUUFBUTlDO1FBQ3JDLE1BQU1pRCxPQUFRTixVQUFVLElBQUksQ0FBQ0EsVUFBVTtRQUN2QyxNQUFNTyxPQUFRTixVQUFVLElBQUksQ0FBQ0EsVUFBVTtRQUV2QyxJQUFJTztRQUNKLElBQUk3QyxhQUFhOEMsV0FBVztZQUN4QixJQUFJUCxVQUFVRixXQUFXRyxTQUFTRixXQUMxQkcsY0FBY2hELFNBQVNpRCxlQUFlaEQsUUFDOUM7Z0JBQ0ksTUFBTSxJQUFJcUQsU0FBUyxDQUFDLG9DQUFvQyxDQUFDO1lBQzdEO1lBQ0FGLFFBQVEsSUFBSTVELFlBQVlRLE9BQU9DO1FBQ25DLE9BQ0s7WUFDRG1ELFFBQVEsSUFBSTVELFlBQVlRLE9BQU9DLFFBQVFNO1FBQzNDO1FBQ0EsSUFBSSxDQUFDQyxJQUFJLENBQUM0QyxPQUFPRixNQUFNQyxNQUFNTCxPQUFPQyxPQUFPQyxXQUFXQztRQUN0RCxJQUFJLENBQUNsRCxNQUFNLEdBQUdxRCxNQUFNckQsTUFBTTtRQUMxQixPQUFPLElBQUk7SUFDZjtJQUVBOzs7OztLQUtDLEdBRUR3RCxNQUFNQyxNQUFNLEVBQUU7UUFDVixJQUFJQSxXQUFXLEdBQUc7WUFDZDtRQUNKO1FBQ0EsSUFBSSxDQUFDQyxPQUFPQyxTQUFTLENBQUNGLFdBQVdBLFNBQVMsR0FBRztZQUN6QyxNQUFNLElBQUk1RCxNQUFNO1FBQ3BCO1FBQ0EsTUFBTStELGNBQWMsSUFBSSxDQUFDNUQsTUFBTSxDQUFDQyxLQUFLO1FBQ3JDLE1BQU00RCxlQUFlLElBQUksQ0FBQzdELE1BQU0sQ0FBQ0UsTUFBTTtRQUN2QyxNQUFNNEQsZ0JBQWdCRixjQUFjSCxTQUFTO1FBQzdDLE1BQU12QyxZQUFZLElBQUksQ0FBQ2xCLE1BQU0sQ0FBQ0csSUFBSTtRQUNsQyxNQUFNNEQsVUFBVSxJQUFJM0QsT0FBT3lELGVBQWVDLGdCQUFnQkw7UUFDMUQsSUFBSU8sY0FBYztRQUNsQixJQUFJQztRQUNKLElBQUlDLFlBQVk7UUFDaEIsSUFBSyxJQUFJbEMsSUFBSSxHQUFHQSxJQUFJNkIsY0FBYyxFQUFFN0IsRUFBRztZQUNuQ2lDLG9CQUFvQkM7WUFDcEIsSUFBSyxJQUFJbkMsSUFBSSxHQUFHQSxJQUFJNkIsYUFBYSxFQUFFN0IsRUFBRztnQkFDbEMsTUFBTW9DLFFBQVFqRCxVQUFVZSxZQUFZLENBQUMrQixhQUFhO2dCQUNsRCxJQUFLLElBQUlJLEtBQUssR0FBR0EsS0FBS1gsUUFBUSxFQUFFVyxHQUFJO29CQUNoQ0wsUUFBUWxDLGFBQWEsQ0FBQ3NDLE9BQU9EO29CQUM3QkEsYUFBYTtnQkFDakI7Z0JBQ0FGLGVBQWU7WUFDbkI7WUFDQSxJQUFLLElBQUlLLEtBQUssR0FBR0EsS0FBS1osUUFBUSxFQUFFWSxHQUFJO2dCQUNoQ04sUUFBUTFELElBQUksQ0FBQzBELFNBQVNHLFdBQVdELG1CQUFtQkM7Z0JBQ3BEQSxhQUFhSjtnQkFDYkcscUJBQXFCSDtZQUN6QjtRQUNKO1FBQ0EsSUFBSSxDQUFDOUQsTUFBTSxHQUFHO1lBQ1ZDLE9BQU8yRCxjQUFjSDtZQUNyQnZELFFBQVEyRCxlQUFlSjtZQUN2QnRELE1BQU00RDtRQUNWO1FBQ0EsT0FBTyxJQUFJO0lBQ2Y7SUFFQTs7Ozs7S0FLQyxHQUVETyxjQUFjQyxXQUFXLEVBQUU7UUFDdkIsTUFBTXRFLFFBQVEsSUFBSSxDQUFDRCxNQUFNLENBQUNDLEtBQUs7UUFDL0IsTUFBTXVFLGVBQWUsSUFBSSxDQUFDeEUsTUFBTSxDQUFDRyxJQUFJLENBQUNQLE1BQU07UUFDNUMsSUFBSW1DLElBQUk7UUFDUixJQUFJQyxJQUFJO1FBRVIsSUFBSyxJQUFJSixLQUFLLEdBQUdBLEtBQUs0QyxjQUFjNUMsTUFBTSxFQUFHO1lBQ3pDMkMsWUFBWXhDLEdBQUdDLEdBQUdKO1lBQ2xCLElBQUksRUFBRUcsTUFBTTlCLE9BQU87Z0JBQ2Y4QixJQUFJO2dCQUNKLEVBQUVDO1lBQ047UUFDSjtJQUNKO0lBRUE7Ozs7O0tBS0MsR0FFRHlDLGVBQWVGLFdBQVcsRUFBRTtRQUN4QixNQUFNQyxlQUFlLElBQUksQ0FBQ3hFLE1BQU0sQ0FBQ0csSUFBSSxDQUFDUCxNQUFNO1FBQzVDLElBQUssSUFBSWdDLEtBQUssR0FBR0EsS0FBSzRDLGNBQWM1QyxNQUFNLEVBQUc7WUFDekMyQyxZQUFZM0M7UUFDaEI7SUFDSjtBQUNKO0FBRUE4QyxPQUFPQyxPQUFPLEdBQUdsRiIsInNvdXJjZXMiOlsid2VicGFjazovL3hhdXVzZC10cmFkaW5nLWFuYWx5emVyLy4vbm9kZV9tb2R1bGVzL2dpZndyYXAvc3JjL2JpdG1hcGltYWdlLmpzP2E0ZmMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKiogQGNsYXNzIEJpdG1hcEltYWdlICovXG5cbmNsYXNzIEJpdG1hcEltYWdlIHtcblxuICAgIC8qKlxuICAgICAqIEJpdG1hcEltYWdlIGlzIGEgY2xhc3MgdGhhdCBob2xkIGFuIFJHQkEgKHJlZCwgZ3JlZW4sIGJsdWUsIGFscGhhKSByZXByZXNlbnRhdGlvbiBvZiBhbiBpbWFnZS4gSXQncyBzaGFwZSBpcyBib3Jyb3dlZCBmcm9tIHRoZSBKaW1wIHBhY2thZ2UgdG8gbWFrZSBpdCBlYXN5IHRvIHRyYW5zZmVyIEdJRiBpbWFnZSBmcmFtZXMgaW50byBKaW1wIGFuZCBKaW1wIGltYWdlcyBpbnRvIEdJRiBpbWFnZSBmcmFtZXMuIEVhY2ggaW5zdGFuY2UgaGFzIGEgYGJpdG1hcGAgcHJvcGVydHkgaGF2aW5nIHRoZSBmb2xsb3dpbmcgcHJvcGVydGllczpcbiAgICAgKiBcbiAgICAgKiBQcm9wZXJ0eSB8IERlc2NyaXB0aW9uXG4gICAgICogLS0tIHwgLS0tXG4gICAgICogYml0bWFwLndpZHRoIHwgd2lkdGggb2YgaW1hZ2UgaW4gcGl4ZWxzXG4gICAgICogYml0bWFwLmhlaWdodCB8IGhlaWdodCBvZiBpbWFnZSBpbiBwaXhlbHNcbiAgICAgKiBiaXRtYXAuZGF0YSB8IGEgQnVmZmVyIHdob3NlIGV2ZXJ5IGZvdXIgYnl0ZXMgcmVwcmVzZW50cyBhIHBpeGVsLCBlYWNoIHNlcXVlbnRpYWwgYnl0ZSBvZiBhIHBpeGVsIGNvcnJlc3BvbmRpbmcgdG8gdGhlIHJlZCwgZ3JlZW4sIGJsdWUsIGFuZCBhbHBoYSB2YWx1ZXMgb2YgdGhlIHBpeGVsXG4gICAgICpcbiAgICAgKiBJdHMgY29uc3RydWN0b3Igc3VwcG9ydHMgdGhlIGZvbGxvd2luZyBzaWduYXR1cmVzOlxuICAgICAqXG4gICAgICogKiBuZXcgQml0bWFwSW1hZ2UoYml0bWFwOiB7IHdpZHRoOiBudW1iZXIsIGhlaWdodDogbnVtYmVyLCBkYXRhOiBCdWZmZXIgfSlcbiAgICAgKiAqIG5ldyBCaXRtYXBJbWFnZShiaXRtYXBJbWFnZTogQml0bWFwSW1hZ2UpXG4gICAgICogKiBuZXcgQml0bWFwSW1hZ2Uod2lkdGg6IG51bWJlciwgaGVpZ2h0OiBudW1iZXIsIGJ1ZmZlcjogQnVmZmVyKVxuICAgICAqICogbmV3IEJpdG1hcEltYWdlKHdpZHRoOiBudW1iZXIsIGhlaWdodDogbnVtYmVyLCBiYWNrZ3JvdW5kUkdCQT86IG51bWJlcilcbiAgICAgKiBcbiAgICAgKiBXaGVuIGEgYEJpdG1hcEltYWdlYCBpcyBwcm92aWRlZCwgdGhlIGNvbnN0cnVjdGVkIGBCaXRtYXBJbWFnZWAgaXMgYSBkZWVwIGNsb25lIG9mIHRoZSBwcm92aWRlZCBvbmUsIHNvIHRoYXQgZWFjaCBpbWFnZSdzIHBpeGVsIGRhdGEgY2FuIHN1YnNlcXVlbnRseSBiZSBtb2RpZmllZCB3aXRob3V0IGFmZmVjdGluZyBlYWNoIG90aGVyLlxuICAgICAqXG4gICAgICogYGJhY2tncm91bmRSR0JBYCBpcyBhbiBvcHRpb25hbCBwYXJhbWV0ZXIgcmVwcmVzZW50aW5nIGEgcGl4ZWwgYXMgYSBzaW5nbGUgbnVtYmVyLiBJbiBoZXgsIHRoZSBudW1iZXIgaXMgYXMgZm9sbG93czogMHhSUkdHQkJBQSwgd2hlcmUgUlIgaXMgdGhlIHJlZCBieXRlLCBHRyB0aGUgZ3JlZW4gYnl0ZSwgQkIsIHRoZSBibHVlIGJ5dGUsIGFuZCBBQSB0aGUgYWxwaGEgdmFsdWUuIEFuIEFBIG9mIDB4MDAgaXMgY29uc2lkZXJlZCB0cmFuc3BhcmVudCwgYW5kIGFsbCBub24temVybyBBQSB2YWx1ZXMgYXJlIHRyZWF0ZWQgYXMgb3BhcXVlLlxuICAgICAqL1xuXG4gICAgY29uc3RydWN0b3IoLi4uYXJncykge1xuICAgICAgICAvLyBkb24ndCBjb25maXJtIHRoZSBudW1iZXIgb2YgYXJncywgYmVjYXVzZSBhIHN1YmNsYXNzIG1heSBoYXZlXG4gICAgICAgIC8vIGFkZGl0aW9uYWwgYXJncyBhbmQgcGFzcyB0aGVtIGFsbCB0byB0aGUgc3VwZXJjbGFzc1xuICAgICAgICBpZiAoYXJncy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcImNvbnN0cnVjdG9yIHJlcXVpcmVzIHBhcmFtZXRlcnNcIik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZmlyc3RBcmcgPSBhcmdzWzBdO1xuICAgICAgICBpZiAoZmlyc3RBcmcgIT09IG51bGwgJiYgdHlwZW9mIGZpcnN0QXJnID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgaWYgKGZpcnN0QXJnIGluc3RhbmNlb2YgQml0bWFwSW1hZ2UpIHtcbiAgICAgICAgICAgICAgICAvLyBjb3B5IGEgcHJvdmlkZWQgQml0bWFwSW1hZ2VcbiAgICAgICAgICAgICAgICBjb25zdCBzb3VyY2VCaXRtYXAgPSBmaXJzdEFyZy5iaXRtYXA7XG4gICAgICAgICAgICAgICAgdGhpcy5iaXRtYXAgPSB7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiBzb3VyY2VCaXRtYXAud2lkdGgsXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogc291cmNlQml0bWFwLmhlaWdodCxcbiAgICAgICAgICAgICAgICAgICAgZGF0YTogbmV3IEJ1ZmZlcihzb3VyY2VCaXRtYXAud2lkdGggKiBzb3VyY2VCaXRtYXAuaGVpZ2h0ICogNClcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIHNvdXJjZUJpdG1hcC5kYXRhLmNvcHkodGhpcy5iaXRtYXAuZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChmaXJzdEFyZy53aWR0aCAmJiBmaXJzdEFyZy5oZWlnaHQgJiYgZmlyc3RBcmcuZGF0YSkge1xuICAgICAgICAgICAgICAgIC8vIHNoYXJlIGEgcHJvdmlkZWQgYml0bWFwXG4gICAgICAgICAgICAgICAgdGhpcy5iaXRtYXAgPSBmaXJzdEFyZztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcInVucmVjb2duaXplZCBjb25zdHJ1Y3RvciBwYXJhbWV0ZXJzXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBmaXJzdEFyZyA9PT0gJ251bWJlcicgJiYgdHlwZW9mIGFyZ3NbMV0gPT09ICdudW1iZXInKVxuICAgICAgICB7XG4gICAgICAgICAgICBjb25zdCB3aWR0aCA9IGZpcnN0QXJnO1xuICAgICAgICAgICAgY29uc3QgaGVpZ2h0ID0gYXJnc1sxXTtcbiAgICAgICAgICAgIGNvbnN0IHRoaXJkQXJnID0gYXJnc1syXTtcbiAgICAgICAgICAgIHRoaXMuYml0bWFwID0geyB3aWR0aCwgaGVpZ2h0IH07XG5cbiAgICAgICAgICAgIGlmIChCdWZmZXIuaXNCdWZmZXIodGhpcmRBcmcpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5iaXRtYXAuZGF0YSA9IHRoaXJkQXJnO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5iaXRtYXAuZGF0YSA9IG5ldyBCdWZmZXIod2lkdGggKiBoZWlnaHQgKiA0KTtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHRoaXJkQXJnID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZpbGxSR0JBKHRoaXJkQXJnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJ1bnJlY29nbml6ZWQgY29uc3RydWN0b3IgcGFyYW1ldGVyc1wiKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIENvcHkgYSBzcXVhcmUgcG9ydGlvbiBvZiB0aGlzIGltYWdlIGludG8gYW5vdGhlciBpbWFnZS4gXG4gICAgICogXG4gICAgICogQHBhcmFtIHtCaXRtYXBJbWFnZX0gdG9JbWFnZSBJbWFnZSBpbnRvIHdoaWNoIHRvIGNvcHkgdGhlIHNxdWFyZVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB0b1ggeC1jb29yZCBpbiB0b0ltYWdlIG9mIHVwcGVyLWxlZnQgY29ybmVyIG9mIHJlY2VpdmluZyBzcXVhcmVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gdG9ZIHktY29vcmQgaW4gdG9JbWFnZSBvZiB1cHBlci1sZWZ0IGNvcm5lciBvZiByZWNlaXZpbmcgc3F1YXJlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGZyb21YIHgtY29vcmQgaW4gdGhpcyBpbWFnZSBvZiB1cHBlci1sZWZ0IGNvcm5lciBvZiBzb3VyY2Ugc3F1YXJlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGZyb21ZIHktY29vcmQgaW4gdGhpcyBpbWFnZSBvZiB1cHBlci1sZWZ0IGNvcm5lciBvZiBzb3VyY2Ugc3F1YXJlXG4gICAgICogQHJldHVybiB7Qml0bWFwSW1hZ2V9IFRoZSBwcmVzZW50IGltYWdlIHRvIGFsbG93IGZvciBjaGFpbmluZy5cbiAgICAgKi9cblxuICAgIGJsaXQodG9JbWFnZSwgdG9YLCB0b1ksIGZyb21YLCBmcm9tWSwgZnJvbVdpZHRoLCBmcm9tSGVpZ2h0KSB7XG4gICAgICAgIGlmIChmcm9tWCArIGZyb21XaWR0aCA+IHRoaXMuYml0bWFwLndpZHRoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJjb3B5IGV4Y2VlZHMgd2lkdGggb2Ygc291cmNlIGJpdG1hcFwiKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodG9YICsgZnJvbVdpZHRoID4gdG9JbWFnZS5iaXRtYXAud2lkdGgpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcImNvcHkgZXhjZWVkcyB3aWR0aCBvZiB0YXJnZXQgYml0bWFwXCIpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChmcm9tWSArIGZyb21IZWlnaHQgPiB0aGlzLmJpdG1hcC5oZWlnaHQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcImNvcHkgZXhjZWVkcyBoZWlnaHQgb2Ygc291cmNlIGJpdG1hcFwiKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodG9ZICsgZnJvbUhlaWdodCA+IHRvSW1hZ2UuYml0bWFwLmhlaWdodCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm8oXCJjb3B5IGV4Y2VlZHMgaGVpZ2h0IG9mIHRhcmdldCBiaXRtYXBcIik7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnN0IHNvdXJjZUJ1ZiA9IHRoaXMuYml0bWFwLmRhdGE7XG4gICAgICAgIGNvbnN0IHRhcmdldEJ1ZiA9IHRvSW1hZ2UuYml0bWFwLmRhdGE7XG4gICAgICAgIGNvbnN0IHNvdXJjZUJ5dGVXaWR0aCA9IHRoaXMuYml0bWFwLndpZHRoICogNDtcbiAgICAgICAgY29uc3QgdGFyZ2V0Qnl0ZVdpZHRoID0gdG9JbWFnZS5iaXRtYXAud2lkdGggKiA0O1xuICAgICAgICBjb25zdCBjb3B5Qnl0ZVdpZHRoID0gZnJvbVdpZHRoICogNDtcbiAgICAgICAgbGV0IHNpID0gZnJvbVkgKiBzb3VyY2VCeXRlV2lkdGggKyBmcm9tWCAqIDQ7XG4gICAgICAgIGxldCB0aSA9IHRvWSAqIHRhcmdldEJ5dGVXaWR0aCArIHRvWCAqIDQ7XG5cbiAgICAgICAgd2hpbGUgKC0tZnJvbUhlaWdodCA+PSAwKSB7XG4gICAgICAgICAgICBzb3VyY2VCdWYuY29weSh0YXJnZXRCdWYsIHRpLCBzaSwgc2kgKyBjb3B5Qnl0ZVdpZHRoKTtcbiAgICAgICAgICAgIHNpICs9IHNvdXJjZUJ5dGVXaWR0aDtcbiAgICAgICAgICAgIHRpICs9IHRhcmdldEJ5dGVXaWR0aDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBGaWxscyB0aGUgaW1hZ2Ugd2l0aCBhIHNpbmdsZSBjb2xvci5cbiAgICAgKiBcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gcmdiYSBDb2xvciB3aXRoIHdoaWNoIHRvIGZpbGwgaW1hZ2UsIGV4cHJlc3NlZCBhcyBhIHNpbmdsZW51bWJlciBpbiB0aGUgZm9ybSAweFJSR0dCQkFBLCB3aGVyZSBBQSBpcyAweDAwIGZvciB0cmFuc3BhcmVudCBhbmQgYW55IG90aGVyIHZhbHVlIGZvciBvcGFxdWUuXG4gICAgICogQHJldHVybiB7Qml0bWFwSW1hZ2V9IFRoZSBwcmVzZW50IGltYWdlIHRvIGFsbG93IGZvciBjaGFpbmluZy5cbiAgICAgKi9cblxuICAgIGZpbGxSR0JBKHJnYmEpIHtcbiAgICAgICAgY29uc3QgYnVmID0gdGhpcy5iaXRtYXAuZGF0YTtcbiAgICAgICAgY29uc3QgYnVmQnl0ZVdpZHRoID0gdGhpcy5iaXRtYXAuaGVpZ2h0ICogNDtcbiAgICAgICAgXG4gICAgICAgIGxldCBiaSA9IDA7XG4gICAgICAgIHdoaWxlIChiaSA8IGJ1ZkJ5dGVXaWR0aCkge1xuICAgICAgICAgICAgYnVmLndyaXRlVUludDMyQkUocmdiYSwgYmkpO1xuICAgICAgICAgICAgYmkgKz0gNDtcbiAgICAgICAgfVxuICAgICAgICB3aGlsZSAoYmkgPCBidWYubGVuZ3RoKSB7XG4gICAgICAgICAgICBidWYuY29weShidWYsIGJpLCAwLCBidWZCeXRlV2lkdGgpO1xuICAgICAgICAgICAgYmkgKz0gYnVmQnl0ZVdpZHRoO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEdldHMgdGhlIFJHQkEgbnVtYmVyIG9mIHRoZSBwaXhlbCBhdCB0aGUgZ2l2ZW4gY29vcmRpbmF0ZSBpbiB0aGUgZm9ybSAweFJSR0dCQkFBLCB3aGVyZSBBQSBpcyB0aGUgYWxwaGEgdmFsdWUsIHdpdGggYWxwaGEgMHgwMCBlbmNvZGluZyB0byB0cmFuc3BhcmVuY3kgaW4gR0lGcy5cbiAgICAgKiBcbiAgICAgKiBAcGFyYW0ge251bWJlcn0geCB4LWNvb3JkIG9mIHBpeGVsXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHkgeS1jb29yZCBvZiBwaXhlbFxuICAgICAqIEByZXR1cm4ge251bWJlcn0gUkdCQSBvZiBwaXhlbCBpbiAweFJSR0dCQkFBIGZvcm1cbiAgICAgKi9cblxuICAgIGdldFJHQkEoeCwgeSkge1xuICAgICAgICBjb25zdCBiaSA9ICh5ICogdGhpcy5iaXRtYXAud2lkdGggKyB4KSAqIDQ7XG4gICAgICAgIHJldHVybiB0aGlzLmJpdG1hcC5kYXRhLnJlYWRVSW50MzJCRShiaSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogR2V0cyBhIHNldCBvZiBhbGwgUkdCQSBjb2xvcnMgZm91bmQgd2l0aGluIHRoZSBpbWFnZS5cbiAgICAgKiBcbiAgICAgKiBAcmV0dXJuIHtTZXR9IFNldCBvZiBhbGwgUkdCQSBjb2xvcnMgdGhhdCB0aGUgaW1hZ2UgY29udGFpbnMuXG4gICAgICovXG5cbiAgICBnZXRSR0JBU2V0KCkge1xuICAgICAgICBjb25zdCByZ2JhU2V0ID0gbmV3IFNldCgpO1xuICAgICAgICBjb25zdCBidWYgPSB0aGlzLmJpdG1hcC5kYXRhO1xuICAgICAgICBmb3IgKGxldCBiaSA9IDA7IGJpIDwgYnVmLmxlbmd0aDsgYmkgKz0gNCkge1xuICAgICAgICAgICAgcmdiYVNldC5hZGQoYnVmLnJlYWRVSW50MzJCRShiaSwgdHJ1ZSkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZ2JhU2V0O1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIENvbnZlcnRzIHRoZSBpbWFnZSB0byBncmV5c2NhbGUgdXNpbmcgaW5mZXJyZWQgQWRvYmUgbWV0cmljcy5cbiAgICAgKiBcbiAgICAgKiBAcmV0dXJuIHtCaXRtYXBJbWFnZX0gVGhlIHByZXNlbnQgaW1hZ2UgdG8gYWxsb3cgZm9yIGNoYWluaW5nLlxuICAgICAqL1xuXG4gICAgZ3JleXNjYWxlKCkge1xuICAgICAgICBjb25zdCBidWYgPSB0aGlzLmJpdG1hcC5kYXRhO1xuICAgICAgICB0aGlzLnNjYW4oMCwgMCwgdGhpcy5iaXRtYXAud2lkdGgsIHRoaXMuYml0bWFwLmhlaWdodCwgKHgsIHksIGlkeCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZ3JleSA9IE1hdGgucm91bmQoXG4gICAgICAgICAgICAgICAgMC4yOTkgKiBidWZbaWR4XSArXG4gICAgICAgICAgICAgICAgMC41ODcgKiBidWZbaWR4ICsgMV0gK1xuICAgICAgICAgICAgICAgIDAuMTE0ICogYnVmW2lkeCArIDJdXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgYnVmW2lkeF0gPSBncmV5O1xuICAgICAgICAgICAgYnVmW2lkeCArIDFdID0gZ3JleTtcbiAgICAgICAgICAgIGJ1ZltpZHggKyAyXSA9IGdyZXk7XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBSZWZyYW1lcyB0aGUgaW1hZ2UgYXMgaWYgcGxhY2luZyBhIGZyYW1lIGFyb3VuZCB0aGUgb3JpZ2luYWwgaW1hZ2UgYW5kIHJlcGxhY2luZyB0aGUgb3JpZ2luYWwgaW1hZ2Ugd2l0aCB0aGUgbmV3bHkgZnJhbWVkIGltYWdlLiBXaGVuIHRoZSBuZXcgZnJhbWUgaXMgc3RyaWN0bHkgd2l0aGluIHRoZSBib3VuZGFyaWVzIG9mIHRoZSBvcmlnaW5hbCBpbWFnZSwgdGhpcyBtZXRob2QgY3JvcHMgdGhlIGltYWdlLiBXaGVuIGFueSBvZiB0aGUgbmV3IGJvdW5kYXJpZXMgZXhjZWVkIHRob3NlIG9mIHRoZSBvcmlnaW5hbCBpbWFnZSwgdGhlIGBmaWxsUkdCQWAgbXVzdCBiZSBwcm92aWRlZCB0byBpbmRpY2F0ZSB0aGUgY29sb3Igd2l0aCB3aGljaCB0byBmaWxsIHRoZSBleHRyYSBzcGFjZSBhZGRlZCB0byB0aGUgaW1hZ2UuXG4gICAgICogXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHhPZmZzZXQgVGhlIHgtY29vcmQgb2Zmc2V0IG9mIHRoZSB1cHBlci1sZWZ0IHBpeGVsIG9mIHRoZSBkZXNpcmVkIGltYWdlIHJlbGF0aXZlIHRvIHRoZSBwcmVzZW50IGltYWdlLlxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB5T2Zmc2V0IFRoZSB5LWNvb3JkIG9mZnNldCBvZiB0aGUgdXBwZXItbGVmdCBwaXhlbCBvZiB0aGUgZGVzaXJlZCBpbWFnZSByZWxhdGl2ZSB0byB0aGUgcHJlc2VudCBpbWFnZS5cbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGggVGhlIHdpZHRoIG9mIHRoZSBuZXcgaW1hZ2UgYWZ0ZXIgcmVmcmFtaW5nXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGhlaWdodCBUaGUgaGVpZ2h0IG9mIHRoZSBuZXcgaW1hZ2UgYWZ0ZXIgcmVmcmFtaW5nXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGZpbGxSR0JBIFRoZSBjb2xvciB3aXRoIHdoaWNoIHRvIGZpbGwgc3BhY2UgYWRkZWQgdG8gdGhlIGltYWdlIGFzIGEgcmVzdWx0IG9mIHRoZSByZWZyYW1pbmcsIGluIDB4UlJHR0JCQUEgZm9ybWF0LCB3aGVyZSBBQSBpcyAweDAwIHRvIGluZGljYXRlIHRyYW5zcGFyZW50IGFuZCBhIG5vbi16ZXJvIHZhbHVlIHRvIGluZGljYXRlIG9wYXF1ZS4gVGhpcyBwYXJhbWV0ZXIgaXMgb25seSByZXF1aXJlZCB3aGVuIHRoZSByZWZyYW1pbmcgZXhjZWVkcyB0aGUgb3JpZ2luYWwgYm91bmRhcmllcyAoaS5lLiBkb2VzIG5vdCBzaW1wbHkgcGVyZm9ybSBhIGNyb3ApLlxuICAgICAqIEByZXR1cm4ge0JpdG1hcEltYWdlfSBUaGUgcHJlc2VudCBpbWFnZSB0byBhbGxvdyBmb3IgY2hhaW5pbmcuXG4gICAgICovXG5cbiAgICByZWZyYW1lKHhPZmZzZXQsIHlPZmZzZXQsIHdpZHRoLCBoZWlnaHQsIGZpbGxSR0JBKSB7XG4gICAgICAgIGNvbnN0IGNyb3BYID0gKHhPZmZzZXQgPCAwID8gMCA6IHhPZmZzZXQpO1xuICAgICAgICBjb25zdCBjcm9wWSA9ICh5T2Zmc2V0IDwgMCA/IDAgOiB5T2Zmc2V0KTtcbiAgICAgICAgY29uc3QgY3JvcFdpZHRoID0gKHdpZHRoICsgY3JvcFggPiB0aGlzLmJpdG1hcC53aWR0aCA/XG4gICAgICAgICAgICAgICAgdGhpcy5iaXRtYXAud2lkdGggLSBjcm9wWCA6IHdpZHRoKTtcbiAgICAgICAgY29uc3QgY3JvcEhlaWdodCA9IChoZWlnaHQgKyBjcm9wWSA+IHRoaXMuYml0bWFwLmhlaWdodCA/XG4gICAgICAgICAgICAgICAgdGhpcy5iaXRtYXAuaGVpZ2h0IC0gY3JvcFkgOiBoZWlnaHQpO1xuICAgICAgICBjb25zdCBuZXdYID0gKHhPZmZzZXQgPCAwID8gLXhPZmZzZXQgOiAwKTtcbiAgICAgICAgY29uc3QgbmV3WSA9ICh5T2Zmc2V0IDwgMCA/IC15T2Zmc2V0IDogMCk7XG5cbiAgICAgICAgbGV0IGltYWdlO1xuICAgICAgICBpZiAoZmlsbFJHQkEgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgaWYgKGNyb3BYICE9PSB4T2Zmc2V0IHx8IGNyb3BZICE9IHlPZmZzZXQgfHxcbiAgICAgICAgICAgICAgICAgICAgY3JvcFdpZHRoICE9PSB3aWR0aCB8fCBjcm9wSGVpZ2h0ICE9PSBoZWlnaHQpXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEdpZkVycm9yKGBmaWxsUkdCQSByZXF1aXJlZCBmb3IgdGhpcyByZWZyYW1pbmdgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGltYWdlID0gbmV3IEJpdG1hcEltYWdlKHdpZHRoLCBoZWlnaHQpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaW1hZ2UgPSBuZXcgQml0bWFwSW1hZ2Uod2lkdGgsIGhlaWdodCwgZmlsbFJHQkEpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuYmxpdChpbWFnZSwgbmV3WCwgbmV3WSwgY3JvcFgsIGNyb3BZLCBjcm9wV2lkdGgsIGNyb3BIZWlnaHQpO1xuICAgICAgICB0aGlzLmJpdG1hcCA9IGltYWdlLmJpdG1hcDtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogU2NhbGVzIHRoZSBpbWFnZSBzaXplIHVwIGJ5IGFuIGludGVnZXIgZmFjdG9yLiBFYWNoIHBpeGVsIG9mIHRoZSBvcmlnaW5hbCBpbWFnZSBiZWNvbWVzIGEgc3F1YXJlIG9mIHRoZSBzYW1lIGNvbG9yIGluIHRoZSBuZXcgaW1hZ2UgaGF2aW5nIGEgc2l6ZSBvZiBgZmFjdG9yYCB4IGBmYWN0b3JgIHBpeGVscy5cbiAgICAgKiBcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gZmFjdG9yIFRoZSBmYWN0b3IgYnkgd2hpY2ggdG8gc2NhbGUgdXAgdGhlIGltYWdlLiBNdXN0IGJlIGFuIGludGVnZXIgPj0gMS5cbiAgICAgKiBAcmV0dXJuIHtCaXRtYXBJbWFnZX0gVGhlIHByZXNlbnQgaW1hZ2UgdG8gYWxsb3cgZm9yIGNoYWluaW5nLlxuICAgICAqL1xuXG4gICAgc2NhbGUoZmFjdG9yKSB7XG4gICAgICAgIGlmIChmYWN0b3IgPT09IDEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIU51bWJlci5pc0ludGVnZXIoZmFjdG9yKSB8fCBmYWN0b3IgPCAxKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJ0aGUgc2NhbGUgbXVzdCBiZSBhbiBpbnRlZ2VyID49IDFcIik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgc291cmNlV2lkdGggPSB0aGlzLmJpdG1hcC53aWR0aDtcbiAgICAgICAgY29uc3Qgc291cmNlSGVpZ2h0ID0gdGhpcy5iaXRtYXAuaGVpZ2h0O1xuICAgICAgICBjb25zdCBkZXN0Qnl0ZVdpZHRoID0gc291cmNlV2lkdGggKiBmYWN0b3IgKiA0O1xuICAgICAgICBjb25zdCBzb3VyY2VCdWYgPSB0aGlzLmJpdG1hcC5kYXRhO1xuICAgICAgICBjb25zdCBkZXN0QnVmID0gbmV3IEJ1ZmZlcihzb3VyY2VIZWlnaHQgKiBkZXN0Qnl0ZVdpZHRoICogZmFjdG9yKTtcbiAgICAgICAgbGV0IHNvdXJjZUluZGV4ID0gMDtcbiAgICAgICAgbGV0IHByaW9yRGVzdFJvd0luZGV4O1xuICAgICAgICBsZXQgZGVzdEluZGV4ID0gMDtcbiAgICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCBzb3VyY2VIZWlnaHQ7ICsreSkge1xuICAgICAgICAgICAgcHJpb3JEZXN0Um93SW5kZXggPSBkZXN0SW5kZXg7XG4gICAgICAgICAgICBmb3IgKGxldCB4ID0gMDsgeCA8IHNvdXJjZVdpZHRoOyArK3gpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBjb2xvciA9IHNvdXJjZUJ1Zi5yZWFkVUludDMyQkUoc291cmNlSW5kZXgsIHRydWUpO1xuICAgICAgICAgICAgICAgIGZvciAobGV0IGN4ID0gMDsgY3ggPCBmYWN0b3I7ICsrY3gpIHtcbiAgICAgICAgICAgICAgICAgICAgZGVzdEJ1Zi53cml0ZVVJbnQzMkJFKGNvbG9yLCBkZXN0SW5kZXgpO1xuICAgICAgICAgICAgICAgICAgICBkZXN0SW5kZXggKz0gNDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgc291cmNlSW5kZXggKz0gNDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZvciAobGV0IGN5ID0gMTsgY3kgPCBmYWN0b3I7ICsrY3kpIHtcbiAgICAgICAgICAgICAgICBkZXN0QnVmLmNvcHkoZGVzdEJ1ZiwgZGVzdEluZGV4LCBwcmlvckRlc3RSb3dJbmRleCwgZGVzdEluZGV4KTtcbiAgICAgICAgICAgICAgICBkZXN0SW5kZXggKz0gZGVzdEJ5dGVXaWR0aDtcbiAgICAgICAgICAgICAgICBwcmlvckRlc3RSb3dJbmRleCArPSBkZXN0Qnl0ZVdpZHRoO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuYml0bWFwID0ge1xuICAgICAgICAgICAgd2lkdGg6IHNvdXJjZVdpZHRoICogZmFjdG9yLFxuICAgICAgICAgICAgaGVpZ2h0OiBzb3VyY2VIZWlnaHQgKiBmYWN0b3IsXG4gICAgICAgICAgICBkYXRhOiBkZXN0QnVmXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIFNjYW5zIGFsbCBjb29yZGluYXRlcyBvZiB0aGUgaW1hZ2UsIGhhbmRpbmcgZWFjaCBpbiB0dXJuIHRvIHRoZSBwcm92aWRlZCBoYW5kbGVyIGZ1bmN0aW9uLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtmdW5jdGlvbn0gc2NhbkhhbmRsZXIgQSBmdW5jdGlvbih4OiBudW1iZXIsIHk6IG51bWJlciwgYmk6IG51bWJlcikgdG8gYmUgY2FsbGVkIGZvciBlYWNoIHBpeGVsIG9mIHRoZSBpbWFnZSB3aXRoIHRoYXQgcGl4ZWwncyB4LWNvb3JkLCB5LWNvb3JkLCBhbmQgaW5kZXggaW50byB0aGUgYGRhdGFgIGJ1ZmZlci4gVGhlIGZ1bmN0aW9uIGFjY2Vzc2VzIHRoZSBwaXhlbCBhdCB0aGlzIGNvb3JkaW5hdGUgYnkgYWNjZXNzaW5nIHRoZSBgdGhpcy5kYXRhYCBhdCBpbmRleCBgYmlgLlxuICAgICAqIEBzZWUgc2NhbkFsbEluZGV4ZXNcbiAgICAgKi9cblxuICAgIHNjYW5BbGxDb29yZHMoc2NhbkhhbmRsZXIpIHtcbiAgICAgICAgY29uc3Qgd2lkdGggPSB0aGlzLmJpdG1hcC53aWR0aDtcbiAgICAgICAgY29uc3QgYnVmZmVyTGVuZ3RoID0gdGhpcy5iaXRtYXAuZGF0YS5sZW5ndGg7XG4gICAgICAgIGxldCB4ID0gMDtcbiAgICAgICAgbGV0IHkgPSAwO1xuXG4gICAgICAgIGZvciAobGV0IGJpID0gMDsgYmkgPCBidWZmZXJMZW5ndGg7IGJpICs9IDQpIHtcbiAgICAgICAgICAgIHNjYW5IYW5kbGVyKHgsIHksIGJpKTtcbiAgICAgICAgICAgIGlmICgrK3ggPT09IHdpZHRoKSB7XG4gICAgICAgICAgICAgICAgeCA9IDA7XG4gICAgICAgICAgICAgICAgKyt5O1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogU2NhbnMgYWxsIHBpeGVscyBvZiB0aGUgaW1hZ2UsIGhhbmRpbmcgdGhlIGluZGV4IG9mIGVhY2ggaW4gdHVybiB0byB0aGUgcHJvdmlkZWQgaGFuZGxlciBmdW5jdGlvbi4gUnVucyBhIGJpdCBmYXN0ZXIgdGhhbiBgc2NhbkFsbENvb3JkcygpYCwgc2hvdWxkIHRoZSBoYW5kbGVyIG5vdCBuZWVkIHBpeGVsIGNvb3JkaW5hdGVzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtmdW5jdGlvbn0gc2NhbkhhbmRsZXIgQSBmdW5jdGlvbihiaTogbnVtYmVyKSB0byBiZSBjYWxsZWQgZm9yIGVhY2ggcGl4ZWwgb2YgdGhlIGltYWdlIHdpdGggdGhhdCBwaXhlbCdzIGluZGV4IGludG8gdGhlIGBkYXRhYCBidWZmZXIuIFRoZSBwaXhlbHMgaXMgZm91bmQgYXQgaW5kZXggJ2JpJyB3aXRoaW4gYHRoaXMuZGF0YWAuXG4gICAgICogQHNlZSBzY2FuQWxsQ29vcmRzXG4gICAgICovXG5cbiAgICBzY2FuQWxsSW5kZXhlcyhzY2FuSGFuZGxlcikge1xuICAgICAgICBjb25zdCBidWZmZXJMZW5ndGggPSB0aGlzLmJpdG1hcC5kYXRhLmxlbmd0aDtcbiAgICAgICAgZm9yIChsZXQgYmkgPSAwOyBiaSA8IGJ1ZmZlckxlbmd0aDsgYmkgKz0gNCkge1xuICAgICAgICAgICAgc2NhbkhhbmRsZXIoYmkpO1xuICAgICAgICB9XG4gICAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IEJpdG1hcEltYWdlO1xuIl0sIm5hbWVzIjpbIkJpdG1hcEltYWdlIiwiY29uc3RydWN0b3IiLCJhcmdzIiwibGVuZ3RoIiwiRXJyb3IiLCJmaXJzdEFyZyIsInNvdXJjZUJpdG1hcCIsImJpdG1hcCIsIndpZHRoIiwiaGVpZ2h0IiwiZGF0YSIsIkJ1ZmZlciIsImNvcHkiLCJ0aGlyZEFyZyIsImlzQnVmZmVyIiwiZmlsbFJHQkEiLCJibGl0IiwidG9JbWFnZSIsInRvWCIsInRvWSIsImZyb21YIiwiZnJvbVkiLCJmcm9tV2lkdGgiLCJmcm9tSGVpZ2h0IiwiRXJybyIsInNvdXJjZUJ1ZiIsInRhcmdldEJ1ZiIsInNvdXJjZUJ5dGVXaWR0aCIsInRhcmdldEJ5dGVXaWR0aCIsImNvcHlCeXRlV2lkdGgiLCJzaSIsInRpIiwicmdiYSIsImJ1ZiIsImJ1ZkJ5dGVXaWR0aCIsImJpIiwid3JpdGVVSW50MzJCRSIsImdldFJHQkEiLCJ4IiwieSIsInJlYWRVSW50MzJCRSIsImdldFJHQkFTZXQiLCJyZ2JhU2V0IiwiU2V0IiwiYWRkIiwiZ3JleXNjYWxlIiwic2NhbiIsImlkeCIsImdyZXkiLCJNYXRoIiwicm91bmQiLCJyZWZyYW1lIiwieE9mZnNldCIsInlPZmZzZXQiLCJjcm9wWCIsImNyb3BZIiwiY3JvcFdpZHRoIiwiY3JvcEhlaWdodCIsIm5ld1giLCJuZXdZIiwiaW1hZ2UiLCJ1bmRlZmluZWQiLCJHaWZFcnJvciIsInNjYWxlIiwiZmFjdG9yIiwiTnVtYmVyIiwiaXNJbnRlZ2VyIiwic291cmNlV2lkdGgiLCJzb3VyY2VIZWlnaHQiLCJkZXN0Qnl0ZVdpZHRoIiwiZGVzdEJ1ZiIsInNvdXJjZUluZGV4IiwicHJpb3JEZXN0Um93SW5kZXgiLCJkZXN0SW5kZXgiLCJjb2xvciIsImN4IiwiY3kiLCJzY2FuQWxsQ29vcmRzIiwic2NhbkhhbmRsZXIiLCJidWZmZXJMZW5ndGgiLCJzY2FuQWxsSW5kZXhlcyIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gifwrap/src/bitmapimage.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gifwrap/src/gif.js":
/*!*****************************************!*\
  !*** ./node_modules/gifwrap/src/gif.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/** @class Gif */ class Gif {\n    // width - width of GIF in pixels\n    // height - height of GIF in pixels\n    // loops - 0 = unending; (n > 0) = iterate n times\n    // usesTransparency - whether any frames have transparent pixels\n    // colorScope - scope of color tables in GIF\n    // frames - array of frames\n    // buffer - GIF-formatted data\n    /**\n     * Gif is a class representing an encoded GIF. It is intended to be a read-only representation of a byte-encoded GIF. Only encoders and decoders should be creating instances of this class.\n     * \n     * Property | Description\n     * --- | ---\n     * width | width of the GIF at its widest\n     * height | height of the GIF at its highest\n     * loops | the number of times the GIF should loop before stopping; 0 => loop indefinitely\n     * usesTransparency | boolean indicating whether at least one frame contains at least one transparent pixel\n     * colorScope | the scope of the color tables as encoded within the GIF; either Gif.GlobalColorsOnly (== 1) or Gif.LocalColorsOnly (== 2).\n     * frames | a array of GifFrame instances, one for each frame of the GIF\n     * buffer | a Buffer holding the encoding's byte data\n     * \n     * Its constructor should only ever be called by the GIF encoder or decoder.\n     *\n     * @param {Buffer} buffer A Buffer containing the encoded bytes\n     * @param {GifFrame[]} frames Array of frames found in the encoding\n     * @param {object} spec Properties of the encoding as listed above\n     */ constructor(buffer, frames, spec){\n        this.width = spec.width;\n        this.height = spec.height;\n        this.loops = spec.loops;\n        this.usesTransparency = spec.usesTransparency;\n        this.colorScope = spec.colorScope;\n        this.frames = frames;\n        this.buffer = buffer;\n    }\n}\nGif.GlobalColorsPreferred = 0;\nGif.GlobalColorsOnly = 1;\nGif.LocalColorsOnly = 2;\n/** @class GifError */ class GifError extends Error {\n    /**\n     * GifError is a class representing a GIF-related error\n     * \n     * @param {string|Error} messageOrError\n     */ constructor(messageOrError){\n        super(messageOrError);\n        if (messageOrError instanceof Error) {\n            this.stack = \"Gif\" + messageOrError.stack;\n        }\n    }\n}\nexports.Gif = Gif;\nexports.GifError = GifError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gifwrap/src/gif.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gifwrap/src/gifcodec.js":
/*!**********************************************!*\
  !*** ./node_modules/gifwrap/src/gifcodec.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst Omggif = __webpack_require__(/*! omggif */ \"(rsc)/./node_modules/omggif/omggif.js\");\nconst { Gif, GifError } = __webpack_require__(/*! ./gif */ \"(rsc)/./node_modules/gifwrap/src/gif.js\");\n// allow circular dependency with GifUtil\nfunction GifUtil() {\n    const data = __webpack_require__(/*! ./gifutil */ \"(rsc)/./node_modules/gifwrap/src/gifutil.js\");\n    GifUtil = function() {\n        return data;\n    };\n    return data;\n}\nconst { GifFrame } = __webpack_require__(/*! ./gifframe */ \"(rsc)/./node_modules/gifwrap/src/gifframe.js\");\nconst PER_GIF_OVERHEAD = 200; // these are guesses at upper limits\nconst PER_FRAME_OVERHEAD = 100;\n// Note: I experimented with accepting a global color table when encoding and returning the global color table when decoding. Doing this properly greatly increased the complexity of the code and the amount of clock cycles required. The main issue is that each frame can specify any color of the global color table to be transparent within the frame, while this GIF library strives to hide GIF formatting details from its clients. E.g. it's possible to have 256 colors in the global color table and different transparencies in each frame, requiring clients to either provide per-frame transparency indexes, or for arcane reasons that won't be apparent to client developers, encode some GIFs with local color tables that previously decoded with global tables.\n/** @class GifCodec */ class GifCodec {\n    // _transparentRGBA - RGB given to transparent pixels (alpha=0) on decode; defaults to null indicating 0x000000, which is fastest\n    /**\n     * GifCodec is a class that both encodes and decodes GIFs. It implements both the `encode()` method expected of an encoder and the `decode()` method expected of a decoder, and it wraps the `omggif` GIF encoder/decoder package. GifCodec serves as this library's default encoder and decoder, but it's possible to wrap other GIF encoders and decoders for use by `gifwrap` as well. GifCodec will not encode GIFs with interlacing.\n     * \n     * Instances of this class are stateless and can be shared across multiple encodings and decodings.\n     * \n     * Its constructor takes one option argument:\n     * \n     * @param {object} options Optionally takes an objection whose only possible property is `transparentRGB`. Images are internally represented in RGBA format, where A is the alpha value of a pixel. When `transparentRGB` is provided, this RGB value (excluding alpha) is assigned to transparent pixels, which are also given alpha value 0x00. (All opaque pixels are given alpha value 0xFF). The RGB color of transparent pixels shouldn't matter for most applications. Defaults to 0x000000.\n     */ constructor(options = {}){\n        this._transparentRGB = null; // 0x000000\n        if (typeof options.transparentRGB === \"number\" && options.transparentRGB !== 0) {\n            this._transparentRGBA = options.transparentRGB * 256;\n        }\n        this._testInitialBufferSize = 0; // assume no buffer scaling test\n    }\n    /**\n     * Decodes a GIF from a Buffer to yield an instance of Gif. Transparent pixels of the GIF are given alpha values of 0x00, and opaque pixels are given alpha values of 0xFF. The RGB values of transparent pixels default to 0x000000 but can be overridden by the constructor's `transparentRGB` option.\n     * \n     * @param {Buffer} buffer Bytes of an encoded GIF to decode.\n     * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the encoded GIF.\n     * @throws {GifError} Error upon encountered an encoding-related problem with a GIF, so that the caller can distinguish between software errors and problems with GIFs.\n     */ decodeGif(buffer) {\n        try {\n            let reader;\n            try {\n                reader = new Omggif.GifReader(buffer);\n            } catch (err) {\n                throw new GifError(err);\n            }\n            const frameCount = reader.numFrames();\n            const frames = [];\n            const spec = {\n                width: reader.width,\n                height: reader.height,\n                loops: reader.loopCount()\n            };\n            spec.usesTransparency = false;\n            for(let i = 0; i < frameCount; ++i){\n                const frameInfo = this._decodeFrame(reader, i, spec.usesTransparency);\n                frames.push(frameInfo.frame);\n                if (frameInfo.usesTransparency) {\n                    spec.usesTransparency = true;\n                }\n            }\n            return Promise.resolve(new Gif(buffer, frames, spec));\n        } catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    /**\n     * Encodes a GIF from provided frames. Each pixel having an alpha value of 0x00 renders as transparent within the encoding, while all pixels of non-zero alpha value render as opaque.\n     * \n     * @param {GifFrame[]} frames Array of frames to encode\n     * @param {object} spec An optional object that may provide values for `loops` and `colorScope`, as defined for the Gif class. However, `colorSpace` may also take the value Gif.GlobalColorsPreferred (== 0) to indicate that the encoder should attempt to create only a global color table. `loop` defaults to 0, looping indefinitely. Set `loop` to null to disable looping, playing only once. `colorScope` defaults to Gif.GlobalColorsPreferred.\n     * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the encoded GIF.\n     * @throws {GifError} Error upon encountered an encoding-related problem with a GIF, so that the caller can distinguish between software errors and problems with GIFs.\n     */ encodeGif(frames, spec = {}) {\n        try {\n            if (frames === null || frames.length === 0) {\n                throw new GifError(\"there are no frames\");\n            }\n            const dims = GifUtil().getMaxDimensions(frames);\n            spec = Object.assign({}, spec); // don't munge caller's spec\n            spec.width = dims.maxWidth;\n            spec.height = dims.maxHeight;\n            if (spec.loops === undefined) {\n                spec.loops = 0;\n            }\n            spec.colorScope = spec.colorScope || Gif.GlobalColorsPreferred;\n            return Promise.resolve(this._encodeGif(frames, spec));\n        } catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    _decodeFrame(reader, frameIndex, alreadyUsedTransparency) {\n        let info, buffer;\n        try {\n            info = reader.frameInfo(frameIndex);\n            buffer = new Buffer(reader.width * reader.height * 4);\n            reader.decodeAndBlitFrameRGBA(frameIndex, buffer);\n            if (info.width !== reader.width || info.height !== reader.height) {\n                if (info.y) {\n                    // skip unused rows\n                    buffer = buffer.slice(info.y * reader.width * 4);\n                }\n                if (reader.width > info.width) {\n                    // skip scanstride\n                    for(let ii = 0; ii < info.height; ++ii){\n                        buffer.copy(buffer, ii * info.width * 4, (info.x + ii * reader.width) * 4, (info.x + ii * reader.width) * 4 + info.width * 4);\n                    }\n                }\n                // trim buffer to size\n                buffer = buffer.slice(0, info.width * info.height * 4);\n            }\n        } catch (err) {\n            throw new GifError(err);\n        }\n        let usesTransparency = false;\n        if (this._transparentRGBA === null) {\n            if (!alreadyUsedTransparency) {\n                for(let i = 3; i < buffer.length; i += 4){\n                    if (buffer[i] === 0) {\n                        usesTransparency = true;\n                        i = buffer.length;\n                    }\n                }\n            }\n        } else {\n            for(let i = 3; i < buffer.length; i += 4){\n                if (buffer[i] === 0) {\n                    buffer.writeUInt32BE(this._transparentRGBA, i - 3);\n                    usesTransparency = true; // GIF might encode unused index\n                }\n            }\n        }\n        const frame = new GifFrame(info.width, info.height, buffer, {\n            xOffset: info.x,\n            yOffset: info.y,\n            disposalMethod: info.disposal,\n            interlaced: info.interlaced,\n            delayCentisecs: info.delay\n        });\n        return {\n            frame,\n            usesTransparency\n        };\n    }\n    _encodeGif(frames, spec) {\n        let colorInfo;\n        if (spec.colorScope === Gif.LocalColorsOnly) {\n            colorInfo = GifUtil().getColorInfo(frames, 0);\n        } else {\n            colorInfo = GifUtil().getColorInfo(frames, 256);\n            if (!colorInfo.colors) {\n                if (spec.colorScope === Gif.GlobalColorsOnly) {\n                    throw new GifError(\"Too many color indexes for global color table\");\n                }\n                spec.colorScope = Gif.LocalColorsOnly;\n            }\n        }\n        spec.usesTransparency = colorInfo.usesTransparency;\n        const localPalettes = colorInfo.palettes;\n        if (spec.colorScope === Gif.LocalColorsOnly) {\n            const localSizeEst = 2000; //this._getSizeEstimateLocal(localPalettes, frames);\n            return _encodeLocal(frames, spec, localSizeEst, localPalettes);\n        }\n        const globalSizeEst = 2000; //this._getSizeEstimateGlobal(colorInfo, frames);\n        return _encodeGlobal(frames, spec, globalSizeEst, colorInfo);\n    }\n    _getSizeEstimateGlobal(globalPalette, frames) {\n        if (this._testInitialBufferSize > 0) {\n            return this._testInitialBufferSize;\n        }\n        let sizeEst = PER_GIF_OVERHEAD + 3 * 256 /* max palette size*/ ;\n        const pixelBitWidth = _getPixelBitWidth(globalPalette);\n        frames.forEach((frame)=>{\n            sizeEst += _getFrameSizeEst(frame, pixelBitWidth);\n        });\n        return sizeEst; // should be the upper limit\n    }\n    _getSizeEstimateLocal(palettes, frames) {\n        if (this._testInitialBufferSize > 0) {\n            return this._testInitialBufferSize;\n        }\n        let sizeEst = PER_GIF_OVERHEAD;\n        for(let i = 0; i < frames.length; ++i){\n            const palette = palettes[i];\n            const pixelBitWidth = _getPixelBitWidth(palette);\n            sizeEst += _getFrameSizeEst(frames[i], pixelBitWidth);\n        }\n        return sizeEst; // should be the upper limit\n    }\n}\nexports.GifCodec = GifCodec;\nfunction _colorLookupLinear(colors, color) {\n    const index = colors.indexOf(color);\n    return index === -1 ? null : index;\n}\nfunction _colorLookupBinary(colors, color) {\n    // adapted from https://stackoverflow.com/a/10264318/650894\n    var lo = 0, hi = colors.length - 1, mid;\n    while(lo <= hi){\n        mid = Math.floor((lo + hi) / 2);\n        if (colors[mid] > color) hi = mid - 1;\n        else if (colors[mid] < color) lo = mid + 1;\n        else return mid;\n    }\n    return null;\n}\nfunction _encodeGlobal(frames, spec, bufferSizeEst, globalPalette) {\n    // would be inefficient for frames to lookup colors in extended palette \n    const extendedGlobalPalette = {\n        colors: globalPalette.colors.slice(),\n        usesTransparency: globalPalette.usesTransparency\n    };\n    _extendPaletteToPowerOf2(extendedGlobalPalette);\n    const options = {\n        palette: extendedGlobalPalette.colors,\n        loop: spec.loops\n    };\n    let buffer = new Buffer(bufferSizeEst);\n    let gifWriter;\n    try {\n        gifWriter = new Omggif.GifWriter(buffer, spec.width, spec.height, options);\n    } catch (err) {\n        throw new GifError(err);\n    }\n    for(let i = 0; i < frames.length; ++i){\n        buffer = _writeFrame(gifWriter, i, frames[i], globalPalette, false);\n    }\n    return new Gif(buffer.slice(0, gifWriter.end()), frames, spec);\n}\nfunction _encodeLocal(frames, spec, bufferSizeEst, localPalettes) {\n    const options = {\n        loop: spec.loops\n    };\n    let buffer = new Buffer(bufferSizeEst);\n    let gifWriter;\n    try {\n        gifWriter = new Omggif.GifWriter(buffer, spec.width, spec.height, options);\n    } catch (err) {\n        throw new GifError(err);\n    }\n    for(let i = 0; i < frames.length; ++i){\n        buffer = _writeFrame(gifWriter, i, frames[i], localPalettes[i], true);\n    }\n    return new Gif(buffer.slice(0, gifWriter.end()), frames, spec);\n}\nfunction _extendPaletteToPowerOf2(palette) {\n    const colors = palette.colors;\n    if (palette.usesTransparency) {\n        colors.push(0);\n    }\n    const colorCount = colors.length;\n    let powerOf2 = 2;\n    while(colorCount > powerOf2){\n        powerOf2 <<= 1;\n    }\n    colors.length = powerOf2;\n    colors.fill(0, colorCount);\n}\nfunction _getFrameSizeEst(frame, pixelBitWidth) {\n    let byteLength = frame.bitmap.width * frame.bitmap.height;\n    byteLength = Math.ceil(byteLength * pixelBitWidth / 8);\n    byteLength += Math.ceil(byteLength / 255); // add block size bytes\n    // assume maximum palete size because it might get extended for power of 2\n    return PER_FRAME_OVERHEAD + byteLength + 3 * 256 /* largest palette */ ;\n}\nfunction _getIndexedImage(frameIndex, frame, palette) {\n    const colors = palette.colors;\n    const colorToIndexFunc = colors.length <= 8 ? _colorLookupLinear : _colorLookupBinary;\n    const colorBuffer = frame.bitmap.data;\n    const indexBuffer = new Buffer(colorBuffer.length / 4);\n    let transparentIndex = colors.length;\n    let i = 0, j = 0;\n    while(i < colorBuffer.length){\n        if (colorBuffer[i + 3] !== 0) {\n            const color = colorBuffer.readUInt32BE(i, true) >> 8 & 0xFFFFFF;\n            // caller guarantees that the color will be in the palette\n            indexBuffer[j] = colorToIndexFunc(colors, color);\n        } else {\n            indexBuffer[j] = transparentIndex;\n        }\n        i += 4; // skip alpha\n        ++j;\n    }\n    if (palette.usesTransparency) {\n        if (transparentIndex === 256) {\n            throw new GifError(`Frame ${frameIndex} already has 256 colors` + `and so can't use transparency`);\n        }\n    } else {\n        transparentIndex = null;\n    }\n    return {\n        buffer: indexBuffer,\n        transparentIndex\n    };\n}\nfunction _getPixelBitWidth(palette) {\n    let indexCount = palette.indexCount;\n    let pixelBitWidth = 0;\n    --indexCount; // start at maximum index\n    while(indexCount){\n        ++pixelBitWidth;\n        indexCount >>= 1;\n    }\n    return pixelBitWidth > 0 ? pixelBitWidth : 1;\n}\nfunction _writeFrame(gifWriter, frameIndex, frame, palette, isLocalPalette) {\n    if (frame.interlaced) {\n        throw new GifError(\"writing interlaced GIFs is not supported\");\n    }\n    const frameInfo = _getIndexedImage(frameIndex, frame, palette);\n    const options = {\n        delay: frame.delayCentisecs,\n        disposal: frame.disposalMethod,\n        transparent: frameInfo.transparentIndex\n    };\n    if (isLocalPalette) {\n        _extendPaletteToPowerOf2(palette); // ok 'cause palette never used again\n        options.palette = palette.colors;\n    }\n    try {\n        let buffer = gifWriter.getOutputBuffer();\n        let startOfFrame = gifWriter.getOutputBufferPosition();\n        let endOfFrame;\n        let tryAgain = true;\n        while(tryAgain){\n            endOfFrame = gifWriter.addFrame(frame.xOffset, frame.yOffset, frame.bitmap.width, frame.bitmap.height, frameInfo.buffer, options);\n            tryAgain = false;\n            if (endOfFrame >= buffer.length - 1) {\n                const biggerBuffer = new Buffer(buffer.length * 1.5);\n                buffer.copy(biggerBuffer);\n                gifWriter.setOutputBuffer(biggerBuffer);\n                gifWriter.setOutputBufferPosition(startOfFrame);\n                buffer = biggerBuffer;\n                tryAgain = true;\n            }\n        }\n        return buffer;\n    } catch (err) {\n        throw new GifError(err);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2lmd3JhcC9zcmMvZ2lmY29kZWMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxNQUFNQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUN2QixNQUFNLEVBQUVDLEdBQUcsRUFBRUMsUUFBUSxFQUFFLEdBQUdGLG1CQUFPQSxDQUFDO0FBRWxDLHlDQUF5QztBQUN6QyxTQUFTRztJQUNMLE1BQU1DLE9BQU9KLG1CQUFPQSxDQUFDO0lBRXJCRyxVQUFVO1FBQ1IsT0FBT0M7SUFDVDtJQUVGLE9BQU9BO0FBQ1Q7QUFFQSxNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHTCxtQkFBT0EsQ0FBQztBQUU3QixNQUFNTSxtQkFBbUIsS0FBSyxvQ0FBb0M7QUFDbEUsTUFBTUMscUJBQXFCO0FBRTNCLHF2QkFBcXZCO0FBRXJ2QixvQkFBb0IsR0FFcEIsTUFBTUM7SUFFRixpSUFBaUk7SUFFakk7Ozs7Ozs7O0tBUUMsR0FFREMsWUFBWUMsVUFBVSxDQUFDLENBQUMsQ0FBRTtRQUN0QixJQUFJLENBQUNDLGVBQWUsR0FBRyxNQUFNLFdBQVc7UUFDeEMsSUFBSSxPQUFPRCxRQUFRRSxjQUFjLEtBQUssWUFDOUJGLFFBQVFFLGNBQWMsS0FBSyxHQUNuQztZQUNJLElBQUksQ0FBQ0MsZ0JBQWdCLEdBQUdILFFBQVFFLGNBQWMsR0FBRztRQUNyRDtRQUNBLElBQUksQ0FBQ0Usc0JBQXNCLEdBQUcsR0FBRyxnQ0FBZ0M7SUFDckU7SUFFQTs7Ozs7O0tBTUMsR0FFREMsVUFBVUMsTUFBTSxFQUFFO1FBQ2QsSUFBSTtZQUNBLElBQUlDO1lBQ0osSUFBSTtnQkFDQUEsU0FBUyxJQUFJbEIsT0FBT21CLFNBQVMsQ0FBQ0Y7WUFDbEMsRUFDQSxPQUFPRyxLQUFLO2dCQUNSLE1BQU0sSUFBSWpCLFNBQVNpQjtZQUN2QjtZQUNBLE1BQU1DLGFBQWFILE9BQU9JLFNBQVM7WUFDbkMsTUFBTUMsU0FBUyxFQUFFO1lBQ2pCLE1BQU1DLE9BQU87Z0JBQ1RDLE9BQU9QLE9BQU9PLEtBQUs7Z0JBQ25CQyxRQUFRUixPQUFPUSxNQUFNO2dCQUNyQkMsT0FBT1QsT0FBT1UsU0FBUztZQUMzQjtZQUVBSixLQUFLSyxnQkFBZ0IsR0FBRztZQUN4QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSVQsWUFBWSxFQUFFUyxFQUFHO2dCQUNqQyxNQUFNQyxZQUNFLElBQUksQ0FBQ0MsWUFBWSxDQUFDZCxRQUFRWSxHQUFHTixLQUFLSyxnQkFBZ0I7Z0JBQzFETixPQUFPVSxJQUFJLENBQUNGLFVBQVVHLEtBQUs7Z0JBQzNCLElBQUlILFVBQVVGLGdCQUFnQixFQUFFO29CQUM1QkwsS0FBS0ssZ0JBQWdCLEdBQUc7Z0JBQzVCO1lBQ0o7WUFDQSxPQUFPTSxRQUFRQyxPQUFPLENBQUMsSUFBSWxDLElBQUllLFFBQVFNLFFBQVFDO1FBQ25ELEVBQ0EsT0FBT0osS0FBSztZQUNSLE9BQU9lLFFBQVFFLE1BQU0sQ0FBQ2pCO1FBQzFCO0lBQ0o7SUFFQTs7Ozs7OztLQU9DLEdBRURrQixVQUFVZixNQUFNLEVBQUVDLE9BQU8sQ0FBQyxDQUFDLEVBQUU7UUFDekIsSUFBSTtZQUNBLElBQUlELFdBQVcsUUFBUUEsT0FBT2dCLE1BQU0sS0FBSyxHQUFHO2dCQUN4QyxNQUFNLElBQUlwQyxTQUFTO1lBQ3ZCO1lBQ0EsTUFBTXFDLE9BQU9wQyxVQUFVcUMsZ0JBQWdCLENBQUNsQjtZQUV4Q0MsT0FBT2tCLE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUduQixPQUFPLDRCQUE0QjtZQUM1REEsS0FBS0MsS0FBSyxHQUFHZSxLQUFLSSxRQUFRO1lBQzFCcEIsS0FBS0UsTUFBTSxHQUFHYyxLQUFLSyxTQUFTO1lBQzVCLElBQUlyQixLQUFLRyxLQUFLLEtBQUttQixXQUFXO2dCQUMxQnRCLEtBQUtHLEtBQUssR0FBRztZQUNqQjtZQUNBSCxLQUFLdUIsVUFBVSxHQUFHdkIsS0FBS3VCLFVBQVUsSUFBSTdDLElBQUk4QyxxQkFBcUI7WUFFOUQsT0FBT2IsUUFBUUMsT0FBTyxDQUFDLElBQUksQ0FBQ2EsVUFBVSxDQUFDMUIsUUFBUUM7UUFDbkQsRUFDQSxPQUFPSixLQUFLO1lBQ1IsT0FBT2UsUUFBUUUsTUFBTSxDQUFDakI7UUFDMUI7SUFDSjtJQUVBWSxhQUFhZCxNQUFNLEVBQUVnQyxVQUFVLEVBQUVDLHVCQUF1QixFQUFFO1FBQ3RELElBQUlDLE1BQU1uQztRQUNWLElBQUk7WUFDQW1DLE9BQU9sQyxPQUFPYSxTQUFTLENBQUNtQjtZQUN4QmpDLFNBQVMsSUFBSW9DLE9BQU9uQyxPQUFPTyxLQUFLLEdBQUdQLE9BQU9RLE1BQU0sR0FBRztZQUNuRFIsT0FBT29DLHNCQUFzQixDQUFDSixZQUFZakM7WUFDMUMsSUFBSW1DLEtBQUszQixLQUFLLEtBQUtQLE9BQU9PLEtBQUssSUFBSTJCLEtBQUsxQixNQUFNLEtBQUtSLE9BQU9RLE1BQU0sRUFBRTtnQkFDOUQsSUFBSTBCLEtBQUtHLENBQUMsRUFBRTtvQkFDUixtQkFBbUI7b0JBQ25CdEMsU0FBU0EsT0FBT3VDLEtBQUssQ0FBQ0osS0FBS0csQ0FBQyxHQUFHckMsT0FBT08sS0FBSyxHQUFHO2dCQUNsRDtnQkFDQSxJQUFJUCxPQUFPTyxLQUFLLEdBQUcyQixLQUFLM0IsS0FBSyxFQUFFO29CQUMzQixrQkFBa0I7b0JBQ2xCLElBQUssSUFBSWdDLEtBQUssR0FBR0EsS0FBS0wsS0FBSzFCLE1BQU0sRUFBRSxFQUFFK0IsR0FBSTt3QkFDckN4QyxPQUFPeUMsSUFBSSxDQUFDekMsUUFBUXdDLEtBQUtMLEtBQUszQixLQUFLLEdBQUcsR0FDbEMsQ0FBQzJCLEtBQUtPLENBQUMsR0FBR0YsS0FBS3ZDLE9BQU9PLEtBQUssSUFBSSxHQUMvQixDQUFDMkIsS0FBS08sQ0FBQyxHQUFHRixLQUFLdkMsT0FBT08sS0FBSyxJQUFJLElBQUkyQixLQUFLM0IsS0FBSyxHQUFHO29CQUN4RDtnQkFDSjtnQkFDQSxzQkFBc0I7Z0JBQ3RCUixTQUFTQSxPQUFPdUMsS0FBSyxDQUFDLEdBQUdKLEtBQUszQixLQUFLLEdBQUcyQixLQUFLMUIsTUFBTSxHQUFHO1lBQ3hEO1FBQ0osRUFDQSxPQUFPTixLQUFLO1lBQ1IsTUFBTSxJQUFJakIsU0FBU2lCO1FBQ3ZCO1FBRUEsSUFBSVMsbUJBQW1CO1FBQ3ZCLElBQUksSUFBSSxDQUFDZixnQkFBZ0IsS0FBSyxNQUFNO1lBQ2hDLElBQUksQ0FBQ3FDLHlCQUF5QjtnQkFDMUIsSUFBSyxJQUFJckIsSUFBSSxHQUFHQSxJQUFJYixPQUFPc0IsTUFBTSxFQUFFVCxLQUFLLEVBQUc7b0JBQ3ZDLElBQUliLE1BQU0sQ0FBQ2EsRUFBRSxLQUFLLEdBQUc7d0JBQ2pCRCxtQkFBbUI7d0JBQ25CQyxJQUFJYixPQUFPc0IsTUFBTTtvQkFDckI7Z0JBQ0o7WUFDSjtRQUNKLE9BQ0s7WUFDRCxJQUFLLElBQUlULElBQUksR0FBR0EsSUFBSWIsT0FBT3NCLE1BQU0sRUFBRVQsS0FBSyxFQUFHO2dCQUN2QyxJQUFJYixNQUFNLENBQUNhLEVBQUUsS0FBSyxHQUFHO29CQUNqQmIsT0FBTzJDLGFBQWEsQ0FBQyxJQUFJLENBQUM5QyxnQkFBZ0IsRUFBRWdCLElBQUk7b0JBQ2hERCxtQkFBbUIsTUFBTSxnQ0FBZ0M7Z0JBQzdEO1lBQ0o7UUFDSjtRQUVBLE1BQU1LLFFBQVEsSUFBSTVCLFNBQVM4QyxLQUFLM0IsS0FBSyxFQUFFMkIsS0FBSzFCLE1BQU0sRUFBRVQsUUFBUTtZQUN4RDRDLFNBQVNULEtBQUtPLENBQUM7WUFDZkcsU0FBU1YsS0FBS0csQ0FBQztZQUNmUSxnQkFBZ0JYLEtBQUtZLFFBQVE7WUFDN0JDLFlBQVliLEtBQUthLFVBQVU7WUFDM0JDLGdCQUFnQmQsS0FBS2UsS0FBSztRQUM5QjtRQUNBLE9BQU87WUFBRWpDO1lBQU9MO1FBQWlCO0lBQ3JDO0lBRUFvQixXQUFXMUIsTUFBTSxFQUFFQyxJQUFJLEVBQUU7UUFDckIsSUFBSTRDO1FBQ0osSUFBSTVDLEtBQUt1QixVQUFVLEtBQUs3QyxJQUFJbUUsZUFBZSxFQUFFO1lBQ3pDRCxZQUFZaEUsVUFBVWtFLFlBQVksQ0FBQy9DLFFBQVE7UUFDL0MsT0FDSztZQUNENkMsWUFBWWhFLFVBQVVrRSxZQUFZLENBQUMvQyxRQUFRO1lBQzNDLElBQUksQ0FBQzZDLFVBQVVHLE1BQU0sRUFBRTtnQkFDbkIsSUFBSS9DLEtBQUt1QixVQUFVLEtBQUs3QyxJQUFJc0UsZ0JBQWdCLEVBQUU7b0JBQzFDLE1BQU0sSUFBSXJFLFNBQ0Y7Z0JBQ1o7Z0JBQ0FxQixLQUFLdUIsVUFBVSxHQUFHN0MsSUFBSW1FLGVBQWU7WUFDekM7UUFDSjtRQUNBN0MsS0FBS0ssZ0JBQWdCLEdBQUd1QyxVQUFVdkMsZ0JBQWdCO1FBRWxELE1BQU00QyxnQkFBZ0JMLFVBQVVNLFFBQVE7UUFDeEMsSUFBSWxELEtBQUt1QixVQUFVLEtBQUs3QyxJQUFJbUUsZUFBZSxFQUFFO1lBQ3pDLE1BQU1NLGVBQWUsTUFBTSxvREFBb0Q7WUFDL0UsT0FBT0MsYUFBYXJELFFBQVFDLE1BQU1tRCxjQUFjRjtRQUNwRDtRQUVBLE1BQU1JLGdCQUFnQixNQUFNLGlEQUFpRDtRQUM3RSxPQUFPQyxjQUFjdkQsUUFBUUMsTUFBTXFELGVBQWVUO0lBQ3REO0lBRUFXLHVCQUF1QkMsYUFBYSxFQUFFekQsTUFBTSxFQUFFO1FBQzFDLElBQUksSUFBSSxDQUFDUixzQkFBc0IsR0FBRyxHQUFHO1lBQ2pDLE9BQU8sSUFBSSxDQUFDQSxzQkFBc0I7UUFDdEM7UUFDQSxJQUFJa0UsVUFBVTFFLG1CQUFtQixJQUFFLElBQUksbUJBQW1CO1FBQzFELE1BQU0yRSxnQkFBZ0JDLGtCQUFrQkg7UUFDeEN6RCxPQUFPNkQsT0FBTyxDQUFDbEQsQ0FBQUE7WUFDWCtDLFdBQVdJLGlCQUFpQm5ELE9BQU9nRDtRQUN2QztRQUNBLE9BQU9ELFNBQVMsNEJBQTRCO0lBQ2hEO0lBRUFLLHNCQUFzQlosUUFBUSxFQUFFbkQsTUFBTSxFQUFFO1FBQ3BDLElBQUksSUFBSSxDQUFDUixzQkFBc0IsR0FBRyxHQUFHO1lBQ2pDLE9BQU8sSUFBSSxDQUFDQSxzQkFBc0I7UUFDdEM7UUFDQSxJQUFJa0UsVUFBVTFFO1FBQ2QsSUFBSyxJQUFJdUIsSUFBSSxHQUFHQSxJQUFJUCxPQUFPZ0IsTUFBTSxFQUFFLEVBQUVULEVBQUk7WUFDckMsTUFBTXlELFVBQVViLFFBQVEsQ0FBQzVDLEVBQUU7WUFDM0IsTUFBTW9ELGdCQUFnQkMsa0JBQWtCSTtZQUN4Q04sV0FBV0ksaUJBQWlCOUQsTUFBTSxDQUFDTyxFQUFFLEVBQUVvRDtRQUMzQztRQUNBLE9BQU9ELFNBQVMsNEJBQTRCO0lBQ2hEO0FBQ0o7QUFDQU8sZ0JBQWdCLEdBQUcvRTtBQUVuQixTQUFTZ0YsbUJBQW1CbEIsTUFBTSxFQUFFbUIsS0FBSztJQUNyQyxNQUFNQyxRQUFRcEIsT0FBT3FCLE9BQU8sQ0FBQ0Y7SUFDN0IsT0FBUUMsVUFBVSxDQUFDLElBQUksT0FBT0E7QUFDbEM7QUFFQSxTQUFTRSxtQkFBbUJ0QixNQUFNLEVBQUVtQixLQUFLO0lBQ3JDLDJEQUEyRDtJQUMzRCxJQUFJSSxLQUFLLEdBQUdDLEtBQUt4QixPQUFPaEMsTUFBTSxHQUFHLEdBQUd5RDtJQUNwQyxNQUFPRixNQUFNQyxHQUFJO1FBQ2JDLE1BQU1DLEtBQUtDLEtBQUssQ0FBQyxDQUFDSixLQUFLQyxFQUFDLElBQUc7UUFDM0IsSUFBSXhCLE1BQU0sQ0FBQ3lCLElBQUksR0FBR04sT0FDZEssS0FBS0MsTUFBTTthQUNWLElBQUl6QixNQUFNLENBQUN5QixJQUFJLEdBQUdOLE9BQ25CSSxLQUFLRSxNQUFNO2FBRVgsT0FBT0E7SUFDZjtJQUNBLE9BQU87QUFDWDtBQUVBLFNBQVNsQixjQUFjdkQsTUFBTSxFQUFFQyxJQUFJLEVBQUUyRSxhQUFhLEVBQUVuQixhQUFhO0lBQzdELHdFQUF3RTtJQUN4RSxNQUFNb0Isd0JBQXdCO1FBQzFCN0IsUUFBUVMsY0FBY1QsTUFBTSxDQUFDZixLQUFLO1FBQ2xDM0Isa0JBQWtCbUQsY0FBY25ELGdCQUFnQjtJQUNwRDtJQUNBd0UseUJBQXlCRDtJQUN6QixNQUFNekYsVUFBVTtRQUNaNEUsU0FBU2Esc0JBQXNCN0IsTUFBTTtRQUNyQytCLE1BQU05RSxLQUFLRyxLQUFLO0lBQ3BCO0lBQ0EsSUFBSVYsU0FBUyxJQUFJb0MsT0FBTzhDO0lBQ3hCLElBQUlJO0lBQ0osSUFBSTtRQUNBQSxZQUFZLElBQUl2RyxPQUFPd0csU0FBUyxDQUFDdkYsUUFBUU8sS0FBS0MsS0FBSyxFQUFFRCxLQUFLRSxNQUFNLEVBQzVDZjtJQUN4QixFQUNBLE9BQU9TLEtBQUs7UUFDUixNQUFNLElBQUlqQixTQUFTaUI7SUFDdkI7SUFDQSxJQUFLLElBQUlVLElBQUksR0FBR0EsSUFBSVAsT0FBT2dCLE1BQU0sRUFBRSxFQUFFVCxFQUFHO1FBQ3BDYixTQUFTd0YsWUFBWUYsV0FBV3pFLEdBQUdQLE1BQU0sQ0FBQ08sRUFBRSxFQUFFa0QsZUFBZTtJQUNqRTtJQUNBLE9BQU8sSUFBSTlFLElBQUllLE9BQU91QyxLQUFLLENBQUMsR0FBRytDLFVBQVVHLEdBQUcsS0FBS25GLFFBQVFDO0FBQzdEO0FBRUEsU0FBU29ELGFBQWFyRCxNQUFNLEVBQUVDLElBQUksRUFBRTJFLGFBQWEsRUFBRTFCLGFBQWE7SUFDNUQsTUFBTTlELFVBQVU7UUFDWjJGLE1BQU05RSxLQUFLRyxLQUFLO0lBQ3BCO0lBQ0EsSUFBSVYsU0FBUyxJQUFJb0MsT0FBTzhDO0lBQ3hCLElBQUlJO0lBQ0osSUFBSTtRQUNBQSxZQUFZLElBQUl2RyxPQUFPd0csU0FBUyxDQUFDdkYsUUFBUU8sS0FBS0MsS0FBSyxFQUFFRCxLQUFLRSxNQUFNLEVBQzVDZjtJQUN4QixFQUNBLE9BQU9TLEtBQUs7UUFDUixNQUFNLElBQUlqQixTQUFTaUI7SUFDdkI7SUFDQSxJQUFLLElBQUlVLElBQUksR0FBR0EsSUFBSVAsT0FBT2dCLE1BQU0sRUFBRSxFQUFFVCxFQUFHO1FBQ3BDYixTQUFTd0YsWUFBWUYsV0FBV3pFLEdBQUdQLE1BQU0sQ0FBQ08sRUFBRSxFQUFFMkMsYUFBYSxDQUFDM0MsRUFBRSxFQUFFO0lBQ3BFO0lBQ0EsT0FBTyxJQUFJNUIsSUFBSWUsT0FBT3VDLEtBQUssQ0FBQyxHQUFHK0MsVUFBVUcsR0FBRyxLQUFLbkYsUUFBUUM7QUFDN0Q7QUFFQSxTQUFTNkUseUJBQXlCZCxPQUFPO0lBQ3JDLE1BQU1oQixTQUFTZ0IsUUFBUWhCLE1BQU07SUFDN0IsSUFBSWdCLFFBQVExRCxnQkFBZ0IsRUFBRTtRQUMxQjBDLE9BQU90QyxJQUFJLENBQUM7SUFDaEI7SUFDQSxNQUFNMEUsYUFBYXBDLE9BQU9oQyxNQUFNO0lBQ2hDLElBQUlxRSxXQUFXO0lBQ2YsTUFBT0QsYUFBYUMsU0FBVTtRQUMxQkEsYUFBYTtJQUNqQjtJQUNBckMsT0FBT2hDLE1BQU0sR0FBR3FFO0lBQ2hCckMsT0FBT3NDLElBQUksQ0FBQyxHQUFHRjtBQUNuQjtBQUVBLFNBQVN0QixpQkFBaUJuRCxLQUFLLEVBQUVnRCxhQUFhO0lBQzFDLElBQUk0QixhQUFhNUUsTUFBTTZFLE1BQU0sQ0FBQ3RGLEtBQUssR0FBR1MsTUFBTTZFLE1BQU0sQ0FBQ3JGLE1BQU07SUFDekRvRixhQUFhYixLQUFLZSxJQUFJLENBQUNGLGFBQWE1QixnQkFBZ0I7SUFDcEQ0QixjQUFjYixLQUFLZSxJQUFJLENBQUNGLGFBQWEsTUFBTSx1QkFBdUI7SUFDbEUsMEVBQTBFO0lBQzFFLE9BQVF0RyxxQkFBcUJzRyxhQUFhLElBQUksSUFBSSxtQkFBbUI7QUFDekU7QUFFQSxTQUFTRyxpQkFBaUIvRCxVQUFVLEVBQUVoQixLQUFLLEVBQUVxRCxPQUFPO0lBQ2hELE1BQU1oQixTQUFTZ0IsUUFBUWhCLE1BQU07SUFDN0IsTUFBTTJDLG1CQUFvQjNDLE9BQU9oQyxNQUFNLElBQUksSUFDbkNrRCxxQkFBcUJJO0lBQzdCLE1BQU1zQixjQUFjakYsTUFBTTZFLE1BQU0sQ0FBQzFHLElBQUk7SUFDckMsTUFBTStHLGNBQWMsSUFBSS9ELE9BQU84RCxZQUFZNUUsTUFBTSxHQUFDO0lBQ2xELElBQUk4RSxtQkFBbUI5QyxPQUFPaEMsTUFBTTtJQUNwQyxJQUFJVCxJQUFJLEdBQUd3RixJQUFJO0lBRWYsTUFBT3hGLElBQUlxRixZQUFZNUUsTUFBTSxDQUFFO1FBQzNCLElBQUk0RSxXQUFXLENBQUNyRixJQUFJLEVBQUUsS0FBSyxHQUFHO1lBQzFCLE1BQU00RCxRQUFRLFlBQWE2QixZQUFZLENBQUN6RixHQUFHLFNBQVMsSUFBSztZQUN6RCwwREFBMEQ7WUFDMURzRixXQUFXLENBQUNFLEVBQUUsR0FBR0osaUJBQWlCM0MsUUFBUW1CO1FBQzlDLE9BQ0s7WUFDRDBCLFdBQVcsQ0FBQ0UsRUFBRSxHQUFHRDtRQUNyQjtRQUNBdkYsS0FBSyxHQUFHLGFBQWE7UUFDckIsRUFBRXdGO0lBQ047SUFFQSxJQUFJL0IsUUFBUTFELGdCQUFnQixFQUFFO1FBQzFCLElBQUl3RixxQkFBcUIsS0FBSztZQUMxQixNQUFNLElBQUlsSCxTQUFTLENBQUMsTUFBTSxFQUFFK0MsV0FBVyx1QkFBdUIsQ0FBQyxHQUN2RCxDQUFDLDZCQUE2QixDQUFDO1FBQzNDO0lBQ0osT0FDSztRQUNEbUUsbUJBQW1CO0lBQ3ZCO0lBRUEsT0FBTztRQUFFcEcsUUFBUW1HO1FBQWFDO0lBQWlCO0FBQ25EO0FBRUEsU0FBU2xDLGtCQUFrQkksT0FBTztJQUM5QixJQUFJaUMsYUFBYWpDLFFBQVFpQyxVQUFVO0lBQ25DLElBQUl0QyxnQkFBZ0I7SUFDcEIsRUFBRXNDLFlBQVkseUJBQXlCO0lBQ3ZDLE1BQU9BLFdBQVk7UUFDZixFQUFFdEM7UUFDRnNDLGVBQWU7SUFDbkI7SUFDQSxPQUFRdEMsZ0JBQWdCLElBQUlBLGdCQUFnQjtBQUNoRDtBQUVBLFNBQVN1QixZQUFZRixTQUFTLEVBQUVyRCxVQUFVLEVBQUVoQixLQUFLLEVBQUVxRCxPQUFPLEVBQUVrQyxjQUFjO0lBQ3RFLElBQUl2RixNQUFNK0IsVUFBVSxFQUFFO1FBQ2xCLE1BQU0sSUFBSTlELFNBQVM7SUFDdkI7SUFDQSxNQUFNNEIsWUFBWWtGLGlCQUFpQi9ELFlBQVloQixPQUFPcUQ7SUFDdEQsTUFBTTVFLFVBQVU7UUFDWndELE9BQU9qQyxNQUFNZ0MsY0FBYztRQUMzQkYsVUFBVTlCLE1BQU02QixjQUFjO1FBQzlCMkQsYUFBYTNGLFVBQVVzRixnQkFBZ0I7SUFDM0M7SUFDQSxJQUFJSSxnQkFBZ0I7UUFDaEJwQix5QkFBeUJkLFVBQVUscUNBQXFDO1FBQ3hFNUUsUUFBUTRFLE9BQU8sR0FBR0EsUUFBUWhCLE1BQU07SUFDcEM7SUFDQSxJQUFJO1FBQ0EsSUFBSXRELFNBQVNzRixVQUFVb0IsZUFBZTtRQUN0QyxJQUFJQyxlQUFlckIsVUFBVXNCLHVCQUF1QjtRQUNwRCxJQUFJQztRQUNKLElBQUlDLFdBQVc7UUFFZixNQUFPQSxTQUFVO1lBQ2JELGFBQWF2QixVQUFVeUIsUUFBUSxDQUFDOUYsTUFBTTJCLE9BQU8sRUFBRTNCLE1BQU00QixPQUFPLEVBQ3BENUIsTUFBTTZFLE1BQU0sQ0FBQ3RGLEtBQUssRUFBRVMsTUFBTTZFLE1BQU0sQ0FBQ3JGLE1BQU0sRUFBRUssVUFBVWQsTUFBTSxFQUFFTjtZQUNuRW9ILFdBQVc7WUFDWCxJQUFJRCxjQUFjN0csT0FBT3NCLE1BQU0sR0FBRyxHQUFHO2dCQUNqQyxNQUFNMEYsZUFBZSxJQUFJNUUsT0FBT3BDLE9BQU9zQixNQUFNLEdBQUc7Z0JBQ2hEdEIsT0FBT3lDLElBQUksQ0FBQ3VFO2dCQUNaMUIsVUFBVTJCLGVBQWUsQ0FBQ0Q7Z0JBQzFCMUIsVUFBVTRCLHVCQUF1QixDQUFDUDtnQkFDbEMzRyxTQUFTZ0g7Z0JBQ1RGLFdBQVc7WUFDZjtRQUNKO1FBQ0EsT0FBTzlHO0lBQ1gsRUFDQSxPQUFPRyxLQUFLO1FBQ1IsTUFBTSxJQUFJakIsU0FBU2lCO0lBQ3ZCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly94YXV1c2QtdHJhZGluZy1hbmFseXplci8uL25vZGVfbW9kdWxlcy9naWZ3cmFwL3NyYy9naWZjb2RlYy5qcz81NTMxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgT21nZ2lmID0gcmVxdWlyZSgnb21nZ2lmJyk7XG5jb25zdCB7IEdpZiwgR2lmRXJyb3IgfSA9IHJlcXVpcmUoJy4vZ2lmJyk7XG5cbi8vIGFsbG93IGNpcmN1bGFyIGRlcGVuZGVuY3kgd2l0aCBHaWZVdGlsXG5mdW5jdGlvbiBHaWZVdGlsKCkge1xuICAgIGNvbnN0IGRhdGEgPSByZXF1aXJlKCcuL2dpZnV0aWwnKTtcblxuICAgIEdpZlV0aWwgPSBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gZGF0YTtcbiAgICB9O1xuXG4gIHJldHVybiBkYXRhO1xufVxuXG5jb25zdCB7IEdpZkZyYW1lIH0gPSByZXF1aXJlKCcuL2dpZmZyYW1lJyk7XG5cbmNvbnN0IFBFUl9HSUZfT1ZFUkhFQUQgPSAyMDA7IC8vIHRoZXNlIGFyZSBndWVzc2VzIGF0IHVwcGVyIGxpbWl0c1xuY29uc3QgUEVSX0ZSQU1FX09WRVJIRUFEID0gMTAwO1xuXG4vLyBOb3RlOiBJIGV4cGVyaW1lbnRlZCB3aXRoIGFjY2VwdGluZyBhIGdsb2JhbCBjb2xvciB0YWJsZSB3aGVuIGVuY29kaW5nIGFuZCByZXR1cm5pbmcgdGhlIGdsb2JhbCBjb2xvciB0YWJsZSB3aGVuIGRlY29kaW5nLiBEb2luZyB0aGlzIHByb3Blcmx5IGdyZWF0bHkgaW5jcmVhc2VkIHRoZSBjb21wbGV4aXR5IG9mIHRoZSBjb2RlIGFuZCB0aGUgYW1vdW50IG9mIGNsb2NrIGN5Y2xlcyByZXF1aXJlZC4gVGhlIG1haW4gaXNzdWUgaXMgdGhhdCBlYWNoIGZyYW1lIGNhbiBzcGVjaWZ5IGFueSBjb2xvciBvZiB0aGUgZ2xvYmFsIGNvbG9yIHRhYmxlIHRvIGJlIHRyYW5zcGFyZW50IHdpdGhpbiB0aGUgZnJhbWUsIHdoaWxlIHRoaXMgR0lGIGxpYnJhcnkgc3RyaXZlcyB0byBoaWRlIEdJRiBmb3JtYXR0aW5nIGRldGFpbHMgZnJvbSBpdHMgY2xpZW50cy4gRS5nLiBpdCdzIHBvc3NpYmxlIHRvIGhhdmUgMjU2IGNvbG9ycyBpbiB0aGUgZ2xvYmFsIGNvbG9yIHRhYmxlIGFuZCBkaWZmZXJlbnQgdHJhbnNwYXJlbmNpZXMgaW4gZWFjaCBmcmFtZSwgcmVxdWlyaW5nIGNsaWVudHMgdG8gZWl0aGVyIHByb3ZpZGUgcGVyLWZyYW1lIHRyYW5zcGFyZW5jeSBpbmRleGVzLCBvciBmb3IgYXJjYW5lIHJlYXNvbnMgdGhhdCB3b24ndCBiZSBhcHBhcmVudCB0byBjbGllbnQgZGV2ZWxvcGVycywgZW5jb2RlIHNvbWUgR0lGcyB3aXRoIGxvY2FsIGNvbG9yIHRhYmxlcyB0aGF0IHByZXZpb3VzbHkgZGVjb2RlZCB3aXRoIGdsb2JhbCB0YWJsZXMuXG5cbi8qKiBAY2xhc3MgR2lmQ29kZWMgKi9cblxuY2xhc3MgR2lmQ29kZWNcbntcbiAgICAvLyBfdHJhbnNwYXJlbnRSR0JBIC0gUkdCIGdpdmVuIHRvIHRyYW5zcGFyZW50IHBpeGVscyAoYWxwaGE9MCkgb24gZGVjb2RlOyBkZWZhdWx0cyB0byBudWxsIGluZGljYXRpbmcgMHgwMDAwMDAsIHdoaWNoIGlzIGZhc3Rlc3RcblxuICAgIC8qKlxuICAgICAqIEdpZkNvZGVjIGlzIGEgY2xhc3MgdGhhdCBib3RoIGVuY29kZXMgYW5kIGRlY29kZXMgR0lGcy4gSXQgaW1wbGVtZW50cyBib3RoIHRoZSBgZW5jb2RlKClgIG1ldGhvZCBleHBlY3RlZCBvZiBhbiBlbmNvZGVyIGFuZCB0aGUgYGRlY29kZSgpYCBtZXRob2QgZXhwZWN0ZWQgb2YgYSBkZWNvZGVyLCBhbmQgaXQgd3JhcHMgdGhlIGBvbWdnaWZgIEdJRiBlbmNvZGVyL2RlY29kZXIgcGFja2FnZS4gR2lmQ29kZWMgc2VydmVzIGFzIHRoaXMgbGlicmFyeSdzIGRlZmF1bHQgZW5jb2RlciBhbmQgZGVjb2RlciwgYnV0IGl0J3MgcG9zc2libGUgdG8gd3JhcCBvdGhlciBHSUYgZW5jb2RlcnMgYW5kIGRlY29kZXJzIGZvciB1c2UgYnkgYGdpZndyYXBgIGFzIHdlbGwuIEdpZkNvZGVjIHdpbGwgbm90IGVuY29kZSBHSUZzIHdpdGggaW50ZXJsYWNpbmcuXG4gICAgICogXG4gICAgICogSW5zdGFuY2VzIG9mIHRoaXMgY2xhc3MgYXJlIHN0YXRlbGVzcyBhbmQgY2FuIGJlIHNoYXJlZCBhY3Jvc3MgbXVsdGlwbGUgZW5jb2RpbmdzIGFuZCBkZWNvZGluZ3MuXG4gICAgICogXG4gICAgICogSXRzIGNvbnN0cnVjdG9yIHRha2VzIG9uZSBvcHRpb24gYXJndW1lbnQ6XG4gICAgICogXG4gICAgICogQHBhcmFtIHtvYmplY3R9IG9wdGlvbnMgT3B0aW9uYWxseSB0YWtlcyBhbiBvYmplY3Rpb24gd2hvc2Ugb25seSBwb3NzaWJsZSBwcm9wZXJ0eSBpcyBgdHJhbnNwYXJlbnRSR0JgLiBJbWFnZXMgYXJlIGludGVybmFsbHkgcmVwcmVzZW50ZWQgaW4gUkdCQSBmb3JtYXQsIHdoZXJlIEEgaXMgdGhlIGFscGhhIHZhbHVlIG9mIGEgcGl4ZWwuIFdoZW4gYHRyYW5zcGFyZW50UkdCYCBpcyBwcm92aWRlZCwgdGhpcyBSR0IgdmFsdWUgKGV4Y2x1ZGluZyBhbHBoYSkgaXMgYXNzaWduZWQgdG8gdHJhbnNwYXJlbnQgcGl4ZWxzLCB3aGljaCBhcmUgYWxzbyBnaXZlbiBhbHBoYSB2YWx1ZSAweDAwLiAoQWxsIG9wYXF1ZSBwaXhlbHMgYXJlIGdpdmVuIGFscGhhIHZhbHVlIDB4RkYpLiBUaGUgUkdCIGNvbG9yIG9mIHRyYW5zcGFyZW50IHBpeGVscyBzaG91bGRuJ3QgbWF0dGVyIGZvciBtb3N0IGFwcGxpY2F0aW9ucy4gRGVmYXVsdHMgdG8gMHgwMDAwMDAuXG4gICAgICovXG5cbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zID0ge30pIHtcbiAgICAgICAgdGhpcy5fdHJhbnNwYXJlbnRSR0IgPSBudWxsOyAvLyAweDAwMDAwMFxuICAgICAgICBpZiAodHlwZW9mIG9wdGlvbnMudHJhbnNwYXJlbnRSR0IgPT09ICdudW1iZXInICYmXG4gICAgICAgICAgICAgICAgb3B0aW9ucy50cmFuc3BhcmVudFJHQiAhPT0gMClcbiAgICAgICAge1xuICAgICAgICAgICAgdGhpcy5fdHJhbnNwYXJlbnRSR0JBID0gb3B0aW9ucy50cmFuc3BhcmVudFJHQiAqIDI1NjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl90ZXN0SW5pdGlhbEJ1ZmZlclNpemUgPSAwOyAvLyBhc3N1bWUgbm8gYnVmZmVyIHNjYWxpbmcgdGVzdFxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIERlY29kZXMgYSBHSUYgZnJvbSBhIEJ1ZmZlciB0byB5aWVsZCBhbiBpbnN0YW5jZSBvZiBHaWYuIFRyYW5zcGFyZW50IHBpeGVscyBvZiB0aGUgR0lGIGFyZSBnaXZlbiBhbHBoYSB2YWx1ZXMgb2YgMHgwMCwgYW5kIG9wYXF1ZSBwaXhlbHMgYXJlIGdpdmVuIGFscGhhIHZhbHVlcyBvZiAweEZGLiBUaGUgUkdCIHZhbHVlcyBvZiB0cmFuc3BhcmVudCBwaXhlbHMgZGVmYXVsdCB0byAweDAwMDAwMCBidXQgY2FuIGJlIG92ZXJyaWRkZW4gYnkgdGhlIGNvbnN0cnVjdG9yJ3MgYHRyYW5zcGFyZW50UkdCYCBvcHRpb24uXG4gICAgICogXG4gICAgICogQHBhcmFtIHtCdWZmZXJ9IGJ1ZmZlciBCeXRlcyBvZiBhbiBlbmNvZGVkIEdJRiB0byBkZWNvZGUuXG4gICAgICogQHJldHVybiB7UHJvbWlzZX0gQSBQcm9taXNlIHRoYXQgcmVzb2x2ZXMgdG8gYW4gaW5zdGFuY2Ugb2YgdGhlIEdpZiBjbGFzcywgcmVwcmVzZW50aW5nIHRoZSBlbmNvZGVkIEdJRi5cbiAgICAgKiBAdGhyb3dzIHtHaWZFcnJvcn0gRXJyb3IgdXBvbiBlbmNvdW50ZXJlZCBhbiBlbmNvZGluZy1yZWxhdGVkIHByb2JsZW0gd2l0aCBhIEdJRiwgc28gdGhhdCB0aGUgY2FsbGVyIGNhbiBkaXN0aW5ndWlzaCBiZXR3ZWVuIHNvZnR3YXJlIGVycm9ycyBhbmQgcHJvYmxlbXMgd2l0aCBHSUZzLlxuICAgICAqL1xuXG4gICAgZGVjb2RlR2lmKGJ1ZmZlcikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgbGV0IHJlYWRlcjtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgcmVhZGVyID0gbmV3IE9tZ2dpZi5HaWZSZWFkZXIoYnVmZmVyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgR2lmRXJyb3IoZXJyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGZyYW1lQ291bnQgPSByZWFkZXIubnVtRnJhbWVzKCk7XG4gICAgICAgICAgICBjb25zdCBmcmFtZXMgPSBbXTtcbiAgICAgICAgICAgIGNvbnN0IHNwZWMgPSB7XG4gICAgICAgICAgICAgICAgd2lkdGg6IHJlYWRlci53aWR0aCxcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IHJlYWRlci5oZWlnaHQsXG4gICAgICAgICAgICAgICAgbG9vcHM6IHJlYWRlci5sb29wQ291bnQoKVxuICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgc3BlYy51c2VzVHJhbnNwYXJlbmN5ID0gZmFsc2U7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGZyYW1lQ291bnQ7ICsraSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZyYW1lSW5mbyA9XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLl9kZWNvZGVGcmFtZShyZWFkZXIsIGksIHNwZWMudXNlc1RyYW5zcGFyZW5jeSk7XG4gICAgICAgICAgICAgICAgZnJhbWVzLnB1c2goZnJhbWVJbmZvLmZyYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoZnJhbWVJbmZvLnVzZXNUcmFuc3BhcmVuY3kpIHtcbiAgICAgICAgICAgICAgICAgICAgc3BlYy51c2VzVHJhbnNwYXJlbmN5ID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKG5ldyBHaWYoYnVmZmVyLCBmcmFtZXMsIHNwZWMpKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEVuY29kZXMgYSBHSUYgZnJvbSBwcm92aWRlZCBmcmFtZXMuIEVhY2ggcGl4ZWwgaGF2aW5nIGFuIGFscGhhIHZhbHVlIG9mIDB4MDAgcmVuZGVycyBhcyB0cmFuc3BhcmVudCB3aXRoaW4gdGhlIGVuY29kaW5nLCB3aGlsZSBhbGwgcGl4ZWxzIG9mIG5vbi16ZXJvIGFscGhhIHZhbHVlIHJlbmRlciBhcyBvcGFxdWUuXG4gICAgICogXG4gICAgICogQHBhcmFtIHtHaWZGcmFtZVtdfSBmcmFtZXMgQXJyYXkgb2YgZnJhbWVzIHRvIGVuY29kZVxuICAgICAqIEBwYXJhbSB7b2JqZWN0fSBzcGVjIEFuIG9wdGlvbmFsIG9iamVjdCB0aGF0IG1heSBwcm92aWRlIHZhbHVlcyBmb3IgYGxvb3BzYCBhbmQgYGNvbG9yU2NvcGVgLCBhcyBkZWZpbmVkIGZvciB0aGUgR2lmIGNsYXNzLiBIb3dldmVyLCBgY29sb3JTcGFjZWAgbWF5IGFsc28gdGFrZSB0aGUgdmFsdWUgR2lmLkdsb2JhbENvbG9yc1ByZWZlcnJlZCAoPT0gMCkgdG8gaW5kaWNhdGUgdGhhdCB0aGUgZW5jb2RlciBzaG91bGQgYXR0ZW1wdCB0byBjcmVhdGUgb25seSBhIGdsb2JhbCBjb2xvciB0YWJsZS4gYGxvb3BgIGRlZmF1bHRzIHRvIDAsIGxvb3BpbmcgaW5kZWZpbml0ZWx5LiBTZXQgYGxvb3BgIHRvIG51bGwgdG8gZGlzYWJsZSBsb29waW5nLCBwbGF5aW5nIG9ubHkgb25jZS4gYGNvbG9yU2NvcGVgIGRlZmF1bHRzIHRvIEdpZi5HbG9iYWxDb2xvcnNQcmVmZXJyZWQuXG4gICAgICogQHJldHVybiB7UHJvbWlzZX0gQSBQcm9taXNlIHRoYXQgcmVzb2x2ZXMgdG8gYW4gaW5zdGFuY2Ugb2YgdGhlIEdpZiBjbGFzcywgcmVwcmVzZW50aW5nIHRoZSBlbmNvZGVkIEdJRi5cbiAgICAgKiBAdGhyb3dzIHtHaWZFcnJvcn0gRXJyb3IgdXBvbiBlbmNvdW50ZXJlZCBhbiBlbmNvZGluZy1yZWxhdGVkIHByb2JsZW0gd2l0aCBhIEdJRiwgc28gdGhhdCB0aGUgY2FsbGVyIGNhbiBkaXN0aW5ndWlzaCBiZXR3ZWVuIHNvZnR3YXJlIGVycm9ycyBhbmQgcHJvYmxlbXMgd2l0aCBHSUZzLlxuICAgICAqL1xuXG4gICAgZW5jb2RlR2lmKGZyYW1lcywgc3BlYyA9IHt9KSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBpZiAoZnJhbWVzID09PSBudWxsIHx8IGZyYW1lcy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgR2lmRXJyb3IoXCJ0aGVyZSBhcmUgbm8gZnJhbWVzXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZGltcyA9IEdpZlV0aWwoKS5nZXRNYXhEaW1lbnNpb25zKGZyYW1lcyk7XG5cbiAgICAgICAgICAgIHNwZWMgPSBPYmplY3QuYXNzaWduKHt9LCBzcGVjKTsgLy8gZG9uJ3QgbXVuZ2UgY2FsbGVyJ3Mgc3BlY1xuICAgICAgICAgICAgc3BlYy53aWR0aCA9IGRpbXMubWF4V2lkdGg7XG4gICAgICAgICAgICBzcGVjLmhlaWdodCA9IGRpbXMubWF4SGVpZ2h0O1xuICAgICAgICAgICAgaWYgKHNwZWMubG9vcHMgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIHNwZWMubG9vcHMgPSAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgc3BlYy5jb2xvclNjb3BlID0gc3BlYy5jb2xvclNjb3BlIHx8IEdpZi5HbG9iYWxDb2xvcnNQcmVmZXJyZWQ7XG5cbiAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUodGhpcy5fZW5jb2RlR2lmKGZyYW1lcywgc3BlYykpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnIpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgX2RlY29kZUZyYW1lKHJlYWRlciwgZnJhbWVJbmRleCwgYWxyZWFkeVVzZWRUcmFuc3BhcmVuY3kpIHtcbiAgICAgICAgbGV0IGluZm8sIGJ1ZmZlcjtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGluZm8gPSByZWFkZXIuZnJhbWVJbmZvKGZyYW1lSW5kZXgpO1xuICAgICAgICAgICAgYnVmZmVyID0gbmV3IEJ1ZmZlcihyZWFkZXIud2lkdGggKiByZWFkZXIuaGVpZ2h0ICogNCk7XG4gICAgICAgICAgICByZWFkZXIuZGVjb2RlQW5kQmxpdEZyYW1lUkdCQShmcmFtZUluZGV4LCBidWZmZXIpO1xuICAgICAgICAgICAgaWYgKGluZm8ud2lkdGggIT09IHJlYWRlci53aWR0aCB8fCBpbmZvLmhlaWdodCAhPT0gcmVhZGVyLmhlaWdodCkge1xuICAgICAgICAgICAgICAgIGlmIChpbmZvLnkpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gc2tpcCB1bnVzZWQgcm93c1xuICAgICAgICAgICAgICAgICAgICBidWZmZXIgPSBidWZmZXIuc2xpY2UoaW5mby55ICogcmVhZGVyLndpZHRoICogNCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChyZWFkZXIud2lkdGggPiBpbmZvLndpZHRoKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIHNraXAgc2NhbnN0cmlkZVxuICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpaSA9IDA7IGlpIDwgaW5mby5oZWlnaHQ7ICsraWkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJ1ZmZlci5jb3B5KGJ1ZmZlciwgaWkgKiBpbmZvLndpZHRoICogNCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoaW5mby54ICsgaWkgKiByZWFkZXIud2lkdGgpICogNCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoaW5mby54ICsgaWkgKiByZWFkZXIud2lkdGgpICogNCArIGluZm8ud2lkdGggKiA0KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyB0cmltIGJ1ZmZlciB0byBzaXplXG4gICAgICAgICAgICAgICAgYnVmZmVyID0gYnVmZmVyLnNsaWNlKDAsIGluZm8ud2lkdGggKiBpbmZvLmhlaWdodCAqIDQpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBHaWZFcnJvcihlcnIpO1xuICAgICAgICB9XG5cbiAgICAgICAgbGV0IHVzZXNUcmFuc3BhcmVuY3kgPSBmYWxzZTtcbiAgICAgICAgaWYgKHRoaXMuX3RyYW5zcGFyZW50UkdCQSA9PT0gbnVsbCkge1xuICAgICAgICAgICAgaWYgKCFhbHJlYWR5VXNlZFRyYW5zcGFyZW5jeSkge1xuICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAzOyBpIDwgYnVmZmVyLmxlbmd0aDsgaSArPSA0KSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChidWZmZXJbaV0gPT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXNUcmFuc3BhcmVuY3kgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgaSA9IGJ1ZmZlci5sZW5ndGg7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMzsgaSA8IGJ1ZmZlci5sZW5ndGg7IGkgKz0gNCkge1xuICAgICAgICAgICAgICAgIGlmIChidWZmZXJbaV0gPT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgYnVmZmVyLndyaXRlVUludDMyQkUodGhpcy5fdHJhbnNwYXJlbnRSR0JBLCBpIC0gMyk7XG4gICAgICAgICAgICAgICAgICAgIHVzZXNUcmFuc3BhcmVuY3kgPSB0cnVlOyAvLyBHSUYgbWlnaHQgZW5jb2RlIHVudXNlZCBpbmRleFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGZyYW1lID0gbmV3IEdpZkZyYW1lKGluZm8ud2lkdGgsIGluZm8uaGVpZ2h0LCBidWZmZXIsIHtcbiAgICAgICAgICAgIHhPZmZzZXQ6IGluZm8ueCxcbiAgICAgICAgICAgIHlPZmZzZXQ6IGluZm8ueSxcbiAgICAgICAgICAgIGRpc3Bvc2FsTWV0aG9kOiBpbmZvLmRpc3Bvc2FsLFxuICAgICAgICAgICAgaW50ZXJsYWNlZDogaW5mby5pbnRlcmxhY2VkLFxuICAgICAgICAgICAgZGVsYXlDZW50aXNlY3M6IGluZm8uZGVsYXlcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiB7IGZyYW1lLCB1c2VzVHJhbnNwYXJlbmN5IH07XG4gICAgfVxuXG4gICAgX2VuY29kZUdpZihmcmFtZXMsIHNwZWMpIHtcbiAgICAgICAgbGV0IGNvbG9ySW5mbztcbiAgICAgICAgaWYgKHNwZWMuY29sb3JTY29wZSA9PT0gR2lmLkxvY2FsQ29sb3JzT25seSkge1xuICAgICAgICAgICAgY29sb3JJbmZvID0gR2lmVXRpbCgpLmdldENvbG9ySW5mbyhmcmFtZXMsIDApO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29sb3JJbmZvID0gR2lmVXRpbCgpLmdldENvbG9ySW5mbyhmcmFtZXMsIDI1Nik7XG4gICAgICAgICAgICBpZiAoIWNvbG9ySW5mby5jb2xvcnMpIHsgLy8gaWYgZ2xvYmFsIHBhbGV0dGUgaW1wb3NzaWJsZVxuICAgICAgICAgICAgICAgIGlmIChzcGVjLmNvbG9yU2NvcGUgPT09IEdpZi5HbG9iYWxDb2xvcnNPbmx5KSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBHaWZFcnJvcihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIlRvbyBtYW55IGNvbG9yIGluZGV4ZXMgZm9yIGdsb2JhbCBjb2xvciB0YWJsZVwiKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgc3BlYy5jb2xvclNjb3BlID0gR2lmLkxvY2FsQ29sb3JzT25seVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHNwZWMudXNlc1RyYW5zcGFyZW5jeSA9IGNvbG9ySW5mby51c2VzVHJhbnNwYXJlbmN5O1xuXG4gICAgICAgIGNvbnN0IGxvY2FsUGFsZXR0ZXMgPSBjb2xvckluZm8ucGFsZXR0ZXM7XG4gICAgICAgIGlmIChzcGVjLmNvbG9yU2NvcGUgPT09IEdpZi5Mb2NhbENvbG9yc09ubHkpIHtcbiAgICAgICAgICAgIGNvbnN0IGxvY2FsU2l6ZUVzdCA9IDIwMDA7IC8vdGhpcy5fZ2V0U2l6ZUVzdGltYXRlTG9jYWwobG9jYWxQYWxldHRlcywgZnJhbWVzKTtcbiAgICAgICAgICAgIHJldHVybiBfZW5jb2RlTG9jYWwoZnJhbWVzLCBzcGVjLCBsb2NhbFNpemVFc3QsIGxvY2FsUGFsZXR0ZXMpO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZ2xvYmFsU2l6ZUVzdCA9IDIwMDA7IC8vdGhpcy5fZ2V0U2l6ZUVzdGltYXRlR2xvYmFsKGNvbG9ySW5mbywgZnJhbWVzKTtcbiAgICAgICAgcmV0dXJuIF9lbmNvZGVHbG9iYWwoZnJhbWVzLCBzcGVjLCBnbG9iYWxTaXplRXN0LCBjb2xvckluZm8pO1xuICAgIH1cblxuICAgIF9nZXRTaXplRXN0aW1hdGVHbG9iYWwoZ2xvYmFsUGFsZXR0ZSwgZnJhbWVzKSB7XG4gICAgICAgIGlmICh0aGlzLl90ZXN0SW5pdGlhbEJ1ZmZlclNpemUgPiAwKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fdGVzdEluaXRpYWxCdWZmZXJTaXplO1xuICAgICAgICB9XG4gICAgICAgIGxldCBzaXplRXN0ID0gUEVSX0dJRl9PVkVSSEVBRCArIDMqMjU2IC8qIG1heCBwYWxldHRlIHNpemUqLztcbiAgICAgICAgY29uc3QgcGl4ZWxCaXRXaWR0aCA9IF9nZXRQaXhlbEJpdFdpZHRoKGdsb2JhbFBhbGV0dGUpO1xuICAgICAgICBmcmFtZXMuZm9yRWFjaChmcmFtZSA9PiB7XG4gICAgICAgICAgICBzaXplRXN0ICs9IF9nZXRGcmFtZVNpemVFc3QoZnJhbWUsIHBpeGVsQml0V2lkdGgpO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHNpemVFc3Q7IC8vIHNob3VsZCBiZSB0aGUgdXBwZXIgbGltaXRcbiAgICB9XG5cbiAgICBfZ2V0U2l6ZUVzdGltYXRlTG9jYWwocGFsZXR0ZXMsIGZyYW1lcykge1xuICAgICAgICBpZiAodGhpcy5fdGVzdEluaXRpYWxCdWZmZXJTaXplID4gMCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX3Rlc3RJbml0aWFsQnVmZmVyU2l6ZTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgc2l6ZUVzdCA9IFBFUl9HSUZfT1ZFUkhFQUQ7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZnJhbWVzLmxlbmd0aDsgKytpICkge1xuICAgICAgICAgICAgY29uc3QgcGFsZXR0ZSA9IHBhbGV0dGVzW2ldO1xuICAgICAgICAgICAgY29uc3QgcGl4ZWxCaXRXaWR0aCA9IF9nZXRQaXhlbEJpdFdpZHRoKHBhbGV0dGUpO1xuICAgICAgICAgICAgc2l6ZUVzdCArPSBfZ2V0RnJhbWVTaXplRXN0KGZyYW1lc1tpXSwgcGl4ZWxCaXRXaWR0aCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNpemVFc3Q7IC8vIHNob3VsZCBiZSB0aGUgdXBwZXIgbGltaXRcbiAgICB9XG59XG5leHBvcnRzLkdpZkNvZGVjID0gR2lmQ29kZWM7XG5cbmZ1bmN0aW9uIF9jb2xvckxvb2t1cExpbmVhcihjb2xvcnMsIGNvbG9yKSB7XG4gICAgY29uc3QgaW5kZXggPSBjb2xvcnMuaW5kZXhPZihjb2xvcik7XG4gICAgcmV0dXJuIChpbmRleCA9PT0gLTEgPyBudWxsIDogaW5kZXgpO1xufVxuXG5mdW5jdGlvbiBfY29sb3JMb29rdXBCaW5hcnkoY29sb3JzLCBjb2xvcikge1xuICAgIC8vIGFkYXB0ZWQgZnJvbSBodHRwczovL3N0YWNrb3ZlcmZsb3cuY29tL2EvMTAyNjQzMTgvNjUwODk0XG4gICAgdmFyIGxvID0gMCwgaGkgPSBjb2xvcnMubGVuZ3RoIC0gMSwgbWlkO1xuICAgIHdoaWxlIChsbyA8PSBoaSkge1xuICAgICAgICBtaWQgPSBNYXRoLmZsb29yKChsbyArIGhpKS8yKTtcbiAgICAgICAgaWYgKGNvbG9yc1ttaWRdID4gY29sb3IpXG4gICAgICAgICAgICBoaSA9IG1pZCAtIDE7XG4gICAgICAgIGVsc2UgaWYgKGNvbG9yc1ttaWRdIDwgY29sb3IpXG4gICAgICAgICAgICBsbyA9IG1pZCArIDE7XG4gICAgICAgIGVsc2VcbiAgICAgICAgICAgIHJldHVybiBtaWQ7XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xufVxuXG5mdW5jdGlvbiBfZW5jb2RlR2xvYmFsKGZyYW1lcywgc3BlYywgYnVmZmVyU2l6ZUVzdCwgZ2xvYmFsUGFsZXR0ZSkge1xuICAgIC8vIHdvdWxkIGJlIGluZWZmaWNpZW50IGZvciBmcmFtZXMgdG8gbG9va3VwIGNvbG9ycyBpbiBleHRlbmRlZCBwYWxldHRlIFxuICAgIGNvbnN0IGV4dGVuZGVkR2xvYmFsUGFsZXR0ZSA9IHtcbiAgICAgICAgY29sb3JzOiBnbG9iYWxQYWxldHRlLmNvbG9ycy5zbGljZSgpLFxuICAgICAgICB1c2VzVHJhbnNwYXJlbmN5OiBnbG9iYWxQYWxldHRlLnVzZXNUcmFuc3BhcmVuY3lcbiAgICB9O1xuICAgIF9leHRlbmRQYWxldHRlVG9Qb3dlck9mMihleHRlbmRlZEdsb2JhbFBhbGV0dGUpO1xuICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgIHBhbGV0dGU6IGV4dGVuZGVkR2xvYmFsUGFsZXR0ZS5jb2xvcnMsXG4gICAgICAgIGxvb3A6IHNwZWMubG9vcHNcbiAgICB9O1xuICAgIGxldCBidWZmZXIgPSBuZXcgQnVmZmVyKGJ1ZmZlclNpemVFc3QpO1xuICAgIGxldCBnaWZXcml0ZXI7XG4gICAgdHJ5IHtcbiAgICAgICAgZ2lmV3JpdGVyID0gbmV3IE9tZ2dpZi5HaWZXcml0ZXIoYnVmZmVyLCBzcGVjLndpZHRoLCBzcGVjLmhlaWdodCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zKTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycikge1xuICAgICAgICB0aHJvdyBuZXcgR2lmRXJyb3IoZXJyKTtcbiAgICB9XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBmcmFtZXMubGVuZ3RoOyArK2kpIHtcbiAgICAgICAgYnVmZmVyID0gX3dyaXRlRnJhbWUoZ2lmV3JpdGVyLCBpLCBmcmFtZXNbaV0sIGdsb2JhbFBhbGV0dGUsIGZhbHNlKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBHaWYoYnVmZmVyLnNsaWNlKDAsIGdpZldyaXRlci5lbmQoKSksIGZyYW1lcywgc3BlYyk7XG59XG5cbmZ1bmN0aW9uIF9lbmNvZGVMb2NhbChmcmFtZXMsIHNwZWMsIGJ1ZmZlclNpemVFc3QsIGxvY2FsUGFsZXR0ZXMpIHtcbiAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgICBsb29wOiBzcGVjLmxvb3BzXG4gICAgfTtcbiAgICBsZXQgYnVmZmVyID0gbmV3IEJ1ZmZlcihidWZmZXJTaXplRXN0KTtcbiAgICBsZXQgZ2lmV3JpdGVyO1xuICAgIHRyeSB7XG4gICAgICAgIGdpZldyaXRlciA9IG5ldyBPbWdnaWYuR2lmV3JpdGVyKGJ1ZmZlciwgc3BlYy53aWR0aCwgc3BlYy5oZWlnaHQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9ucyk7XG4gICAgfSAgICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHRocm93IG5ldyBHaWZFcnJvcihlcnIpO1xuICAgIH1cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGZyYW1lcy5sZW5ndGg7ICsraSkge1xuICAgICAgICBidWZmZXIgPSBfd3JpdGVGcmFtZShnaWZXcml0ZXIsIGksIGZyYW1lc1tpXSwgbG9jYWxQYWxldHRlc1tpXSwgdHJ1ZSk7XG4gICAgfVxuICAgIHJldHVybiBuZXcgR2lmKGJ1ZmZlci5zbGljZSgwLCBnaWZXcml0ZXIuZW5kKCkpLCBmcmFtZXMsIHNwZWMpO1xufVxuXG5mdW5jdGlvbiBfZXh0ZW5kUGFsZXR0ZVRvUG93ZXJPZjIocGFsZXR0ZSkge1xuICAgIGNvbnN0IGNvbG9ycyA9IHBhbGV0dGUuY29sb3JzO1xuICAgIGlmIChwYWxldHRlLnVzZXNUcmFuc3BhcmVuY3kpIHtcbiAgICAgICAgY29sb3JzLnB1c2goMCk7XG4gICAgfVxuICAgIGNvbnN0IGNvbG9yQ291bnQgPSBjb2xvcnMubGVuZ3RoO1xuICAgIGxldCBwb3dlck9mMiA9IDI7XG4gICAgd2hpbGUgKGNvbG9yQ291bnQgPiBwb3dlck9mMikge1xuICAgICAgICBwb3dlck9mMiA8PD0gMTtcbiAgICB9XG4gICAgY29sb3JzLmxlbmd0aCA9IHBvd2VyT2YyO1xuICAgIGNvbG9ycy5maWxsKDAsIGNvbG9yQ291bnQpO1xufVxuXG5mdW5jdGlvbiBfZ2V0RnJhbWVTaXplRXN0KGZyYW1lLCBwaXhlbEJpdFdpZHRoKSB7XG4gICAgbGV0IGJ5dGVMZW5ndGggPSBmcmFtZS5iaXRtYXAud2lkdGggKiBmcmFtZS5iaXRtYXAuaGVpZ2h0O1xuICAgIGJ5dGVMZW5ndGggPSBNYXRoLmNlaWwoYnl0ZUxlbmd0aCAqIHBpeGVsQml0V2lkdGggLyA4KTtcbiAgICBieXRlTGVuZ3RoICs9IE1hdGguY2VpbChieXRlTGVuZ3RoIC8gMjU1KTsgLy8gYWRkIGJsb2NrIHNpemUgYnl0ZXNcbiAgICAvLyBhc3N1bWUgbWF4aW11bSBwYWxldGUgc2l6ZSBiZWNhdXNlIGl0IG1pZ2h0IGdldCBleHRlbmRlZCBmb3IgcG93ZXIgb2YgMlxuICAgIHJldHVybiAoUEVSX0ZSQU1FX09WRVJIRUFEICsgYnl0ZUxlbmd0aCArIDMgKiAyNTYgLyogbGFyZ2VzdCBwYWxldHRlICovKTtcbn1cblxuZnVuY3Rpb24gX2dldEluZGV4ZWRJbWFnZShmcmFtZUluZGV4LCBmcmFtZSwgcGFsZXR0ZSkge1xuICAgIGNvbnN0IGNvbG9ycyA9IHBhbGV0dGUuY29sb3JzO1xuICAgIGNvbnN0IGNvbG9yVG9JbmRleEZ1bmMgPSAoY29sb3JzLmxlbmd0aCA8PSA4ID8gLy8gZ3Vlc3MgYXQgdGhlIGJyZWFrLWV2ZW5cbiAgICAgICAgICAgIF9jb2xvckxvb2t1cExpbmVhciA6IF9jb2xvckxvb2t1cEJpbmFyeSk7XG4gICAgY29uc3QgY29sb3JCdWZmZXIgPSBmcmFtZS5iaXRtYXAuZGF0YTtcbiAgICBjb25zdCBpbmRleEJ1ZmZlciA9IG5ldyBCdWZmZXIoY29sb3JCdWZmZXIubGVuZ3RoLzQpO1xuICAgIGxldCB0cmFuc3BhcmVudEluZGV4ID0gY29sb3JzLmxlbmd0aDtcbiAgICBsZXQgaSA9IDAsIGogPSAwO1xuXG4gICAgd2hpbGUgKGkgPCBjb2xvckJ1ZmZlci5sZW5ndGgpIHtcbiAgICAgICAgaWYgKGNvbG9yQnVmZmVyW2kgKyAzXSAhPT0gMCkge1xuICAgICAgICAgICAgY29uc3QgY29sb3IgPSAoY29sb3JCdWZmZXIucmVhZFVJbnQzMkJFKGksIHRydWUpID4+IDgpICYgMHhGRkZGRkY7XG4gICAgICAgICAgICAvLyBjYWxsZXIgZ3VhcmFudGVlcyB0aGF0IHRoZSBjb2xvciB3aWxsIGJlIGluIHRoZSBwYWxldHRlXG4gICAgICAgICAgICBpbmRleEJ1ZmZlcltqXSA9IGNvbG9yVG9JbmRleEZ1bmMoY29sb3JzLCBjb2xvcik7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpbmRleEJ1ZmZlcltqXSA9IHRyYW5zcGFyZW50SW5kZXg7XG4gICAgICAgIH1cbiAgICAgICAgaSArPSA0OyAvLyBza2lwIGFscGhhXG4gICAgICAgICsrajtcbiAgICB9XG5cbiAgICBpZiAocGFsZXR0ZS51c2VzVHJhbnNwYXJlbmN5KSB7XG4gICAgICAgIGlmICh0cmFuc3BhcmVudEluZGV4ID09PSAyNTYpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBHaWZFcnJvcihgRnJhbWUgJHtmcmFtZUluZGV4fSBhbHJlYWR5IGhhcyAyNTYgY29sb3JzYCArXG4gICAgICAgICAgICAgICAgICAgIGBhbmQgc28gY2FuJ3QgdXNlIHRyYW5zcGFyZW5jeWApO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0cmFuc3BhcmVudEluZGV4ID0gbnVsbDtcbiAgICB9XG5cbiAgICByZXR1cm4geyBidWZmZXI6IGluZGV4QnVmZmVyLCB0cmFuc3BhcmVudEluZGV4IH07XG59XG5cbmZ1bmN0aW9uIF9nZXRQaXhlbEJpdFdpZHRoKHBhbGV0dGUpIHtcbiAgICBsZXQgaW5kZXhDb3VudCA9IHBhbGV0dGUuaW5kZXhDb3VudDtcbiAgICBsZXQgcGl4ZWxCaXRXaWR0aCA9IDA7XG4gICAgLS1pbmRleENvdW50OyAvLyBzdGFydCBhdCBtYXhpbXVtIGluZGV4XG4gICAgd2hpbGUgKGluZGV4Q291bnQpIHtcbiAgICAgICAgKytwaXhlbEJpdFdpZHRoO1xuICAgICAgICBpbmRleENvdW50ID4+PSAxO1xuICAgIH1cbiAgICByZXR1cm4gKHBpeGVsQml0V2lkdGggPiAwID8gcGl4ZWxCaXRXaWR0aCA6IDEpO1xufVxuXG5mdW5jdGlvbiBfd3JpdGVGcmFtZShnaWZXcml0ZXIsIGZyYW1lSW5kZXgsIGZyYW1lLCBwYWxldHRlLCBpc0xvY2FsUGFsZXR0ZSkge1xuICAgIGlmIChmcmFtZS5pbnRlcmxhY2VkKSB7XG4gICAgICAgIHRocm93IG5ldyBHaWZFcnJvcihcIndyaXRpbmcgaW50ZXJsYWNlZCBHSUZzIGlzIG5vdCBzdXBwb3J0ZWRcIik7XG4gICAgfVxuICAgIGNvbnN0IGZyYW1lSW5mbyA9IF9nZXRJbmRleGVkSW1hZ2UoZnJhbWVJbmRleCwgZnJhbWUsIHBhbGV0dGUpO1xuICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgIGRlbGF5OiBmcmFtZS5kZWxheUNlbnRpc2VjcyxcbiAgICAgICAgZGlzcG9zYWw6IGZyYW1lLmRpc3Bvc2FsTWV0aG9kLFxuICAgICAgICB0cmFuc3BhcmVudDogZnJhbWVJbmZvLnRyYW5zcGFyZW50SW5kZXhcbiAgICB9O1xuICAgIGlmIChpc0xvY2FsUGFsZXR0ZSkge1xuICAgICAgICBfZXh0ZW5kUGFsZXR0ZVRvUG93ZXJPZjIocGFsZXR0ZSk7IC8vIG9rICdjYXVzZSBwYWxldHRlIG5ldmVyIHVzZWQgYWdhaW5cbiAgICAgICAgb3B0aW9ucy5wYWxldHRlID0gcGFsZXR0ZS5jb2xvcnM7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICAgIGxldCBidWZmZXIgPSBnaWZXcml0ZXIuZ2V0T3V0cHV0QnVmZmVyKCk7XG4gICAgICAgIGxldCBzdGFydE9mRnJhbWUgPSBnaWZXcml0ZXIuZ2V0T3V0cHV0QnVmZmVyUG9zaXRpb24oKTtcbiAgICAgICAgbGV0IGVuZE9mRnJhbWU7XG4gICAgICAgIGxldCB0cnlBZ2FpbiA9IHRydWU7XG5cbiAgICAgICAgd2hpbGUgKHRyeUFnYWluKSB7XG4gICAgICAgICAgICBlbmRPZkZyYW1lID0gZ2lmV3JpdGVyLmFkZEZyYW1lKGZyYW1lLnhPZmZzZXQsIGZyYW1lLnlPZmZzZXQsXG4gICAgICAgICAgICAgICAgICAgIGZyYW1lLmJpdG1hcC53aWR0aCwgZnJhbWUuYml0bWFwLmhlaWdodCwgZnJhbWVJbmZvLmJ1ZmZlciwgb3B0aW9ucyk7XG4gICAgICAgICAgICB0cnlBZ2FpbiA9IGZhbHNlO1xuICAgICAgICAgICAgaWYgKGVuZE9mRnJhbWUgPj0gYnVmZmVyLmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBiaWdnZXJCdWZmZXIgPSBuZXcgQnVmZmVyKGJ1ZmZlci5sZW5ndGggKiAxLjUpO1xuICAgICAgICAgICAgICAgIGJ1ZmZlci5jb3B5KGJpZ2dlckJ1ZmZlcik7XG4gICAgICAgICAgICAgICAgZ2lmV3JpdGVyLnNldE91dHB1dEJ1ZmZlcihiaWdnZXJCdWZmZXIpO1xuICAgICAgICAgICAgICAgIGdpZldyaXRlci5zZXRPdXRwdXRCdWZmZXJQb3NpdGlvbihzdGFydE9mRnJhbWUpO1xuICAgICAgICAgICAgICAgIGJ1ZmZlciA9IGJpZ2dlckJ1ZmZlcjtcbiAgICAgICAgICAgICAgICB0cnlBZ2FpbiA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGJ1ZmZlcjtcbiAgICB9XG4gICAgY2F0Y2ggKGVycikge1xuICAgICAgICB0aHJvdyBuZXcgR2lmRXJyb3IoZXJyKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOlsiT21nZ2lmIiwicmVxdWlyZSIsIkdpZiIsIkdpZkVycm9yIiwiR2lmVXRpbCIsImRhdGEiLCJHaWZGcmFtZSIsIlBFUl9HSUZfT1ZFUkhFQUQiLCJQRVJfRlJBTUVfT1ZFUkhFQUQiLCJHaWZDb2RlYyIsImNvbnN0cnVjdG9yIiwib3B0aW9ucyIsIl90cmFuc3BhcmVudFJHQiIsInRyYW5zcGFyZW50UkdCIiwiX3RyYW5zcGFyZW50UkdCQSIsIl90ZXN0SW5pdGlhbEJ1ZmZlclNpemUiLCJkZWNvZGVHaWYiLCJidWZmZXIiLCJyZWFkZXIiLCJHaWZSZWFkZXIiLCJlcnIiLCJmcmFtZUNvdW50IiwibnVtRnJhbWVzIiwiZnJhbWVzIiwic3BlYyIsIndpZHRoIiwiaGVpZ2h0IiwibG9vcHMiLCJsb29wQ291bnQiLCJ1c2VzVHJhbnNwYXJlbmN5IiwiaSIsImZyYW1lSW5mbyIsIl9kZWNvZGVGcmFtZSIsInB1c2giLCJmcmFtZSIsIlByb21pc2UiLCJyZXNvbHZlIiwicmVqZWN0IiwiZW5jb2RlR2lmIiwibGVuZ3RoIiwiZGltcyIsImdldE1heERpbWVuc2lvbnMiLCJPYmplY3QiLCJhc3NpZ24iLCJtYXhXaWR0aCIsIm1heEhlaWdodCIsInVuZGVmaW5lZCIsImNvbG9yU2NvcGUiLCJHbG9iYWxDb2xvcnNQcmVmZXJyZWQiLCJfZW5jb2RlR2lmIiwiZnJhbWVJbmRleCIsImFscmVhZHlVc2VkVHJhbnNwYXJlbmN5IiwiaW5mbyIsIkJ1ZmZlciIsImRlY29kZUFuZEJsaXRGcmFtZVJHQkEiLCJ5Iiwic2xpY2UiLCJpaSIsImNvcHkiLCJ4Iiwid3JpdGVVSW50MzJCRSIsInhPZmZzZXQiLCJ5T2Zmc2V0IiwiZGlzcG9zYWxNZXRob2QiLCJkaXNwb3NhbCIsImludGVybGFjZWQiLCJkZWxheUNlbnRpc2VjcyIsImRlbGF5IiwiY29sb3JJbmZvIiwiTG9jYWxDb2xvcnNPbmx5IiwiZ2V0Q29sb3JJbmZvIiwiY29sb3JzIiwiR2xvYmFsQ29sb3JzT25seSIsImxvY2FsUGFsZXR0ZXMiLCJwYWxldHRlcyIsImxvY2FsU2l6ZUVzdCIsIl9lbmNvZGVMb2NhbCIsImdsb2JhbFNpemVFc3QiLCJfZW5jb2RlR2xvYmFsIiwiX2dldFNpemVFc3RpbWF0ZUdsb2JhbCIsImdsb2JhbFBhbGV0dGUiLCJzaXplRXN0IiwicGl4ZWxCaXRXaWR0aCIsIl9nZXRQaXhlbEJpdFdpZHRoIiwiZm9yRWFjaCIsIl9nZXRGcmFtZVNpemVFc3QiLCJfZ2V0U2l6ZUVzdGltYXRlTG9jYWwiLCJwYWxldHRlIiwiZXhwb3J0cyIsIl9jb2xvckxvb2t1cExpbmVhciIsImNvbG9yIiwiaW5kZXgiLCJpbmRleE9mIiwiX2NvbG9yTG9va3VwQmluYXJ5IiwibG8iLCJoaSIsIm1pZCIsIk1hdGgiLCJmbG9vciIsImJ1ZmZlclNpemVFc3QiLCJleHRlbmRlZEdsb2JhbFBhbGV0dGUiLCJfZXh0ZW5kUGFsZXR0ZVRvUG93ZXJPZjIiLCJsb29wIiwiZ2lmV3JpdGVyIiwiR2lmV3JpdGVyIiwiX3dyaXRlRnJhbWUiLCJlbmQiLCJjb2xvckNvdW50IiwicG93ZXJPZjIiLCJmaWxsIiwiYnl0ZUxlbmd0aCIsImJpdG1hcCIsImNlaWwiLCJfZ2V0SW5kZXhlZEltYWdlIiwiY29sb3JUb0luZGV4RnVuYyIsImNvbG9yQnVmZmVyIiwiaW5kZXhCdWZmZXIiLCJ0cmFuc3BhcmVudEluZGV4IiwiaiIsInJlYWRVSW50MzJCRSIsImluZGV4Q291bnQiLCJpc0xvY2FsUGFsZXR0ZSIsInRyYW5zcGFyZW50IiwiZ2V0T3V0cHV0QnVmZmVyIiwic3RhcnRPZkZyYW1lIiwiZ2V0T3V0cHV0QnVmZmVyUG9zaXRpb24iLCJlbmRPZkZyYW1lIiwidHJ5QWdhaW4iLCJhZGRGcmFtZSIsImJpZ2dlckJ1ZmZlciIsInNldE91dHB1dEJ1ZmZlciIsInNldE91dHB1dEJ1ZmZlclBvc2l0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gifwrap/src/gifcodec.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gifwrap/src/gifframe.js":
/*!**********************************************!*\
  !*** ./node_modules/gifwrap/src/gifframe.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst BitmapImage = __webpack_require__(/*! ./bitmapimage */ \"(rsc)/./node_modules/gifwrap/src/bitmapimage.js\");\nconst { GifError } = __webpack_require__(/*! ./gif */ \"(rsc)/./node_modules/gifwrap/src/gif.js\");\n/** @class GifFrame */ class GifFrame extends BitmapImage {\n    // xOffset - x offset of bitmap on GIF (defaults to 0)\n    // yOffset - y offset of bitmap on GIF (defaults to 0)\n    // disposalMethod - pixel disposal method when handling partial images\n    // delayCentisecs - duration of frame in hundredths of a second\n    // interlaced - whether the image is interlaced (defaults to false)\n    /**\n     * GifFrame is a class representing an image frame of a GIF. GIFs contain one or more instances of GifFrame.\n     * \n     * Property | Description\n     * --- | ---\n     * xOffset | x-coord of position within GIF at which to render the image (defaults to 0)\n     * yOffset | y-coord of position within GIF at which to render the image (defaults to 0)\n     * disposalMethod | GIF disposal method; only relevant when the frames aren't all the same size (defaults to 2, disposing to background color)\n     * delayCentisecs | duration of the frame in hundreths of a second\n     * interlaced | boolean indicating whether the frame renders interlaced\n     * \n     * Its constructor supports the following signatures:\n     * \n     * * new GifFrame(bitmap: {width: number, height: number, data: Buffer}, options?)\n     * * new GifFrame(bitmapImage: BitmapImage, options?)\n     * * new GifFrame(width: number, height: number, buffer: Buffer, options?)\n     * * new GifFrame(width: number, height: number, backgroundRGBA?: number, options?)\n     * * new GifFrame(frame: GifFrame)\n     * \n     * See the base class BitmapImage for a discussion of all parameters but `options` and `frame`. `options` is an optional argument providing initial values for the above-listed GifFrame properties. Each property within option is itself optional.\n     * \n     * Provide a `frame` to the constructor to create a clone of the provided frame. The new frame includes a copy of the provided frame's pixel data so that each can subsequently be modified without affecting each other.\n     */ constructor(...args){\n        super(...args);\n        if (args[0] instanceof GifFrame) {\n            // copy a provided GifFrame\n            const source = args[0];\n            this.xOffset = source.xOffset;\n            this.yOffset = source.yOffset;\n            this.disposalMethod = source.disposalMethod;\n            this.delayCentisecs = source.delayCentisecs;\n            this.interlaced = source.interlaced;\n        } else {\n            const lastArg = args[args.length - 1];\n            let options = {};\n            if (typeof lastArg === \"object\" && !(lastArg instanceof BitmapImage)) {\n                options = lastArg;\n            }\n            this.xOffset = options.xOffset || 0;\n            this.yOffset = options.yOffset || 0;\n            this.disposalMethod = options.disposalMethod !== undefined ? options.disposalMethod : GifFrame.DisposeToBackgroundColor;\n            this.delayCentisecs = options.delayCentisecs || 8;\n            this.interlaced = options.interlaced || false;\n        }\n    }\n    /**\n     * Get a summary of the colors found within the frame. The return value is an object of the following form:\n     * \n     * Property | Description\n     * --- | ---\n     * colors | An array of all the opaque colors found within the frame. Each color is given as an RGB number of the form 0xRRGGBB. The array is sorted by increasing number. Will be an empty array when the image is completely transparent.\n     * usesTransparency | boolean indicating whether there are any transparent pixels within the frame. A pixel is considered transparent if its alpha value is 0x00.\n     * indexCount | The number of color indexes required to represent this palette of colors. It is equal to the number of opaque colors plus one if the image includes transparency.\n     * \n     * @return {object} An object representing a color palette as described above.\n     */ getPalette() {\n        // returns with colors sorted low to high\n        const colorSet = new Set();\n        const buf = this.bitmap.data;\n        let i = 0;\n        let usesTransparency = false;\n        while(i < buf.length){\n            if (buf[i + 3] === 0) {\n                usesTransparency = true;\n            } else {\n                // can eliminate the bitshift by starting one byte prior\n                const color = buf.readUInt32BE(i, true) >> 8 & 0xFFFFFF;\n                colorSet.add(color);\n            }\n            i += 4; // skip alpha\n        }\n        const colors = new Array(colorSet.size);\n        const iter = colorSet.values();\n        for(i = 0; i < colors.length; ++i){\n            colors[i] = iter.next().value;\n        }\n        colors.sort((a, b)=>a - b);\n        let indexCount = colors.length;\n        if (usesTransparency) {\n            ++indexCount;\n        }\n        return {\n            colors,\n            usesTransparency,\n            indexCount\n        };\n    }\n}\nGifFrame.DisposeToAnything = 0;\nGifFrame.DisposeNothing = 1;\nGifFrame.DisposeToBackgroundColor = 2;\nGifFrame.DisposeToPrevious = 3;\nexports.GifFrame = GifFrame;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gifwrap/src/gifframe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gifwrap/src/gifutil.js":
/*!*********************************************!*\
  !*** ./node_modules/gifwrap/src/gifutil.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/** @namespace GifUtil */ const fs = __webpack_require__(/*! fs */ \"fs\");\nconst ImageQ = __webpack_require__(/*! image-q */ \"(rsc)/./node_modules/image-q/dist/cjs/image-q.cjs\");\nconst BitmapImage = __webpack_require__(/*! ./bitmapimage */ \"(rsc)/./node_modules/gifwrap/src/bitmapimage.js\");\nconst { GifFrame } = __webpack_require__(/*! ./gifframe */ \"(rsc)/./node_modules/gifwrap/src/gifframe.js\");\nconst { GifError } = __webpack_require__(/*! ./gif */ \"(rsc)/./node_modules/gifwrap/src/gif.js\");\nconst { GifCodec } = __webpack_require__(/*! ./gifcodec */ \"(rsc)/./node_modules/gifwrap/src/gifcodec.js\");\nconst INVALID_SUFFIXES = [\n    \".jpg\",\n    \".jpeg\",\n    \".png\",\n    \".bmp\"\n];\nconst defaultCodec = new GifCodec();\n/**\n * cloneFrames() clones provided frames. It's a utility method for cloning an entire array of frames at once.\n * \n * @function cloneFrames\n * @memberof GifUtil\n * @param {GifFrame[]} frames An array of GifFrame instances to clone\n * @return {GifFrame[]} An array of GifFrame clones of the provided frames.\n */ exports.cloneFrames = function(frames) {\n    let clones = [];\n    frames.forEach((frame)=>{\n        clones.push(new GifFrame(frame));\n    });\n    return clones;\n};\n/**\n * getColorInfo() gets information about the colors used in the provided frames. The method is able to return an array of all colors found across all frames.\n * \n * `maxGlobalIndex` controls whether the computation short-circuits to avoid doing work that the caller doesn't need. The method only returns `colors` and `indexCount` for the colors across all frames when the number of indexes required to store the colors and transparency in a GIF (which is the value of `indexCount`) is less than or equal to `maxGlobalIndex`. Such short-circuiting is useful when the caller just needs to determine whether any frame includes transparency.\n * \n * @function getColorInfo\n * @memberof GifUtil\n * @param {GifFrame[]} frames Frames to examine for color and transparency.\n * @param {number} maxGlobalIndex Maximum number of color indexes (including one for transparency) allowed among the returned compilation of colors. `colors` and `indexCount` are not returned if the number of color indexes required to accommodate  all frames exceeds this number. Returns `colors` and `indexCount` by default.\n * @returns {object} Object containing at least `palettes` and `usesTransparency`. `palettes` is an array of all the palettes returned by GifFrame#getPalette(). `usesTransparency` indicates whether at least one frame uses transparency. If `maxGlobalIndex` is not exceeded, the object also contains `colors`, an array of all colors (RGB) found across all palettes, sorted by increasing value, and `indexCount` indicating the number of indexes required to store the colors and the transparency in a GIF.\n * @throws {GifError} When any frame requires more than 256 color indexes.\n */ exports.getColorInfo = function(frames, maxGlobalIndex) {\n    let usesTransparency = false;\n    const palettes = [];\n    for(let i = 0; i < frames.length; ++i){\n        let palette = frames[i].getPalette();\n        if (palette.usesTransparency) {\n            usesTransparency = true;\n        }\n        if (palette.indexCount > 256) {\n            throw new GifError(`Frame ${i} uses more than 256 color indexes`);\n        }\n        palettes.push(palette);\n    }\n    if (maxGlobalIndex === 0) {\n        return {\n            usesTransparency,\n            palettes\n        };\n    }\n    const globalColorSet = new Set();\n    palettes.forEach((palette)=>{\n        palette.colors.forEach((color)=>{\n            globalColorSet.add(color);\n        });\n    });\n    let indexCount = globalColorSet.size;\n    if (usesTransparency) {\n        // odd that GIF requires a color table entry at transparent index\n        ++indexCount;\n    }\n    if (maxGlobalIndex && indexCount > maxGlobalIndex) {\n        return {\n            usesTransparency,\n            palettes\n        };\n    }\n    const colors = new Array(globalColorSet.size);\n    const iter = globalColorSet.values();\n    for(let i = 0; i < colors.length; ++i){\n        colors[i] = iter.next().value;\n    }\n    colors.sort((a, b)=>a - b);\n    return {\n        colors,\n        indexCount,\n        usesTransparency,\n        palettes\n    };\n};\n/**\n * copyAsJimp() returns a Jimp that contains a copy of the provided bitmap image (which may be either a BitmapImage or a GifFrame). Modifying the Jimp does not affect the provided bitmap image. This method serves as a macro for simplifying working with Jimp.\n *\n * @function copyAsJimp\n * @memberof GifUtil\n * @param {object} Reference to the Jimp package, keeping this library from being dependent on Jimp.\n * @param {bitmapImageToCopy} Instance of BitmapImage (may be a GifUtil) with which to source the Jimp.\n * @return {object} An new instance of Jimp containing a copy of the image in bitmapImageToCopy.\n */ exports.copyAsJimp = function(jimp, bitmapImageToCopy) {\n    return exports.shareAsJimp(jimp, new BitmapImage(bitmapImageToCopy));\n};\n/**\n * getMaxDimensions() returns the pixel width and height required to accommodate all of the provided frames, according to the offsets and dimensions of each frame.\n * \n * @function getMaxDimensions\n * @memberof GifUtil\n * @param {GifFrame[]} frames Frames to measure for their aggregate maximum dimensions.\n * @return {object} An object of the form {maxWidth, maxHeight} indicating the maximum width and height required to accommodate all frames.\n */ exports.getMaxDimensions = function(frames) {\n    let maxWidth = 0, maxHeight = 0;\n    frames.forEach((frame)=>{\n        const width = frame.xOffset + frame.bitmap.width;\n        if (width > maxWidth) {\n            maxWidth = width;\n        }\n        const height = frame.yOffset + frame.bitmap.height;\n        if (height > maxHeight) {\n            maxHeight = height;\n        }\n    });\n    return {\n        maxWidth,\n        maxHeight\n    };\n};\n/**\n * Quantizes colors so that there are at most a given number of color indexes (including transparency) across all provided images. Uses an algorithm by Anthony Dekker.\n * \n * The method treats different RGBA combinations as different colors, so if the frame has multiple alpha values or multiple RGB values for an alpha value, the caller may first want to normalize them by converting all transparent pixels to the same RGBA values.\n * \n * The method may increase the number of colors if there are fewer than the provided maximum.\n * \n * @function quantizeDekker\n * @memberof GifUtil\n * @param {BitmapImage|BitmapImage[]} imageOrImages Image or array of images (such as GifFrame instances) to be color-quantized. Quantizing across multiple images ensures color consistency from frame to frame.\n * @param {number} maxColorIndexes The maximum number of color indexes that will exist in the palette after completing quantization. Defaults to 256.\n * @param {object} dither (optional) An object configuring the dithering to apply. The properties are as followings, imported from the [`image-q` package](https://github.com/ibezkrovnyi/image-quantization) without explanation: { `ditherAlgorithm`: One of 'FloydSteinberg', 'FalseFloydSteinberg', 'Stucki', 'Atkinson', 'Jarvis', 'Burkes', 'Sierra', 'TwoSierra', 'SierraLite'; `minimumColorDistanceToDither`: (optional) A number defaulting to 0; `serpentine`: (optional) A boolean defaulting to true; `calculateErrorLikeGIMP`: (optional) A boolean defaulting to false. }\n */ exports.quantizeDekker = function(imageOrImages, maxColorIndexes, dither) {\n    maxColorIndexes = maxColorIndexes || 256;\n    _quantize(imageOrImages, \"NeuQuantFloat\", maxColorIndexes, 0, dither);\n};\n/**\n * Quantizes colors so that there are at most a given number of color indexes (including transparency) across all provided images. Uses an algorithm by Leon Sorokin. This quantization method differs from the other two by likely never increasing the number of colors, should there be fewer than the provided maximum.\n * \n * The method treats different RGBA combinations as different colors, so if the frame has multiple alpha values or multiple RGB values for an alpha value, the caller may first want to normalize them by converting all transparent pixels to the same RGBA values.\n * \n * @function quantizeSorokin\n * @memberof GifUtil\n * @param {BitmapImage|BitmapImage[]} imageOrImages Image or array of images (such as GifFrame instances) to be color-quantized. Quantizing across multiple images ensures color consistency from frame to frame.\n * @param {number} maxColorIndexes The maximum number of color indexes that will exist in the palette after completing quantization. Defaults to 256.\n * @param {string} histogram (optional) Histogram method: 'top-pop' for global top-population, 'min-pop' for minimum-population threshhold within subregions. Defaults to 'min-pop'.\n * @param {object} dither (optional) An object configuring the dithering to apply, as explained for `quantizeDekker()`.\n */ exports.quantizeSorokin = function(imageOrImages, maxColorIndexes, histogram, dither) {\n    maxColorIndexes = maxColorIndexes || 256;\n    histogram = histogram || \"min-pop\";\n    let histogramID;\n    switch(histogram){\n        case \"min-pop\":\n            histogramID = 2;\n            break;\n        case \"top-pop\":\n            histogramID = 1;\n            break;\n        default:\n            throw new Error(`Invalid quantizeSorokin histogram '${histogram}'`);\n    }\n    _quantize(imageOrImages, \"RGBQuant\", maxColorIndexes, histogramID, dither);\n};\n/**\n * Quantizes colors so that there are at most a given number of color indexes (including transparency) across all provided images. Uses an algorithm by Xiaolin Wu.\n * \n * The method treats different RGBA combinations as different colors, so if the frame has multiple alpha values or multiple RGB values for an alpha value, the caller may first want to normalize them by converting all transparent pixels to the same RGBA values.\n * \n * The method may increase the number of colors if there are fewer than the provided maximum.\n * \n * @function quantizeWu\n * @memberof GifUtil\n * @param {BitmapImage|BitmapImage[]} imageOrImages Image or array of images (such as GifFrame instances) to be color-quantized. Quantizing across multiple images ensures color consistency from frame to frame.\n * @param {number} maxColorIndexes The maximum number of color indexes that will exist in the palette after completing quantization. Defaults to 256.\n * @param {number} significantBits (optional) This is the number of significant high bits in each RGB color channel. Takes integer values from 1 through 8. Higher values correspond to higher quality. Defaults to 5.\n * @param {object} dither (optional) An object configuring the dithering to apply, as explained for `quantizeDekker()`.\n */ exports.quantizeWu = function(imageOrImages, maxColorIndexes, significantBits, dither) {\n    maxColorIndexes = maxColorIndexes || 256;\n    significantBits = significantBits || 5;\n    if (significantBits < 1 || significantBits > 8) {\n        throw new Error(\"Invalid quantization quality\");\n    }\n    _quantize(imageOrImages, \"WuQuant\", maxColorIndexes, significantBits, dither);\n};\n/**\n * read() decodes an encoded GIF, whether provided as a filename or as a byte buffer.\n * \n * @function read\n * @memberof GifUtil\n * @param {string|Buffer} source Source to decode. When a string, it's the GIF filename to load and parse. When a Buffer, it's an encoded GIF to parse.\n * @param {object} decoder An optional GIF decoder object implementing the `decode` method of class GifCodec. When provided, the method decodes the GIF using this decoder. When not provided, the method uses GifCodec.\n * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the decoded GIF.\n */ exports.read = function(source, decoder) {\n    decoder = decoder || defaultCodec;\n    if (Buffer.isBuffer(source)) {\n        return decoder.decodeGif(source);\n    }\n    return _readBinary(source).then((buffer)=>{\n        return decoder.decodeGif(buffer);\n    });\n};\n/**\n * shareAsJimp() returns a Jimp that shares a bitmap with the provided bitmap image (which may be either a BitmapImage or a GifFrame). Modifying the image in either the Jimp or the BitmapImage affects the other objects. This method serves as a macro for simplifying working with Jimp.\n *\n * @function shareAsJimp\n * @memberof GifUtil\n * @param {object} Reference to the Jimp package, keeping this library from being dependent on Jimp.\n * @param {bitmapImageToShare} Instance of BitmapImage (may be a GifUtil) with which to source the Jimp.\n * @return {object} An new instance of Jimp that shares the image in bitmapImageToShare.\n */ exports.shareAsJimp = function(jimp, bitmapImageToShare) {\n    const jimpImage = new jimp(bitmapImageToShare.bitmap.width, bitmapImageToShare.bitmap.height, 0);\n    jimpImage.bitmap.data = bitmapImageToShare.bitmap.data;\n    return jimpImage;\n};\n/**\n * write() encodes a GIF and saves it as a file.\n * \n * @function write\n * @memberof GifUtil\n * @param {string} path Filename to write GIF out as. Will overwrite an existing file.\n * @param {GifFrame[]} frames Array of frames to be written into GIF.\n * @param {object} spec An optional object that may provide values for `loops` and `colorScope`, as defined for the Gif class. However, `colorSpace` may also take the value Gif.GlobalColorsPreferred (== 0) to indicate that the encoder should attempt to create only a global color table. `loop` defaults to 0, looping indefinitely, and `colorScope` defaults to Gif.GlobalColorsPreferred.\n * @param {object} encoder An optional GIF encoder object implementing the `encode` method of class GifCodec. When provided, the method encodes the GIF using this encoder. When not provided, the method uses GifCodec.\n * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the encoded GIF.\n */ exports.write = function(path, frames, spec, encoder) {\n    encoder = encoder || defaultCodec;\n    const matches = path.match(/\\.[a-zA-Z]+$/); // prevent accidents\n    if (matches !== null && INVALID_SUFFIXES.includes(matches[0].toLowerCase())) {\n        throw new Error(`GIF '${path}' has an unexpected suffix`);\n    }\n    return encoder.encodeGif(frames, spec).then((gif)=>{\n        return _writeBinary(path, gif.buffer).then(()=>{\n            return gif;\n        });\n    });\n};\nfunction _quantize(imageOrImages, method, maxColorIndexes, modifier, dither) {\n    const images = Array.isArray(imageOrImages) ? imageOrImages : [\n        imageOrImages\n    ];\n    const ditherAlgs = [\n        \"FloydSteinberg\",\n        \"FalseFloydSteinberg\",\n        \"Stucki\",\n        \"Atkinson\",\n        \"Jarvis\",\n        \"Burkes\",\n        \"Sierra\",\n        \"TwoSierra\",\n        \"SierraLite\"\n    ];\n    if (dither) {\n        if (ditherAlgs.indexOf(dither.ditherAlgorithm) < 0) {\n            throw new Error(`Invalid ditherAlgorithm '${dither.ditherAlgorithm}'`);\n        }\n        if (dither.serpentine === undefined) {\n            dither.serpentine = true;\n        }\n        if (dither.minimumColorDistanceToDither === undefined) {\n            dither.minimumColorDistanceToDither = 0;\n        }\n        if (dither.calculateErrorLikeGIMP === undefined) {\n            dither.calculateErrorLikeGIMP = false;\n        }\n    }\n    const distCalculator = new ImageQ.distance.Euclidean();\n    const quantizer = new ImageQ.palette[method](distCalculator, maxColorIndexes, modifier);\n    let imageMaker;\n    if (dither) {\n        imageMaker = new ImageQ.image.ErrorDiffusionArray(distCalculator, ImageQ.image.ErrorDiffusionArrayKernel[dither.ditherAlgorithm], dither.serpentine, dither.minimumColorDistanceToDither, dither.calculateErrorLikeGIMP);\n    } else {\n        imageMaker = new ImageQ.image.NearestColor(distCalculator);\n    }\n    const inputContainers = [];\n    images.forEach((image)=>{\n        const imageBuf = image.bitmap.data;\n        const inputBuf = new ArrayBuffer(imageBuf.length);\n        const inputArray = new Uint32Array(inputBuf);\n        for(let bi = 0, ai = 0; bi < imageBuf.length; bi += 4, ++ai){\n            inputArray[ai] = imageBuf.readUInt32LE(bi, true);\n        }\n        const inputContainer = ImageQ.utils.PointContainer.fromUint32Array(inputArray, image.bitmap.width, image.bitmap.height);\n        quantizer.sample(inputContainer);\n        inputContainers.push(inputContainer);\n    });\n    const limitedPalette = quantizer.quantizeSync();\n    for(let i = 0; i < images.length; ++i){\n        const imageBuf = images[i].bitmap.data;\n        const outputContainer = imageMaker.quantizeSync(inputContainers[i], limitedPalette);\n        const outputArray = outputContainer.toUint32Array();\n        for(let bi = 0, ai = 0; bi < imageBuf.length; bi += 4, ++ai){\n            imageBuf.writeUInt32LE(outputArray[ai], bi);\n        }\n    }\n}\nfunction _readBinary(path) {\n    // TBD: add support for URLs\n    return new Promise((resolve, reject)=>{\n        fs.readFile(path, (err, buffer)=>{\n            if (err) {\n                return reject(err);\n            }\n            return resolve(buffer);\n        });\n    });\n}\nfunction _writeBinary(path, buffer) {\n    // TBD: add support for URLs\n    return new Promise((resolve, reject)=>{\n        fs.writeFile(path, buffer, (err)=>{\n            if (err) {\n                return reject(err);\n            }\n            return resolve();\n        });\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2lmd3JhcC9zcmMvZ2lmdXRpbC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLHVCQUF1QixHQUV2QixNQUFNQSxLQUFLQyxtQkFBT0EsQ0FBQztBQUNuQixNQUFNQyxTQUFTRCxtQkFBT0EsQ0FBQztBQUV2QixNQUFNRSxjQUFjRixtQkFBT0EsQ0FBQztBQUM1QixNQUFNLEVBQUVHLFFBQVEsRUFBRSxHQUFHSCxtQkFBT0EsQ0FBQztBQUM3QixNQUFNLEVBQUVJLFFBQVEsRUFBRSxHQUFHSixtQkFBT0EsQ0FBQztBQUM3QixNQUFNLEVBQUVLLFFBQVEsRUFBRSxHQUFHTCxtQkFBT0EsQ0FBQztBQUU3QixNQUFNTSxtQkFBbUI7SUFBQztJQUFRO0lBQVM7SUFBUTtDQUFPO0FBRTFELE1BQU1DLGVBQWUsSUFBSUY7QUFFekI7Ozs7Ozs7Q0FPQyxHQUVERyxtQkFBbUIsR0FBRyxTQUFVRSxNQUFNO0lBQ2xDLElBQUlDLFNBQVMsRUFBRTtJQUNmRCxPQUFPRSxPQUFPLENBQUNDLENBQUFBO1FBRVhGLE9BQU9HLElBQUksQ0FBQyxJQUFJWCxTQUFTVTtJQUM3QjtJQUNBLE9BQU9GO0FBQ1g7QUFFQTs7Ozs7Ozs7Ozs7Q0FXQyxHQUVESCxvQkFBb0IsR0FBRyxTQUFVRSxNQUFNLEVBQUVNLGNBQWM7SUFDbkQsSUFBSUMsbUJBQW1CO0lBQ3ZCLE1BQU1DLFdBQVcsRUFBRTtJQUNuQixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSVQsT0FBT1UsTUFBTSxFQUFFLEVBQUVELEVBQUc7UUFDcEMsSUFBSUUsVUFBVVgsTUFBTSxDQUFDUyxFQUFFLENBQUNHLFVBQVU7UUFDbEMsSUFBSUQsUUFBUUosZ0JBQWdCLEVBQUU7WUFDMUJBLG1CQUFtQjtRQUN2QjtRQUNBLElBQUlJLFFBQVFFLFVBQVUsR0FBRyxLQUFLO1lBQzFCLE1BQU0sSUFBSW5CLFNBQVMsQ0FBQyxNQUFNLEVBQUVlLEVBQUUsaUNBQWlDLENBQUM7UUFDcEU7UUFDQUQsU0FBU0osSUFBSSxDQUFDTztJQUNsQjtJQUNBLElBQUlMLG1CQUFtQixHQUFHO1FBQ3RCLE9BQU87WUFBRUM7WUFBa0JDO1FBQVM7SUFDeEM7SUFFQSxNQUFNTSxpQkFBaUIsSUFBSUM7SUFDM0JQLFNBQVNOLE9BQU8sQ0FBQ1MsQ0FBQUE7UUFFYkEsUUFBUUssTUFBTSxDQUFDZCxPQUFPLENBQUNlLENBQUFBO1lBRW5CSCxlQUFlSSxHQUFHLENBQUNEO1FBQ3ZCO0lBQ0o7SUFDQSxJQUFJSixhQUFhQyxlQUFlSyxJQUFJO0lBQ3BDLElBQUlaLGtCQUFrQjtRQUNsQixpRUFBaUU7UUFDakUsRUFBRU07SUFDTjtJQUNBLElBQUlQLGtCQUFrQk8sYUFBYVAsZ0JBQWdCO1FBQy9DLE9BQU87WUFBRUM7WUFBa0JDO1FBQVM7SUFDeEM7SUFFQSxNQUFNUSxTQUFTLElBQUlJLE1BQU1OLGVBQWVLLElBQUk7SUFDNUMsTUFBTUUsT0FBT1AsZUFBZVEsTUFBTTtJQUNsQyxJQUFLLElBQUliLElBQUksR0FBR0EsSUFBSU8sT0FBT04sTUFBTSxFQUFFLEVBQUVELEVBQUc7UUFDcENPLE1BQU0sQ0FBQ1AsRUFBRSxHQUFHWSxLQUFLRSxJQUFJLEdBQUdDLEtBQUs7SUFDakM7SUFDQVIsT0FBT1MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU9ELElBQUlDO0lBQzNCLE9BQU87UUFBRVg7UUFBUUg7UUFBWU47UUFBa0JDO0lBQVM7QUFDNUQ7QUFFQTs7Ozs7Ozs7Q0FRQyxHQUVEVixrQkFBa0IsR0FBRyxTQUFVK0IsSUFBSSxFQUFFQyxpQkFBaUI7SUFDbEQsT0FBT2hDLFFBQVFpQyxXQUFXLENBQUNGLE1BQU0sSUFBSXJDLFlBQVlzQztBQUNyRDtBQUVBOzs7Ozs7O0NBT0MsR0FFRGhDLHdCQUF3QixHQUFHLFNBQVVFLE1BQU07SUFDdkMsSUFBSWlDLFdBQVcsR0FBR0MsWUFBWTtJQUM5QmxDLE9BQU9FLE9BQU8sQ0FBQ0MsQ0FBQUE7UUFDWCxNQUFNZ0MsUUFBUWhDLE1BQU1pQyxPQUFPLEdBQUdqQyxNQUFNa0MsTUFBTSxDQUFDRixLQUFLO1FBQ2hELElBQUlBLFFBQVFGLFVBQVU7WUFDbEJBLFdBQVdFO1FBQ2Y7UUFDQSxNQUFNRyxTQUFTbkMsTUFBTW9DLE9BQU8sR0FBR3BDLE1BQU1rQyxNQUFNLENBQUNDLE1BQU07UUFDbEQsSUFBSUEsU0FBU0osV0FBVztZQUNwQkEsWUFBWUk7UUFDaEI7SUFDSjtJQUNBLE9BQU87UUFBRUw7UUFBVUM7SUFBVTtBQUNqQztBQUVBOzs7Ozs7Ozs7Ozs7Q0FZQyxHQUVEcEMsc0JBQXNCLEdBQUcsU0FBVTJDLGFBQWEsRUFBRUMsZUFBZSxFQUFFQyxNQUFNO0lBQ3JFRCxrQkFBa0JBLG1CQUFtQjtJQUNyQ0UsVUFBVUgsZUFBZSxpQkFBaUJDLGlCQUFpQixHQUFHQztBQUNsRTtBQUVBOzs7Ozs7Ozs7OztDQVdDLEdBRUQ3Qyx1QkFBdUIsR0FBRyxTQUFVMkMsYUFBYSxFQUFFQyxlQUFlLEVBQUVJLFNBQVMsRUFBRUgsTUFBTTtJQUNqRkQsa0JBQWtCQSxtQkFBbUI7SUFDckNJLFlBQVlBLGFBQWE7SUFDekIsSUFBSUM7SUFDSixPQUFRRDtRQUNKLEtBQUs7WUFDTEMsY0FBYztZQUNkO1FBRUEsS0FBSztZQUNMQSxjQUFjO1lBQ2Q7UUFFQTtZQUNBLE1BQU0sSUFBSUMsTUFBTSxDQUFDLG1DQUFtQyxFQUFFRixVQUFVLENBQUMsQ0FBQztJQUN0RTtJQUNBRixVQUFVSCxlQUFlLFlBQVlDLGlCQUFpQkssYUFBYUo7QUFDdkU7QUFFQTs7Ozs7Ozs7Ozs7OztDQWFDLEdBRUQ3QyxrQkFBa0IsR0FBRyxTQUFVMkMsYUFBYSxFQUFFQyxlQUFlLEVBQUVRLGVBQWUsRUFBRVAsTUFBTTtJQUNsRkQsa0JBQWtCQSxtQkFBbUI7SUFDckNRLGtCQUFrQkEsbUJBQW1CO0lBQ3JDLElBQUlBLGtCQUFrQixLQUFLQSxrQkFBa0IsR0FBRztRQUM1QyxNQUFNLElBQUlGLE1BQU07SUFDcEI7SUFDQUosVUFBVUgsZUFBZSxXQUFXQyxpQkFBaUJRLGlCQUFpQlA7QUFDMUU7QUFFQTs7Ozs7Ozs7Q0FRQyxHQUVEN0MsWUFBWSxHQUFHLFNBQVVzRCxNQUFNLEVBQUVDLE9BQU87SUFDcENBLFVBQVVBLFdBQVd4RDtJQUNyQixJQUFJeUQsT0FBT0MsUUFBUSxDQUFDSCxTQUFTO1FBQ3pCLE9BQU9DLFFBQVFHLFNBQVMsQ0FBQ0o7SUFDN0I7SUFDQSxPQUFPSyxZQUFZTCxRQUNsQk0sSUFBSSxDQUFDQyxDQUFBQTtRQUVGLE9BQU9OLFFBQVFHLFNBQVMsQ0FBQ0c7SUFDN0I7QUFDSjtBQUVBOzs7Ozs7OztDQVFDLEdBRUQ3RCxtQkFBbUIsR0FBRyxTQUFVK0IsSUFBSSxFQUFFK0Isa0JBQWtCO0lBQ3BELE1BQU1DLFlBQVksSUFBSWhDLEtBQUsrQixtQkFBbUJ2QixNQUFNLENBQUNGLEtBQUssRUFDbER5QixtQkFBbUJ2QixNQUFNLENBQUNDLE1BQU0sRUFBRTtJQUMxQ3VCLFVBQVV4QixNQUFNLENBQUN5QixJQUFJLEdBQUdGLG1CQUFtQnZCLE1BQU0sQ0FBQ3lCLElBQUk7SUFDdEQsT0FBT0Q7QUFDWDtBQUVBOzs7Ozs7Ozs7O0NBVUMsR0FFRC9ELGFBQWEsR0FBRyxTQUFVa0UsSUFBSSxFQUFFaEUsTUFBTSxFQUFFaUUsSUFBSSxFQUFFQyxPQUFPO0lBQ2pEQSxVQUFVQSxXQUFXckU7SUFDckIsTUFBTXNFLFVBQVVILEtBQUtJLEtBQUssQ0FBQyxpQkFBaUIsb0JBQW9CO0lBQ2hFLElBQUlELFlBQVksUUFDUnZFLGlCQUFpQnlFLFFBQVEsQ0FBQ0YsT0FBTyxDQUFDLEVBQUUsQ0FBQ0csV0FBVyxLQUN4RDtRQUNJLE1BQU0sSUFBSXRCLE1BQU0sQ0FBQyxLQUFLLEVBQUVnQixLQUFLLDBCQUEwQixDQUFDO0lBQzVEO0lBRUEsT0FBT0UsUUFBUUssU0FBUyxDQUFDdkUsUUFBUWlFLE1BQ2hDUCxJQUFJLENBQUNjLENBQUFBO1FBRUYsT0FBT0MsYUFBYVQsTUFBTVEsSUFBSWIsTUFBTSxFQUNuQ0QsSUFBSSxDQUFDO1lBRUYsT0FBT2M7UUFDWDtJQUNKO0FBQ0o7QUFFQSxTQUFTNUIsVUFBVUgsYUFBYSxFQUFFaUMsTUFBTSxFQUFFaEMsZUFBZSxFQUFFaUMsUUFBUSxFQUFFaEMsTUFBTTtJQUN2RSxNQUFNaUMsU0FBU3hELE1BQU15RCxPQUFPLENBQUNwQyxpQkFBaUJBLGdCQUFnQjtRQUFDQTtLQUFjO0lBQzdFLE1BQU1xQyxhQUFhO1FBQ2Y7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0g7SUFFRCxJQUFJbkMsUUFBUTtRQUNSLElBQUltQyxXQUFXQyxPQUFPLENBQUNwQyxPQUFPcUMsZUFBZSxJQUFJLEdBQUc7WUFDaEQsTUFBTSxJQUFJaEMsTUFBTSxDQUFDLHlCQUF5QixFQUFFTCxPQUFPcUMsZUFBZSxDQUFDLENBQUMsQ0FBQztRQUN6RTtRQUNBLElBQUlyQyxPQUFPc0MsVUFBVSxLQUFLQyxXQUFXO1lBQ2pDdkMsT0FBT3NDLFVBQVUsR0FBRztRQUN4QjtRQUNBLElBQUl0QyxPQUFPd0MsNEJBQTRCLEtBQUtELFdBQVc7WUFDbkR2QyxPQUFPd0MsNEJBQTRCLEdBQUc7UUFDMUM7UUFDQSxJQUFJeEMsT0FBT3lDLHNCQUFzQixLQUFLRixXQUFXO1lBQzdDdkMsT0FBT3lDLHNCQUFzQixHQUFHO1FBQ3BDO0lBQ0o7SUFFQSxNQUFNQyxpQkFBaUIsSUFBSTlGLE9BQU8rRixRQUFRLENBQUNDLFNBQVM7SUFDcEQsTUFBTUMsWUFBWSxJQUFJakcsT0FBT29CLE9BQU8sQ0FBQytELE9BQU8sQ0FBQ1csZ0JBQWdCM0MsaUJBQWlCaUM7SUFDOUUsSUFBSWM7SUFDSixJQUFJOUMsUUFBUTtRQUNSOEMsYUFBYSxJQUFJbEcsT0FBT21HLEtBQUssQ0FBQ0MsbUJBQW1CLENBQzdDTixnQkFDQTlGLE9BQU9tRyxLQUFLLENBQUNFLHlCQUF5QixDQUFDakQsT0FBT3FDLGVBQWUsQ0FBQyxFQUM5RHJDLE9BQU9zQyxVQUFVLEVBQ2pCdEMsT0FBT3dDLDRCQUE0QixFQUNuQ3hDLE9BQU95QyxzQkFBc0I7SUFFckMsT0FDSztRQUNESyxhQUFhLElBQUlsRyxPQUFPbUcsS0FBSyxDQUFDRyxZQUFZLENBQUNSO0lBQy9DO0lBRUEsTUFBTVMsa0JBQWtCLEVBQUU7SUFDMUJsQixPQUFPMUUsT0FBTyxDQUFDd0YsQ0FBQUE7UUFFWCxNQUFNSyxXQUFXTCxNQUFNckQsTUFBTSxDQUFDeUIsSUFBSTtRQUNsQyxNQUFNa0MsV0FBVyxJQUFJQyxZQUFZRixTQUFTckYsTUFBTTtRQUNoRCxNQUFNd0YsYUFBYSxJQUFJQyxZQUFZSDtRQUNuQyxJQUFLLElBQUlJLEtBQUssR0FBR0MsS0FBSyxHQUFHRCxLQUFLTCxTQUFTckYsTUFBTSxFQUFFMEYsTUFBTSxHQUFHLEVBQUVDLEdBQUk7WUFDMURILFVBQVUsQ0FBQ0csR0FBRyxHQUFHTixTQUFTTyxZQUFZLENBQUNGLElBQUk7UUFDL0M7UUFDQSxNQUFNRyxpQkFBaUJoSCxPQUFPaUgsS0FBSyxDQUFDQyxjQUFjLENBQUNDLGVBQWUsQ0FDMURSLFlBQVlSLE1BQU1yRCxNQUFNLENBQUNGLEtBQUssRUFBRXVELE1BQU1yRCxNQUFNLENBQUNDLE1BQU07UUFDM0RrRCxVQUFVbUIsTUFBTSxDQUFDSjtRQUNqQlQsZ0JBQWdCMUYsSUFBSSxDQUFDbUc7SUFDekI7SUFFQSxNQUFNSyxpQkFBaUJwQixVQUFVcUIsWUFBWTtJQUU3QyxJQUFLLElBQUlwRyxJQUFJLEdBQUdBLElBQUltRSxPQUFPbEUsTUFBTSxFQUFFLEVBQUVELEVBQUc7UUFDcEMsTUFBTXNGLFdBQVduQixNQUFNLENBQUNuRSxFQUFFLENBQUM0QixNQUFNLENBQUN5QixJQUFJO1FBQ3RDLE1BQU1nRCxrQkFBa0JyQixXQUFXb0IsWUFBWSxDQUFDZixlQUFlLENBQUNyRixFQUFFLEVBQUVtRztRQUNwRSxNQUFNRyxjQUFjRCxnQkFBZ0JFLGFBQWE7UUFDakQsSUFBSyxJQUFJWixLQUFLLEdBQUdDLEtBQUssR0FBR0QsS0FBS0wsU0FBU3JGLE1BQU0sRUFBRTBGLE1BQU0sR0FBRyxFQUFFQyxHQUFJO1lBQzFETixTQUFTa0IsYUFBYSxDQUFDRixXQUFXLENBQUNWLEdBQUcsRUFBRUQ7UUFDNUM7SUFDSjtBQUNKO0FBRUEsU0FBUzNDLFlBQVlPLElBQUk7SUFDckIsNEJBQTRCO0lBQzVCLE9BQU8sSUFBSWtELFFBQVEsQ0FBQ0MsU0FBU0M7UUFFekIvSCxHQUFHZ0ksUUFBUSxDQUFDckQsTUFBTSxDQUFDc0QsS0FBSzNEO1lBRXBCLElBQUkyRCxLQUFLO2dCQUNMLE9BQU9GLE9BQU9FO1lBQ2xCO1lBQ0EsT0FBT0gsUUFBUXhEO1FBQ25CO0lBQ0o7QUFDSjtBQUVBLFNBQVNjLGFBQWFULElBQUksRUFBRUwsTUFBTTtJQUM5Qiw0QkFBNEI7SUFDNUIsT0FBTyxJQUFJdUQsUUFBUSxDQUFDQyxTQUFTQztRQUV6Qi9ILEdBQUdrSSxTQUFTLENBQUN2RCxNQUFNTCxRQUFRMkQsQ0FBQUE7WUFFdkIsSUFBSUEsS0FBSztnQkFDTCxPQUFPRixPQUFPRTtZQUNsQjtZQUNBLE9BQU9IO1FBQ1g7SUFDSjtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvLi9ub2RlX21vZHVsZXMvZ2lmd3JhcC9zcmMvZ2lmdXRpbC5qcz80MWYxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqIEBuYW1lc3BhY2UgR2lmVXRpbCAqL1xuXG5jb25zdCBmcyA9IHJlcXVpcmUoJ2ZzJyk7XG5jb25zdCBJbWFnZVEgPSByZXF1aXJlKCdpbWFnZS1xJyk7XG5cbmNvbnN0IEJpdG1hcEltYWdlID0gcmVxdWlyZSgnLi9iaXRtYXBpbWFnZScpO1xuY29uc3QgeyBHaWZGcmFtZSB9ID0gcmVxdWlyZSgnLi9naWZmcmFtZScpO1xuY29uc3QgeyBHaWZFcnJvciB9ID0gcmVxdWlyZSgnLi9naWYnKTtcbmNvbnN0IHsgR2lmQ29kZWMgfSA9IHJlcXVpcmUoJy4vZ2lmY29kZWMnKTtcblxuY29uc3QgSU5WQUxJRF9TVUZGSVhFUyA9IFsnLmpwZycsICcuanBlZycsICcucG5nJywgJy5ibXAnXTtcblxuY29uc3QgZGVmYXVsdENvZGVjID0gbmV3IEdpZkNvZGVjKCk7XG5cbi8qKlxuICogY2xvbmVGcmFtZXMoKSBjbG9uZXMgcHJvdmlkZWQgZnJhbWVzLiBJdCdzIGEgdXRpbGl0eSBtZXRob2QgZm9yIGNsb25pbmcgYW4gZW50aXJlIGFycmF5IG9mIGZyYW1lcyBhdCBvbmNlLlxuICogXG4gKiBAZnVuY3Rpb24gY2xvbmVGcmFtZXNcbiAqIEBtZW1iZXJvZiBHaWZVdGlsXG4gKiBAcGFyYW0ge0dpZkZyYW1lW119IGZyYW1lcyBBbiBhcnJheSBvZiBHaWZGcmFtZSBpbnN0YW5jZXMgdG8gY2xvbmVcbiAqIEByZXR1cm4ge0dpZkZyYW1lW119IEFuIGFycmF5IG9mIEdpZkZyYW1lIGNsb25lcyBvZiB0aGUgcHJvdmlkZWQgZnJhbWVzLlxuICovXG5cbmV4cG9ydHMuY2xvbmVGcmFtZXMgPSBmdW5jdGlvbiAoZnJhbWVzKSB7XG4gICAgbGV0IGNsb25lcyA9IFtdO1xuICAgIGZyYW1lcy5mb3JFYWNoKGZyYW1lID0+IHtcblxuICAgICAgICBjbG9uZXMucHVzaChuZXcgR2lmRnJhbWUoZnJhbWUpKTtcbiAgICB9KTtcbiAgICByZXR1cm4gY2xvbmVzO1xufVxuXG4vKipcbiAqIGdldENvbG9ySW5mbygpIGdldHMgaW5mb3JtYXRpb24gYWJvdXQgdGhlIGNvbG9ycyB1c2VkIGluIHRoZSBwcm92aWRlZCBmcmFtZXMuIFRoZSBtZXRob2QgaXMgYWJsZSB0byByZXR1cm4gYW4gYXJyYXkgb2YgYWxsIGNvbG9ycyBmb3VuZCBhY3Jvc3MgYWxsIGZyYW1lcy5cbiAqIFxuICogYG1heEdsb2JhbEluZGV4YCBjb250cm9scyB3aGV0aGVyIHRoZSBjb21wdXRhdGlvbiBzaG9ydC1jaXJjdWl0cyB0byBhdm9pZCBkb2luZyB3b3JrIHRoYXQgdGhlIGNhbGxlciBkb2Vzbid0IG5lZWQuIFRoZSBtZXRob2Qgb25seSByZXR1cm5zIGBjb2xvcnNgIGFuZCBgaW5kZXhDb3VudGAgZm9yIHRoZSBjb2xvcnMgYWNyb3NzIGFsbCBmcmFtZXMgd2hlbiB0aGUgbnVtYmVyIG9mIGluZGV4ZXMgcmVxdWlyZWQgdG8gc3RvcmUgdGhlIGNvbG9ycyBhbmQgdHJhbnNwYXJlbmN5IGluIGEgR0lGICh3aGljaCBpcyB0aGUgdmFsdWUgb2YgYGluZGV4Q291bnRgKSBpcyBsZXNzIHRoYW4gb3IgZXF1YWwgdG8gYG1heEdsb2JhbEluZGV4YC4gU3VjaCBzaG9ydC1jaXJjdWl0aW5nIGlzIHVzZWZ1bCB3aGVuIHRoZSBjYWxsZXIganVzdCBuZWVkcyB0byBkZXRlcm1pbmUgd2hldGhlciBhbnkgZnJhbWUgaW5jbHVkZXMgdHJhbnNwYXJlbmN5LlxuICogXG4gKiBAZnVuY3Rpb24gZ2V0Q29sb3JJbmZvXG4gKiBAbWVtYmVyb2YgR2lmVXRpbFxuICogQHBhcmFtIHtHaWZGcmFtZVtdfSBmcmFtZXMgRnJhbWVzIHRvIGV4YW1pbmUgZm9yIGNvbG9yIGFuZCB0cmFuc3BhcmVuY3kuXG4gKiBAcGFyYW0ge251bWJlcn0gbWF4R2xvYmFsSW5kZXggTWF4aW11bSBudW1iZXIgb2YgY29sb3IgaW5kZXhlcyAoaW5jbHVkaW5nIG9uZSBmb3IgdHJhbnNwYXJlbmN5KSBhbGxvd2VkIGFtb25nIHRoZSByZXR1cm5lZCBjb21waWxhdGlvbiBvZiBjb2xvcnMuIGBjb2xvcnNgIGFuZCBgaW5kZXhDb3VudGAgYXJlIG5vdCByZXR1cm5lZCBpZiB0aGUgbnVtYmVyIG9mIGNvbG9yIGluZGV4ZXMgcmVxdWlyZWQgdG8gYWNjb21tb2RhdGUgIGFsbCBmcmFtZXMgZXhjZWVkcyB0aGlzIG51bWJlci4gUmV0dXJucyBgY29sb3JzYCBhbmQgYGluZGV4Q291bnRgIGJ5IGRlZmF1bHQuXG4gKiBAcmV0dXJucyB7b2JqZWN0fSBPYmplY3QgY29udGFpbmluZyBhdCBsZWFzdCBgcGFsZXR0ZXNgIGFuZCBgdXNlc1RyYW5zcGFyZW5jeWAuIGBwYWxldHRlc2AgaXMgYW4gYXJyYXkgb2YgYWxsIHRoZSBwYWxldHRlcyByZXR1cm5lZCBieSBHaWZGcmFtZSNnZXRQYWxldHRlKCkuIGB1c2VzVHJhbnNwYXJlbmN5YCBpbmRpY2F0ZXMgd2hldGhlciBhdCBsZWFzdCBvbmUgZnJhbWUgdXNlcyB0cmFuc3BhcmVuY3kuIElmIGBtYXhHbG9iYWxJbmRleGAgaXMgbm90IGV4Y2VlZGVkLCB0aGUgb2JqZWN0IGFsc28gY29udGFpbnMgYGNvbG9yc2AsIGFuIGFycmF5IG9mIGFsbCBjb2xvcnMgKFJHQikgZm91bmQgYWNyb3NzIGFsbCBwYWxldHRlcywgc29ydGVkIGJ5IGluY3JlYXNpbmcgdmFsdWUsIGFuZCBgaW5kZXhDb3VudGAgaW5kaWNhdGluZyB0aGUgbnVtYmVyIG9mIGluZGV4ZXMgcmVxdWlyZWQgdG8gc3RvcmUgdGhlIGNvbG9ycyBhbmQgdGhlIHRyYW5zcGFyZW5jeSBpbiBhIEdJRi5cbiAqIEB0aHJvd3Mge0dpZkVycm9yfSBXaGVuIGFueSBmcmFtZSByZXF1aXJlcyBtb3JlIHRoYW4gMjU2IGNvbG9yIGluZGV4ZXMuXG4gKi9cblxuZXhwb3J0cy5nZXRDb2xvckluZm8gPSBmdW5jdGlvbiAoZnJhbWVzLCBtYXhHbG9iYWxJbmRleCkge1xuICAgIGxldCB1c2VzVHJhbnNwYXJlbmN5ID0gZmFsc2U7XG4gICAgY29uc3QgcGFsZXR0ZXMgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGZyYW1lcy5sZW5ndGg7ICsraSkge1xuICAgICAgICBsZXQgcGFsZXR0ZSA9IGZyYW1lc1tpXS5nZXRQYWxldHRlKCk7XG4gICAgICAgIGlmIChwYWxldHRlLnVzZXNUcmFuc3BhcmVuY3kpIHtcbiAgICAgICAgICAgIHVzZXNUcmFuc3BhcmVuY3kgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwYWxldHRlLmluZGV4Q291bnQgPiAyNTYpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBHaWZFcnJvcihgRnJhbWUgJHtpfSB1c2VzIG1vcmUgdGhhbiAyNTYgY29sb3IgaW5kZXhlc2ApO1xuICAgICAgICB9XG4gICAgICAgIHBhbGV0dGVzLnB1c2gocGFsZXR0ZSk7XG4gICAgfVxuICAgIGlmIChtYXhHbG9iYWxJbmRleCA9PT0gMCkge1xuICAgICAgICByZXR1cm4geyB1c2VzVHJhbnNwYXJlbmN5LCBwYWxldHRlcyB9O1xuICAgIH1cblxuICAgIGNvbnN0IGdsb2JhbENvbG9yU2V0ID0gbmV3IFNldCgpO1xuICAgIHBhbGV0dGVzLmZvckVhY2gocGFsZXR0ZSA9PiB7XG5cbiAgICAgICAgcGFsZXR0ZS5jb2xvcnMuZm9yRWFjaChjb2xvciA9PiB7XG5cbiAgICAgICAgICAgIGdsb2JhbENvbG9yU2V0LmFkZChjb2xvcik7XG4gICAgICAgIH0pO1xuICAgIH0pO1xuICAgIGxldCBpbmRleENvdW50ID0gZ2xvYmFsQ29sb3JTZXQuc2l6ZTtcbiAgICBpZiAodXNlc1RyYW5zcGFyZW5jeSkge1xuICAgICAgICAvLyBvZGQgdGhhdCBHSUYgcmVxdWlyZXMgYSBjb2xvciB0YWJsZSBlbnRyeSBhdCB0cmFuc3BhcmVudCBpbmRleFxuICAgICAgICArK2luZGV4Q291bnQ7XG4gICAgfVxuICAgIGlmIChtYXhHbG9iYWxJbmRleCAmJiBpbmRleENvdW50ID4gbWF4R2xvYmFsSW5kZXgpIHtcbiAgICAgICAgcmV0dXJuIHsgdXNlc1RyYW5zcGFyZW5jeSwgcGFsZXR0ZXMgfTtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgY29sb3JzID0gbmV3IEFycmF5KGdsb2JhbENvbG9yU2V0LnNpemUpO1xuICAgIGNvbnN0IGl0ZXIgPSBnbG9iYWxDb2xvclNldC52YWx1ZXMoKTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNvbG9ycy5sZW5ndGg7ICsraSkge1xuICAgICAgICBjb2xvcnNbaV0gPSBpdGVyLm5leHQoKS52YWx1ZTtcbiAgICB9XG4gICAgY29sb3JzLnNvcnQoKGEsIGIpID0+IChhIC0gYikpO1xuICAgIHJldHVybiB7IGNvbG9ycywgaW5kZXhDb3VudCwgdXNlc1RyYW5zcGFyZW5jeSwgcGFsZXR0ZXMgfTtcbn07XG5cbi8qKlxuICogY29weUFzSmltcCgpIHJldHVybnMgYSBKaW1wIHRoYXQgY29udGFpbnMgYSBjb3B5IG9mIHRoZSBwcm92aWRlZCBiaXRtYXAgaW1hZ2UgKHdoaWNoIG1heSBiZSBlaXRoZXIgYSBCaXRtYXBJbWFnZSBvciBhIEdpZkZyYW1lKS4gTW9kaWZ5aW5nIHRoZSBKaW1wIGRvZXMgbm90IGFmZmVjdCB0aGUgcHJvdmlkZWQgYml0bWFwIGltYWdlLiBUaGlzIG1ldGhvZCBzZXJ2ZXMgYXMgYSBtYWNybyBmb3Igc2ltcGxpZnlpbmcgd29ya2luZyB3aXRoIEppbXAuXG4gKlxuICogQGZ1bmN0aW9uIGNvcHlBc0ppbXBcbiAqIEBtZW1iZXJvZiBHaWZVdGlsXG4gKiBAcGFyYW0ge29iamVjdH0gUmVmZXJlbmNlIHRvIHRoZSBKaW1wIHBhY2thZ2UsIGtlZXBpbmcgdGhpcyBsaWJyYXJ5IGZyb20gYmVpbmcgZGVwZW5kZW50IG9uIEppbXAuXG4gKiBAcGFyYW0ge2JpdG1hcEltYWdlVG9Db3B5fSBJbnN0YW5jZSBvZiBCaXRtYXBJbWFnZSAobWF5IGJlIGEgR2lmVXRpbCkgd2l0aCB3aGljaCB0byBzb3VyY2UgdGhlIEppbXAuXG4gKiBAcmV0dXJuIHtvYmplY3R9IEFuIG5ldyBpbnN0YW5jZSBvZiBKaW1wIGNvbnRhaW5pbmcgYSBjb3B5IG9mIHRoZSBpbWFnZSBpbiBiaXRtYXBJbWFnZVRvQ29weS5cbiAqL1xuIFxuZXhwb3J0cy5jb3B5QXNKaW1wID0gZnVuY3Rpb24gKGppbXAsIGJpdG1hcEltYWdlVG9Db3B5KSB7XG4gICAgcmV0dXJuIGV4cG9ydHMuc2hhcmVBc0ppbXAoamltcCwgbmV3IEJpdG1hcEltYWdlKGJpdG1hcEltYWdlVG9Db3B5KSk7XG59O1xuXG4vKipcbiAqIGdldE1heERpbWVuc2lvbnMoKSByZXR1cm5zIHRoZSBwaXhlbCB3aWR0aCBhbmQgaGVpZ2h0IHJlcXVpcmVkIHRvIGFjY29tbW9kYXRlIGFsbCBvZiB0aGUgcHJvdmlkZWQgZnJhbWVzLCBhY2NvcmRpbmcgdG8gdGhlIG9mZnNldHMgYW5kIGRpbWVuc2lvbnMgb2YgZWFjaCBmcmFtZS5cbiAqIFxuICogQGZ1bmN0aW9uIGdldE1heERpbWVuc2lvbnNcbiAqIEBtZW1iZXJvZiBHaWZVdGlsXG4gKiBAcGFyYW0ge0dpZkZyYW1lW119IGZyYW1lcyBGcmFtZXMgdG8gbWVhc3VyZSBmb3IgdGhlaXIgYWdncmVnYXRlIG1heGltdW0gZGltZW5zaW9ucy5cbiAqIEByZXR1cm4ge29iamVjdH0gQW4gb2JqZWN0IG9mIHRoZSBmb3JtIHttYXhXaWR0aCwgbWF4SGVpZ2h0fSBpbmRpY2F0aW5nIHRoZSBtYXhpbXVtIHdpZHRoIGFuZCBoZWlnaHQgcmVxdWlyZWQgdG8gYWNjb21tb2RhdGUgYWxsIGZyYW1lcy5cbiAqL1xuXG5leHBvcnRzLmdldE1heERpbWVuc2lvbnMgPSBmdW5jdGlvbiAoZnJhbWVzKSB7XG4gICAgbGV0IG1heFdpZHRoID0gMCwgbWF4SGVpZ2h0ID0gMDtcbiAgICBmcmFtZXMuZm9yRWFjaChmcmFtZSA9PiB7XG4gICAgICAgIGNvbnN0IHdpZHRoID0gZnJhbWUueE9mZnNldCArIGZyYW1lLmJpdG1hcC53aWR0aDtcbiAgICAgICAgaWYgKHdpZHRoID4gbWF4V2lkdGgpIHtcbiAgICAgICAgICAgIG1heFdpZHRoID0gd2lkdGg7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaGVpZ2h0ID0gZnJhbWUueU9mZnNldCArIGZyYW1lLmJpdG1hcC5oZWlnaHQ7XG4gICAgICAgIGlmIChoZWlnaHQgPiBtYXhIZWlnaHQpIHtcbiAgICAgICAgICAgIG1heEhlaWdodCA9IGhlaWdodDtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiB7IG1heFdpZHRoLCBtYXhIZWlnaHQgfTtcbn07XG5cbi8qKlxuICogUXVhbnRpemVzIGNvbG9ycyBzbyB0aGF0IHRoZXJlIGFyZSBhdCBtb3N0IGEgZ2l2ZW4gbnVtYmVyIG9mIGNvbG9yIGluZGV4ZXMgKGluY2x1ZGluZyB0cmFuc3BhcmVuY3kpIGFjcm9zcyBhbGwgcHJvdmlkZWQgaW1hZ2VzLiBVc2VzIGFuIGFsZ29yaXRobSBieSBBbnRob255IERla2tlci5cbiAqIFxuICogVGhlIG1ldGhvZCB0cmVhdHMgZGlmZmVyZW50IFJHQkEgY29tYmluYXRpb25zIGFzIGRpZmZlcmVudCBjb2xvcnMsIHNvIGlmIHRoZSBmcmFtZSBoYXMgbXVsdGlwbGUgYWxwaGEgdmFsdWVzIG9yIG11bHRpcGxlIFJHQiB2YWx1ZXMgZm9yIGFuIGFscGhhIHZhbHVlLCB0aGUgY2FsbGVyIG1heSBmaXJzdCB3YW50IHRvIG5vcm1hbGl6ZSB0aGVtIGJ5IGNvbnZlcnRpbmcgYWxsIHRyYW5zcGFyZW50IHBpeGVscyB0byB0aGUgc2FtZSBSR0JBIHZhbHVlcy5cbiAqIFxuICogVGhlIG1ldGhvZCBtYXkgaW5jcmVhc2UgdGhlIG51bWJlciBvZiBjb2xvcnMgaWYgdGhlcmUgYXJlIGZld2VyIHRoYW4gdGhlIHByb3ZpZGVkIG1heGltdW0uXG4gKiBcbiAqIEBmdW5jdGlvbiBxdWFudGl6ZURla2tlclxuICogQG1lbWJlcm9mIEdpZlV0aWxcbiAqIEBwYXJhbSB7Qml0bWFwSW1hZ2V8Qml0bWFwSW1hZ2VbXX0gaW1hZ2VPckltYWdlcyBJbWFnZSBvciBhcnJheSBvZiBpbWFnZXMgKHN1Y2ggYXMgR2lmRnJhbWUgaW5zdGFuY2VzKSB0byBiZSBjb2xvci1xdWFudGl6ZWQuIFF1YW50aXppbmcgYWNyb3NzIG11bHRpcGxlIGltYWdlcyBlbnN1cmVzIGNvbG9yIGNvbnNpc3RlbmN5IGZyb20gZnJhbWUgdG8gZnJhbWUuXG4gKiBAcGFyYW0ge251bWJlcn0gbWF4Q29sb3JJbmRleGVzIFRoZSBtYXhpbXVtIG51bWJlciBvZiBjb2xvciBpbmRleGVzIHRoYXQgd2lsbCBleGlzdCBpbiB0aGUgcGFsZXR0ZSBhZnRlciBjb21wbGV0aW5nIHF1YW50aXphdGlvbi4gRGVmYXVsdHMgdG8gMjU2LlxuICogQHBhcmFtIHtvYmplY3R9IGRpdGhlciAob3B0aW9uYWwpIEFuIG9iamVjdCBjb25maWd1cmluZyB0aGUgZGl0aGVyaW5nIHRvIGFwcGx5LiBUaGUgcHJvcGVydGllcyBhcmUgYXMgZm9sbG93aW5ncywgaW1wb3J0ZWQgZnJvbSB0aGUgW2BpbWFnZS1xYCBwYWNrYWdlXShodHRwczovL2dpdGh1Yi5jb20vaWJlemtyb3ZueWkvaW1hZ2UtcXVhbnRpemF0aW9uKSB3aXRob3V0IGV4cGxhbmF0aW9uOiB7IGBkaXRoZXJBbGdvcml0aG1gOiBPbmUgb2YgJ0Zsb3lkU3RlaW5iZXJnJywgJ0ZhbHNlRmxveWRTdGVpbmJlcmcnLCAnU3R1Y2tpJywgJ0F0a2luc29uJywgJ0phcnZpcycsICdCdXJrZXMnLCAnU2llcnJhJywgJ1R3b1NpZXJyYScsICdTaWVycmFMaXRlJzsgYG1pbmltdW1Db2xvckRpc3RhbmNlVG9EaXRoZXJgOiAob3B0aW9uYWwpIEEgbnVtYmVyIGRlZmF1bHRpbmcgdG8gMDsgYHNlcnBlbnRpbmVgOiAob3B0aW9uYWwpIEEgYm9vbGVhbiBkZWZhdWx0aW5nIHRvIHRydWU7IGBjYWxjdWxhdGVFcnJvckxpa2VHSU1QYDogKG9wdGlvbmFsKSBBIGJvb2xlYW4gZGVmYXVsdGluZyB0byBmYWxzZS4gfVxuICovXG5cbmV4cG9ydHMucXVhbnRpemVEZWtrZXIgPSBmdW5jdGlvbiAoaW1hZ2VPckltYWdlcywgbWF4Q29sb3JJbmRleGVzLCBkaXRoZXIpIHtcbiAgICBtYXhDb2xvckluZGV4ZXMgPSBtYXhDb2xvckluZGV4ZXMgfHwgMjU2O1xuICAgIF9xdWFudGl6ZShpbWFnZU9ySW1hZ2VzLCAnTmV1UXVhbnRGbG9hdCcsIG1heENvbG9ySW5kZXhlcywgMCwgZGl0aGVyKTtcbn1cblxuLyoqXG4gKiBRdWFudGl6ZXMgY29sb3JzIHNvIHRoYXQgdGhlcmUgYXJlIGF0IG1vc3QgYSBnaXZlbiBudW1iZXIgb2YgY29sb3IgaW5kZXhlcyAoaW5jbHVkaW5nIHRyYW5zcGFyZW5jeSkgYWNyb3NzIGFsbCBwcm92aWRlZCBpbWFnZXMuIFVzZXMgYW4gYWxnb3JpdGhtIGJ5IExlb24gU29yb2tpbi4gVGhpcyBxdWFudGl6YXRpb24gbWV0aG9kIGRpZmZlcnMgZnJvbSB0aGUgb3RoZXIgdHdvIGJ5IGxpa2VseSBuZXZlciBpbmNyZWFzaW5nIHRoZSBudW1iZXIgb2YgY29sb3JzLCBzaG91bGQgdGhlcmUgYmUgZmV3ZXIgdGhhbiB0aGUgcHJvdmlkZWQgbWF4aW11bS5cbiAqIFxuICogVGhlIG1ldGhvZCB0cmVhdHMgZGlmZmVyZW50IFJHQkEgY29tYmluYXRpb25zIGFzIGRpZmZlcmVudCBjb2xvcnMsIHNvIGlmIHRoZSBmcmFtZSBoYXMgbXVsdGlwbGUgYWxwaGEgdmFsdWVzIG9yIG11bHRpcGxlIFJHQiB2YWx1ZXMgZm9yIGFuIGFscGhhIHZhbHVlLCB0aGUgY2FsbGVyIG1heSBmaXJzdCB3YW50IHRvIG5vcm1hbGl6ZSB0aGVtIGJ5IGNvbnZlcnRpbmcgYWxsIHRyYW5zcGFyZW50IHBpeGVscyB0byB0aGUgc2FtZSBSR0JBIHZhbHVlcy5cbiAqIFxuICogQGZ1bmN0aW9uIHF1YW50aXplU29yb2tpblxuICogQG1lbWJlcm9mIEdpZlV0aWxcbiAqIEBwYXJhbSB7Qml0bWFwSW1hZ2V8Qml0bWFwSW1hZ2VbXX0gaW1hZ2VPckltYWdlcyBJbWFnZSBvciBhcnJheSBvZiBpbWFnZXMgKHN1Y2ggYXMgR2lmRnJhbWUgaW5zdGFuY2VzKSB0byBiZSBjb2xvci1xdWFudGl6ZWQuIFF1YW50aXppbmcgYWNyb3NzIG11bHRpcGxlIGltYWdlcyBlbnN1cmVzIGNvbG9yIGNvbnNpc3RlbmN5IGZyb20gZnJhbWUgdG8gZnJhbWUuXG4gKiBAcGFyYW0ge251bWJlcn0gbWF4Q29sb3JJbmRleGVzIFRoZSBtYXhpbXVtIG51bWJlciBvZiBjb2xvciBpbmRleGVzIHRoYXQgd2lsbCBleGlzdCBpbiB0aGUgcGFsZXR0ZSBhZnRlciBjb21wbGV0aW5nIHF1YW50aXphdGlvbi4gRGVmYXVsdHMgdG8gMjU2LlxuICogQHBhcmFtIHtzdHJpbmd9IGhpc3RvZ3JhbSAob3B0aW9uYWwpIEhpc3RvZ3JhbSBtZXRob2Q6ICd0b3AtcG9wJyBmb3IgZ2xvYmFsIHRvcC1wb3B1bGF0aW9uLCAnbWluLXBvcCcgZm9yIG1pbmltdW0tcG9wdWxhdGlvbiB0aHJlc2hob2xkIHdpdGhpbiBzdWJyZWdpb25zLiBEZWZhdWx0cyB0byAnbWluLXBvcCcuXG4gKiBAcGFyYW0ge29iamVjdH0gZGl0aGVyIChvcHRpb25hbCkgQW4gb2JqZWN0IGNvbmZpZ3VyaW5nIHRoZSBkaXRoZXJpbmcgdG8gYXBwbHksIGFzIGV4cGxhaW5lZCBmb3IgYHF1YW50aXplRGVra2VyKClgLlxuICovXG5cbmV4cG9ydHMucXVhbnRpemVTb3Jva2luID0gZnVuY3Rpb24gKGltYWdlT3JJbWFnZXMsIG1heENvbG9ySW5kZXhlcywgaGlzdG9ncmFtLCBkaXRoZXIpIHtcbiAgICBtYXhDb2xvckluZGV4ZXMgPSBtYXhDb2xvckluZGV4ZXMgfHwgMjU2O1xuICAgIGhpc3RvZ3JhbSA9IGhpc3RvZ3JhbSB8fCAnbWluLXBvcCc7XG4gICAgbGV0IGhpc3RvZ3JhbUlEO1xuICAgIHN3aXRjaCAoaGlzdG9ncmFtKSB7XG4gICAgICAgIGNhc2UgJ21pbi1wb3AnOlxuICAgICAgICBoaXN0b2dyYW1JRCA9IDI7XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICAgIGNhc2UgJ3RvcC1wb3AnOlxuICAgICAgICBoaXN0b2dyYW1JRCA9IDE7XG4gICAgICAgIGJyZWFrXG5cbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIHF1YW50aXplU29yb2tpbiBoaXN0b2dyYW0gJyR7aGlzdG9ncmFtfSdgKTtcbiAgICB9XG4gICAgX3F1YW50aXplKGltYWdlT3JJbWFnZXMsICdSR0JRdWFudCcsIG1heENvbG9ySW5kZXhlcywgaGlzdG9ncmFtSUQsIGRpdGhlcik7XG59XG5cbi8qKlxuICogUXVhbnRpemVzIGNvbG9ycyBzbyB0aGF0IHRoZXJlIGFyZSBhdCBtb3N0IGEgZ2l2ZW4gbnVtYmVyIG9mIGNvbG9yIGluZGV4ZXMgKGluY2x1ZGluZyB0cmFuc3BhcmVuY3kpIGFjcm9zcyBhbGwgcHJvdmlkZWQgaW1hZ2VzLiBVc2VzIGFuIGFsZ29yaXRobSBieSBYaWFvbGluIFd1LlxuICogXG4gKiBUaGUgbWV0aG9kIHRyZWF0cyBkaWZmZXJlbnQgUkdCQSBjb21iaW5hdGlvbnMgYXMgZGlmZmVyZW50IGNvbG9ycywgc28gaWYgdGhlIGZyYW1lIGhhcyBtdWx0aXBsZSBhbHBoYSB2YWx1ZXMgb3IgbXVsdGlwbGUgUkdCIHZhbHVlcyBmb3IgYW4gYWxwaGEgdmFsdWUsIHRoZSBjYWxsZXIgbWF5IGZpcnN0IHdhbnQgdG8gbm9ybWFsaXplIHRoZW0gYnkgY29udmVydGluZyBhbGwgdHJhbnNwYXJlbnQgcGl4ZWxzIHRvIHRoZSBzYW1lIFJHQkEgdmFsdWVzLlxuICogXG4gKiBUaGUgbWV0aG9kIG1heSBpbmNyZWFzZSB0aGUgbnVtYmVyIG9mIGNvbG9ycyBpZiB0aGVyZSBhcmUgZmV3ZXIgdGhhbiB0aGUgcHJvdmlkZWQgbWF4aW11bS5cbiAqIFxuICogQGZ1bmN0aW9uIHF1YW50aXplV3VcbiAqIEBtZW1iZXJvZiBHaWZVdGlsXG4gKiBAcGFyYW0ge0JpdG1hcEltYWdlfEJpdG1hcEltYWdlW119IGltYWdlT3JJbWFnZXMgSW1hZ2Ugb3IgYXJyYXkgb2YgaW1hZ2VzIChzdWNoIGFzIEdpZkZyYW1lIGluc3RhbmNlcykgdG8gYmUgY29sb3ItcXVhbnRpemVkLiBRdWFudGl6aW5nIGFjcm9zcyBtdWx0aXBsZSBpbWFnZXMgZW5zdXJlcyBjb2xvciBjb25zaXN0ZW5jeSBmcm9tIGZyYW1lIHRvIGZyYW1lLlxuICogQHBhcmFtIHtudW1iZXJ9IG1heENvbG9ySW5kZXhlcyBUaGUgbWF4aW11bSBudW1iZXIgb2YgY29sb3IgaW5kZXhlcyB0aGF0IHdpbGwgZXhpc3QgaW4gdGhlIHBhbGV0dGUgYWZ0ZXIgY29tcGxldGluZyBxdWFudGl6YXRpb24uIERlZmF1bHRzIHRvIDI1Ni5cbiAqIEBwYXJhbSB7bnVtYmVyfSBzaWduaWZpY2FudEJpdHMgKG9wdGlvbmFsKSBUaGlzIGlzIHRoZSBudW1iZXIgb2Ygc2lnbmlmaWNhbnQgaGlnaCBiaXRzIGluIGVhY2ggUkdCIGNvbG9yIGNoYW5uZWwuIFRha2VzIGludGVnZXIgdmFsdWVzIGZyb20gMSB0aHJvdWdoIDguIEhpZ2hlciB2YWx1ZXMgY29ycmVzcG9uZCB0byBoaWdoZXIgcXVhbGl0eS4gRGVmYXVsdHMgdG8gNS5cbiAqIEBwYXJhbSB7b2JqZWN0fSBkaXRoZXIgKG9wdGlvbmFsKSBBbiBvYmplY3QgY29uZmlndXJpbmcgdGhlIGRpdGhlcmluZyB0byBhcHBseSwgYXMgZXhwbGFpbmVkIGZvciBgcXVhbnRpemVEZWtrZXIoKWAuXG4gKi9cblxuZXhwb3J0cy5xdWFudGl6ZVd1ID0gZnVuY3Rpb24gKGltYWdlT3JJbWFnZXMsIG1heENvbG9ySW5kZXhlcywgc2lnbmlmaWNhbnRCaXRzLCBkaXRoZXIpIHtcbiAgICBtYXhDb2xvckluZGV4ZXMgPSBtYXhDb2xvckluZGV4ZXMgfHwgMjU2O1xuICAgIHNpZ25pZmljYW50Qml0cyA9IHNpZ25pZmljYW50Qml0cyB8fCA1O1xuICAgIGlmIChzaWduaWZpY2FudEJpdHMgPCAxIHx8IHNpZ25pZmljYW50Qml0cyA+IDgpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBxdWFudGl6YXRpb24gcXVhbGl0eVwiKTtcbiAgICB9XG4gICAgX3F1YW50aXplKGltYWdlT3JJbWFnZXMsICdXdVF1YW50JywgbWF4Q29sb3JJbmRleGVzLCBzaWduaWZpY2FudEJpdHMsIGRpdGhlcik7XG59XG5cbi8qKlxuICogcmVhZCgpIGRlY29kZXMgYW4gZW5jb2RlZCBHSUYsIHdoZXRoZXIgcHJvdmlkZWQgYXMgYSBmaWxlbmFtZSBvciBhcyBhIGJ5dGUgYnVmZmVyLlxuICogXG4gKiBAZnVuY3Rpb24gcmVhZFxuICogQG1lbWJlcm9mIEdpZlV0aWxcbiAqIEBwYXJhbSB7c3RyaW5nfEJ1ZmZlcn0gc291cmNlIFNvdXJjZSB0byBkZWNvZGUuIFdoZW4gYSBzdHJpbmcsIGl0J3MgdGhlIEdJRiBmaWxlbmFtZSB0byBsb2FkIGFuZCBwYXJzZS4gV2hlbiBhIEJ1ZmZlciwgaXQncyBhbiBlbmNvZGVkIEdJRiB0byBwYXJzZS5cbiAqIEBwYXJhbSB7b2JqZWN0fSBkZWNvZGVyIEFuIG9wdGlvbmFsIEdJRiBkZWNvZGVyIG9iamVjdCBpbXBsZW1lbnRpbmcgdGhlIGBkZWNvZGVgIG1ldGhvZCBvZiBjbGFzcyBHaWZDb2RlYy4gV2hlbiBwcm92aWRlZCwgdGhlIG1ldGhvZCBkZWNvZGVzIHRoZSBHSUYgdXNpbmcgdGhpcyBkZWNvZGVyLiBXaGVuIG5vdCBwcm92aWRlZCwgdGhlIG1ldGhvZCB1c2VzIEdpZkNvZGVjLlxuICogQHJldHVybiB7UHJvbWlzZX0gQSBQcm9taXNlIHRoYXQgcmVzb2x2ZXMgdG8gYW4gaW5zdGFuY2Ugb2YgdGhlIEdpZiBjbGFzcywgcmVwcmVzZW50aW5nIHRoZSBkZWNvZGVkIEdJRi5cbiAqL1xuXG5leHBvcnRzLnJlYWQgPSBmdW5jdGlvbiAoc291cmNlLCBkZWNvZGVyKSB7XG4gICAgZGVjb2RlciA9IGRlY29kZXIgfHwgZGVmYXVsdENvZGVjO1xuICAgIGlmIChCdWZmZXIuaXNCdWZmZXIoc291cmNlKSkge1xuICAgICAgICByZXR1cm4gZGVjb2Rlci5kZWNvZGVHaWYoc291cmNlKTtcbiAgICB9XG4gICAgcmV0dXJuIF9yZWFkQmluYXJ5KHNvdXJjZSlcbiAgICAudGhlbihidWZmZXIgPT4ge1xuXG4gICAgICAgIHJldHVybiBkZWNvZGVyLmRlY29kZUdpZihidWZmZXIpO1xuICAgIH0pO1xufTtcblxuLyoqXG4gKiBzaGFyZUFzSmltcCgpIHJldHVybnMgYSBKaW1wIHRoYXQgc2hhcmVzIGEgYml0bWFwIHdpdGggdGhlIHByb3ZpZGVkIGJpdG1hcCBpbWFnZSAod2hpY2ggbWF5IGJlIGVpdGhlciBhIEJpdG1hcEltYWdlIG9yIGEgR2lmRnJhbWUpLiBNb2RpZnlpbmcgdGhlIGltYWdlIGluIGVpdGhlciB0aGUgSmltcCBvciB0aGUgQml0bWFwSW1hZ2UgYWZmZWN0cyB0aGUgb3RoZXIgb2JqZWN0cy4gVGhpcyBtZXRob2Qgc2VydmVzIGFzIGEgbWFjcm8gZm9yIHNpbXBsaWZ5aW5nIHdvcmtpbmcgd2l0aCBKaW1wLlxuICpcbiAqIEBmdW5jdGlvbiBzaGFyZUFzSmltcFxuICogQG1lbWJlcm9mIEdpZlV0aWxcbiAqIEBwYXJhbSB7b2JqZWN0fSBSZWZlcmVuY2UgdG8gdGhlIEppbXAgcGFja2FnZSwga2VlcGluZyB0aGlzIGxpYnJhcnkgZnJvbSBiZWluZyBkZXBlbmRlbnQgb24gSmltcC5cbiAqIEBwYXJhbSB7Yml0bWFwSW1hZ2VUb1NoYXJlfSBJbnN0YW5jZSBvZiBCaXRtYXBJbWFnZSAobWF5IGJlIGEgR2lmVXRpbCkgd2l0aCB3aGljaCB0byBzb3VyY2UgdGhlIEppbXAuXG4gKiBAcmV0dXJuIHtvYmplY3R9IEFuIG5ldyBpbnN0YW5jZSBvZiBKaW1wIHRoYXQgc2hhcmVzIHRoZSBpbWFnZSBpbiBiaXRtYXBJbWFnZVRvU2hhcmUuXG4gKi9cbiBcbmV4cG9ydHMuc2hhcmVBc0ppbXAgPSBmdW5jdGlvbiAoamltcCwgYml0bWFwSW1hZ2VUb1NoYXJlKSB7XG4gICAgY29uc3QgamltcEltYWdlID0gbmV3IGppbXAoYml0bWFwSW1hZ2VUb1NoYXJlLmJpdG1hcC53aWR0aCxcbiAgICAgICAgICAgIGJpdG1hcEltYWdlVG9TaGFyZS5iaXRtYXAuaGVpZ2h0LCAwKTtcbiAgICBqaW1wSW1hZ2UuYml0bWFwLmRhdGEgPSBiaXRtYXBJbWFnZVRvU2hhcmUuYml0bWFwLmRhdGE7XG4gICAgcmV0dXJuIGppbXBJbWFnZTtcbn07XG5cbi8qKlxuICogd3JpdGUoKSBlbmNvZGVzIGEgR0lGIGFuZCBzYXZlcyBpdCBhcyBhIGZpbGUuXG4gKiBcbiAqIEBmdW5jdGlvbiB3cml0ZVxuICogQG1lbWJlcm9mIEdpZlV0aWxcbiAqIEBwYXJhbSB7c3RyaW5nfSBwYXRoIEZpbGVuYW1lIHRvIHdyaXRlIEdJRiBvdXQgYXMuIFdpbGwgb3ZlcndyaXRlIGFuIGV4aXN0aW5nIGZpbGUuXG4gKiBAcGFyYW0ge0dpZkZyYW1lW119IGZyYW1lcyBBcnJheSBvZiBmcmFtZXMgdG8gYmUgd3JpdHRlbiBpbnRvIEdJRi5cbiAqIEBwYXJhbSB7b2JqZWN0fSBzcGVjIEFuIG9wdGlvbmFsIG9iamVjdCB0aGF0IG1heSBwcm92aWRlIHZhbHVlcyBmb3IgYGxvb3BzYCBhbmQgYGNvbG9yU2NvcGVgLCBhcyBkZWZpbmVkIGZvciB0aGUgR2lmIGNsYXNzLiBIb3dldmVyLCBgY29sb3JTcGFjZWAgbWF5IGFsc28gdGFrZSB0aGUgdmFsdWUgR2lmLkdsb2JhbENvbG9yc1ByZWZlcnJlZCAoPT0gMCkgdG8gaW5kaWNhdGUgdGhhdCB0aGUgZW5jb2RlciBzaG91bGQgYXR0ZW1wdCB0byBjcmVhdGUgb25seSBhIGdsb2JhbCBjb2xvciB0YWJsZS4gYGxvb3BgIGRlZmF1bHRzIHRvIDAsIGxvb3BpbmcgaW5kZWZpbml0ZWx5LCBhbmQgYGNvbG9yU2NvcGVgIGRlZmF1bHRzIHRvIEdpZi5HbG9iYWxDb2xvcnNQcmVmZXJyZWQuXG4gKiBAcGFyYW0ge29iamVjdH0gZW5jb2RlciBBbiBvcHRpb25hbCBHSUYgZW5jb2RlciBvYmplY3QgaW1wbGVtZW50aW5nIHRoZSBgZW5jb2RlYCBtZXRob2Qgb2YgY2xhc3MgR2lmQ29kZWMuIFdoZW4gcHJvdmlkZWQsIHRoZSBtZXRob2QgZW5jb2RlcyB0aGUgR0lGIHVzaW5nIHRoaXMgZW5jb2Rlci4gV2hlbiBub3QgcHJvdmlkZWQsIHRoZSBtZXRob2QgdXNlcyBHaWZDb2RlYy5cbiAqIEByZXR1cm4ge1Byb21pc2V9IEEgUHJvbWlzZSB0aGF0IHJlc29sdmVzIHRvIGFuIGluc3RhbmNlIG9mIHRoZSBHaWYgY2xhc3MsIHJlcHJlc2VudGluZyB0aGUgZW5jb2RlZCBHSUYuXG4gKi9cblxuZXhwb3J0cy53cml0ZSA9IGZ1bmN0aW9uIChwYXRoLCBmcmFtZXMsIHNwZWMsIGVuY29kZXIpIHtcbiAgICBlbmNvZGVyID0gZW5jb2RlciB8fCBkZWZhdWx0Q29kZWM7XG4gICAgY29uc3QgbWF0Y2hlcyA9IHBhdGgubWF0Y2goL1xcLlthLXpBLVpdKyQvKTsgLy8gcHJldmVudCBhY2NpZGVudHNcbiAgICBpZiAobWF0Y2hlcyAhPT0gbnVsbCAmJlxuICAgICAgICAgICAgSU5WQUxJRF9TVUZGSVhFUy5pbmNsdWRlcyhtYXRjaGVzWzBdLnRvTG93ZXJDYXNlKCkpKVxuICAgIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBHSUYgJyR7cGF0aH0nIGhhcyBhbiB1bmV4cGVjdGVkIHN1ZmZpeGApO1xuICAgIH1cblxuICAgIHJldHVybiBlbmNvZGVyLmVuY29kZUdpZihmcmFtZXMsIHNwZWMpXG4gICAgLnRoZW4oZ2lmID0+IHtcblxuICAgICAgICByZXR1cm4gX3dyaXRlQmluYXJ5KHBhdGgsIGdpZi5idWZmZXIpXG4gICAgICAgIC50aGVuKCgpID0+IHtcblxuICAgICAgICAgICAgcmV0dXJuIGdpZjtcbiAgICAgICAgfSk7XG4gICAgfSk7XG59O1xuXG5mdW5jdGlvbiBfcXVhbnRpemUoaW1hZ2VPckltYWdlcywgbWV0aG9kLCBtYXhDb2xvckluZGV4ZXMsIG1vZGlmaWVyLCBkaXRoZXIpIHtcbiAgICBjb25zdCBpbWFnZXMgPSBBcnJheS5pc0FycmF5KGltYWdlT3JJbWFnZXMpID8gaW1hZ2VPckltYWdlcyA6IFtpbWFnZU9ySW1hZ2VzXTtcbiAgICBjb25zdCBkaXRoZXJBbGdzID0gW1xuICAgICAgICAnRmxveWRTdGVpbmJlcmcnLFxuICAgICAgICAnRmFsc2VGbG95ZFN0ZWluYmVyZycsXG4gICAgICAgICdTdHVja2knLFxuICAgICAgICAnQXRraW5zb24nLFxuICAgICAgICAnSmFydmlzJyxcbiAgICAgICAgJ0J1cmtlcycsXG4gICAgICAgICdTaWVycmEnLFxuICAgICAgICAnVHdvU2llcnJhJyxcbiAgICAgICAgJ1NpZXJyYUxpdGUnXG4gICAgXTtcblxuICAgIGlmIChkaXRoZXIpIHtcbiAgICAgICAgaWYgKGRpdGhlckFsZ3MuaW5kZXhPZihkaXRoZXIuZGl0aGVyQWxnb3JpdGhtKSA8IDApIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW52YWxpZCBkaXRoZXJBbGdvcml0aG0gJyR7ZGl0aGVyLmRpdGhlckFsZ29yaXRobX0nYCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGRpdGhlci5zZXJwZW50aW5lID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGRpdGhlci5zZXJwZW50aW5lID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGl0aGVyLm1pbmltdW1Db2xvckRpc3RhbmNlVG9EaXRoZXIgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgZGl0aGVyLm1pbmltdW1Db2xvckRpc3RhbmNlVG9EaXRoZXIgPSAwO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkaXRoZXIuY2FsY3VsYXRlRXJyb3JMaWtlR0lNUCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBkaXRoZXIuY2FsY3VsYXRlRXJyb3JMaWtlR0lNUCA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgZGlzdENhbGN1bGF0b3IgPSBuZXcgSW1hZ2VRLmRpc3RhbmNlLkV1Y2xpZGVhbigpO1xuICAgIGNvbnN0IHF1YW50aXplciA9IG5ldyBJbWFnZVEucGFsZXR0ZVttZXRob2RdKGRpc3RDYWxjdWxhdG9yLCBtYXhDb2xvckluZGV4ZXMsIG1vZGlmaWVyKTtcbiAgICBsZXQgaW1hZ2VNYWtlcjtcbiAgICBpZiAoZGl0aGVyKSB7XG4gICAgICAgIGltYWdlTWFrZXIgPSBuZXcgSW1hZ2VRLmltYWdlLkVycm9yRGlmZnVzaW9uQXJyYXkoXG4gICAgICAgICAgICBkaXN0Q2FsY3VsYXRvcixcbiAgICAgICAgICAgIEltYWdlUS5pbWFnZS5FcnJvckRpZmZ1c2lvbkFycmF5S2VybmVsW2RpdGhlci5kaXRoZXJBbGdvcml0aG1dLFxuICAgICAgICAgICAgZGl0aGVyLnNlcnBlbnRpbmUsXG4gICAgICAgICAgICBkaXRoZXIubWluaW11bUNvbG9yRGlzdGFuY2VUb0RpdGhlcixcbiAgICAgICAgICAgIGRpdGhlci5jYWxjdWxhdGVFcnJvckxpa2VHSU1QXG4gICAgICAgICk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBpbWFnZU1ha2VyID0gbmV3IEltYWdlUS5pbWFnZS5OZWFyZXN0Q29sb3IoZGlzdENhbGN1bGF0b3IpO1xuICAgIH1cblxuICAgIGNvbnN0IGlucHV0Q29udGFpbmVycyA9IFtdO1xuICAgIGltYWdlcy5mb3JFYWNoKGltYWdlID0+IHtcblxuICAgICAgICBjb25zdCBpbWFnZUJ1ZiA9IGltYWdlLmJpdG1hcC5kYXRhO1xuICAgICAgICBjb25zdCBpbnB1dEJ1ZiA9IG5ldyBBcnJheUJ1ZmZlcihpbWFnZUJ1Zi5sZW5ndGgpO1xuICAgICAgICBjb25zdCBpbnB1dEFycmF5ID0gbmV3IFVpbnQzMkFycmF5KGlucHV0QnVmKTtcbiAgICAgICAgZm9yIChsZXQgYmkgPSAwLCBhaSA9IDA7IGJpIDwgaW1hZ2VCdWYubGVuZ3RoOyBiaSArPSA0LCArK2FpKSB7XG4gICAgICAgICAgICBpbnB1dEFycmF5W2FpXSA9IGltYWdlQnVmLnJlYWRVSW50MzJMRShiaSwgdHJ1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaW5wdXRDb250YWluZXIgPSBJbWFnZVEudXRpbHMuUG9pbnRDb250YWluZXIuZnJvbVVpbnQzMkFycmF5KFxuICAgICAgICAgICAgICAgIGlucHV0QXJyYXksIGltYWdlLmJpdG1hcC53aWR0aCwgaW1hZ2UuYml0bWFwLmhlaWdodCk7XG4gICAgICAgIHF1YW50aXplci5zYW1wbGUoaW5wdXRDb250YWluZXIpO1xuICAgICAgICBpbnB1dENvbnRhaW5lcnMucHVzaChpbnB1dENvbnRhaW5lcik7XG4gICAgfSk7XG4gICAgXG4gICAgY29uc3QgbGltaXRlZFBhbGV0dGUgPSBxdWFudGl6ZXIucXVhbnRpemVTeW5jKCk7XG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGltYWdlcy5sZW5ndGg7ICsraSkge1xuICAgICAgICBjb25zdCBpbWFnZUJ1ZiA9IGltYWdlc1tpXS5iaXRtYXAuZGF0YTtcbiAgICAgICAgY29uc3Qgb3V0cHV0Q29udGFpbmVyID0gaW1hZ2VNYWtlci5xdWFudGl6ZVN5bmMoaW5wdXRDb250YWluZXJzW2ldLCBsaW1pdGVkUGFsZXR0ZSk7XG4gICAgICAgIGNvbnN0IG91dHB1dEFycmF5ID0gb3V0cHV0Q29udGFpbmVyLnRvVWludDMyQXJyYXkoKTtcbiAgICAgICAgZm9yIChsZXQgYmkgPSAwLCBhaSA9IDA7IGJpIDwgaW1hZ2VCdWYubGVuZ3RoOyBiaSArPSA0LCArK2FpKSB7XG4gICAgICAgICAgICBpbWFnZUJ1Zi53cml0ZVVJbnQzMkxFKG91dHB1dEFycmF5W2FpXSwgYmkpO1xuICAgICAgICB9XG4gICAgfVxufVxuXG5mdW5jdGlvbiBfcmVhZEJpbmFyeShwYXRoKSB7XG4gICAgLy8gVEJEOiBhZGQgc3VwcG9ydCBmb3IgVVJMc1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG5cbiAgICAgICAgZnMucmVhZEZpbGUocGF0aCwgKGVyciwgYnVmZmVyKSA9PiB7XG5cbiAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVqZWN0KGVycik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcmVzb2x2ZShidWZmZXIpO1xuICAgICAgICB9KTtcbiAgICB9KTtcbn1cblxuZnVuY3Rpb24gX3dyaXRlQmluYXJ5KHBhdGgsIGJ1ZmZlcikge1xuICAgIC8vIFRCRDogYWRkIHN1cHBvcnQgZm9yIFVSTHNcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuXG4gICAgICAgIGZzLndyaXRlRmlsZShwYXRoLCBidWZmZXIsIGVyciA9PiB7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVqZWN0KGVycik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcmVzb2x2ZSgpO1xuICAgICAgICB9KTtcbiAgICB9KTtcbn1cbiJdLCJuYW1lcyI6WyJmcyIsInJlcXVpcmUiLCJJbWFnZVEiLCJCaXRtYXBJbWFnZSIsIkdpZkZyYW1lIiwiR2lmRXJyb3IiLCJHaWZDb2RlYyIsIklOVkFMSURfU1VGRklYRVMiLCJkZWZhdWx0Q29kZWMiLCJleHBvcnRzIiwiY2xvbmVGcmFtZXMiLCJmcmFtZXMiLCJjbG9uZXMiLCJmb3JFYWNoIiwiZnJhbWUiLCJwdXNoIiwiZ2V0Q29sb3JJbmZvIiwibWF4R2xvYmFsSW5kZXgiLCJ1c2VzVHJhbnNwYXJlbmN5IiwicGFsZXR0ZXMiLCJpIiwibGVuZ3RoIiwicGFsZXR0ZSIsImdldFBhbGV0dGUiLCJpbmRleENvdW50IiwiZ2xvYmFsQ29sb3JTZXQiLCJTZXQiLCJjb2xvcnMiLCJjb2xvciIsImFkZCIsInNpemUiLCJBcnJheSIsIml0ZXIiLCJ2YWx1ZXMiLCJuZXh0IiwidmFsdWUiLCJzb3J0IiwiYSIsImIiLCJjb3B5QXNKaW1wIiwiamltcCIsImJpdG1hcEltYWdlVG9Db3B5Iiwic2hhcmVBc0ppbXAiLCJnZXRNYXhEaW1lbnNpb25zIiwibWF4V2lkdGgiLCJtYXhIZWlnaHQiLCJ3aWR0aCIsInhPZmZzZXQiLCJiaXRtYXAiLCJoZWlnaHQiLCJ5T2Zmc2V0IiwicXVhbnRpemVEZWtrZXIiLCJpbWFnZU9ySW1hZ2VzIiwibWF4Q29sb3JJbmRleGVzIiwiZGl0aGVyIiwiX3F1YW50aXplIiwicXVhbnRpemVTb3Jva2luIiwiaGlzdG9ncmFtIiwiaGlzdG9ncmFtSUQiLCJFcnJvciIsInF1YW50aXplV3UiLCJzaWduaWZpY2FudEJpdHMiLCJyZWFkIiwic291cmNlIiwiZGVjb2RlciIsIkJ1ZmZlciIsImlzQnVmZmVyIiwiZGVjb2RlR2lmIiwiX3JlYWRCaW5hcnkiLCJ0aGVuIiwiYnVmZmVyIiwiYml0bWFwSW1hZ2VUb1NoYXJlIiwiamltcEltYWdlIiwiZGF0YSIsIndyaXRlIiwicGF0aCIsInNwZWMiLCJlbmNvZGVyIiwibWF0Y2hlcyIsIm1hdGNoIiwiaW5jbHVkZXMiLCJ0b0xvd2VyQ2FzZSIsImVuY29kZUdpZiIsImdpZiIsIl93cml0ZUJpbmFyeSIsIm1ldGhvZCIsIm1vZGlmaWVyIiwiaW1hZ2VzIiwiaXNBcnJheSIsImRpdGhlckFsZ3MiLCJpbmRleE9mIiwiZGl0aGVyQWxnb3JpdGhtIiwic2VycGVudGluZSIsInVuZGVmaW5lZCIsIm1pbmltdW1Db2xvckRpc3RhbmNlVG9EaXRoZXIiLCJjYWxjdWxhdGVFcnJvckxpa2VHSU1QIiwiZGlzdENhbGN1bGF0b3IiLCJkaXN0YW5jZSIsIkV1Y2xpZGVhbiIsInF1YW50aXplciIsImltYWdlTWFrZXIiLCJpbWFnZSIsIkVycm9yRGlmZnVzaW9uQXJyYXkiLCJFcnJvckRpZmZ1c2lvbkFycmF5S2VybmVsIiwiTmVhcmVzdENvbG9yIiwiaW5wdXRDb250YWluZXJzIiwiaW1hZ2VCdWYiLCJpbnB1dEJ1ZiIsIkFycmF5QnVmZmVyIiwiaW5wdXRBcnJheSIsIlVpbnQzMkFycmF5IiwiYmkiLCJhaSIsInJlYWRVSW50MzJMRSIsImlucHV0Q29udGFpbmVyIiwidXRpbHMiLCJQb2ludENvbnRhaW5lciIsImZyb21VaW50MzJBcnJheSIsInNhbXBsZSIsImxpbWl0ZWRQYWxldHRlIiwicXVhbnRpemVTeW5jIiwib3V0cHV0Q29udGFpbmVyIiwib3V0cHV0QXJyYXkiLCJ0b1VpbnQzMkFycmF5Iiwid3JpdGVVSW50MzJMRSIsIlByb21pc2UiLCJyZXNvbHZlIiwicmVqZWN0IiwicmVhZEZpbGUiLCJlcnIiLCJ3cml0ZUZpbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gifwrap/src/gifutil.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gifwrap/src/index.js":
/*!*******************************************!*\
  !*** ./node_modules/gifwrap/src/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst BitmapImage = __webpack_require__(/*! ./bitmapimage */ \"(rsc)/./node_modules/gifwrap/src/bitmapimage.js\");\nconst { Gif, GifError } = __webpack_require__(/*! ./gif */ \"(rsc)/./node_modules/gifwrap/src/gif.js\");\nconst { GifCodec } = __webpack_require__(/*! ./gifcodec */ \"(rsc)/./node_modules/gifwrap/src/gifcodec.js\");\nconst { GifFrame } = __webpack_require__(/*! ./gifframe */ \"(rsc)/./node_modules/gifwrap/src/gifframe.js\");\nconst GifUtil = __webpack_require__(/*! ./gifutil */ \"(rsc)/./node_modules/gifwrap/src/gifutil.js\");\nmodule.exports = {\n    BitmapImage,\n    Gif,\n    GifCodec,\n    GifFrame,\n    GifUtil,\n    GifError\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2lmd3JhcC9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxNQUFNQSxjQUFjQyxtQkFBT0EsQ0FBQztBQUM1QixNQUFNLEVBQUVDLEdBQUcsRUFBRUMsUUFBUSxFQUFFLEdBQUdGLG1CQUFPQSxDQUFDO0FBQ2xDLE1BQU0sRUFBRUcsUUFBUSxFQUFFLEdBQUdILG1CQUFPQSxDQUFDO0FBQzdCLE1BQU0sRUFBRUksUUFBUSxFQUFFLEdBQUdKLG1CQUFPQSxDQUFDO0FBQzdCLE1BQU1LLFVBQVVMLG1CQUFPQSxDQUFDO0FBRXhCTSxPQUFPQyxPQUFPLEdBQUc7SUFDYlI7SUFDQUU7SUFDQUU7SUFDQUM7SUFDQUM7SUFDQUg7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL3hhdXVzZC10cmFkaW5nLWFuYWx5emVyLy4vbm9kZV9tb2R1bGVzL2dpZndyYXAvc3JjL2luZGV4LmpzP2FjNWIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBCaXRtYXBJbWFnZSA9IHJlcXVpcmUoJy4vYml0bWFwaW1hZ2UnKTtcbmNvbnN0IHsgR2lmLCBHaWZFcnJvciB9ID0gcmVxdWlyZSgnLi9naWYnKTtcbmNvbnN0IHsgR2lmQ29kZWMgfSA9IHJlcXVpcmUoJy4vZ2lmY29kZWMnKTtcbmNvbnN0IHsgR2lmRnJhbWUgfSA9IHJlcXVpcmUoJy4vZ2lmZnJhbWUnKTtcbmNvbnN0IEdpZlV0aWwgPSByZXF1aXJlKCcuL2dpZnV0aWwnKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgQml0bWFwSW1hZ2UsXG4gICAgR2lmLFxuICAgIEdpZkNvZGVjLFxuICAgIEdpZkZyYW1lLFxuICAgIEdpZlV0aWwsXG4gICAgR2lmRXJyb3Jcbn07XG4iXSwibmFtZXMiOlsiQml0bWFwSW1hZ2UiLCJyZXF1aXJlIiwiR2lmIiwiR2lmRXJyb3IiLCJHaWZDb2RlYyIsIkdpZkZyYW1lIiwiR2lmVXRpbCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gifwrap/src/index.js\n");

/***/ })

};
;