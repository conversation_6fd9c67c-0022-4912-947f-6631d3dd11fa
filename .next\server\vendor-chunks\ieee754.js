/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ieee754";
exports.ids = ["vendor-chunks/ieee754"];
exports.modules = {

/***/ "(rsc)/./node_modules/ieee754/index.js":
/*!***************************************!*\
  !*** ./node_modules/ieee754/index.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */ exports.read = function(buffer, offset, isLE, mLen, nBytes) {\n    var e, m;\n    var eLen = nBytes * 8 - mLen - 1;\n    var eMax = (1 << eLen) - 1;\n    var eBias = eMax >> 1;\n    var nBits = -7;\n    var i = isLE ? nBytes - 1 : 0;\n    var d = isLE ? -1 : 1;\n    var s = buffer[offset + i];\n    i += d;\n    e = s & (1 << -nBits) - 1;\n    s >>= -nBits;\n    nBits += eLen;\n    for(; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8){}\n    m = e & (1 << -nBits) - 1;\n    e >>= -nBits;\n    nBits += mLen;\n    for(; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8){}\n    if (e === 0) {\n        e = 1 - eBias;\n    } else if (e === eMax) {\n        return m ? NaN : (s ? -1 : 1) * Infinity;\n    } else {\n        m = m + Math.pow(2, mLen);\n        e = e - eBias;\n    }\n    return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\nexports.write = function(buffer, value, offset, isLE, mLen, nBytes) {\n    var e, m, c;\n    var eLen = nBytes * 8 - mLen - 1;\n    var eMax = (1 << eLen) - 1;\n    var eBias = eMax >> 1;\n    var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n    var i = isLE ? 0 : nBytes - 1;\n    var d = isLE ? 1 : -1;\n    var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n    value = Math.abs(value);\n    if (isNaN(value) || value === Infinity) {\n        m = isNaN(value) ? 1 : 0;\n        e = eMax;\n    } else {\n        e = Math.floor(Math.log(value) / Math.LN2);\n        if (value * (c = Math.pow(2, -e)) < 1) {\n            e--;\n            c *= 2;\n        }\n        if (e + eBias >= 1) {\n            value += rt / c;\n        } else {\n            value += rt * Math.pow(2, 1 - eBias);\n        }\n        if (value * c >= 2) {\n            e++;\n            c /= 2;\n        }\n        if (e + eBias >= eMax) {\n            m = 0;\n            e = eMax;\n        } else if (e + eBias >= 1) {\n            m = (value * c - 1) * Math.pow(2, mLen);\n            e = e + eBias;\n        } else {\n            m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n            e = 0;\n        }\n    }\n    for(; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8){}\n    e = e << mLen | m;\n    eLen += mLen;\n    for(; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8){}\n    buffer[offset + i - d] |= s * 128;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ieee754/index.js\n");

/***/ })

};
;