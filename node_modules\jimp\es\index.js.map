{"version": 3, "file": "index.js", "names": ["configure", "types", "plugins"], "sources": ["../src/index.js"], "sourcesContent": ["import configure from \"@jimp/custom\";\n\nimport types from \"@jimp/types\";\nimport plugins from \"@jimp/plugins\";\n\nexport default configure({\n  types: [types],\n  plugins: [plugins],\n});\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,cAAc;AAEpC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,OAAO,MAAM,eAAe;AAEnC,eAAeF,SAAS,CAAC;EACvBC,KAAK,EAAE,CAACA,KAAK,CAAC;EACdC,OAAO,EAAE,CAACA,OAAO;AACnB,CAAC,CAAC"}