{"version": 3, "file": "measure-text.js", "names": ["measureText", "font", "text", "x", "i", "length", "chars", "kerning", "kernings", "xadvance", "splitLines", "max<PERSON><PERSON><PERSON>", "words", "replace", "split", "lines", "currentLine", "longestLine", "for<PERSON>ach", "word", "line", "join", "includes", "push", "measureTextHeight", "common", "lineHeight"], "sources": ["../src/measure-text.js"], "sourcesContent": ["export function measureText(font, text) {\n  let x = 0;\n\n  for (let i = 0; i < text.length; i++) {\n    if (font.chars[text[i]]) {\n      const kerning =\n        font.kernings[text[i]] && font.kernings[text[i]][text[i + 1]]\n          ? font.kernings[text[i]][text[i + 1]]\n          : 0;\n\n      x += (font.chars[text[i]].xadvance || 0) + kerning;\n    }\n  }\n\n  return x;\n}\n\nexport function splitLines(font, text, maxWidth) {\n  const words = text.replace(/[\\r\\n]+/g, \" \\n\").split(\" \");\n\n  const lines = [];\n  let currentLine = [];\n  let longestLine = 0;\n\n  words.forEach((word) => {\n    const line = [...currentLine, word].join(\" \");\n    const length = measureText(font, line);\n\n    if (length <= maxWidth && !word.includes(\"\\n\")) {\n      if (length > longestLine) {\n        longestLine = length;\n      }\n\n      currentLine.push(word);\n    } else {\n      lines.push(currentLine);\n      currentLine = [word.replace(\"\\n\", \"\")];\n    }\n  });\n  lines.push(currentLine);\n  return {\n    lines,\n    longestLine,\n  };\n}\n\nexport function measureTextHeight(font, text, maxWidth) {\n  const { lines } = splitLines(font, text, maxWidth);\n\n  return lines.length * font.common.lineHeight;\n}\n"], "mappings": ";;;;;;;;AAAO,SAASA,WAAW,CAACC,IAAI,EAAEC,IAAI,EAAE;EACtC,IAAIC,CAAC,GAAG,CAAC;EAET,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIH,IAAI,CAACK,KAAK,CAACJ,IAAI,CAACE,CAAC,CAAC,CAAC,EAAE;MACvB,MAAMG,OAAO,GACXN,IAAI,CAACO,QAAQ,CAACN,IAAI,CAACE,CAAC,CAAC,CAAC,IAAIH,IAAI,CAACO,QAAQ,CAACN,IAAI,CAACE,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GACzDH,IAAI,CAACO,QAAQ,CAACN,IAAI,CAACE,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GACnC,CAAC;MAEPD,CAAC,IAAI,CAACF,IAAI,CAACK,KAAK,CAACJ,IAAI,CAACE,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAI,CAAC,IAAIF,OAAO;IACpD;EACF;EAEA,OAAOJ,CAAC;AACV;AAEO,SAASO,UAAU,CAACT,IAAI,EAAEC,IAAI,EAAES,QAAQ,EAAE;EAC/C,MAAMC,KAAK,GAAGV,IAAI,CAACW,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EAExD,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,CAAC;EAEnBL,KAAK,CAACM,OAAO,CAAEC,IAAI,IAAK;IACtB,MAAMC,IAAI,GAAG,CAAC,GAAGJ,WAAW,EAAEG,IAAI,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;IAC7C,MAAMhB,MAAM,GAAGL,WAAW,CAACC,IAAI,EAAEmB,IAAI,CAAC;IAEtC,IAAIf,MAAM,IAAIM,QAAQ,IAAI,CAACQ,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC9C,IAAIjB,MAAM,GAAGY,WAAW,EAAE;QACxBA,WAAW,GAAGZ,MAAM;MACtB;MAEAW,WAAW,CAACO,IAAI,CAACJ,IAAI,CAAC;IACxB,CAAC,MAAM;MACLJ,KAAK,CAACQ,IAAI,CAACP,WAAW,CAAC;MACvBA,WAAW,GAAG,CAACG,IAAI,CAACN,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACxC;EACF,CAAC,CAAC;EACFE,KAAK,CAACQ,IAAI,CAACP,WAAW,CAAC;EACvB,OAAO;IACLD,KAAK;IACLE;EACF,CAAC;AACH;AAEO,SAASO,iBAAiB,CAACvB,IAAI,EAAEC,IAAI,EAAES,QAAQ,EAAE;EACtD,MAAM;IAAEI;EAAM,CAAC,GAAGL,UAAU,CAACT,IAAI,EAAEC,IAAI,EAAES,QAAQ,CAAC;EAElD,OAAOI,KAAK,CAACV,MAAM,GAAGJ,IAAI,CAACwB,MAAM,CAACC,UAAU;AAC9C"}