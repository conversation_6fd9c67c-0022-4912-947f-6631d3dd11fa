{"version": 3, "file": "index.js", "names": ["plugins", "blit", "blur", "circle", "color", "contain", "cover", "crop", "displace", "dither", "fisheye", "flip", "gaussian", "invert", "mask", "normalize", "print", "resize", "rotate", "scale", "shadow", "threshold", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "initializedPlugins", "map", "pluginModule", "plugin", "class", "constants", "mergeDeep"], "sources": ["../src/index.js"], "sourcesContent": ["import { mergeDeep } from \"timm\";\n\nimport blit from \"@jimp/plugin-blit\";\nimport blur from \"@jimp/plugin-blur\";\nimport circle from \"@jimp/plugin-circle\";\nimport color from \"@jimp/plugin-color\";\nimport contain from \"@jimp/plugin-contain\";\nimport cover from \"@jimp/plugin-cover\";\nimport crop from \"@jimp/plugin-crop\";\nimport displace from \"@jimp/plugin-displace\";\nimport dither from \"@jimp/plugin-dither\";\nimport fisheye from \"@jimp/plugin-fisheye\";\nimport flip from \"@jimp/plugin-flip\";\nimport gaussian from \"@jimp/plugin-gaussian\";\nimport invert from \"@jimp/plugin-invert\";\nimport mask from \"@jimp/plugin-mask\";\nimport normalize from \"@jimp/plugin-normalize\";\nimport print from \"@jimp/plugin-print\";\nimport resize from \"@jimp/plugin-resize\";\nimport rotate from \"@jimp/plugin-rotate\";\nimport scale from \"@jimp/plugin-scale\";\nimport shadow from \"@jimp/plugin-shadow\";\nimport threshold from \"@jimp/plugin-threshold\";\n\nconst plugins = [\n  blit,\n  blur,\n  circle,\n  color,\n  contain,\n  cover,\n  crop,\n  displace,\n  dither,\n  fisheye,\n  flip,\n  gaussian,\n  invert,\n  mask,\n  normalize,\n  print,\n  resize,\n  rotate,\n  scale,\n  shadow,\n  threshold,\n];\n\nexport default (jimpEvChange) => {\n  const initializedPlugins = plugins.map((pluginModule) => {\n    let plugin = pluginModule(jimpEvChange) || {};\n\n    if (!plugin.class && !plugin.constants) {\n      // Default to class function\n      plugin = { class: plugin };\n    }\n\n    return plugin;\n  });\n\n  return mergeDeep(...initializedPlugins);\n};\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAA+C;AAE/C,MAAMA,OAAO,GAAG,CACdC,mBAAI,EACJC,mBAAI,EACJC,qBAAM,EACNC,oBAAK,EACLC,sBAAO,EACPC,oBAAK,EACLC,mBAAI,EACJC,uBAAQ,EACRC,qBAAM,EACNC,sBAAO,EACPC,mBAAI,EACJC,uBAAQ,EACRC,qBAAM,EACNC,mBAAI,EACJC,wBAAS,EACTC,oBAAK,EACLC,qBAAM,EACNC,qBAAM,EACNC,oBAAK,EACLC,qBAAM,EACNC,wBAAS,CACV;AAAC,eAEcC,YAAY,IAAK;EAC/B,MAAMC,kBAAkB,GAAGvB,OAAO,CAACwB,GAAG,CAAEC,YAAY,IAAK;IACvD,IAAIC,MAAM,GAAGD,YAAY,CAACH,YAAY,CAAC,IAAI,CAAC,CAAC;IAE7C,IAAI,CAACI,MAAM,CAACC,KAAK,IAAI,CAACD,MAAM,CAACE,SAAS,EAAE;MACtC;MACAF,MAAM,GAAG;QAAEC,KAAK,EAAED;MAAO,CAAC;IAC5B;IAEA,OAAOA,MAAM;EACf,CAAC,CAAC;EAEF,OAAO,IAAAG,eAAS,EAAC,GAAGN,kBAAkB,CAAC;AACzC,CAAC;AAAA;AAAA;AAAA"}