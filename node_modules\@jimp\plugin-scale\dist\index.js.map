{"version": 3, "file": "index.js", "names": ["scale", "f", "mode", "cb", "throwError", "call", "w", "bitmap", "width", "h", "height", "resize", "isNodePattern", "scaleToFit"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\nexport default () => ({\n  /**\n   * Uniformly scales the image by a factor.\n   * @param {number} f the factor to scale the image by\n   * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n   * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  scale(f, mode, cb) {\n    if (typeof f !== \"number\") {\n      return throwError.call(this, \"f must be a number\", cb);\n    }\n\n    if (f < 0) {\n      return throwError.call(this, \"f must be a positive number\", cb);\n    }\n\n    if (typeof mode === \"function\" && typeof cb === \"undefined\") {\n      cb = mode;\n      mode = null;\n    }\n\n    const w = this.bitmap.width * f;\n    const h = this.bitmap.height * f;\n    this.resize(w, h, mode);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Scale the image to the largest size that fits inside the rectangle that has the given width and height.\n   * @param {number} w the width to resize the image to\n   * @param {number} h the height to resize the image to\n   * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  scaleToFit(w, h, mode, cb) {\n    if (typeof w !== \"number\" || typeof h !== \"number\") {\n      return throwError.call(this, \"w and h must be numbers\", cb);\n    }\n\n    if (typeof mode === \"function\" && typeof cb === \"undefined\") {\n      cb = mode;\n      mode = null;\n    }\n\n    const f =\n      w / h > this.bitmap.width / this.bitmap.height\n        ? h / this.bitmap.height\n        : w / this.bitmap.width;\n    this.scale(f, mode);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": ";;;;;;AAAA;AAAwD,eAEzC,OAAO;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;EACEA,KAAK,CAACC,CAAC,EAAEC,IAAI,EAAEC,EAAE,EAAE;IACjB,IAAI,OAAOF,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAOG,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEF,EAAE,CAAC;IACxD;IAEA,IAAIF,CAAC,GAAG,CAAC,EAAE;MACT,OAAOG,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEF,EAAE,CAAC;IACjE;IAEA,IAAI,OAAOD,IAAI,KAAK,UAAU,IAAI,OAAOC,EAAE,KAAK,WAAW,EAAE;MAC3DA,EAAE,GAAGD,IAAI;MACTA,IAAI,GAAG,IAAI;IACb;IAEA,MAAMI,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,KAAK,GAAGP,CAAC;IAC/B,MAAMQ,CAAC,GAAG,IAAI,CAACF,MAAM,CAACG,MAAM,GAAGT,CAAC;IAChC,IAAI,CAACU,MAAM,CAACL,CAAC,EAAEG,CAAC,EAAEP,IAAI,CAAC;IAEvB,IAAI,IAAAU,oBAAa,EAACT,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEQ,UAAU,CAACP,CAAC,EAAEG,CAAC,EAAEP,IAAI,EAAEC,EAAE,EAAE;IACzB,IAAI,OAAOG,CAAC,KAAK,QAAQ,IAAI,OAAOG,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAOL,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEF,EAAE,CAAC;IAC7D;IAEA,IAAI,OAAOD,IAAI,KAAK,UAAU,IAAI,OAAOC,EAAE,KAAK,WAAW,EAAE;MAC3DA,EAAE,GAAGD,IAAI;MACTA,IAAI,GAAG,IAAI;IACb;IAEA,MAAMD,CAAC,GACLK,CAAC,GAAGG,CAAC,GAAG,IAAI,CAACF,MAAM,CAACC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACG,MAAM,GAC1CD,CAAC,GAAG,IAAI,CAACF,MAAM,CAACG,MAAM,GACtBJ,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,KAAK;IAC3B,IAAI,CAACR,KAAK,CAACC,CAAC,EAAEC,IAAI,CAAC;IAEnB,IAAI,IAAAU,oBAAa,EAACT,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}