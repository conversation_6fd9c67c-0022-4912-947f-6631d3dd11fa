{"version": 3, "file": "index.js", "names": ["composite", "src", "x", "y", "options", "cb", "constructor", "throwError", "call", "mode", "opacitySource", "opacityDest", "constants", "BLEND_SOURCE_OVER", "blendmode", "compositeModes", "Math", "round", "baseImage", "opacity", "scanQuiet", "bitmap", "width", "height", "sx", "sy", "idx", "dstIdx", "getPixelIndex", "EDGE_CROP", "blended", "r", "data", "g", "b", "a", "limit255", "isNodePattern"], "sources": ["../../src/composite/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\nimport * as constants from \"../constants\";\n\nimport * as compositeModes from \"./composite-modes\";\n\n/**\n * Composites a source image over to this image respecting alpha channels\n * @param {Jimp} src the source Jimp instance\n * @param {number} x the x position to blit the image\n * @param {number} y the y position to blit the image\n * @param {object} options determine what mode to use\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default function composite(src, x, y, options = {}, cb) {\n  if (typeof options === \"function\") {\n    cb = options;\n    options = {};\n  }\n\n  if (!(src instanceof this.constructor)) {\n    return throwError.call(this, \"The source must be a Jimp image\", cb);\n  }\n\n  if (typeof x !== \"number\" || typeof y !== \"number\") {\n    return throwError.call(this, \"x and y must be numbers\", cb);\n  }\n\n  let { mode, opacitySource, opacityDest } = options;\n\n  if (!mode) {\n    mode = constants.BLEND_SOURCE_OVER;\n  }\n\n  if (\n    typeof opacitySource !== \"number\" ||\n    opacitySource < 0 ||\n    opacitySource > 1\n  ) {\n    opacitySource = 1.0;\n  }\n\n  if (typeof opacityDest !== \"number\" || opacityDest < 0 || opacityDest > 1) {\n    opacityDest = 1.0;\n  }\n\n  // eslint-disable-next-line import/namespace\n  const blendmode = compositeModes[mode];\n\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n\n  const baseImage = this;\n\n  if (opacityDest !== 1.0) {\n    baseImage.opacity(opacityDest);\n  }\n\n  src.scanQuiet(\n    0,\n    0,\n    src.bitmap.width,\n    src.bitmap.height,\n    function (sx, sy, idx) {\n      const dstIdx = baseImage.getPixelIndex(\n        x + sx,\n        y + sy,\n        constants.EDGE_CROP\n      );\n\n      if (dstIdx === -1) {\n        // Skip target pixels outside of dst\n        return;\n      }\n\n      const blended = blendmode(\n        {\n          r: this.bitmap.data[idx + 0] / 255,\n          g: this.bitmap.data[idx + 1] / 255,\n          b: this.bitmap.data[idx + 2] / 255,\n          a: this.bitmap.data[idx + 3] / 255,\n        },\n        {\n          r: baseImage.bitmap.data[dstIdx + 0] / 255,\n          g: baseImage.bitmap.data[dstIdx + 1] / 255,\n          b: baseImage.bitmap.data[dstIdx + 2] / 255,\n          a: baseImage.bitmap.data[dstIdx + 3] / 255,\n        },\n        opacitySource\n      );\n\n      baseImage.bitmap.data[dstIdx + 0] = this.constructor.limit255(\n        blended.r * 255\n      );\n      baseImage.bitmap.data[dstIdx + 1] = this.constructor.limit255(\n        blended.g * 255\n      );\n      baseImage.bitmap.data[dstIdx + 2] = this.constructor.limit255(\n        blended.b * 255\n      );\n      baseImage.bitmap.data[dstIdx + 3] = this.constructor.limit255(\n        blended.a * 255\n      );\n    }\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n"], "mappings": ";;;;;;AAAA;AACA;AAEA;AAAoD;AAAA;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,SAAS,CAACC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAoB;EAAA,IAAlBC,OAAO,uEAAG,CAAC,CAAC;EAAA,IAAEC,EAAE;EAC3D,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;IACjCC,EAAE,GAAGD,OAAO;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAI,EAAEH,GAAG,YAAY,IAAI,CAACK,WAAW,CAAC,EAAE;IACtC,OAAOC,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,iCAAiC,EAAEH,EAAE,CAAC;EACrE;EAEA,IAAI,OAAOH,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,OAAOI,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEH,EAAE,CAAC;EAC7D;EAEA,IAAI;IAAEI,IAAI;IAAEC,aAAa;IAAEC;EAAY,CAAC,GAAGP,OAAO;EAElD,IAAI,CAACK,IAAI,EAAE;IACTA,IAAI,GAAGG,SAAS,CAACC,iBAAiB;EACpC;EAEA,IACE,OAAOH,aAAa,KAAK,QAAQ,IACjCA,aAAa,GAAG,CAAC,IACjBA,aAAa,GAAG,CAAC,EACjB;IACAA,aAAa,GAAG,GAAG;EACrB;EAEA,IAAI,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAE;IACzEA,WAAW,GAAG,GAAG;EACnB;;EAEA;EACA,MAAMG,SAAS,GAAGC,cAAc,CAACN,IAAI,CAAC;;EAEtC;EACAP,CAAC,GAAGc,IAAI,CAACC,KAAK,CAACf,CAAC,CAAC;EACjBC,CAAC,GAAGa,IAAI,CAACC,KAAK,CAACd,CAAC,CAAC;EAEjB,MAAMe,SAAS,GAAG,IAAI;EAEtB,IAAIP,WAAW,KAAK,GAAG,EAAE;IACvBO,SAAS,CAACC,OAAO,CAACR,WAAW,CAAC;EAChC;EAEAV,GAAG,CAACmB,SAAS,CACX,CAAC,EACD,CAAC,EACDnB,GAAG,CAACoB,MAAM,CAACC,KAAK,EAChBrB,GAAG,CAACoB,MAAM,CAACE,MAAM,EACjB,UAAUC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAE;IACrB,MAAMC,MAAM,GAAGT,SAAS,CAACU,aAAa,CACpC1B,CAAC,GAAGsB,EAAE,EACNrB,CAAC,GAAGsB,EAAE,EACNb,SAAS,CAACiB,SAAS,CACpB;IAED,IAAIF,MAAM,KAAK,CAAC,CAAC,EAAE;MACjB;MACA;IACF;IAEA,MAAMG,OAAO,GAAGhB,SAAS,CACvB;MACEiB,CAAC,EAAE,IAAI,CAACV,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;MAClCO,CAAC,EAAE,IAAI,CAACZ,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;MAClCQ,CAAC,EAAE,IAAI,CAACb,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;MAClCS,CAAC,EAAE,IAAI,CAACd,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG;IACjC,CAAC,EACD;MACEK,CAAC,EAAEb,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MAC1CM,CAAC,EAAEf,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MAC1CO,CAAC,EAAEhB,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MAC1CQ,CAAC,EAAEjB,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG;IACzC,CAAC,EACDjB,aAAa,CACd;IAEDQ,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrB,WAAW,CAAC8B,QAAQ,CAC3DN,OAAO,CAACC,CAAC,GAAG,GAAG,CAChB;IACDb,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrB,WAAW,CAAC8B,QAAQ,CAC3DN,OAAO,CAACG,CAAC,GAAG,GAAG,CAChB;IACDf,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrB,WAAW,CAAC8B,QAAQ,CAC3DN,OAAO,CAACI,CAAC,GAAG,GAAG,CAChB;IACDhB,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrB,WAAW,CAAC8B,QAAQ,CAC3DN,OAAO,CAACK,CAAC,GAAG,GAAG,CAChB;EACH,CAAC,CACF;EAED,IAAI,IAAAE,oBAAa,EAAChC,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACG,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;AAAC;AAAA"}