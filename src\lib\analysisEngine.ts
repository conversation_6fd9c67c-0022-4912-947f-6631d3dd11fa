import { AdvancedImageProcessor } from './advancedImageProcessing'
import { SMCAnalyzer } from './smcAnalysis'
import { ICTAnalyzer } from './ictAnalysis'
import { TechnicalAnalyzer } from './technicalIndicators'
import { ScalpingStrategy, SwingTradingStrategy } from './tradingStrategies'
import { MarketDataProvider } from './marketDataProvider'
import { AnalysisResult, ChartData } from '@/types/trading'
import { database } from './database'

export class TradingAnalysisEngine {
  private imageProcessor: AdvancedImageProcessor
  private marketDataProvider: MarketDataProvider
  private learningData: Map<string, any[]> = new Map()

  constructor() {
    this.imageProcessor = new AdvancedImageProcessor()
    this.marketDataProvider = MarketDataProvider.getInstance()
  }

  async analyzeChart(
    imageBuffer: Buffer, 
    strategy: 'scalping' | 'swing'
  ): Promise<AnalysisResult> {
    try {
      // Step 1: Process the chart image with advanced algorithms
      const chartData = await this.imageProcessor.processChartImage(imageBuffer)

      // Step 1.5: Enhance with real market data
      const enhancedChartData = await this.enhanceWithMarketData(chartData)

      // Step 2: Perform SMC analysis
      const smcAnalyzer = new SMCAnalyzer(enhancedChartData)
      const smcAnalysis = smcAnalyzer.analyze()

      // Step 3: Perform ICT analysis
      const ictAnalyzer = new ICTAnalyzer(enhancedChartData)
      const ictAnalysis = ictAnalyzer.analyze()

      // Step 4: Calculate technical indicators
      const technicalAnalyzer = new TechnicalAnalyzer(enhancedChartData.candlesticks)
      const technicalIndicators = technicalAnalyzer.calculateIndicators()
      
      // Step 5: Generate trade setup based on strategy
      let tradeSetup
      if (strategy === 'scalping') {
        const scalpingStrategy = new ScalpingStrategy(
          enhancedChartData,
          smcAnalysis,
          ictAnalysis,
          technicalIndicators
        )
        tradeSetup = scalpingStrategy.generateTradeSetup()
      } else {
        const swingStrategy = new SwingTradingStrategy(
          enhancedChartData,
          smcAnalysis,
          ictAnalysis,
          technicalIndicators
        )
        tradeSetup = swingStrategy.generateTradeSetup()
      }
      
      // Step 6: Create analysis result
      const analysisResult: AnalysisResult = {
        tradeSetup,
        smcAnalysis,
        ictAnalysis,
        technicalIndicators,
        timeframe: enhancedChartData.timeframe,
        strategy,
        timestamp: new Date().toISOString(),
        chartData: enhancedChartData
      }
      
      // Step 7: Store analysis in database
      const analysisId = generateAnalysisId()
      await this.storeAnalysisInDatabase(analysisResult, analysisId)

      // Step 8: Store for learning (simplified)
      this.storeAnalysisForLearning(analysisResult)

      // Add analysis ID to result
      const resultWithId = {
        ...analysisResult,
        analysisId
      }

      return resultWithId
      
    } catch (error) {
      console.error('Analysis engine error:', error)
      throw new Error('Failed to analyze chart. Please ensure the image is a valid trading chart.')
    }
  }

  private async enhanceWithMarketData(chartData: ChartData): Promise<ChartData> {
    try {
      // Get real-time market data
      const currentPrice = this.marketDataProvider.getCurrentPrice()
      const recentHistory = this.marketDataProvider.getPriceHistory(50)

      // Merge chart data with real market data
      const enhancedCandlesticks = [...chartData.candlesticks]

      // Update the last few candles with real market data if available
      if (recentHistory.length > 0) {
        const realDataCount = Math.min(10, recentHistory.length)
        enhancedCandlesticks.splice(-realDataCount, realDataCount, ...recentHistory.slice(-realDataCount))
      }

      // Update current price with real market price
      const enhancedCurrentPrice = currentPrice || chartData.currentPrice

      return {
        ...chartData,
        candlesticks: enhancedCandlesticks,
        currentPrice: enhancedCurrentPrice
      }
    } catch (error) {
      console.error('Error enhancing with market data:', error)
      return chartData // Return original data if enhancement fails
    }
  }

  private async storeAnalysisInDatabase(result: AnalysisResult, analysisId: string): Promise<void> {
    try {
      await database.storeAnalysis({
        id: analysisId,
        timestamp: result.timestamp,
        strategy: result.strategy,
        timeframe: result.timeframe,
        direction: result.tradeSetup.direction,
        confidence: result.tradeSetup.confidence,
        entryPrice: result.tradeSetup.entry,
        stopLoss: result.tradeSetup.stopLoss,
        tp1: result.tradeSetup.takeProfits.tp1,
        tp2: result.tradeSetup.takeProfits.tp2,
        tp3: result.tradeSetup.takeProfits.tp3,
        marketStructure: result.smcAnalysis.marketStructure.trend,
        reasoning: JSON.stringify(result.tradeSetup.reasoning)
      })
    } catch (error) {
      console.error('Failed to store analysis in database:', error)
      // Don't throw error to avoid breaking the analysis flow
    }
  }

  private storeAnalysisForLearning(result: AnalysisResult): void {
    const key = `${result.strategy}_${result.timeframe}`
    
    if (!this.learningData.has(key)) {
      this.learningData.set(key, [])
    }
    
    const data = this.learningData.get(key)!
    data.push({
      timestamp: result.timestamp,
      direction: result.tradeSetup.direction,
      confidence: result.tradeSetup.confidence,
      marketStructure: result.smcAnalysis.marketStructure.trend,
      orderBlockCount: result.smcAnalysis.orderBlocks.length,
      fvgCount: result.smcAnalysis.fairValueGaps.length,
      liquidityLevelCount: result.smcAnalysis.liquidityLevels.length,
      institutionalFlow: result.ictAnalysis.institutionalOrderFlow,
      killZoneActive: Object.values(result.ictAnalysis.killZones).some(Boolean),
      rsi: result.technicalIndicators.rsi[result.technicalIndicators.rsi.length - 1],
      macdSignal: result.technicalIndicators.macd.histogram[result.technicalIndicators.macd.histogram.length - 1] > 0
    })
    
    // Keep only last 100 analyses for each strategy/timeframe
    if (data.length > 100) {
      data.shift()
    }
  }

  // Machine learning adaptation methods
  adaptAnalysisBasedOnFeedback(
    analysisId: string, 
    feedback: {
      accuracy: number
      profitability: number
      actualOutcome?: {
        entryHit: boolean
        stopLossHit: boolean
        takeProfitsHit: { tp1: boolean; tp2: boolean; tp3: boolean }
      }
    }
  ): void {
    // This would implement learning algorithms to improve future analysis
    // For now, we'll store the feedback for future use
    console.log('Feedback received:', { analysisId, feedback })
    
    // In a real implementation, this would:
    // 1. Update confidence scoring algorithms
    // 2. Adjust weight factors for different indicators
    // 3. Improve pattern recognition accuracy
    // 4. Refine entry/exit calculations
  }

  getAnalysisStatistics(): {
    totalAnalyses: number
    strategyBreakdown: { [key: string]: number }
    averageConfidence: number
    directionBreakdown: { BUY: number; SELL: number; NEUTRAL: number }
  } {
    let totalAnalyses = 0
    const strategyBreakdown: { [key: string]: number } = {}
    const directionBreakdown = { BUY: 0, SELL: 0, NEUTRAL: 0 }
    let totalConfidence = 0
    
    this.learningData.forEach((analyses, strategy) => {
      strategyBreakdown[strategy] = analyses.length
      totalAnalyses += analyses.length
      
      analyses.forEach(analysis => {
        totalConfidence += analysis.confidence
        directionBreakdown[analysis.direction as keyof typeof directionBreakdown]++
      })
    })
    
    return {
      totalAnalyses,
      strategyBreakdown,
      averageConfidence: totalAnalyses > 0 ? totalConfidence / totalAnalyses : 0,
      directionBreakdown
    }
  }

  // Advanced pattern recognition (simplified implementation)
  identifyChartPatterns(chartData: ChartData): string[] {
    const patterns: string[] = []
    const candlesticks = chartData.candlesticks
    
    if (candlesticks.length < 10) return patterns
    
    // Head and Shoulders pattern detection
    if (this.detectHeadAndShoulders(candlesticks)) {
      patterns.push('Head and Shoulders')
    }
    
    // Double Top/Bottom detection
    if (this.detectDoubleTop(candlesticks)) {
      patterns.push('Double Top')
    }
    
    if (this.detectDoubleBottom(candlesticks)) {
      patterns.push('Double Bottom')
    }
    
    // Triangle patterns
    if (this.detectTrianglePattern(candlesticks)) {
      patterns.push('Triangle Pattern')
    }
    
    // Flag/Pennant patterns
    if (this.detectFlagPattern(candlesticks)) {
      patterns.push('Flag Pattern')
    }
    
    return patterns
  }

  private detectHeadAndShoulders(candlesticks: any[]): boolean {
    // Simplified head and shoulders detection
    const highs = candlesticks.map(c => c.high)
    const recentHighs = highs.slice(-15)
    
    if (recentHighs.length < 15) return false
    
    // Look for three peaks with middle one being highest
    const peaks = this.findPeaks(recentHighs)
    
    return peaks.length >= 3 && 
           peaks[1] > peaks[0] && 
           peaks[1] > peaks[2] &&
           Math.abs(peaks[0] - peaks[2]) < peaks[0] * 0.02 // Shoulders roughly equal
  }

  private detectDoubleTop(candlesticks: any[]): boolean {
    const highs = candlesticks.map(c => c.high)
    const recentHighs = highs.slice(-20)
    const peaks = this.findPeaks(recentHighs)
    
    return peaks.length >= 2 && 
           Math.abs(peaks[peaks.length - 1] - peaks[peaks.length - 2]) < peaks[peaks.length - 1] * 0.01
  }

  private detectDoubleBottom(candlesticks: any[]): boolean {
    const lows = candlesticks.map(c => c.low)
    const recentLows = lows.slice(-20)
    const troughs = this.findTroughs(recentLows)
    
    return troughs.length >= 2 && 
           Math.abs(troughs[troughs.length - 1] - troughs[troughs.length - 2]) < troughs[troughs.length - 1] * 0.01
  }

  private detectTrianglePattern(candlesticks: any[]): boolean {
    const highs = candlesticks.map(c => c.high).slice(-20)
    const lows = candlesticks.map(c => c.low).slice(-20)
    
    // Check if highs are trending down and lows are trending up (converging)
    const highTrend = this.calculateTrend(highs)
    const lowTrend = this.calculateTrend(lows)
    
    return highTrend < -0.001 && lowTrend > 0.001 // Converging lines
  }

  private detectFlagPattern(candlesticks: any[]): boolean {
    // Look for strong move followed by consolidation
    const recentCandles = candlesticks.slice(-15)
    if (recentCandles.length < 15) return false
    
    const firstHalf = recentCandles.slice(0, 7)
    const secondHalf = recentCandles.slice(7)
    
    const firstHalfRange = Math.max(...firstHalf.map(c => c.high)) - Math.min(...firstHalf.map(c => c.low))
    const secondHalfRange = Math.max(...secondHalf.map(c => c.high)) - Math.min(...secondHalf.map(c => c.low))
    
    return firstHalfRange > secondHalfRange * 2 // Strong move followed by consolidation
  }

  private findPeaks(prices: number[]): number[] {
    const peaks: number[] = []
    
    for (let i = 1; i < prices.length - 1; i++) {
      if (prices[i] > prices[i - 1] && prices[i] > prices[i + 1]) {
        peaks.push(prices[i])
      }
    }
    
    return peaks
  }

  private findTroughs(prices: number[]): number[] {
    const troughs: number[] = []
    
    for (let i = 1; i < prices.length - 1; i++) {
      if (prices[i] < prices[i - 1] && prices[i] < prices[i + 1]) {
        troughs.push(prices[i])
      }
    }
    
    return troughs
  }

  private calculateTrend(prices: number[]): number {
    if (prices.length < 2) return 0
    
    const firstPrice = prices[0]
    const lastPrice = prices[prices.length - 1]
    
    return (lastPrice - firstPrice) / firstPrice
  }
}

function generateAnalysisId(): string {
  return `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}
