{"version": 3, "file": "phash.js", "names": ["ImagePHash", "size", "smallerSize", "initCoefficients", "prototype", "distance", "s1", "s2", "counter", "k", "length", "getHash", "img", "clone", "resize", "grayscale", "vals", "x", "bitmap", "width", "y", "height", "intToRGBA", "getPixelColor", "b", "dctVals", "applyDCT", "total", "avg", "hash", "i", "a", "g", "r", "c", "Math", "sqrt", "f", "N", "F", "u", "v", "sum", "j", "cos", "PI"], "sources": ["../../src/modules/phash.js"], "sourcesContent": ["/*\nCopyright (c) 2011 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n// https://code.google.com/p/ironchef-team21/source/browse/ironchef_team21/src/ImagePHash.java\n\n/*\n * pHash-like image hash.\n * Author: Elliot Shepherd (<EMAIL>\n * Based On: http://www.hackerfactor.com/blog/index.php?/archives/432-Looks-Like-It.html\n */\n\nfunction ImagePHash(size, smallerSize) {\n  this.size = this.size || size;\n  this.smallerSize = this.smallerSize || smallerSize;\n  initCoefficients(this.size);\n}\n\nImagePHash.prototype.size = 32;\nImagePHash.prototype.smallerSize = 8;\n\nImagePHash.prototype.distance = function (s1, s2) {\n  let counter = 0;\n\n  for (let k = 0; k < s1.length; k++) {\n    if (s1[k] !== s2[k]) {\n      counter++;\n    }\n  }\n\n  return counter / s1.length;\n};\n\n// Returns a 'binary string' (like. 001010111011100010) which is easy to do a hamming distance on.\nImagePHash.prototype.getHash = function (img) {\n  /* 1. Reduce size.\n   * Like Average Hash, pHash starts with a small image.\n   * However, the image is larger than 8x8; 32x32 is a good size.\n   * This is really done to simplify the DCT computation and not\n   * because it is needed to reduce the high frequencies.\n   */\n  img = img.clone().resize(this.size, this.size);\n\n  /* 2. Reduce color.\n   * The image is reduced to a grayscale just to further simplify\n   * the number of computations.\n   */\n  img.grayscale();\n\n  const vals = [];\n\n  for (let x = 0; x < img.bitmap.width; x++) {\n    vals[x] = [];\n    for (let y = 0; y < img.bitmap.height; y++) {\n      vals[x][y] = intToRGBA(img.getPixelColor(x, y)).b;\n    }\n  }\n\n  /* 3. Compute the DCT.\n   * The DCT separates the image into a collection of frequencies\n   * and scalars. While JPEG uses an 8x8 DCT, this algorithm uses\n   * a 32x32 DCT.\n   */\n  const dctVals = applyDCT(vals, this.size);\n\n  /* 4. Reduce the DCT.\n   * This is the magic step. While the DCT is 32x32, just keep the\n   * top-left 8x8. Those represent the lowest frequencies in the\n   * picture.\n   */\n  /* 5. Compute the average value.\n   * Like the Average Hash, compute the mean DCT value (using only\n   * the 8x8 DCT low-frequency values and excluding the first term\n   * since the DC coefficient can be significantly different from\n   * the other values and will throw off the average).\n   */\n  let total = 0;\n\n  for (let x = 0; x < this.smallerSize; x++) {\n    for (let y = 0; y < this.smallerSize; y++) {\n      total += dctVals[x][y];\n    }\n  }\n\n  const avg = total / (this.smallerSize * this.smallerSize);\n\n  /* 6. Further reduce the DCT.\n   * This is the magic step. Set the 64 hash bits to 0 or 1\n   * depending on whether each of the 64 DCT values is above or\n   * below the average value. The result doesn't tell us the\n   * actual low frequencies; it just tells us the very-rough\n   * relative scale of the frequencies to the mean. The result\n   * will not vary as long as the overall structure of the image\n   * remains the same; this can survive gamma and color histogram\n   * adjustments without a problem.\n   */\n  let hash = \"\";\n\n  for (let x = 0; x < this.smallerSize; x++) {\n    for (let y = 0; y < this.smallerSize; y++) {\n      hash += dctVals[x][y] > avg ? \"1\" : \"0\";\n    }\n  }\n\n  return hash;\n};\n\n// DCT function stolen from http://stackoverflow.com/questions/4240490/problems-with-dct-and-idct-algorithm-in-java\n\n/**\n Convert a 32-bit integer color value to an RGBA object.\n */\nfunction intToRGBA(i) {\n  const a = i & 0xff;\n  i >>>= 8;\n  const b = i & 0xff;\n  i >>>= 8;\n  const g = i & 0xff;\n  i >>>= 8;\n  const r = i & 0xff;\n\n  return {r, g, b, a};\n}\n\nconst c = [];\nfunction initCoefficients(size) {\n  for (let i = 1; i < size; i++) {\n    c[i] = 1;\n  }\n\n  c[0] = 1 / Math.sqrt(2.0);\n}\n\nfunction applyDCT(f, size) {\n  const N = size;\n  const F = [];\n\n  for (let u = 0; u < N; u++) {\n    F[u] = [];\n    for (let v = 0; v < N; v++) {\n      let sum = 0;\n      for (let i = 0; i < N; i++) {\n        for (let j = 0; j < N; j++) {\n          sum +=\n            Math.cos(((2 * i + 1) / (2.0 * N)) * u * Math.PI) *\n            Math.cos(((2 * j + 1) / (2.0 * N)) * v * Math.PI) *\n            f[i][j];\n        }\n      }\n\n      sum *= (c[u] * c[v]) / 4;\n      F[u][v] = sum;\n    }\n  }\n\n  return F;\n}\n\nexport default ImagePHash;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAU,CAACC,IAAI,EAAEC,WAAW,EAAE;EACrC,IAAI,CAACD,IAAI,GAAG,IAAI,CAACA,IAAI,IAAIA,IAAI;EAC7B,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,IAAIA,WAAW;EAClDC,gBAAgB,CAAC,IAAI,CAACF,IAAI,CAAC;AAC7B;AAEAD,UAAU,CAACI,SAAS,CAACH,IAAI,GAAG,EAAE;AAC9BD,UAAU,CAACI,SAAS,CAACF,WAAW,GAAG,CAAC;AAEpCF,UAAU,CAACI,SAAS,CAACC,QAAQ,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAE;EAChD,IAAIC,OAAO,GAAG,CAAC;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,IAAIH,EAAE,CAACG,CAAC,CAAC,KAAKF,EAAE,CAACE,CAAC,CAAC,EAAE;MACnBD,OAAO,EAAE;IACX;EACF;EAEA,OAAOA,OAAO,GAAGF,EAAE,CAACI,MAAM;AAC5B,CAAC;;AAED;AACAV,UAAU,CAACI,SAAS,CAACO,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC5C;AACF;AACA;AACA;AACA;AACA;EACEA,GAAG,GAAGA,GAAG,CAACC,KAAK,EAAE,CAACC,MAAM,CAAC,IAAI,CAACb,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC;;EAE9C;AACF;AACA;AACA;EACEW,GAAG,CAACG,SAAS,EAAE;EAEf,MAAMC,IAAI,GAAG,EAAE;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACM,MAAM,CAACC,KAAK,EAAEF,CAAC,EAAE,EAAE;IACzCD,IAAI,CAACC,CAAC,CAAC,GAAG,EAAE;IACZ,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,CAACM,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1CJ,IAAI,CAACC,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGE,SAAS,CAACV,GAAG,CAACW,aAAa,CAACN,CAAC,EAAEG,CAAC,CAAC,CAAC,CAACI,CAAC;IACnD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,OAAO,GAAGC,QAAQ,CAACV,IAAI,EAAE,IAAI,CAACf,IAAI,CAAC;;EAEzC;AACF;AACA;AACA;AACA;EACE;AACF;AACA;AACA;AACA;AACA;EACE,IAAI0B,KAAK,GAAG,CAAC;EAEb,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACf,WAAW,EAAEe,CAAC,EAAE,EAAE;IACzC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAClB,WAAW,EAAEkB,CAAC,EAAE,EAAE;MACzCO,KAAK,IAAIF,OAAO,CAACR,CAAC,CAAC,CAACG,CAAC,CAAC;IACxB;EACF;EAEA,MAAMQ,GAAG,GAAGD,KAAK,IAAI,IAAI,CAACzB,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC;;EAEzD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI2B,IAAI,GAAG,EAAE;EAEb,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACf,WAAW,EAAEe,CAAC,EAAE,EAAE;IACzC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAClB,WAAW,EAAEkB,CAAC,EAAE,EAAE;MACzCS,IAAI,IAAIJ,OAAO,CAACR,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGQ,GAAG,GAAG,GAAG,GAAG,GAAG;IACzC;EACF;EAEA,OAAOC,IAAI;AACb,CAAC;;AAED;;AAEA;AACA;AACA;AACA,SAASP,SAAS,CAACQ,CAAC,EAAE;EACpB,MAAMC,CAAC,GAAGD,CAAC,GAAG,IAAI;EAClBA,CAAC,MAAM,CAAC;EACR,MAAMN,CAAC,GAAGM,CAAC,GAAG,IAAI;EAClBA,CAAC,MAAM,CAAC;EACR,MAAME,CAAC,GAAGF,CAAC,GAAG,IAAI;EAClBA,CAAC,MAAM,CAAC;EACR,MAAMG,CAAC,GAAGH,CAAC,GAAG,IAAI;EAElB,OAAO;IAACG,CAAC;IAAED,CAAC;IAAER,CAAC;IAAEO;EAAC,CAAC;AACrB;AAEA,MAAMG,CAAC,GAAG,EAAE;AACZ,SAAS/B,gBAAgB,CAACF,IAAI,EAAE;EAC9B,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,IAAI,EAAE6B,CAAC,EAAE,EAAE;IAC7BI,CAAC,CAACJ,CAAC,CAAC,GAAG,CAAC;EACV;EAEAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC;AAC3B;AAEA,SAASV,QAAQ,CAACW,CAAC,EAAEpC,IAAI,EAAE;EACzB,MAAMqC,CAAC,GAAGrC,IAAI;EACd,MAAMsC,CAAC,GAAG,EAAE;EAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IAC1BD,CAAC,CAACC,CAAC,CAAC,GAAG,EAAE;IACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAIC,GAAG,GAAG,CAAC;MACX,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,CAAC,EAAER,CAAC,EAAE,EAAE;QAC1B,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;UAC1BD,GAAG,IACDP,IAAI,CAACS,GAAG,CAAE,CAAC,CAAC,GAAGd,CAAC,GAAG,CAAC,KAAK,GAAG,GAAGQ,CAAC,CAAC,GAAIE,CAAC,GAAGL,IAAI,CAACU,EAAE,CAAC,GACjDV,IAAI,CAACS,GAAG,CAAE,CAAC,CAAC,GAAGD,CAAC,GAAG,CAAC,KAAK,GAAG,GAAGL,CAAC,CAAC,GAAIG,CAAC,GAAGN,IAAI,CAACU,EAAE,CAAC,GACjDR,CAAC,CAACP,CAAC,CAAC,CAACa,CAAC,CAAC;QACX;MACF;MAEAD,GAAG,IAAKR,CAAC,CAACM,CAAC,CAAC,GAAGN,CAAC,CAACO,CAAC,CAAC,GAAI,CAAC;MACxBF,CAAC,CAACC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGC,GAAG;IACf;EACF;EAEA,OAAOH,CAAC;AACV;AAEA,eAAevC,UAAU"}