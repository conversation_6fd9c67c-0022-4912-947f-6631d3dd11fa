<!DOCTYPE html>
<html>
  <head>
    <title><PERSON><PERSON> browser example 2</title>
  </head>
  <body>
    <h1>
      Demonstrates loading a relative file using <PERSON><PERSON> on a WebWorker thread
    </h1>
    <script>
      var worker = new Worker("jimp-worker.js");
      worker.onmessage = function (e) {
        var img = document.createElement("img");
        img.setAttribute("src", e.data);
        document.body.appendChild(img);
      };
      worker.postMessage("lenna.png");
    </script>
  </body>
</html>
