"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pixelmatch";
exports.ids = ["vendor-chunks/pixelmatch"];
exports.modules = {

/***/ "(rsc)/./node_modules/pixelmatch/index.js":
/*!******************************************!*\
  !*** ./node_modules/pixelmatch/index.js ***!
  \******************************************/
/***/ ((module) => {

eval("\nmodule.exports = pixelmatch;\nfunction pixelmatch(img1, img2, output, width, height, options) {\n    if (!options) options = {};\n    var threshold = options.threshold === undefined ? 0.1 : options.threshold;\n    // maximum acceptable square distance between two colors;\n    // 35215 is the maximum possible value for the YIQ difference metric\n    var maxDelta = 35215 * threshold * threshold, diff = 0;\n    // compare each pixel of one image against the other one\n    for(var y = 0; y < height; y++){\n        for(var x = 0; x < width; x++){\n            var pos = (y * width + x) * 4;\n            // squared YUV distance between colors at this pixel position\n            var delta = colorDelta(img1, img2, pos, pos);\n            // the color difference is above the threshold\n            if (delta > maxDelta) {\n                // check it's a real rendering difference or just anti-aliasing\n                if (!options.includeAA && (antialiased(img1, x, y, width, height, img2) || antialiased(img2, x, y, width, height, img1))) {\n                    // one of the pixels is anti-aliasing; draw as yellow and do not count as difference\n                    if (output) drawPixel(output, pos, 255, 255, 0);\n                } else {\n                    // found substantial difference not caused by anti-aliasing; draw it as red\n                    if (output) drawPixel(output, pos, 255, 0, 0);\n                    diff++;\n                }\n            } else if (output) {\n                // pixels are similar; draw background as grayscale image blended with white\n                var val = blend(grayPixel(img1, pos), 0.1);\n                drawPixel(output, pos, val, val, val);\n            }\n        }\n    }\n    // return the number of different pixels\n    return diff;\n}\n// check if a pixel is likely a part of anti-aliasing;\n// based on \"Anti-aliased Pixel and Intensity Slope Detector\" paper by V. Vysniauskas, 2009\nfunction antialiased(img, x1, y1, width, height, img2) {\n    var x0 = Math.max(x1 - 1, 0), y0 = Math.max(y1 - 1, 0), x2 = Math.min(x1 + 1, width - 1), y2 = Math.min(y1 + 1, height - 1), pos = (y1 * width + x1) * 4, zeroes = 0, positives = 0, negatives = 0, min = 0, max = 0, minX, minY, maxX, maxY;\n    // go through 8 adjacent pixels\n    for(var x = x0; x <= x2; x++){\n        for(var y = y0; y <= y2; y++){\n            if (x === x1 && y === y1) continue;\n            // brightness delta between the center pixel and adjacent one\n            var delta = colorDelta(img, img, pos, (y * width + x) * 4, true);\n            // count the number of equal, darker and brighter adjacent pixels\n            if (delta === 0) zeroes++;\n            else if (delta < 0) negatives++;\n            else if (delta > 0) positives++;\n            // if found more than 2 equal siblings, it's definitely not anti-aliasing\n            if (zeroes > 2) return false;\n            if (!img2) continue;\n            // remember the darkest pixel\n            if (delta < min) {\n                min = delta;\n                minX = x;\n                minY = y;\n            }\n            // remember the brightest pixel\n            if (delta > max) {\n                max = delta;\n                maxX = x;\n                maxY = y;\n            }\n        }\n    }\n    if (!img2) return true;\n    // if there are no both darker and brighter pixels among siblings, it's not anti-aliasing\n    if (negatives === 0 || positives === 0) return false;\n    // if either the darkest or the brightest pixel has more than 2 equal siblings in both images\n    // (definitely not anti-aliased), this pixel is anti-aliased\n    return !antialiased(img, minX, minY, width, height) && !antialiased(img2, minX, minY, width, height) || !antialiased(img, maxX, maxY, width, height) && !antialiased(img2, maxX, maxY, width, height);\n}\n// calculate color difference according to the paper \"Measuring perceived color difference\n// using YIQ NTSC transmission color space in mobile applications\" by Y. Kotsarenko and F. Ramos\nfunction colorDelta(img1, img2, k, m, yOnly) {\n    var a1 = img1[k + 3] / 255, a2 = img2[m + 3] / 255, r1 = blend(img1[k + 0], a1), g1 = blend(img1[k + 1], a1), b1 = blend(img1[k + 2], a1), r2 = blend(img2[m + 0], a2), g2 = blend(img2[m + 1], a2), b2 = blend(img2[m + 2], a2), y = rgb2y(r1, g1, b1) - rgb2y(r2, g2, b2);\n    if (yOnly) return y; // brightness difference only\n    var i = rgb2i(r1, g1, b1) - rgb2i(r2, g2, b2), q = rgb2q(r1, g1, b1) - rgb2q(r2, g2, b2);\n    return 0.5053 * y * y + 0.299 * i * i + 0.1957 * q * q;\n}\nfunction rgb2y(r, g, b) {\n    return r * 0.29889531 + g * 0.58662247 + b * 0.11448223;\n}\nfunction rgb2i(r, g, b) {\n    return r * 0.59597799 - g * 0.27417610 - b * 0.32180189;\n}\nfunction rgb2q(r, g, b) {\n    return r * 0.21147017 - g * 0.52261711 + b * 0.31114694;\n}\n// blend semi-transparent color with white\nfunction blend(c, a) {\n    return 255 + (c - 255) * a;\n}\nfunction drawPixel(output, pos, r, g, b) {\n    output[pos + 0] = r;\n    output[pos + 1] = g;\n    output[pos + 2] = b;\n    output[pos + 3] = 255;\n}\nfunction grayPixel(img, i) {\n    var a = img[i + 3] / 255, r = blend(img[i + 0], a), g = blend(img[i + 1], a), b = blend(img[i + 2], a);\n    return rgb2y(r, g, b);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pixelmatch/index.js\n");

/***/ })

};
;