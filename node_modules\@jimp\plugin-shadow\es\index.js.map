{"version": 3, "file": "index.js", "names": ["isNodePattern", "shadow", "options", "cb", "opacity", "size", "x", "y", "blur", "orig", "clone", "scan", "bitmap", "width", "height", "idx", "data", "constructor", "limit255", "resize", "composite", "call"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Creates a circle out of an image.\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} options (optional)\n * opacity - opacity of the shadow between 0 and 1\n * size,- of the shadow\n * blur - how blurry the shadow is\n * x- x position of shadow\n * y - y position of shadow\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jim<PERSON>} this for chaining of methods\n */\nexport default () => ({\n  shadow(options = {}, cb) {\n    if (typeof options === \"function\") {\n      cb = options;\n      options = {};\n    }\n\n    const { opacity = 0.7, size = 1.1, x = -25, y = 25, blur = 5 } = options;\n\n    // clone the image\n    const orig = this.clone();\n    const shadow = this.clone();\n\n    // turn all it's pixels black\n    shadow.scan(\n      0,\n      0,\n      shadow.bitmap.width,\n      shadow.bitmap.height,\n      (x, y, idx) => {\n        shadow.bitmap.data[idx] = 0x00;\n        shadow.bitmap.data[idx + 1] = 0x00;\n        shadow.bitmap.data[idx + 2] = 0x00;\n        // up the opacity a little,\n        shadow.bitmap.data[idx + 3] = shadow.constructor.limit255(\n          shadow.bitmap.data[idx + 3] * opacity\n        );\n\n        this.bitmap.data[idx] = 0x00;\n        this.bitmap.data[idx + 1] = 0x00;\n        this.bitmap.data[idx + 2] = 0x00;\n        this.bitmap.data[idx + 3] = 0x00;\n      }\n    );\n\n    // enlarge it. This creates a \"shadow\".\n    shadow\n      .resize(shadow.bitmap.width * size, shadow.bitmap.height * size)\n      .blur(blur);\n\n    // Then blit the \"shadow\" onto the background and the image on top of that.\n    this.composite(shadow, x, y);\n    this.composite(orig, 0, 0);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,MAAM,GAAmB;IAAA,IAAlBC,OAAO,uEAAG,CAAC,CAAC;IAAA,IAAEC,EAAE;IACrB,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MACjCC,EAAE,GAAGD,OAAO;MACZA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAM;MAAEE,OAAO,GAAG,GAAG;MAAEC,IAAI,GAAG,GAAG;MAAEC,CAAC,GAAG,CAAC,EAAE;MAAEC,CAAC,GAAG,EAAE;MAAEC,IAAI,GAAG;IAAE,CAAC,GAAGN,OAAO;;IAExE;IACA,MAAMO,IAAI,GAAG,IAAI,CAACC,KAAK,EAAE;IACzB,MAAMT,MAAM,GAAG,IAAI,CAACS,KAAK,EAAE;;IAE3B;IACAT,MAAM,CAACU,IAAI,CACT,CAAC,EACD,CAAC,EACDV,MAAM,CAACW,MAAM,CAACC,KAAK,EACnBZ,MAAM,CAACW,MAAM,CAACE,MAAM,EACpB,CAACR,CAAC,EAAEC,CAAC,EAAEQ,GAAG,KAAK;MACbd,MAAM,CAACW,MAAM,CAACI,IAAI,CAACD,GAAG,CAAC,GAAG,IAAI;MAC9Bd,MAAM,CAACW,MAAM,CAACI,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;MAClCd,MAAM,CAACW,MAAM,CAACI,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;MAClC;MACAd,MAAM,CAACW,MAAM,CAACI,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAGd,MAAM,CAACgB,WAAW,CAACC,QAAQ,CACvDjB,MAAM,CAACW,MAAM,CAACI,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAGX,OAAO,CACtC;MAED,IAAI,CAACQ,MAAM,CAACI,IAAI,CAACD,GAAG,CAAC,GAAG,IAAI;MAC5B,IAAI,CAACH,MAAM,CAACI,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;MAChC,IAAI,CAACH,MAAM,CAACI,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;MAChC,IAAI,CAACH,MAAM,CAACI,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;IAClC,CAAC,CACF;;IAED;IACAd,MAAM,CACHkB,MAAM,CAAClB,MAAM,CAACW,MAAM,CAACC,KAAK,GAAGR,IAAI,EAAEJ,MAAM,CAACW,MAAM,CAACE,MAAM,GAAGT,IAAI,CAAC,CAC/DG,IAAI,CAACA,IAAI,CAAC;;IAEb;IACA,IAAI,CAACY,SAAS,CAACnB,MAAM,EAAEK,CAAC,EAAEC,CAAC,CAAC;IAC5B,IAAI,CAACa,SAAS,CAACX,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,IAAIT,aAAa,CAACG,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACkB,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}