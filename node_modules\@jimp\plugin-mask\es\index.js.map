{"version": 3, "file": "index.js", "names": ["isNodePattern", "throwError", "mask", "src", "x", "y", "cb", "constructor", "call", "Math", "round", "w", "bitmap", "width", "h", "height", "baseImage", "scanQuiet", "sx", "sy", "idx", "destX", "destY", "dstIdx", "getPixelIndex", "data", "avg"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Masks a source image on to this image using average pixel colour. A completely black pixel on the mask will turn a pixel in the image completely transparent.\n * @param {Jimp} src the source Jimp instance\n * @param {number} x the horizontal position to blit the image\n * @param {number} y the vertical position to blit the image\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  mask(src, x = 0, y = 0, cb) {\n    if (!(src instanceof this.constructor)) {\n      return throwError.call(this, \"The source must be a Jimp image\", cb);\n    }\n\n    if (typeof x !== \"number\" || typeof y !== \"number\") {\n      return throwError.call(this, \"x and y must be numbers\", cb);\n    }\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n\n    const w = this.bitmap.width;\n    const h = this.bitmap.height;\n    const baseImage = this;\n\n    src.scanQuiet(\n      0,\n      0,\n      src.bitmap.width,\n      src.bitmap.height,\n      function (sx, sy, idx) {\n        const destX = x + sx;\n        const destY = y + sy;\n\n        if (destX >= 0 && destY >= 0 && destX < w && destY < h) {\n          const dstIdx = baseImage.getPixelIndex(destX, destY);\n          const { data } = this.bitmap;\n          const avg = (data[idx + 0] + data[idx + 1] + data[idx + 2]) / 3;\n\n          baseImage.bitmap.data[dstIdx + 3] *= avg / 255;\n        }\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,IAAI,CAACC,GAAG,EAAoB;IAAA,IAAlBC,CAAC,uEAAG,CAAC;IAAA,IAAEC,CAAC,uEAAG,CAAC;IAAA,IAAEC,EAAE;IACxB,IAAI,EAAEH,GAAG,YAAY,IAAI,CAACI,WAAW,CAAC,EAAE;MACtC,OAAON,UAAU,CAACO,IAAI,CAAC,IAAI,EAAE,iCAAiC,EAAEF,EAAE,CAAC;IACrE;IAEA,IAAI,OAAOF,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAOJ,UAAU,CAACO,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEF,EAAE,CAAC;IAC7D;;IAEA;IACAF,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;IACjBC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC;IAEjB,MAAMM,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,KAAK;IAC3B,MAAMC,CAAC,GAAG,IAAI,CAACF,MAAM,CAACG,MAAM;IAC5B,MAAMC,SAAS,GAAG,IAAI;IAEtBb,GAAG,CAACc,SAAS,CACX,CAAC,EACD,CAAC,EACDd,GAAG,CAACS,MAAM,CAACC,KAAK,EAChBV,GAAG,CAACS,MAAM,CAACG,MAAM,EACjB,UAAUG,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAE;MACrB,MAAMC,KAAK,GAAGjB,CAAC,GAAGc,EAAE;MACpB,MAAMI,KAAK,GAAGjB,CAAC,GAAGc,EAAE;MAEpB,IAAIE,KAAK,IAAI,CAAC,IAAIC,KAAK,IAAI,CAAC,IAAID,KAAK,GAAGV,CAAC,IAAIW,KAAK,GAAGR,CAAC,EAAE;QACtD,MAAMS,MAAM,GAAGP,SAAS,CAACQ,aAAa,CAACH,KAAK,EAAEC,KAAK,CAAC;QACpD,MAAM;UAAEG;QAAK,CAAC,GAAG,IAAI,CAACb,MAAM;QAC5B,MAAMc,GAAG,GAAG,CAACD,IAAI,CAACL,GAAG,GAAG,CAAC,CAAC,GAAGK,IAAI,CAACL,GAAG,GAAG,CAAC,CAAC,GAAGK,IAAI,CAACL,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC;QAE/DJ,SAAS,CAACJ,MAAM,CAACa,IAAI,CAACF,MAAM,GAAG,CAAC,CAAC,IAAIG,GAAG,GAAG,GAAG;MAChD;IACF,CAAC,CACF;IAED,IAAI1B,aAAa,CAACM,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}