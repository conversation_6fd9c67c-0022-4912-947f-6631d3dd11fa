"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-bmfont-ascii";
exports.ids = ["vendor-chunks/parse-bmfont-ascii"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse-bmfont-ascii/index.js":
/*!**************************************************!*\
  !*** ./node_modules/parse-bmfont-ascii/index.js ***!
  \**************************************************/
/***/ ((module) => {

eval("\nmodule.exports = function parseBMFontAscii(data) {\n    if (!data) throw new Error(\"no data provided\");\n    data = data.toString().trim();\n    var output = {\n        pages: [],\n        chars: [],\n        kernings: []\n    };\n    var lines = data.split(/\\r\\n?|\\n/g);\n    if (lines.length === 0) throw new Error(\"no data in BMFont file\");\n    for(var i = 0; i < lines.length; i++){\n        var lineData = splitLine(lines[i], i);\n        if (!lineData) continue;\n        if (lineData.key === \"page\") {\n            if (typeof lineData.data.id !== \"number\") throw new Error(\"malformed file at line \" + i + \" -- needs page id=N\");\n            if (typeof lineData.data.file !== \"string\") throw new Error(\"malformed file at line \" + i + ' -- needs page file=\"path\"');\n            output.pages[lineData.data.id] = lineData.data.file;\n        } else if (lineData.key === \"chars\" || lineData.key === \"kernings\") {\n        //... do nothing for these two ...\n        } else if (lineData.key === \"char\") {\n            output.chars.push(lineData.data);\n        } else if (lineData.key === \"kerning\") {\n            output.kernings.push(lineData.data);\n        } else {\n            output[lineData.key] = lineData.data;\n        }\n    }\n    return output;\n};\nfunction splitLine(line, idx) {\n    line = line.replace(/\\t+/g, \" \").trim();\n    if (!line) return null;\n    var space = line.indexOf(\" \");\n    if (space === -1) throw new Error(\"no named row at line \" + idx);\n    var key = line.substring(0, space);\n    line = line.substring(space + 1);\n    //clear \"letter\" field as it is non-standard and\n    //requires additional complexity to parse \" / = symbols\n    line = line.replace(/letter=[\\'\\\"]\\S+[\\'\\\"]/gi, \"\");\n    line = line.split(\"=\");\n    line = line.map(function(str) {\n        return str.trim().match(/(\".*?\"|[^\"\\s]+)+(?=\\s*|\\s*$)/g);\n    });\n    var data = [];\n    for(var i = 0; i < line.length; i++){\n        var dt = line[i];\n        if (i === 0) {\n            data.push({\n                key: dt[0],\n                data: \"\"\n            });\n        } else if (i === line.length - 1) {\n            data[data.length - 1].data = parseData(dt[0]);\n        } else {\n            data[data.length - 1].data = parseData(dt[0]);\n            data.push({\n                key: dt[1],\n                data: \"\"\n            });\n        }\n    }\n    var out = {\n        key: key,\n        data: {}\n    };\n    data.forEach(function(v) {\n        out.data[v.key] = v.data;\n    });\n    return out;\n}\nfunction parseData(data) {\n    if (!data || data.length === 0) return \"\";\n    if (data.indexOf('\"') === 0 || data.indexOf(\"'\") === 0) return data.substring(1, data.length - 1);\n    if (data.indexOf(\",\") !== -1) return parseIntList(data);\n    return parseInt(data, 10);\n}\nfunction parseIntList(data) {\n    return data.split(\",\").map(function(val) {\n        return parseInt(val, 10);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse-bmfont-ascii/index.js\n");

/***/ })

};
;