"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/omggif";
exports.ids = ["vendor-chunks/omggif"];
exports.modules = {

/***/ "(rsc)/./node_modules/omggif/omggif.js":
/*!***************************************!*\
  !*** ./node_modules/omggif/omggif.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// (c) <PERSON> McNamee <<EMAIL>>, 2013.\n//\n// https://github.com/deanm/omggif\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to\n// deal in the Software without restriction, including without limitation the\n// rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n// sell copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n// IN THE SOFTWARE.\n//\n// omggif is a JavaScript implementation of a GIF 89a encoder and decoder,\n// including animation and compression.  It does not rely on any specific\n// underlying system, so should run in the browser, Node, or Plask.\n\nfunction GifWriter(buf, width, height, gopts) {\n    var p = 0;\n    var gopts = gopts === undefined ? {} : gopts;\n    var loop_count = gopts.loop === undefined ? null : gopts.loop;\n    var global_palette = gopts.palette === undefined ? null : gopts.palette;\n    if (width <= 0 || height <= 0 || width > 65535 || height > 65535) throw new Error(\"Width/Height invalid.\");\n    function check_palette_and_num_colors(palette) {\n        var num_colors = palette.length;\n        if (num_colors < 2 || num_colors > 256 || num_colors & num_colors - 1) {\n            throw new Error(\"Invalid code/color length, must be power of 2 and 2 .. 256.\");\n        }\n        return num_colors;\n    }\n    // - Header.\n    buf[p++] = 0x47;\n    buf[p++] = 0x49;\n    buf[p++] = 0x46; // GIF\n    buf[p++] = 0x38;\n    buf[p++] = 0x39;\n    buf[p++] = 0x61; // 89a\n    // Handling of Global Color Table (palette) and background index.\n    var gp_num_colors_pow2 = 0;\n    var background = 0;\n    if (global_palette !== null) {\n        var gp_num_colors = check_palette_and_num_colors(global_palette);\n        while(gp_num_colors >>= 1)++gp_num_colors_pow2;\n        gp_num_colors = 1 << gp_num_colors_pow2;\n        --gp_num_colors_pow2;\n        if (gopts.background !== undefined) {\n            background = gopts.background;\n            if (background >= gp_num_colors) throw new Error(\"Background index out of range.\");\n            // The GIF spec states that a background index of 0 should be ignored, so\n            // this is probably a mistake and you really want to set it to another\n            // slot in the palette.  But actually in the end most browsers, etc end\n            // up ignoring this almost completely (including for dispose background).\n            if (background === 0) throw new Error(\"Background index explicitly passed as 0.\");\n        }\n    }\n    // - Logical Screen Descriptor.\n    // NOTE(deanm): w/h apparently ignored by implementations, but set anyway.\n    buf[p++] = width & 0xff;\n    buf[p++] = width >> 8 & 0xff;\n    buf[p++] = height & 0xff;\n    buf[p++] = height >> 8 & 0xff;\n    // NOTE: Indicates 0-bpp original color resolution (unused?).\n    buf[p++] = (global_palette !== null ? 0x80 : 0) | // Global Color Table Flag.\n    gp_num_colors_pow2; // NOTE: No sort flag (unused?).\n    buf[p++] = background; // Background Color Index.\n    buf[p++] = 0; // Pixel aspect ratio (unused?).\n    // - Global Color Table\n    if (global_palette !== null) {\n        for(var i = 0, il = global_palette.length; i < il; ++i){\n            var rgb = global_palette[i];\n            buf[p++] = rgb >> 16 & 0xff;\n            buf[p++] = rgb >> 8 & 0xff;\n            buf[p++] = rgb & 0xff;\n        }\n    }\n    if (loop_count !== null) {\n        if (loop_count < 0 || loop_count > 65535) throw new Error(\"Loop count invalid.\");\n        // Extension code, label, and length.\n        buf[p++] = 0x21;\n        buf[p++] = 0xff;\n        buf[p++] = 0x0b;\n        // NETSCAPE2.0\n        buf[p++] = 0x4e;\n        buf[p++] = 0x45;\n        buf[p++] = 0x54;\n        buf[p++] = 0x53;\n        buf[p++] = 0x43;\n        buf[p++] = 0x41;\n        buf[p++] = 0x50;\n        buf[p++] = 0x45;\n        buf[p++] = 0x32;\n        buf[p++] = 0x2e;\n        buf[p++] = 0x30;\n        // Sub-block\n        buf[p++] = 0x03;\n        buf[p++] = 0x01;\n        buf[p++] = loop_count & 0xff;\n        buf[p++] = loop_count >> 8 & 0xff;\n        buf[p++] = 0x00; // Terminator.\n    }\n    var ended = false;\n    this.addFrame = function(x, y, w, h, indexed_pixels, opts) {\n        if (ended === true) {\n            --p;\n            ended = false;\n        } // Un-end.\n        opts = opts === undefined ? {} : opts;\n        // TODO(deanm): Bounds check x, y.  Do they need to be within the virtual\n        // canvas width/height, I imagine?\n        if (x < 0 || y < 0 || x > 65535 || y > 65535) throw new Error(\"x/y invalid.\");\n        if (w <= 0 || h <= 0 || w > 65535 || h > 65535) throw new Error(\"Width/Height invalid.\");\n        if (indexed_pixels.length < w * h) throw new Error(\"Not enough pixels for the frame size.\");\n        var using_local_palette = true;\n        var palette = opts.palette;\n        if (palette === undefined || palette === null) {\n            using_local_palette = false;\n            palette = global_palette;\n        }\n        if (palette === undefined || palette === null) throw new Error(\"Must supply either a local or global palette.\");\n        var num_colors = check_palette_and_num_colors(palette);\n        // Compute the min_code_size (power of 2), destroying num_colors.\n        var min_code_size = 0;\n        while(num_colors >>= 1)++min_code_size;\n        num_colors = 1 << min_code_size; // Now we can easily get it back.\n        var delay = opts.delay === undefined ? 0 : opts.delay;\n        // From the spec:\n        //     0 -   No disposal specified. The decoder is\n        //           not required to take any action.\n        //     1 -   Do not dispose. The graphic is to be left\n        //           in place.\n        //     2 -   Restore to background color. The area used by the\n        //           graphic must be restored to the background color.\n        //     3 -   Restore to previous. The decoder is required to\n        //           restore the area overwritten by the graphic with\n        //           what was there prior to rendering the graphic.\n        //  4-7 -    To be defined.\n        // NOTE(deanm): Dispose background doesn't really work, apparently most\n        // browsers ignore the background palette index and clear to transparency.\n        var disposal = opts.disposal === undefined ? 0 : opts.disposal;\n        if (disposal < 0 || disposal > 3) throw new Error(\"Disposal out of range.\");\n        var use_transparency = false;\n        var transparent_index = 0;\n        if (opts.transparent !== undefined && opts.transparent !== null) {\n            use_transparency = true;\n            transparent_index = opts.transparent;\n            if (transparent_index < 0 || transparent_index >= num_colors) throw new Error(\"Transparent color index.\");\n        }\n        if (disposal !== 0 || use_transparency || delay !== 0) {\n            // - Graphics Control Extension\n            buf[p++] = 0x21;\n            buf[p++] = 0xf9; // Extension / Label.\n            buf[p++] = 4; // Byte size.\n            buf[p++] = disposal << 2 | (use_transparency === true ? 1 : 0);\n            buf[p++] = delay & 0xff;\n            buf[p++] = delay >> 8 & 0xff;\n            buf[p++] = transparent_index; // Transparent color index.\n            buf[p++] = 0; // Block Terminator.\n        }\n        // - Image Descriptor\n        buf[p++] = 0x2c; // Image Seperator.\n        buf[p++] = x & 0xff;\n        buf[p++] = x >> 8 & 0xff; // Left.\n        buf[p++] = y & 0xff;\n        buf[p++] = y >> 8 & 0xff; // Top.\n        buf[p++] = w & 0xff;\n        buf[p++] = w >> 8 & 0xff;\n        buf[p++] = h & 0xff;\n        buf[p++] = h >> 8 & 0xff;\n        // NOTE: No sort flag (unused?).\n        // TODO(deanm): Support interlace.\n        buf[p++] = using_local_palette === true ? 0x80 | min_code_size - 1 : 0;\n        // - Local Color Table\n        if (using_local_palette === true) {\n            for(var i = 0, il = palette.length; i < il; ++i){\n                var rgb = palette[i];\n                buf[p++] = rgb >> 16 & 0xff;\n                buf[p++] = rgb >> 8 & 0xff;\n                buf[p++] = rgb & 0xff;\n            }\n        }\n        p = GifWriterOutputLZWCodeStream(buf, p, min_code_size < 2 ? 2 : min_code_size, indexed_pixels);\n        return p;\n    };\n    this.end = function() {\n        if (ended === false) {\n            buf[p++] = 0x3b; // Trailer.\n            ended = true;\n        }\n        return p;\n    };\n    this.getOutputBuffer = function() {\n        return buf;\n    };\n    this.setOutputBuffer = function(v) {\n        buf = v;\n    };\n    this.getOutputBufferPosition = function() {\n        return p;\n    };\n    this.setOutputBufferPosition = function(v) {\n        p = v;\n    };\n}\n// Main compression routine, palette indexes -> LZW code stream.\n// |index_stream| must have at least one entry.\nfunction GifWriterOutputLZWCodeStream(buf, p, min_code_size, index_stream) {\n    buf[p++] = min_code_size;\n    var cur_subblock = p++; // Pointing at the length field.\n    var clear_code = 1 << min_code_size;\n    var code_mask = clear_code - 1;\n    var eoi_code = clear_code + 1;\n    var next_code = eoi_code + 1;\n    var cur_code_size = min_code_size + 1; // Number of bits per code.\n    var cur_shift = 0;\n    // We have at most 12-bit codes, so we should have to hold a max of 19\n    // bits here (and then we would write out).\n    var cur = 0;\n    function emit_bytes_to_buffer(bit_block_size) {\n        while(cur_shift >= bit_block_size){\n            buf[p++] = cur & 0xff;\n            cur >>= 8;\n            cur_shift -= 8;\n            if (p === cur_subblock + 256) {\n                buf[cur_subblock] = 255;\n                cur_subblock = p++;\n            }\n        }\n    }\n    function emit_code(c) {\n        cur |= c << cur_shift;\n        cur_shift += cur_code_size;\n        emit_bytes_to_buffer(8);\n    }\n    // I am not an expert on the topic, and I don't want to write a thesis.\n    // However, it is good to outline here the basic algorithm and the few data\n    // structures and optimizations here that make this implementation fast.\n    // The basic idea behind LZW is to build a table of previously seen runs\n    // addressed by a short id (herein called output code).  All data is\n    // referenced by a code, which represents one or more values from the\n    // original input stream.  All input bytes can be referenced as the same\n    // value as an output code.  So if you didn't want any compression, you\n    // could more or less just output the original bytes as codes (there are\n    // some details to this, but it is the idea).  In order to achieve\n    // compression, values greater then the input range (codes can be up to\n    // 12-bit while input only 8-bit) represent a sequence of previously seen\n    // inputs.  The decompressor is able to build the same mapping while\n    // decoding, so there is always a shared common knowledge between the\n    // encoding and decoder, which is also important for \"timing\" aspects like\n    // how to handle variable bit width code encoding.\n    //\n    // One obvious but very important consequence of the table system is there\n    // is always a unique id (at most 12-bits) to map the runs.  'A' might be\n    // 4, then 'AA' might be 10, 'AAA' 11, 'AAAA' 12, etc.  This relationship\n    // can be used for an effecient lookup strategy for the code mapping.  We\n    // need to know if a run has been seen before, and be able to map that run\n    // to the output code.  Since we start with known unique ids (input bytes),\n    // and then from those build more unique ids (table entries), we can\n    // continue this chain (almost like a linked list) to always have small\n    // integer values that represent the current byte chains in the encoder.\n    // This means instead of tracking the input bytes (AAAABCD) to know our\n    // current state, we can track the table entry for AAAABC (it is guaranteed\n    // to exist by the nature of the algorithm) and the next character D.\n    // Therefor the tuple of (table_entry, byte) is guaranteed to also be\n    // unique.  This allows us to create a simple lookup key for mapping input\n    // sequences to codes (table indices) without having to store or search\n    // any of the code sequences.  So if 'AAAA' has a table entry of 12, the\n    // tuple of ('AAAA', K) for any input byte K will be unique, and can be our\n    // key.  This leads to a integer value at most 20-bits, which can always\n    // fit in an SMI value and be used as a fast sparse array / object key.\n    // Output code for the current contents of the index buffer.\n    var ib_code = index_stream[0] & code_mask; // Load first input index.\n    var code_table = {}; // Key'd on our 20-bit \"tuple\".\n    emit_code(clear_code); // Spec says first code should be a clear code.\n    // First index already loaded, process the rest of the stream.\n    for(var i = 1, il = index_stream.length; i < il; ++i){\n        var k = index_stream[i] & code_mask;\n        var cur_key = ib_code << 8 | k; // (prev, k) unique tuple.\n        var cur_code = code_table[cur_key]; // buffer + k.\n        // Check if we have to create a new code table entry.\n        if (cur_code === undefined) {\n            // Emit index buffer (without k).\n            // This is an inline version of emit_code, because this is the core\n            // writing routine of the compressor (and V8 cannot inline emit_code\n            // because it is a closure here in a different context).  Additionally\n            // we can call emit_byte_to_buffer less often, because we can have\n            // 30-bits (from our 31-bit signed SMI), and we know our codes will only\n            // be 12-bits, so can safely have 18-bits there without overflow.\n            // emit_code(ib_code);\n            cur |= ib_code << cur_shift;\n            cur_shift += cur_code_size;\n            while(cur_shift >= 8){\n                buf[p++] = cur & 0xff;\n                cur >>= 8;\n                cur_shift -= 8;\n                if (p === cur_subblock + 256) {\n                    buf[cur_subblock] = 255;\n                    cur_subblock = p++;\n                }\n            }\n            if (next_code === 4096) {\n                emit_code(clear_code);\n                next_code = eoi_code + 1;\n                cur_code_size = min_code_size + 1;\n                code_table = {};\n            } else {\n                // Increase our variable bit code sizes if necessary.  This is a bit\n                // tricky as it is based on \"timing\" between the encoding and\n                // decoder.  From the encoders perspective this should happen after\n                // we've already emitted the index buffer and are about to create the\n                // first table entry that would overflow our current code bit size.\n                if (next_code >= 1 << cur_code_size) ++cur_code_size;\n                code_table[cur_key] = next_code++; // Insert into code table.\n            }\n            ib_code = k; // Index buffer to single input k.\n        } else {\n            ib_code = cur_code; // Index buffer to sequence in code table.\n        }\n    }\n    emit_code(ib_code); // There will still be something in the index buffer.\n    emit_code(eoi_code); // End Of Information.\n    // Flush / finalize the sub-blocks stream to the buffer.\n    emit_bytes_to_buffer(1);\n    // Finish the sub-blocks, writing out any unfinished lengths and\n    // terminating with a sub-block of length 0.  If we have already started\n    // but not yet used a sub-block it can just become the terminator.\n    if (cur_subblock + 1 === p) {\n        buf[cur_subblock] = 0;\n    } else {\n        buf[cur_subblock] = p - cur_subblock - 1;\n        buf[p++] = 0;\n    }\n    return p;\n}\nfunction GifReader(buf) {\n    var p = 0;\n    // - Header (GIF87a or GIF89a).\n    if (buf[p++] !== 0x47 || buf[p++] !== 0x49 || buf[p++] !== 0x46 || buf[p++] !== 0x38 || (buf[p++] + 1 & 0xfd) !== 0x38 || buf[p++] !== 0x61) {\n        throw new Error(\"Invalid GIF 87a/89a header.\");\n    }\n    // - Logical Screen Descriptor.\n    var width = buf[p++] | buf[p++] << 8;\n    var height = buf[p++] | buf[p++] << 8;\n    var pf0 = buf[p++]; // <Packed Fields>.\n    var global_palette_flag = pf0 >> 7;\n    var num_global_colors_pow2 = pf0 & 0x7;\n    var num_global_colors = 1 << num_global_colors_pow2 + 1;\n    var background = buf[p++];\n    buf[p++]; // Pixel aspect ratio (unused?).\n    var global_palette_offset = null;\n    var global_palette_size = null;\n    if (global_palette_flag) {\n        global_palette_offset = p;\n        global_palette_size = num_global_colors;\n        p += num_global_colors * 3; // Seek past palette.\n    }\n    var no_eof = true;\n    var frames = [];\n    var delay = 0;\n    var transparent_index = null;\n    var disposal = 0; // 0 - No disposal specified.\n    var loop_count = null;\n    this.width = width;\n    this.height = height;\n    while(no_eof && p < buf.length){\n        switch(buf[p++]){\n            case 0x21:\n                switch(buf[p++]){\n                    case 0xff:\n                        // Try if it's a Netscape block (with animation loop counter).\n                        if (buf[p] !== 0x0b || // 21 FF already read, check block size.\n                        // NETSCAPE2.0\n                        buf[p + 1] == 0x4e && buf[p + 2] == 0x45 && buf[p + 3] == 0x54 && buf[p + 4] == 0x53 && buf[p + 5] == 0x43 && buf[p + 6] == 0x41 && buf[p + 7] == 0x50 && buf[p + 8] == 0x45 && buf[p + 9] == 0x32 && buf[p + 10] == 0x2e && buf[p + 11] == 0x30 && // Sub-block\n                        buf[p + 12] == 0x03 && buf[p + 13] == 0x01 && buf[p + 16] == 0) {\n                            p += 14;\n                            loop_count = buf[p++] | buf[p++] << 8;\n                            p++; // Skip terminator.\n                        } else {\n                            p += 12;\n                            while(true){\n                                var block_size = buf[p++];\n                                // Bad block size (ex: undefined from an out of bounds read).\n                                if (!(block_size >= 0)) throw Error(\"Invalid block size\");\n                                if (block_size === 0) break; // 0 size is terminator\n                                p += block_size;\n                            }\n                        }\n                        break;\n                    case 0xf9:\n                        if (buf[p++] !== 0x4 || buf[p + 4] !== 0) throw new Error(\"Invalid graphics extension block.\");\n                        var pf1 = buf[p++];\n                        delay = buf[p++] | buf[p++] << 8;\n                        transparent_index = buf[p++];\n                        if ((pf1 & 1) === 0) transparent_index = null;\n                        disposal = pf1 >> 2 & 0x7;\n                        p++; // Skip terminator.\n                        break;\n                    case 0xfe:\n                        while(true){\n                            var block_size = buf[p++];\n                            // Bad block size (ex: undefined from an out of bounds read).\n                            if (!(block_size >= 0)) throw Error(\"Invalid block size\");\n                            if (block_size === 0) break; // 0 size is terminator\n                            // console.log(buf.slice(p, p+block_size).toString('ascii'));\n                            p += block_size;\n                        }\n                        break;\n                    default:\n                        throw new Error(\"Unknown graphic control label: 0x\" + buf[p - 1].toString(16));\n                }\n                break;\n            case 0x2c:\n                var x = buf[p++] | buf[p++] << 8;\n                var y = buf[p++] | buf[p++] << 8;\n                var w = buf[p++] | buf[p++] << 8;\n                var h = buf[p++] | buf[p++] << 8;\n                var pf2 = buf[p++];\n                var local_palette_flag = pf2 >> 7;\n                var interlace_flag = pf2 >> 6 & 1;\n                var num_local_colors_pow2 = pf2 & 0x7;\n                var num_local_colors = 1 << num_local_colors_pow2 + 1;\n                var palette_offset = global_palette_offset;\n                var palette_size = global_palette_size;\n                var has_local_palette = false;\n                if (local_palette_flag) {\n                    var has_local_palette = true;\n                    palette_offset = p; // Override with local palette.\n                    palette_size = num_local_colors;\n                    p += num_local_colors * 3; // Seek past palette.\n                }\n                var data_offset = p;\n                p++; // codesize\n                while(true){\n                    var block_size = buf[p++];\n                    // Bad block size (ex: undefined from an out of bounds read).\n                    if (!(block_size >= 0)) throw Error(\"Invalid block size\");\n                    if (block_size === 0) break; // 0 size is terminator\n                    p += block_size;\n                }\n                frames.push({\n                    x: x,\n                    y: y,\n                    width: w,\n                    height: h,\n                    has_local_palette: has_local_palette,\n                    palette_offset: palette_offset,\n                    palette_size: palette_size,\n                    data_offset: data_offset,\n                    data_length: p - data_offset,\n                    transparent_index: transparent_index,\n                    interlaced: !!interlace_flag,\n                    delay: delay,\n                    disposal: disposal\n                });\n                break;\n            case 0x3b:\n                no_eof = false;\n                break;\n            default:\n                throw new Error(\"Unknown gif block: 0x\" + buf[p - 1].toString(16));\n                break;\n        }\n    }\n    this.numFrames = function() {\n        return frames.length;\n    };\n    this.loopCount = function() {\n        return loop_count;\n    };\n    this.frameInfo = function(frame_num) {\n        if (frame_num < 0 || frame_num >= frames.length) throw new Error(\"Frame index out of range.\");\n        return frames[frame_num];\n    };\n    this.decodeAndBlitFrameBGRA = function(frame_num, pixels) {\n        var frame = this.frameInfo(frame_num);\n        var num_pixels = frame.width * frame.height;\n        var index_stream = new Uint8Array(num_pixels); // At most 8-bit indices.\n        GifReaderLZWOutputIndexStream(buf, frame.data_offset, index_stream, num_pixels);\n        var palette_offset = frame.palette_offset;\n        // NOTE(deanm): It seems to be much faster to compare index to 256 than\n        // to === null.  Not sure why, but CompareStub_EQ_STRICT shows up high in\n        // the profile, not sure if it's related to using a Uint8Array.\n        var trans = frame.transparent_index;\n        if (trans === null) trans = 256;\n        // We are possibly just blitting to a portion of the entire frame.\n        // That is a subrect within the framerect, so the additional pixels\n        // must be skipped over after we finished a scanline.\n        var framewidth = frame.width;\n        var framestride = width - framewidth;\n        var xleft = framewidth; // Number of subrect pixels left in scanline.\n        // Output indicies of the top left and bottom right corners of the subrect.\n        var opbeg = (frame.y * width + frame.x) * 4;\n        var opend = ((frame.y + frame.height) * width + frame.x) * 4;\n        var op = opbeg;\n        var scanstride = framestride * 4;\n        // Use scanstride to skip past the rows when interlacing.  This is skipping\n        // 7 rows for the first two passes, then 3 then 1.\n        if (frame.interlaced === true) {\n            scanstride += width * 4 * 7; // Pass 1.\n        }\n        var interlaceskip = 8; // Tracking the row interval in the current pass.\n        for(var i = 0, il = index_stream.length; i < il; ++i){\n            var index = index_stream[i];\n            if (xleft === 0) {\n                op += scanstride;\n                xleft = framewidth;\n                if (op >= opend) {\n                    scanstride = framestride * 4 + width * 4 * (interlaceskip - 1);\n                    // interlaceskip / 2 * 4 is interlaceskip << 1.\n                    op = opbeg + (framewidth + framestride) * (interlaceskip << 1);\n                    interlaceskip >>= 1;\n                }\n            }\n            if (index === trans) {\n                op += 4;\n            } else {\n                var r = buf[palette_offset + index * 3];\n                var g = buf[palette_offset + index * 3 + 1];\n                var b = buf[palette_offset + index * 3 + 2];\n                pixels[op++] = b;\n                pixels[op++] = g;\n                pixels[op++] = r;\n                pixels[op++] = 255;\n            }\n            --xleft;\n        }\n    };\n    // I will go to copy and paste hell one day...\n    this.decodeAndBlitFrameRGBA = function(frame_num, pixels) {\n        var frame = this.frameInfo(frame_num);\n        var num_pixels = frame.width * frame.height;\n        var index_stream = new Uint8Array(num_pixels); // At most 8-bit indices.\n        GifReaderLZWOutputIndexStream(buf, frame.data_offset, index_stream, num_pixels);\n        var palette_offset = frame.palette_offset;\n        // NOTE(deanm): It seems to be much faster to compare index to 256 than\n        // to === null.  Not sure why, but CompareStub_EQ_STRICT shows up high in\n        // the profile, not sure if it's related to using a Uint8Array.\n        var trans = frame.transparent_index;\n        if (trans === null) trans = 256;\n        // We are possibly just blitting to a portion of the entire frame.\n        // That is a subrect within the framerect, so the additional pixels\n        // must be skipped over after we finished a scanline.\n        var framewidth = frame.width;\n        var framestride = width - framewidth;\n        var xleft = framewidth; // Number of subrect pixels left in scanline.\n        // Output indicies of the top left and bottom right corners of the subrect.\n        var opbeg = (frame.y * width + frame.x) * 4;\n        var opend = ((frame.y + frame.height) * width + frame.x) * 4;\n        var op = opbeg;\n        var scanstride = framestride * 4;\n        // Use scanstride to skip past the rows when interlacing.  This is skipping\n        // 7 rows for the first two passes, then 3 then 1.\n        if (frame.interlaced === true) {\n            scanstride += width * 4 * 7; // Pass 1.\n        }\n        var interlaceskip = 8; // Tracking the row interval in the current pass.\n        for(var i = 0, il = index_stream.length; i < il; ++i){\n            var index = index_stream[i];\n            if (xleft === 0) {\n                op += scanstride;\n                xleft = framewidth;\n                if (op >= opend) {\n                    scanstride = framestride * 4 + width * 4 * (interlaceskip - 1);\n                    // interlaceskip / 2 * 4 is interlaceskip << 1.\n                    op = opbeg + (framewidth + framestride) * (interlaceskip << 1);\n                    interlaceskip >>= 1;\n                }\n            }\n            if (index === trans) {\n                op += 4;\n            } else {\n                var r = buf[palette_offset + index * 3];\n                var g = buf[palette_offset + index * 3 + 1];\n                var b = buf[palette_offset + index * 3 + 2];\n                pixels[op++] = r;\n                pixels[op++] = g;\n                pixels[op++] = b;\n                pixels[op++] = 255;\n            }\n            --xleft;\n        }\n    };\n}\nfunction GifReaderLZWOutputIndexStream(code_stream, p, output, output_length) {\n    var min_code_size = code_stream[p++];\n    var clear_code = 1 << min_code_size;\n    var eoi_code = clear_code + 1;\n    var next_code = eoi_code + 1;\n    var cur_code_size = min_code_size + 1; // Number of bits per code.\n    // NOTE: This shares the same name as the encoder, but has a different\n    // meaning here.  Here this masks each code coming from the code stream.\n    var code_mask = (1 << cur_code_size) - 1;\n    var cur_shift = 0;\n    var cur = 0;\n    var op = 0; // Output pointer.\n    var subblock_size = code_stream[p++];\n    // TODO(deanm): Would using a TypedArray be any faster?  At least it would\n    // solve the fast mode / backing store uncertainty.\n    // var code_table = Array(4096);\n    var code_table = new Int32Array(4096); // Can be signed, we only use 20 bits.\n    var prev_code = null; // Track code-1.\n    while(true){\n        // Read up to two bytes, making sure we always 12-bits for max sized code.\n        while(cur_shift < 16){\n            if (subblock_size === 0) break; // No more data to be read.\n            cur |= code_stream[p++] << cur_shift;\n            cur_shift += 8;\n            if (subblock_size === 1) {\n                subblock_size = code_stream[p++]; // Next subblock.\n            } else {\n                --subblock_size;\n            }\n        }\n        // TODO(deanm): We should never really get here, we should have received\n        // and EOI.\n        if (cur_shift < cur_code_size) break;\n        var code = cur & code_mask;\n        cur >>= cur_code_size;\n        cur_shift -= cur_code_size;\n        // TODO(deanm): Maybe should check that the first code was a clear code,\n        // at least this is what you're supposed to do.  But actually our encoder\n        // now doesn't emit a clear code first anyway.\n        if (code === clear_code) {\n            // We don't actually have to clear the table.  This could be a good idea\n            // for greater error checking, but we don't really do any anyway.  We\n            // will just track it with next_code and overwrite old entries.\n            next_code = eoi_code + 1;\n            cur_code_size = min_code_size + 1;\n            code_mask = (1 << cur_code_size) - 1;\n            // Don't update prev_code ?\n            prev_code = null;\n            continue;\n        } else if (code === eoi_code) {\n            break;\n        }\n        // We have a similar situation as the decoder, where we want to store\n        // variable length entries (code table entries), but we want to do in a\n        // faster manner than an array of arrays.  The code below stores sort of a\n        // linked list within the code table, and then \"chases\" through it to\n        // construct the dictionary entries.  When a new entry is created, just the\n        // last byte is stored, and the rest (prefix) of the entry is only\n        // referenced by its table entry.  Then the code chases through the\n        // prefixes until it reaches a single byte code.  We have to chase twice,\n        // first to compute the length, and then to actually copy the data to the\n        // output (backwards, since we know the length).  The alternative would be\n        // storing something in an intermediate stack, but that doesn't make any\n        // more sense.  I implemented an approach where it also stored the length\n        // in the code table, although it's a bit tricky because you run out of\n        // bits (12 + 12 + 8), but I didn't measure much improvements (the table\n        // entries are generally not the long).  Even when I created benchmarks for\n        // very long table entries the complexity did not seem worth it.\n        // The code table stores the prefix entry in 12 bits and then the suffix\n        // byte in 8 bits, so each entry is 20 bits.\n        var chase_code = code < next_code ? code : prev_code;\n        // Chase what we will output, either {CODE} or {CODE-1}.\n        var chase_length = 0;\n        var chase = chase_code;\n        while(chase > clear_code){\n            chase = code_table[chase] >> 8;\n            ++chase_length;\n        }\n        var k = chase;\n        var op_end = op + chase_length + (chase_code !== code ? 1 : 0);\n        if (op_end > output_length) {\n            console.log(\"Warning, gif stream longer than expected.\");\n            return;\n        }\n        // Already have the first byte from the chase, might as well write it fast.\n        output[op++] = k;\n        op += chase_length;\n        var b = op; // Track pointer, writing backwards.\n        if (chase_code !== code) output[op++] = k;\n        chase = chase_code;\n        while(chase_length--){\n            chase = code_table[chase];\n            output[--b] = chase & 0xff; // Write backwards.\n            chase >>= 8; // Pull down to the prefix code.\n        }\n        if (prev_code !== null && next_code < 4096) {\n            code_table[next_code++] = prev_code << 8 | k;\n            // TODO(deanm): Figure out this clearing vs code growth logic better.  I\n            // have an feeling that it should just happen somewhere else, for now it\n            // is awkward between when we grow past the max and then hit a clear code.\n            // For now just check if we hit the max 12-bits (then a clear code should\n            // follow, also of course encoded in 12-bits).\n            if (next_code >= code_mask + 1 && cur_code_size < 12) {\n                ++cur_code_size;\n                code_mask = code_mask << 1 | 1;\n            }\n        }\n        prev_code = code;\n    }\n    if (op !== output_length) {\n        console.log(\"Warning, gif stream shorter than expected.\");\n    }\n    return output;\n}\n// CommonJS.\ntry {\n    exports.GifWriter = GifWriter;\n    exports.GifReader = GifReader;\n} catch (e) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/omggif/omggif.js\n");

/***/ })

};
;