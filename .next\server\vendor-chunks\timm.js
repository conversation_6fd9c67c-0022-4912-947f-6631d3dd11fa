"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/timm";
exports.ids = ["vendor-chunks/timm"];
exports.modules = {

/***/ "(rsc)/./node_modules/timm/lib/timm.js":
/*!***************************************!*\
  !*** ./node_modules/timm/lib/timm.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.clone = clone;\nexports.addLast = addLast;\nexports.addFirst = addFirst;\nexports.removeLast = removeLast;\nexports.removeFirst = removeFirst;\nexports.insert = insert;\nexports.removeAt = removeAt;\nexports.replaceAt = replaceAt;\nexports.getIn = getIn;\nexports.set = set;\nexports.setIn = setIn;\nexports.update = update;\nexports.updateIn = updateIn;\nexports.merge = merge;\nexports.mergeDeep = mergeDeep;\nexports.mergeIn = mergeIn;\nexports.omit = omit;\nexports.addDefaults = addDefaults;\nexports[\"default\"] = void 0;\n/* eslint-disable @typescript-eslint/ban-types */ /*!\n * Timm\n *\n * Immutability helpers with fast reads and acceptable writes.\n *\n * @copyright Guillermo Grau Panea 2016\n * @license MIT\n */ const INVALID_ARGS = \"INVALID_ARGS\";\nconst IS_DEV = \"development\" !== \"production\";\n// ===============================================\n// ### Helpers\n// ===============================================\nfunction throwStr(msg) {\n    throw new Error(msg);\n}\nfunction getKeysAndSymbols(obj) {\n    const keys = Object.keys(obj);\n    if (Object.getOwnPropertySymbols) {\n        // @ts-ignore\n        return keys.concat(Object.getOwnPropertySymbols(obj));\n    }\n    return keys;\n}\nconst hasOwnProperty = {}.hasOwnProperty;\nfunction clone(obj0) {\n    // As array\n    if (Array.isArray(obj0)) return obj0.slice(); // As object\n    const obj = obj0;\n    const keys = getKeysAndSymbols(obj);\n    const out = {};\n    for(let i = 0; i < keys.length; i++){\n        const key = keys[i];\n        out[key] = obj[key];\n    } // @ts-ignore (see type tests)\n    return out;\n} // Custom guard\nfunction isObject(o) {\n    return o != null && typeof o === \"object\";\n} // _deepFreeze = (obj) ->\n//   Object.freeze obj\n//   for key in Object.getOwnPropertyNames obj\n//     val = obj[key]\n//     if isObject(val) and not Object.isFrozen val\n//       _deepFreeze val\n//   obj\n// ===============================================\n// -- ### Arrays\n// ===============================================\n// -- #### addLast()\n// -- Returns a new array with an appended item or items.\n// --\n// -- Usage: `addLast(array, val)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = addLast(arr, 'c')\n// -- // ['a', 'b', 'c']\n// -- arr2 === arr\n// -- // false\n// -- arr3 = addLast(arr, ['c', 'd'])\n// -- // ['a', 'b', 'c', 'd']\n// -- ```\n// `array.concat(val)` also handles the scalar case,\n// but is apparently very slow\nfunction addLast(array, val) {\n    if (Array.isArray(val)) return array.concat(val);\n    return array.concat([\n        val\n    ]);\n} // -- #### addFirst()\n// -- Returns a new array with a prepended item or items.\n// --\n// -- Usage: `addFirst(array, val)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = addFirst(arr, 'c')\n// -- // ['c', 'a', 'b']\n// -- arr2 === arr\n// -- // false\n// -- arr3 = addFirst(arr, ['c', 'd'])\n// -- // ['c', 'd', 'a', 'b']\n// -- ```\nfunction addFirst(array, val) {\n    if (Array.isArray(val)) return val.concat(array);\n    return [\n        val\n    ].concat(array);\n} // -- #### removeLast()\n// -- Returns a new array removing the last item.\n// --\n// -- Usage: `removeLast(array)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = removeLast(arr)\n// -- // ['a']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same array is returned if there are no changes:\n// -- arr3 = []\n// -- removeLast(arr3) === arr3\n// -- // true\n// -- ```\nfunction removeLast(array) {\n    if (!array.length) return array;\n    return array.slice(0, array.length - 1);\n} // -- #### removeFirst()\n// -- Returns a new array removing the first item.\n// --\n// -- Usage: `removeFirst(array)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = removeFirst(arr)\n// -- // ['b']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same array is returned if there are no changes:\n// -- arr3 = []\n// -- removeFirst(arr3) === arr3\n// -- // true\n// -- ```\nfunction removeFirst(array) {\n    if (!array.length) return array;\n    return array.slice(1);\n} // -- #### insert()\n// -- Returns a new array obtained by inserting an item or items\n// -- at a specified index.\n// --\n// -- Usage: `insert(array, idx, val)`\n// --\n// -- ```js\n// -- arr = ['a', 'b', 'c']\n// -- arr2 = insert(arr, 1, 'd')\n// -- // ['a', 'd', 'b', 'c']\n// -- arr2 === arr\n// -- // false\n// -- insert(arr, 1, ['d', 'e'])\n// -- // ['a', 'd', 'e', 'b', 'c']\n// -- ```\nfunction insert(array, idx, val) {\n    return array.slice(0, idx).concat(Array.isArray(val) ? val : [\n        val\n    ]).concat(array.slice(idx));\n} // -- #### removeAt()\n// -- Returns a new array obtained by removing an item at\n// -- a specified index.\n// --\n// -- Usage: `removeAt(array, idx)`\n// --\n// -- ```js\n// -- arr = ['a', 'b', 'c']\n// -- arr2 = removeAt(arr, 1)\n// -- // ['a', 'c']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same array is returned if there are no changes:\n// -- removeAt(arr, 4) === arr\n// -- // true\n// -- ```\nfunction removeAt(array, idx) {\n    if (idx >= array.length || idx < 0) return array;\n    return array.slice(0, idx).concat(array.slice(idx + 1));\n} // -- #### replaceAt()\n// -- Returns a new array obtained by replacing an item at\n// -- a specified index. If the provided item is the same as\n// -- (*referentially equal to*) the previous item at that position,\n// -- the original array is returned.\n// --\n// -- Usage: `replaceAt(array, idx, newItem)`\n// --\n// -- ```js\n// -- arr = ['a', 'b', 'c']\n// -- arr2 = replaceAt(arr, 1, 'd')\n// -- // ['a', 'd', 'c']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- replaceAt(arr, 1, 'b') === arr\n// -- // true\n// -- ```\nfunction replaceAt(array, idx, newItem) {\n    if (array[idx] === newItem) return array;\n    const len = array.length;\n    const result = Array(len);\n    for(let i = 0; i < len; i++){\n        result[i] = array[i];\n    }\n    result[idx] = newItem;\n    return result;\n} // ===============================================\n// -- ### Collections (objects and arrays)\n// ===============================================\n// -- #### getIn()\n// -- Returns a value from an object at a given path. Works with\n// -- nested arrays and objects. If the path does not exist, it returns\n// -- `undefined`.\n// --\n// -- Usage: `getIn(obj, path)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: ['a', 'b', 'c'] }\n// -- getIn(obj, ['d', 'd1'])\n// -- // 3\n// -- getIn(obj, ['e', 1])\n// -- // 'b'\n// -- ```\nfunction getIn(obj, path) {\n    if (!Array.isArray(path)) {\n        throwStr(IS_DEV ? \"A path array should be provided when calling getIn()\" : INVALID_ARGS);\n    }\n    if (obj == null) return undefined;\n    let ptr = obj;\n    for(let i = 0; i < path.length; i++){\n        const key = path[i];\n        ptr = ptr != null ? ptr[key] : undefined;\n        if (ptr === undefined) return ptr;\n    }\n    return ptr;\n} // -- #### set()\n// -- Returns a new object with a modified attribute.\n// -- If the provided value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// --\n// -- Usage: `set(obj, key, val)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, c: 3 }\n// -- obj2 = set(obj, 'b', 5)\n// -- // { a: 1, b: 5, c: 3 }\n// -- obj2 === obj\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- set(obj, 'b', 2) === obj\n// -- // true\n// -- ```\n// When called with an undefined/null `obj`, `set()` returns either\n// a single-element array, or a single-key object\n// Implementation\nfunction set(obj0, key, val) {\n    let obj = obj0;\n    if (obj == null) obj = typeof key === \"number\" ? [] : {};\n    if (obj[key] === val) return obj;\n    const obj2 = clone(obj);\n    obj2[key] = val;\n    return obj2;\n} // -- #### setIn()\n// -- Returns a new object with a modified **nested** attribute.\n// --\n// -- Notes:\n// --\n// -- * If the provided value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// -- * If the path does not exist, it will be created before setting\n// -- the new value.\n// --\n// -- Usage: `setIn(obj, path, val)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }\n// -- obj2 = setIn(obj, ['d', 'd1'], 4)\n// -- // { a: 1, b: 2, d: { d1: 4, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }\n// -- obj2 === obj\n// -- // false\n// -- obj2.d === obj.d\n// -- // false\n// -- obj2.e === obj.e\n// -- // true\n// --\n// -- // The same object is returned if there are no changes:\n// -- obj3 = setIn(obj, ['d', 'd1'], 3)\n// -- // { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }\n// -- obj3 === obj\n// -- // true\n// -- obj3.d === obj.d\n// -- // true\n// -- obj3.e === obj.e\n// -- // true\n// --\n// -- // ... unknown paths create intermediate keys. Numeric segments are treated as array indices:\n// -- setIn({ a: 3 }, ['unknown', 0, 'path'], 4)\n// -- // { a: 3, unknown: [{ path: 4 }] }\n// -- ```\nfunction setIn(obj, path, val) {\n    if (!path.length) return val;\n    return doSetIn(obj, path, val, 0);\n}\nfunction doSetIn(obj, path, val, idx) {\n    let newValue;\n    const key = path[idx];\n    if (idx === path.length - 1) {\n        newValue = val;\n    } else {\n        const nestedObj = isObject(obj) && isObject(obj[key]) ? obj[key] : typeof path[idx + 1] === \"number\" ? [] : {};\n        newValue = doSetIn(nestedObj, path, val, idx + 1);\n    }\n    return set(obj, key, newValue);\n} // -- #### update()\n// -- Returns a new object with a modified attribute,\n// -- calculated via a user-provided callback based on the current value.\n// -- If the calculated value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// --\n// -- Usage: `update(obj, key, fnUpdate)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, c: 3 }\n// -- obj2 = update(obj, 'b', (val) => val + 1)\n// -- // { a: 1, b: 3, c: 3 }\n// -- obj2 === obj\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- update(obj, 'b', (val) => val) === obj\n// -- // true\n// -- ```\nfunction update(obj, key, fnUpdate) {\n    const prevVal = obj == null ? undefined : obj[key];\n    const nextVal = fnUpdate(prevVal);\n    return set(obj, key, nextVal);\n} // -- #### updateIn()\n// -- Returns a new object with a modified **nested** attribute,\n// -- calculated via a user-provided callback based on the current value.\n// -- If the calculated value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// --\n// -- Usage: `updateIn<T: ArrayOrObject>(obj: T, path: Array<Key>,\n// -- fnUpdate: (prevValue: any) => any): T`\n// --\n// -- ```js\n// -- obj = { a: 1, d: { d1: 3, d2: 4 } }\n// -- obj2 = updateIn(obj, ['d', 'd1'], (val) => val + 1)\n// -- // { a: 1, d: { d1: 4, d2: 4 } }\n// -- obj2 === obj\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- obj3 = updateIn(obj, ['d', 'd1'], (val) => val)\n// -- // { a: 1, d: { d1: 3, d2: 4 } }\n// -- obj3 === obj\n// -- // true\n// -- ```\nfunction updateIn(obj, path, fnUpdate) {\n    const prevVal = getIn(obj, path);\n    const nextVal = fnUpdate(prevVal);\n    return setIn(obj, path, nextVal);\n} // -- #### merge()\n// -- Returns a new object built as follows: the overlapping keys from the\n// -- second one overwrite the corresponding entries from the first one.\n// -- Similar to `Object.assign()`, but immutable.\n// --\n// -- Usage:\n// --\n// -- * `merge(obj1, obj2)`\n// -- * `merge(obj1, ...objects)`\n// --\n// -- The unmodified `obj1` is returned if `obj2` does not *provide something\n// -- new to* `obj1`, i.e. if either of the following\n// -- conditions are true:\n// --\n// -- * `obj2` is `null` or `undefined`\n// -- * `obj2` is an object, but it is empty\n// -- * All attributes of `obj2` are `undefined`\n// -- * All attributes of `obj2` are referentially equal to the\n// --   corresponding attributes of `obj1`\n// --\n// -- Note that `undefined` attributes in `obj2` do not modify the\n// -- corresponding attributes in `obj1`.\n// --\n// -- ```js\n// -- obj1 = { a: 1, b: 2, c: 3 }\n// -- obj2 = { c: 4, d: 5 }\n// -- obj3 = merge(obj1, obj2)\n// -- // { a: 1, b: 2, c: 4, d: 5 }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- merge(obj1, { c: 3 }) === obj1\n// -- // true\n// -- ```\n// Signatures:\n// - 1 arg\n// Implementation\nfunction merge(a, b, c, d, e, f, ...rest) {\n    return rest.length ? doMerge.call(null, false, false, a, b, c, d, e, f, ...rest) : doMerge(false, false, a, b, c, d, e, f);\n} // -- #### mergeDeep()\n// -- Returns a new object built as follows: the overlapping keys from the\n// -- second one overwrite the corresponding entries from the first one.\n// -- If both the first and second entries are objects they are merged recursively.\n// -- Similar to `Object.assign()`, but immutable, and deeply merging.\n// --\n// -- Usage:\n// --\n// -- * `mergeDeep(obj1, obj2)`\n// -- * `mergeDeep(obj1, ...objects)`\n// --\n// -- The unmodified `obj1` is returned if `obj2` does not *provide something\n// -- new to* `obj1`, i.e. if either of the following\n// -- conditions are true:\n// --\n// -- * `obj2` is `null` or `undefined`\n// -- * `obj2` is an object, but it is empty\n// -- * All attributes of `obj2` are `undefined`\n// -- * All attributes of `obj2` are referentially equal to the\n// --   corresponding attributes of `obj1`\n// --\n// -- Note that `undefined` attributes in `obj2` do not modify the\n// -- corresponding attributes in `obj1`.\n// --\n// -- ```js\n// -- obj1 = { a: 1, b: 2, c: { a: 1 } }\n// -- obj2 = { b: 3, c: { b: 2 } }\n// -- obj3 = mergeDeep(obj1, obj2)\n// -- // { a: 1, b: 3, c: { a: 1, b: 2 }  }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- mergeDeep(obj1, { c: { a: 1 } }) === obj1\n// -- // true\n// -- ```\nfunction mergeDeep(a, b, c, d, e, f, ...rest) {\n    return rest.length ? doMerge.call(null, false, true, a, b, c, d, e, f, ...rest) : doMerge(false, true, a, b, c, d, e, f);\n} // -- #### mergeIn()\n// -- Similar to `merge()`, but merging the value at a given nested path.\n// --\n// -- Usage examples:\n// --\n// -- * `mergeIn(obj1, path, obj2)`\n// -- * `mergeIn(obj1, path, ...objects)`\n// --\n// -- ```js\n// -- obj1 = { a: 1, d: { b: { d1: 3, d2: 4 } } }\n// -- obj2 = { d3: 5 }\n// -- obj3 = mergeIn(obj1, ['d', 'b'], obj2)\n// -- // { a: 1, d: { b: { d1: 3, d2: 4, d3: 5 } } }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- mergeIn(obj1, ['d', 'b'], { d2: 4 }) === obj1\n// -- // true\n// -- ```\nfunction mergeIn(a, path, b, c, d, e, f, ...rest) {\n    let prevVal = getIn(a, path);\n    if (prevVal == null) prevVal = {};\n    let nextVal;\n    if (rest.length) {\n        nextVal = doMerge.call(null, false, false, prevVal, b, c, d, e, f, ...rest);\n    } else {\n        nextVal = doMerge(false, false, prevVal, b, c, d, e, f);\n    }\n    return setIn(a, path, nextVal);\n} // -- #### omit()\n// -- Returns an object excluding one or several attributes.\n// --\n// -- Usage: `omit(obj, attrs)`\n//\n// -- ```js\n// -- obj = { a: 1, b: 2, c: 3, d: 4 }\n// -- omit(obj, 'a')\n// -- // { b: 2, c: 3, d: 4 }\n// -- omit(obj, ['b', 'c'])\n// -- // { a: 1, d: 4 }\n// --\n// -- // The same object is returned if there are no changes:\n// -- omit(obj, 'z') === obj1\n// -- // true\n// -- ```\nfunction omit(obj, attrs) {\n    const omitList = Array.isArray(attrs) ? attrs : [\n        attrs\n    ];\n    let fDoSomething = false;\n    for(let i = 0; i < omitList.length; i++){\n        if (hasOwnProperty.call(obj, omitList[i])) {\n            fDoSomething = true;\n            break;\n        }\n    }\n    if (!fDoSomething) return obj;\n    const out = {};\n    const keys = getKeysAndSymbols(obj);\n    for(let i = 0; i < keys.length; i++){\n        const key = keys[i];\n        if (omitList.indexOf(key) >= 0) continue;\n        out[key] = obj[key];\n    }\n    return out;\n} // -- #### addDefaults()\n// -- Returns a new object built as follows: `undefined` keys in the first one\n// -- are filled in with the corresponding values from the second one\n// -- (even if they are `null`).\n// --\n// -- Usage:\n// --\n// -- * `addDefaults(obj, defaults)`\n// -- * `addDefaults(obj, ...defaultObjects)`\n// --\n// -- ```js\n// -- obj1 = { a: 1, b: 2, c: 3 }\n// -- obj2 = { c: 4, d: 5, e: null }\n// -- obj3 = addDefaults(obj1, obj2)\n// -- // { a: 1, b: 2, c: 3, d: 5, e: null }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- addDefaults(obj1, { c: 4 }) === obj1\n// -- // true\n// -- ```\n// Signatures:\n// - 2 args\n// Implementation and catch-all\nfunction addDefaults(a, b, c, d, e, f, ...rest) {\n    return rest.length ? doMerge.call(null, true, false, a, b, c, d, e, f, ...rest) : doMerge(true, false, a, b, c, d, e, f);\n}\nfunction doMerge(fAddDefaults, fDeep, first, ...rest) {\n    let out = first;\n    if (!(out != null)) {\n        throwStr(IS_DEV ? \"At least one object should be provided to merge()\" : INVALID_ARGS);\n    }\n    let fChanged = false;\n    for(let idx = 0; idx < rest.length; idx++){\n        const obj = rest[idx];\n        if (obj == null) continue;\n        const keys = getKeysAndSymbols(obj);\n        if (!keys.length) continue;\n        for(let j = 0; j <= keys.length; j++){\n            const key = keys[j];\n            if (fAddDefaults && out[key] !== undefined) continue;\n            let nextVal = obj[key];\n            if (fDeep && isObject(out[key]) && isObject(nextVal)) {\n                nextVal = doMerge(fAddDefaults, fDeep, out[key], nextVal);\n            }\n            if (nextVal === undefined || nextVal === out[key]) continue;\n            if (!fChanged) {\n                fChanged = true;\n                out = clone(out);\n            }\n            out[key] = nextVal;\n        }\n    }\n    return out;\n} // ===============================================\n// ### Public API\n// ===============================================\nconst timm = {\n    clone,\n    addLast,\n    addFirst,\n    removeLast,\n    removeFirst,\n    insert,\n    removeAt,\n    replaceAt,\n    getIn,\n    set,\n    setIn,\n    update,\n    updateIn,\n    merge,\n    mergeDeep,\n    mergeIn,\n    omit,\n    addDefaults\n};\nvar _default = timm;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/timm/lib/timm.js\n");

/***/ })

};
;