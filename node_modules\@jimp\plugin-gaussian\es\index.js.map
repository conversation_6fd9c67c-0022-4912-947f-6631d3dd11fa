{"version": 3, "file": "index.js", "names": ["isNodePattern", "throwError", "gaussian", "r", "cb", "call", "rs", "Math", "ceil", "range", "rr2", "rr2pi", "PI", "weights", "y", "x", "dsq", "exp", "bitmap", "height", "width", "red", "green", "blue", "alpha", "wsum", "iy", "ix", "x1", "min", "max", "y1", "weight", "idx", "data", "round"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Applies a true Gaussian blur to the image (warning: this is VERY slow)\n * @param {number} r the pixel radius of the blur\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  gaussian(r, cb) {\n    // http://blog.ivank.net/fastest-gaussian-blur.html\n    if (typeof r !== \"number\") {\n      return throwError.call(this, \"r must be a number\", cb);\n    }\n\n    if (r < 1) {\n      return throwError.call(this, \"r must be greater than 0\", cb);\n    }\n\n    const rs = Math.ceil(r * 2.57); // significant radius\n    const range = rs * 2 + 1;\n    const rr2 = r * r * 2;\n    const rr2pi = rr2 * Math.PI;\n\n    const weights = [];\n\n    for (let y = 0; y < range; y++) {\n      weights[y] = [];\n      for (let x = 0; x < range; x++) {\n        const dsq = (x - rs) ** 2 + (y - rs) ** 2;\n        weights[y][x] = Math.exp(-dsq / rr2) / rr2pi;\n      }\n    }\n\n    for (let y = 0; y < this.bitmap.height; y++) {\n      for (let x = 0; x < this.bitmap.width; x++) {\n        let red = 0;\n        let green = 0;\n        let blue = 0;\n        let alpha = 0;\n        let wsum = 0;\n\n        for (let iy = 0; iy < range; iy++) {\n          for (let ix = 0; ix < range; ix++) {\n            const x1 = Math.min(\n              this.bitmap.width - 1,\n              Math.max(0, ix + x - rs)\n            );\n            const y1 = Math.min(\n              this.bitmap.height - 1,\n              Math.max(0, iy + y - rs)\n            );\n            const weight = weights[iy][ix];\n            const idx = (y1 * this.bitmap.width + x1) << 2;\n\n            red += this.bitmap.data[idx] * weight;\n            green += this.bitmap.data[idx + 1] * weight;\n            blue += this.bitmap.data[idx + 2] * weight;\n            alpha += this.bitmap.data[idx + 3] * weight;\n            wsum += weight;\n          }\n\n          const idx = (y * this.bitmap.width + x) << 2;\n\n          this.bitmap.data[idx] = Math.round(red / wsum);\n          this.bitmap.data[idx + 1] = Math.round(green / wsum);\n          this.bitmap.data[idx + 2] = Math.round(blue / wsum);\n          this.bitmap.data[idx + 3] = Math.round(alpha / wsum);\n        }\n      }\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,QAAQ,CAACC,CAAC,EAAEC,EAAE,EAAE;IACd;IACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAOF,UAAU,CAACI,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAED,EAAE,CAAC;IACxD;IAEA,IAAID,CAAC,GAAG,CAAC,EAAE;MACT,OAAOF,UAAU,CAACI,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAED,EAAE,CAAC;IAC9D;IAEA,MAAME,EAAE,GAAGC,IAAI,CAACC,IAAI,CAACL,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAChC,MAAMM,KAAK,GAAGH,EAAE,GAAG,CAAC,GAAG,CAAC;IACxB,MAAMI,GAAG,GAAGP,CAAC,GAAGA,CAAC,GAAG,CAAC;IACrB,MAAMQ,KAAK,GAAGD,GAAG,GAAGH,IAAI,CAACK,EAAE;IAE3B,MAAMC,OAAO,GAAG,EAAE;IAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,EAAEK,CAAC,EAAE,EAAE;MAC9BD,OAAO,CAACC,CAAC,CAAC,GAAG,EAAE;MACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,EAAE,EAAE;QAC9B,MAAMC,GAAG,GAAG,CAACD,CAAC,GAAGT,EAAE,KAAK,CAAC,GAAG,CAACQ,CAAC,GAAGR,EAAE,KAAK,CAAC;QACzCO,OAAO,CAACC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGR,IAAI,CAACU,GAAG,CAAC,CAACD,GAAG,GAAGN,GAAG,CAAC,GAAGC,KAAK;MAC9C;IACF;IAEA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACI,MAAM,CAACC,MAAM,EAAEL,CAAC,EAAE,EAAE;MAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACG,MAAM,CAACE,KAAK,EAAEL,CAAC,EAAE,EAAE;QAC1C,IAAIM,GAAG,GAAG,CAAC;QACX,IAAIC,KAAK,GAAG,CAAC;QACb,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,KAAK,GAAG,CAAC;QACb,IAAIC,IAAI,GAAG,CAAC;QAEZ,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGjB,KAAK,EAAEiB,EAAE,EAAE,EAAE;UACjC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGlB,KAAK,EAAEkB,EAAE,EAAE,EAAE;YACjC,MAAMC,EAAE,GAAGrB,IAAI,CAACsB,GAAG,CACjB,IAAI,CAACX,MAAM,CAACE,KAAK,GAAG,CAAC,EACrBb,IAAI,CAACuB,GAAG,CAAC,CAAC,EAAEH,EAAE,GAAGZ,CAAC,GAAGT,EAAE,CAAC,CACzB;YACD,MAAMyB,EAAE,GAAGxB,IAAI,CAACsB,GAAG,CACjB,IAAI,CAACX,MAAM,CAACC,MAAM,GAAG,CAAC,EACtBZ,IAAI,CAACuB,GAAG,CAAC,CAAC,EAAEJ,EAAE,GAAGZ,CAAC,GAAGR,EAAE,CAAC,CACzB;YACD,MAAM0B,MAAM,GAAGnB,OAAO,CAACa,EAAE,CAAC,CAACC,EAAE,CAAC;YAC9B,MAAMM,GAAG,GAAIF,EAAE,GAAG,IAAI,CAACb,MAAM,CAACE,KAAK,GAAGQ,EAAE,IAAK,CAAC;YAE9CP,GAAG,IAAI,IAAI,CAACH,MAAM,CAACgB,IAAI,CAACD,GAAG,CAAC,GAAGD,MAAM;YACrCV,KAAK,IAAI,IAAI,CAACJ,MAAM,CAACgB,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAGD,MAAM;YAC3CT,IAAI,IAAI,IAAI,CAACL,MAAM,CAACgB,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAGD,MAAM;YAC1CR,KAAK,IAAI,IAAI,CAACN,MAAM,CAACgB,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAGD,MAAM;YAC3CP,IAAI,IAAIO,MAAM;UAChB;UAEA,MAAMC,GAAG,GAAInB,CAAC,GAAG,IAAI,CAACI,MAAM,CAACE,KAAK,GAAGL,CAAC,IAAK,CAAC;UAE5C,IAAI,CAACG,MAAM,CAACgB,IAAI,CAACD,GAAG,CAAC,GAAG1B,IAAI,CAAC4B,KAAK,CAACd,GAAG,GAAGI,IAAI,CAAC;UAC9C,IAAI,CAACP,MAAM,CAACgB,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG1B,IAAI,CAAC4B,KAAK,CAACb,KAAK,GAAGG,IAAI,CAAC;UACpD,IAAI,CAACP,MAAM,CAACgB,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG1B,IAAI,CAAC4B,KAAK,CAACZ,IAAI,GAAGE,IAAI,CAAC;UACnD,IAAI,CAACP,MAAM,CAACgB,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG1B,IAAI,CAAC4B,KAAK,CAACX,KAAK,GAAGC,IAAI,CAAC;QACtD;MACF;IACF;IAEA,IAAIzB,aAAa,CAACI,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}