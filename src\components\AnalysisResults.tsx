'use client'

import { useState } from 'react'
import { TrendingUp, TrendingDown, Target, Shield, DollarSign, Clock, AlertTriangle, CheckCircle, MessageSquare } from 'lucide-react'
import FeedbackModal from './FeedbackModal'

interface AnalysisResultsProps {
  results: {
    direction: 'BUY' | 'SELL' | 'NEUTRAL'
    confidence: number
    entry: number
    stopLoss: number
    takeProfits: {
      tp1: number
      tp2: number
      tp3: number
    }
    riskReward: {
      tp1: number
      tp2: number
      tp3: number
    }
    timeframe: string
    strategy: string
    reasoning: {
      marketStructure: string
      orderBlocks: string[]
      fairValueGaps: string[]
      liquidityLevels: string[]
      ictConcepts: string[]
      riskAssessment: string
    }
    timestamp: string
    analysisId?: string
  }
}

export default function AnalysisResults({ results }: AnalysisResultsProps) {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)

  const getDirectionColor = (direction: string) => {
    switch (direction) {
      case 'BUY': return 'text-green-500'
      case 'SELL': return 'text-red-500'
      default: return 'text-yellow-500'
    }
  }

  const getDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'BUY': return <TrendingUp className="w-6 h-6" />
      case 'SELL': return <TrendingDown className="w-6 h-6" />
      default: return <AlertTriangle className="w-6 h-6" />
    }
  }

  const formatPrice = (price: number) => {
    return price.toFixed(2)
  }

  return (
    <div className="bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold text-white">Analysis Results</h2>
        <div className="flex items-center justify-center space-x-4">
          <div className={`flex items-center space-x-2 ${getDirectionColor(results.direction)}`}>
            {getDirectionIcon(results.direction)}
            <span className="text-2xl font-bold">{results.direction}</span>
          </div>
          <div className="text-gray-400">•</div>
          <div className="text-white">
            <span className="text-lg font-semibold">{results.confidence}%</span>
            <span className="text-gray-400 ml-1">confidence</span>
          </div>
        </div>
      </div>

      {/* Trade Setup */}
      <div className="grid md:grid-cols-2 gap-6">
        <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
            <Target className="w-5 h-5 mr-2 text-gold-400" />
            Trade Setup
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Entry Price:</span>
              <span className="text-white font-mono text-lg">${formatPrice(results.entry)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Stop Loss:</span>
              <span className="text-red-400 font-mono text-lg">${formatPrice(results.stopLoss)}</span>
            </div>
            <div className="border-t border-gray-700 pt-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">TP1:</span>
                  <span className="text-green-400 font-mono">${formatPrice(results.takeProfits.tp1)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">TP2:</span>
                  <span className="text-green-400 font-mono">${formatPrice(results.takeProfits.tp2)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">TP3:</span>
                  <span className="text-green-400 font-mono">${formatPrice(results.takeProfits.tp3)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
            <DollarSign className="w-5 h-5 mr-2 text-gold-400" />
            Risk/Reward Ratios
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">TP1 R:R:</span>
              <span className="text-gold-400 font-semibold">1:{results.riskReward.tp1}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">TP2 R:R:</span>
              <span className="text-gold-400 font-semibold">1:{results.riskReward.tp2}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">TP3 R:R:</span>
              <span className="text-gold-400 font-semibold">1:{results.riskReward.tp3}</span>
            </div>
            <div className="border-t border-gray-700 pt-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Strategy:</span>
                <span className="text-white capitalize">{results.strategy}</span>
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-gray-400">Timeframe:</span>
                <span className="text-white">{results.timeframe}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Analysis Reasoning */}
      <div className="space-y-6">
        <h3 className="text-2xl font-semibold text-white">Detailed Analysis</h3>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
              Market Structure
            </h4>
            <p className="text-gray-300">{results.reasoning.marketStructure}</p>
          </div>

          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
              <Shield className="w-5 h-5 mr-2 text-blue-400" />
              Risk Assessment
            </h4>
            <p className="text-gray-300">{results.reasoning.riskAssessment}</p>
          </div>
        </div>

        {results.reasoning.orderBlocks.length > 0 && (
          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-3">Order Blocks Identified</h4>
            <ul className="space-y-2">
              {results.reasoning.orderBlocks.map((block, index) => (
                <li key={index} className="text-gray-300 flex items-start">
                  <span className="text-gold-400 mr-2">•</span>
                  {block}
                </li>
              ))}
            </ul>
          </div>
        )}

        {results.reasoning.fairValueGaps.length > 0 && (
          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-3">Fair Value Gaps</h4>
            <ul className="space-y-2">
              {results.reasoning.fairValueGaps.map((gap, index) => (
                <li key={index} className="text-gray-300 flex items-start">
                  <span className="text-gold-400 mr-2">•</span>
                  {gap}
                </li>
              ))}
            </ul>
          </div>
        )}

        {results.reasoning.ictConcepts.length > 0 && (
          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-3">ICT Concepts Applied</h4>
            <ul className="space-y-2">
              {results.reasoning.ictConcepts.map((concept, index) => (
                <li key={index} className="text-gray-300 flex items-start">
                  <span className="text-gold-400 mr-2">•</span>
                  {concept}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Feedback Section */}
      <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-white">Help Improve Our Analysis</h3>
          <p className="text-gray-400">
            Your feedback helps our AI learn and provide better trading analysis
          </p>
          <button
            onClick={() => setShowFeedbackModal(true)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2 mx-auto"
          >
            <MessageSquare className="w-5 h-5" />
            <span>Provide Feedback</span>
          </button>
        </div>
      </div>

      {/* Timestamp */}
      <div className="text-center text-gray-500 text-sm flex items-center justify-center">
        <Clock className="w-4 h-4 mr-2" />
        Analysis completed: {new Date(results.timestamp).toLocaleString()}
      </div>

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        analysisId={results.analysisId || 'unknown'}
      />
    </div>
  )
}
