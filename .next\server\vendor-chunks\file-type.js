"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/file-type";
exports.ids = ["vendor-chunks/file-type"];
exports.modules = {

/***/ "(rsc)/./node_modules/file-type/core.js":
/*!****************************************!*\
  !*** ./node_modules/file-type/core.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Token = __webpack_require__(/*! token-types */ \"(rsc)/./node_modules/token-types/lib/index.js\");\nconst strtok3 = __webpack_require__(/*! strtok3/lib/core */ \"(rsc)/./node_modules/strtok3/lib/core.js\");\nconst { stringToBytes, tarHeaderChecksumMatches, uint32SyncSafeToken } = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/file-type/util.js\");\nconst supported = __webpack_require__(/*! ./supported */ \"(rsc)/./node_modules/file-type/supported.js\");\nconst minimumBytes = 4100; // A fair amount of file-types are detectable within this range\nasync function fromStream(stream1) {\n    const tokenizer1 = await strtok3.fromStream(stream1);\n    try {\n        return await fromTokenizer(tokenizer1);\n    } finally{\n        await tokenizer1.close();\n    }\n}\nasync function fromBuffer(input1) {\n    if (!(input1 instanceof Uint8Array || input1 instanceof ArrayBuffer || Buffer.isBuffer(input1))) {\n        throw new TypeError(`Expected the \\`input\\` argument to be of type \\`Uint8Array\\` or \\`Buffer\\` or \\`ArrayBuffer\\`, got \\`${typeof input1}\\``);\n    }\n    const buffer1 = input1 instanceof Buffer ? input1 : Buffer.from(input1);\n    if (!(buffer1 && buffer1.length > 1)) {\n        return;\n    }\n    const tokenizer1 = strtok3.fromBuffer(buffer1);\n    return fromTokenizer(tokenizer1);\n}\nfunction _check(buffer1, headers1, options1) {\n    options1 = {\n        offset: 0,\n        ...options1\n    };\n    for (const [index1, header1] of headers1.entries()){\n        // If a bitmask is set\n        if (options1.mask) {\n            // If header doesn't equal `buf` with bits masked off\n            if (header1 !== (options1.mask[index1] & buffer1[index1 + options1.offset])) {\n                return false;\n            }\n        } else if (header1 !== buffer1[index1 + options1.offset]) {\n            return false;\n        }\n    }\n    return true;\n}\nasync function fromTokenizer(tokenizer1) {\n    try {\n        return _fromTokenizer(tokenizer1);\n    } catch (error1) {\n        if (!(error1 instanceof strtok3.EndOfStreamError)) {\n            throw error1;\n        }\n    }\n}\nasync function _fromTokenizer(tokenizer1) {\n    let buffer1 = Buffer.alloc(minimumBytes);\n    const bytesRead1 = 12;\n    const check1 = (header1, options1)=>_check(buffer1, header1, options1);\n    const checkString1 = (header1, options1)=>check1(stringToBytes(header1), options1);\n    // Keep reading until EOF if the file size is unknown.\n    if (!tokenizer1.fileInfo.size) {\n        tokenizer1.fileInfo.size = Number.MAX_SAFE_INTEGER;\n    }\n    await tokenizer1.peekBuffer(buffer1, {\n        length: bytesRead1,\n        mayBeLess: true\n    });\n    // -- 2-byte signatures --\n    if (check1([\n        0x42,\n        0x4D\n    ])) {\n        return {\n            ext: \"bmp\",\n            mime: \"image/bmp\"\n        };\n    }\n    if (check1([\n        0x0B,\n        0x77\n    ])) {\n        return {\n            ext: \"ac3\",\n            mime: \"audio/vnd.dolby.dd-raw\"\n        };\n    }\n    if (check1([\n        0x78,\n        0x01\n    ])) {\n        return {\n            ext: \"dmg\",\n            mime: \"application/x-apple-diskimage\"\n        };\n    }\n    if (check1([\n        0x4D,\n        0x5A\n    ])) {\n        return {\n            ext: \"exe\",\n            mime: \"application/x-msdownload\"\n        };\n    }\n    if (check1([\n        0x25,\n        0x21\n    ])) {\n        await tokenizer1.peekBuffer(buffer1, {\n            length: 24,\n            mayBeLess: true\n        });\n        if (checkString1(\"PS-Adobe-\", {\n            offset: 2\n        }) && checkString1(\" EPSF-\", {\n            offset: 14\n        })) {\n            return {\n                ext: \"eps\",\n                mime: \"application/eps\"\n            };\n        }\n        return {\n            ext: \"ps\",\n            mime: \"application/postscript\"\n        };\n    }\n    if (check1([\n        0x1F,\n        0xA0\n    ]) || check1([\n        0x1F,\n        0x9D\n    ])) {\n        return {\n            ext: \"Z\",\n            mime: \"application/x-compress\"\n        };\n    }\n    // -- 3-byte signatures --\n    if (check1([\n        0xFF,\n        0xD8,\n        0xFF\n    ])) {\n        return {\n            ext: \"jpg\",\n            mime: \"image/jpeg\"\n        };\n    }\n    if (check1([\n        0x49,\n        0x49,\n        0xBC\n    ])) {\n        return {\n            ext: \"jxr\",\n            mime: \"image/vnd.ms-photo\"\n        };\n    }\n    if (check1([\n        0x1F,\n        0x8B,\n        0x8\n    ])) {\n        return {\n            ext: \"gz\",\n            mime: \"application/gzip\"\n        };\n    }\n    if (check1([\n        0x42,\n        0x5A,\n        0x68\n    ])) {\n        return {\n            ext: \"bz2\",\n            mime: \"application/x-bzip2\"\n        };\n    }\n    if (checkString1(\"ID3\")) {\n        await tokenizer1.ignore(6); // Skip ID3 header until the header size\n        const id3HeaderLen1 = await tokenizer1.readToken(uint32SyncSafeToken);\n        if (tokenizer1.position + id3HeaderLen1 > tokenizer1.fileInfo.size) {\n            // Guess file type based on ID3 header for backward compatibility\n            return {\n                ext: \"mp3\",\n                mime: \"audio/mpeg\"\n            };\n        }\n        await tokenizer1.ignore(id3HeaderLen1);\n        return fromTokenizer(tokenizer1); // Skip ID3 header, recursion\n    }\n    // Musepack, SV7\n    if (checkString1(\"MP+\")) {\n        return {\n            ext: \"mpc\",\n            mime: \"audio/x-musepack\"\n        };\n    }\n    if ((buffer1[0] === 0x43 || buffer1[0] === 0x46) && check1([\n        0x57,\n        0x53\n    ], {\n        offset: 1\n    })) {\n        return {\n            ext: \"swf\",\n            mime: \"application/x-shockwave-flash\"\n        };\n    }\n    // -- 4-byte signatures --\n    if (check1([\n        0x47,\n        0x49,\n        0x46\n    ])) {\n        return {\n            ext: \"gif\",\n            mime: \"image/gif\"\n        };\n    }\n    if (checkString1(\"FLIF\")) {\n        return {\n            ext: \"flif\",\n            mime: \"image/flif\"\n        };\n    }\n    if (checkString1(\"8BPS\")) {\n        return {\n            ext: \"psd\",\n            mime: \"image/vnd.adobe.photoshop\"\n        };\n    }\n    if (checkString1(\"WEBP\", {\n        offset: 8\n    })) {\n        return {\n            ext: \"webp\",\n            mime: \"image/webp\"\n        };\n    }\n    // Musepack, SV8\n    if (checkString1(\"MPCK\")) {\n        return {\n            ext: \"mpc\",\n            mime: \"audio/x-musepack\"\n        };\n    }\n    if (checkString1(\"FORM\")) {\n        return {\n            ext: \"aif\",\n            mime: \"audio/aiff\"\n        };\n    }\n    if (checkString1(\"icns\", {\n        offset: 0\n    })) {\n        return {\n            ext: \"icns\",\n            mime: \"image/icns\"\n        };\n    }\n    // Zip-based file formats\n    // Need to be before the `zip` check\n    if (check1([\n        0x50,\n        0x4B,\n        0x3,\n        0x4\n    ])) {\n        try {\n            while(tokenizer1.position + 30 < tokenizer1.fileInfo.size){\n                await tokenizer1.readBuffer(buffer1, {\n                    length: 30\n                });\n                // https://en.wikipedia.org/wiki/Zip_(file_format)#File_headers\n                const zipHeader1 = {\n                    compressedSize: buffer1.readUInt32LE(18),\n                    uncompressedSize: buffer1.readUInt32LE(22),\n                    filenameLength: buffer1.readUInt16LE(26),\n                    extraFieldLength: buffer1.readUInt16LE(28)\n                };\n                zipHeader1.filename = await tokenizer1.readToken(new Token.StringType(zipHeader1.filenameLength, \"utf-8\"));\n                await tokenizer1.ignore(zipHeader1.extraFieldLength);\n                // Assumes signed `.xpi` from addons.mozilla.org\n                if (zipHeader1.filename === \"META-INF/mozilla.rsa\") {\n                    return {\n                        ext: \"xpi\",\n                        mime: \"application/x-xpinstall\"\n                    };\n                }\n                if (zipHeader1.filename.endsWith(\".rels\") || zipHeader1.filename.endsWith(\".xml\")) {\n                    const type1 = zipHeader1.filename.split(\"/\")[0];\n                    switch(type1){\n                        case \"_rels\":\n                            break;\n                        case \"word\":\n                            return {\n                                ext: \"docx\",\n                                mime: \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n                            };\n                        case \"ppt\":\n                            return {\n                                ext: \"pptx\",\n                                mime: \"application/vnd.openxmlformats-officedocument.presentationml.presentation\"\n                            };\n                        case \"xl\":\n                            return {\n                                ext: \"xlsx\",\n                                mime: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n                            };\n                        default:\n                            break;\n                    }\n                }\n                if (zipHeader1.filename.startsWith(\"xl/\")) {\n                    return {\n                        ext: \"xlsx\",\n                        mime: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n                    };\n                }\n                if (zipHeader1.filename.startsWith(\"3D/\") && zipHeader1.filename.endsWith(\".model\")) {\n                    return {\n                        ext: \"3mf\",\n                        mime: \"model/3mf\"\n                    };\n                }\n                // The docx, xlsx and pptx file types extend the Office Open XML file format:\n                // https://en.wikipedia.org/wiki/Office_Open_XML_file_formats\n                // We look for:\n                // - one entry named '[Content_Types].xml' or '_rels/.rels',\n                // - one entry indicating specific type of file.\n                // MS Office, OpenOffice and LibreOffice may put the parts in different order, so the check should not rely on it.\n                if (zipHeader1.filename === \"mimetype\" && zipHeader1.compressedSize === zipHeader1.uncompressedSize) {\n                    const mimeType1 = await tokenizer1.readToken(new Token.StringType(zipHeader1.compressedSize, \"utf-8\"));\n                    switch(mimeType1){\n                        case \"application/epub+zip\":\n                            return {\n                                ext: \"epub\",\n                                mime: \"application/epub+zip\"\n                            };\n                        case \"application/vnd.oasis.opendocument.text\":\n                            return {\n                                ext: \"odt\",\n                                mime: \"application/vnd.oasis.opendocument.text\"\n                            };\n                        case \"application/vnd.oasis.opendocument.spreadsheet\":\n                            return {\n                                ext: \"ods\",\n                                mime: \"application/vnd.oasis.opendocument.spreadsheet\"\n                            };\n                        case \"application/vnd.oasis.opendocument.presentation\":\n                            return {\n                                ext: \"odp\",\n                                mime: \"application/vnd.oasis.opendocument.presentation\"\n                            };\n                        default:\n                    }\n                }\n                // Try to find next header manually when current one is corrupted\n                if (zipHeader1.compressedSize === 0) {\n                    let nextHeaderIndex1 = -1;\n                    while(nextHeaderIndex1 < 0 && tokenizer1.position < tokenizer1.fileInfo.size){\n                        await tokenizer1.peekBuffer(buffer1, {\n                            mayBeLess: true\n                        });\n                        nextHeaderIndex1 = buffer1.indexOf(\"504B0304\", 0, \"hex\");\n                        // Move position to the next header if found, skip the whole buffer otherwise\n                        await tokenizer1.ignore(nextHeaderIndex1 >= 0 ? nextHeaderIndex1 : buffer1.length);\n                    }\n                } else {\n                    await tokenizer1.ignore(zipHeader1.compressedSize);\n                }\n            }\n        } catch (error1) {\n            if (!(error1 instanceof strtok3.EndOfStreamError)) {\n                throw error1;\n            }\n        }\n        return {\n            ext: \"zip\",\n            mime: \"application/zip\"\n        };\n    }\n    if (checkString1(\"OggS\")) {\n        // This is an OGG container\n        await tokenizer1.ignore(28);\n        const type1 = Buffer.alloc(8);\n        await tokenizer1.readBuffer(type1);\n        // Needs to be before `ogg` check\n        if (_check(type1, [\n            0x4F,\n            0x70,\n            0x75,\n            0x73,\n            0x48,\n            0x65,\n            0x61,\n            0x64\n        ])) {\n            return {\n                ext: \"opus\",\n                mime: \"audio/opus\"\n            };\n        }\n        // If ' theora' in header.\n        if (_check(type1, [\n            0x80,\n            0x74,\n            0x68,\n            0x65,\n            0x6F,\n            0x72,\n            0x61\n        ])) {\n            return {\n                ext: \"ogv\",\n                mime: \"video/ogg\"\n            };\n        }\n        // If '\\x01video' in header.\n        if (_check(type1, [\n            0x01,\n            0x76,\n            0x69,\n            0x64,\n            0x65,\n            0x6F,\n            0x00\n        ])) {\n            return {\n                ext: \"ogm\",\n                mime: \"video/ogg\"\n            };\n        }\n        // If ' FLAC' in header  https://xiph.org/flac/faq.html\n        if (_check(type1, [\n            0x7F,\n            0x46,\n            0x4C,\n            0x41,\n            0x43\n        ])) {\n            return {\n                ext: \"oga\",\n                mime: \"audio/ogg\"\n            };\n        }\n        // 'Speex  ' in header https://en.wikipedia.org/wiki/Speex\n        if (_check(type1, [\n            0x53,\n            0x70,\n            0x65,\n            0x65,\n            0x78,\n            0x20,\n            0x20\n        ])) {\n            return {\n                ext: \"spx\",\n                mime: \"audio/ogg\"\n            };\n        }\n        // If '\\x01vorbis' in header\n        if (_check(type1, [\n            0x01,\n            0x76,\n            0x6F,\n            0x72,\n            0x62,\n            0x69,\n            0x73\n        ])) {\n            return {\n                ext: \"ogg\",\n                mime: \"audio/ogg\"\n            };\n        }\n        // Default OGG container https://www.iana.org/assignments/media-types/application/ogg\n        return {\n            ext: \"ogx\",\n            mime: \"application/ogg\"\n        };\n    }\n    if (check1([\n        0x50,\n        0x4B\n    ]) && (buffer1[2] === 0x3 || buffer1[2] === 0x5 || buffer1[2] === 0x7) && (buffer1[3] === 0x4 || buffer1[3] === 0x6 || buffer1[3] === 0x8)) {\n        return {\n            ext: \"zip\",\n            mime: \"application/zip\"\n        };\n    }\n    //\n    // File Type Box (https://en.wikipedia.org/wiki/ISO_base_media_file_format)\n    // It's not required to be first, but it's recommended to be. Almost all ISO base media files start with `ftyp` box.\n    // `ftyp` box must contain a brand major identifier, which must consist of ISO 8859-1 printable characters.\n    // Here we check for 8859-1 printable characters (for simplicity, it's a mask which also catches one non-printable character).\n    if (checkString1(\"ftyp\", {\n        offset: 4\n    }) && (buffer1[8] & 0x60) !== 0x00 // Brand major, first character ASCII?\n    ) {\n        // They all can have MIME `video/mp4` except `application/mp4` special-case which is hard to detect.\n        // For some cases, we're specific, everything else falls to `video/mp4` with `mp4` extension.\n        const brandMajor1 = buffer1.toString(\"binary\", 8, 12).replace(\"\\x00\", \" \").trim();\n        switch(brandMajor1){\n            case \"avif\":\n                return {\n                    ext: \"avif\",\n                    mime: \"image/avif\"\n                };\n            case \"mif1\":\n                return {\n                    ext: \"heic\",\n                    mime: \"image/heif\"\n                };\n            case \"msf1\":\n                return {\n                    ext: \"heic\",\n                    mime: \"image/heif-sequence\"\n                };\n            case \"heic\":\n            case \"heix\":\n                return {\n                    ext: \"heic\",\n                    mime: \"image/heic\"\n                };\n            case \"hevc\":\n            case \"hevx\":\n                return {\n                    ext: \"heic\",\n                    mime: \"image/heic-sequence\"\n                };\n            case \"qt\":\n                return {\n                    ext: \"mov\",\n                    mime: \"video/quicktime\"\n                };\n            case \"M4V\":\n            case \"M4VH\":\n            case \"M4VP\":\n                return {\n                    ext: \"m4v\",\n                    mime: \"video/x-m4v\"\n                };\n            case \"M4P\":\n                return {\n                    ext: \"m4p\",\n                    mime: \"video/mp4\"\n                };\n            case \"M4B\":\n                return {\n                    ext: \"m4b\",\n                    mime: \"audio/mp4\"\n                };\n            case \"M4A\":\n                return {\n                    ext: \"m4a\",\n                    mime: \"audio/x-m4a\"\n                };\n            case \"F4V\":\n                return {\n                    ext: \"f4v\",\n                    mime: \"video/mp4\"\n                };\n            case \"F4P\":\n                return {\n                    ext: \"f4p\",\n                    mime: \"video/mp4\"\n                };\n            case \"F4A\":\n                return {\n                    ext: \"f4a\",\n                    mime: \"audio/mp4\"\n                };\n            case \"F4B\":\n                return {\n                    ext: \"f4b\",\n                    mime: \"audio/mp4\"\n                };\n            case \"crx\":\n                return {\n                    ext: \"cr3\",\n                    mime: \"image/x-canon-cr3\"\n                };\n            default:\n                if (brandMajor1.startsWith(\"3g\")) {\n                    if (brandMajor1.startsWith(\"3g2\")) {\n                        return {\n                            ext: \"3g2\",\n                            mime: \"video/3gpp2\"\n                        };\n                    }\n                    return {\n                        ext: \"3gp\",\n                        mime: \"video/3gpp\"\n                    };\n                }\n                return {\n                    ext: \"mp4\",\n                    mime: \"video/mp4\"\n                };\n        }\n    }\n    if (checkString1(\"MThd\")) {\n        return {\n            ext: \"mid\",\n            mime: \"audio/midi\"\n        };\n    }\n    if (checkString1(\"wOFF\") && (check1([\n        0x00,\n        0x01,\n        0x00,\n        0x00\n    ], {\n        offset: 4\n    }) || checkString1(\"OTTO\", {\n        offset: 4\n    }))) {\n        return {\n            ext: \"woff\",\n            mime: \"font/woff\"\n        };\n    }\n    if (checkString1(\"wOF2\") && (check1([\n        0x00,\n        0x01,\n        0x00,\n        0x00\n    ], {\n        offset: 4\n    }) || checkString1(\"OTTO\", {\n        offset: 4\n    }))) {\n        return {\n            ext: \"woff2\",\n            mime: \"font/woff2\"\n        };\n    }\n    if (check1([\n        0xD4,\n        0xC3,\n        0xB2,\n        0xA1\n    ]) || check1([\n        0xA1,\n        0xB2,\n        0xC3,\n        0xD4\n    ])) {\n        return {\n            ext: \"pcap\",\n            mime: \"application/vnd.tcpdump.pcap\"\n        };\n    }\n    // Sony DSD Stream File (DSF)\n    if (checkString1(\"DSD \")) {\n        return {\n            ext: \"dsf\",\n            mime: \"audio/x-dsf\" // Non-standard\n        };\n    }\n    if (checkString1(\"LZIP\")) {\n        return {\n            ext: \"lz\",\n            mime: \"application/x-lzip\"\n        };\n    }\n    if (checkString1(\"fLaC\")) {\n        return {\n            ext: \"flac\",\n            mime: \"audio/x-flac\"\n        };\n    }\n    if (check1([\n        0x42,\n        0x50,\n        0x47,\n        0xFB\n    ])) {\n        return {\n            ext: \"bpg\",\n            mime: \"image/bpg\"\n        };\n    }\n    if (checkString1(\"wvpk\")) {\n        return {\n            ext: \"wv\",\n            mime: \"audio/wavpack\"\n        };\n    }\n    if (checkString1(\"%PDF\")) {\n        await tokenizer1.ignore(1350);\n        const maxBufferSize1 = 10 * 1024 * 1024;\n        const buffer1 = Buffer.alloc(Math.min(maxBufferSize1, tokenizer1.fileInfo.size));\n        await tokenizer1.readBuffer(buffer1, {\n            mayBeLess: true\n        });\n        // Check if this is an Adobe Illustrator file\n        if (buffer1.includes(Buffer.from(\"AIPrivateData\"))) {\n            return {\n                ext: \"ai\",\n                mime: \"application/postscript\"\n            };\n        }\n        // Assume this is just a normal PDF\n        return {\n            ext: \"pdf\",\n            mime: \"application/pdf\"\n        };\n    }\n    if (check1([\n        0x00,\n        0x61,\n        0x73,\n        0x6D\n    ])) {\n        return {\n            ext: \"wasm\",\n            mime: \"application/wasm\"\n        };\n    }\n    // TIFF, little-endian type\n    if (check1([\n        0x49,\n        0x49,\n        0x2A,\n        0x0\n    ])) {\n        if (checkString1(\"CR\", {\n            offset: 8\n        })) {\n            return {\n                ext: \"cr2\",\n                mime: \"image/x-canon-cr2\"\n            };\n        }\n        if (check1([\n            0x1C,\n            0x00,\n            0xFE,\n            0x00\n        ], {\n            offset: 8\n        }) || check1([\n            0x1F,\n            0x00,\n            0x0B,\n            0x00\n        ], {\n            offset: 8\n        })) {\n            return {\n                ext: \"nef\",\n                mime: \"image/x-nikon-nef\"\n            };\n        }\n        if (check1([\n            0x08,\n            0x00,\n            0x00,\n            0x00\n        ], {\n            offset: 4\n        }) && (check1([\n            0x2D,\n            0x00,\n            0xFE,\n            0x00\n        ], {\n            offset: 8\n        }) || check1([\n            0x27,\n            0x00,\n            0xFE,\n            0x00\n        ], {\n            offset: 8\n        }))) {\n            return {\n                ext: \"dng\",\n                mime: \"image/x-adobe-dng\"\n            };\n        }\n        buffer1 = Buffer.alloc(24);\n        await tokenizer1.peekBuffer(buffer1);\n        if ((check1([\n            0x10,\n            0xFB,\n            0x86,\n            0x01\n        ], {\n            offset: 4\n        }) || check1([\n            0x08,\n            0x00,\n            0x00,\n            0x00\n        ], {\n            offset: 4\n        })) && // This pattern differentiates ARW from other TIFF-ish file types:\n        check1([\n            0x00,\n            0xFE,\n            0x00,\n            0x04,\n            0x00,\n            0x01,\n            0x00,\n            0x00,\n            0x00,\n            0x01,\n            0x00,\n            0x00,\n            0x00,\n            0x03,\n            0x01\n        ], {\n            offset: 9\n        })) {\n            return {\n                ext: \"arw\",\n                mime: \"image/x-sony-arw\"\n            };\n        }\n        return {\n            ext: \"tif\",\n            mime: \"image/tiff\"\n        };\n    }\n    // TIFF, big-endian type\n    if (check1([\n        0x4D,\n        0x4D,\n        0x0,\n        0x2A\n    ])) {\n        return {\n            ext: \"tif\",\n            mime: \"image/tiff\"\n        };\n    }\n    if (checkString1(\"MAC \")) {\n        return {\n            ext: \"ape\",\n            mime: \"audio/ape\"\n        };\n    }\n    // https://github.com/threatstack/libmagic/blob/master/magic/Magdir/matroska\n    if (check1([\n        0x1A,\n        0x45,\n        0xDF,\n        0xA3\n    ])) {\n        async function readField1() {\n            const msb1 = await tokenizer1.peekNumber(Token.UINT8);\n            let mask1 = 0x80;\n            let ic1 = 0; // 0 = A, 1 = B, 2 = C, 3 = D\n            while((msb1 & mask1) === 0 && mask1 !== 0){\n                ++ic1;\n                mask1 >>= 1;\n            }\n            const id1 = Buffer.alloc(ic1 + 1);\n            await tokenizer1.readBuffer(id1);\n            return id1;\n        }\n        async function readElement1() {\n            const id1 = await readField1();\n            const lenField1 = await readField1();\n            lenField1[0] ^= 0x80 >> lenField1.length - 1;\n            const nrLen1 = Math.min(6, lenField1.length); // JavaScript can max read 6 bytes integer\n            return {\n                id: id1.readUIntBE(0, id1.length),\n                len: lenField1.readUIntBE(lenField1.length - nrLen1, nrLen1)\n            };\n        }\n        async function readChildren1(level1, children1) {\n            while(children1 > 0){\n                const e1 = await readElement1();\n                if (e1.id === 0x4282) {\n                    return tokenizer1.readToken(new Token.StringType(e1.len, \"utf-8\")); // Return DocType\n                }\n                await tokenizer1.ignore(e1.len); // ignore payload\n                --children1;\n            }\n        }\n        const re1 = await readElement1();\n        const docType1 = await readChildren1(1, re1.len);\n        switch(docType1){\n            case \"webm\":\n                return {\n                    ext: \"webm\",\n                    mime: \"video/webm\"\n                };\n            case \"matroska\":\n                return {\n                    ext: \"mkv\",\n                    mime: \"video/x-matroska\"\n                };\n            default:\n                return;\n        }\n    }\n    // RIFF file format which might be AVI, WAV, QCP, etc\n    if (check1([\n        0x52,\n        0x49,\n        0x46,\n        0x46\n    ])) {\n        if (check1([\n            0x41,\n            0x56,\n            0x49\n        ], {\n            offset: 8\n        })) {\n            return {\n                ext: \"avi\",\n                mime: \"video/vnd.avi\"\n            };\n        }\n        if (check1([\n            0x57,\n            0x41,\n            0x56,\n            0x45\n        ], {\n            offset: 8\n        })) {\n            return {\n                ext: \"wav\",\n                mime: \"audio/vnd.wave\"\n            };\n        }\n        // QLCM, QCP file\n        if (check1([\n            0x51,\n            0x4C,\n            0x43,\n            0x4D\n        ], {\n            offset: 8\n        })) {\n            return {\n                ext: \"qcp\",\n                mime: \"audio/qcelp\"\n            };\n        }\n    }\n    if (checkString1(\"SQLi\")) {\n        return {\n            ext: \"sqlite\",\n            mime: \"application/x-sqlite3\"\n        };\n    }\n    if (check1([\n        0x4E,\n        0x45,\n        0x53,\n        0x1A\n    ])) {\n        return {\n            ext: \"nes\",\n            mime: \"application/x-nintendo-nes-rom\"\n        };\n    }\n    if (checkString1(\"Cr24\")) {\n        return {\n            ext: \"crx\",\n            mime: \"application/x-google-chrome-extension\"\n        };\n    }\n    if (checkString1(\"MSCF\") || checkString1(\"ISc(\")) {\n        return {\n            ext: \"cab\",\n            mime: \"application/vnd.ms-cab-compressed\"\n        };\n    }\n    if (check1([\n        0xED,\n        0xAB,\n        0xEE,\n        0xDB\n    ])) {\n        return {\n            ext: \"rpm\",\n            mime: \"application/x-rpm\"\n        };\n    }\n    if (check1([\n        0xC5,\n        0xD0,\n        0xD3,\n        0xC6\n    ])) {\n        return {\n            ext: \"eps\",\n            mime: \"application/eps\"\n        };\n    }\n    if (check1([\n        0x28,\n        0xB5,\n        0x2F,\n        0xFD\n    ])) {\n        return {\n            ext: \"zst\",\n            mime: \"application/zstd\"\n        };\n    }\n    // -- 5-byte signatures --\n    if (check1([\n        0x4F,\n        0x54,\n        0x54,\n        0x4F,\n        0x00\n    ])) {\n        return {\n            ext: \"otf\",\n            mime: \"font/otf\"\n        };\n    }\n    if (checkString1(\"#!AMR\")) {\n        return {\n            ext: \"amr\",\n            mime: \"audio/amr\"\n        };\n    }\n    if (checkString1(\"{\\\\rtf\")) {\n        return {\n            ext: \"rtf\",\n            mime: \"application/rtf\"\n        };\n    }\n    if (check1([\n        0x46,\n        0x4C,\n        0x56,\n        0x01\n    ])) {\n        return {\n            ext: \"flv\",\n            mime: \"video/x-flv\"\n        };\n    }\n    if (checkString1(\"IMPM\")) {\n        return {\n            ext: \"it\",\n            mime: \"audio/x-it\"\n        };\n    }\n    if (checkString1(\"-lh0-\", {\n        offset: 2\n    }) || checkString1(\"-lh1-\", {\n        offset: 2\n    }) || checkString1(\"-lh2-\", {\n        offset: 2\n    }) || checkString1(\"-lh3-\", {\n        offset: 2\n    }) || checkString1(\"-lh4-\", {\n        offset: 2\n    }) || checkString1(\"-lh5-\", {\n        offset: 2\n    }) || checkString1(\"-lh6-\", {\n        offset: 2\n    }) || checkString1(\"-lh7-\", {\n        offset: 2\n    }) || checkString1(\"-lzs-\", {\n        offset: 2\n    }) || checkString1(\"-lz4-\", {\n        offset: 2\n    }) || checkString1(\"-lz5-\", {\n        offset: 2\n    }) || checkString1(\"-lhd-\", {\n        offset: 2\n    })) {\n        return {\n            ext: \"lzh\",\n            mime: \"application/x-lzh-compressed\"\n        };\n    }\n    // MPEG program stream (PS or MPEG-PS)\n    if (check1([\n        0x00,\n        0x00,\n        0x01,\n        0xBA\n    ])) {\n        //  MPEG-PS, MPEG-1 Part 1\n        if (check1([\n            0x21\n        ], {\n            offset: 4,\n            mask: [\n                0xF1\n            ]\n        })) {\n            return {\n                ext: \"mpg\",\n                mime: \"video/MP1S\"\n            };\n        }\n        // MPEG-PS, MPEG-2 Part 1\n        if (check1([\n            0x44\n        ], {\n            offset: 4,\n            mask: [\n                0xC4\n            ]\n        })) {\n            return {\n                ext: \"mpg\",\n                mime: \"video/MP2P\"\n            };\n        }\n    }\n    if (checkString1(\"ITSF\")) {\n        return {\n            ext: \"chm\",\n            mime: \"application/vnd.ms-htmlhelp\"\n        };\n    }\n    // -- 6-byte signatures --\n    if (check1([\n        0xFD,\n        0x37,\n        0x7A,\n        0x58,\n        0x5A,\n        0x00\n    ])) {\n        return {\n            ext: \"xz\",\n            mime: \"application/x-xz\"\n        };\n    }\n    if (checkString1(\"<?xml \")) {\n        return {\n            ext: \"xml\",\n            mime: \"application/xml\"\n        };\n    }\n    if (check1([\n        0x37,\n        0x7A,\n        0xBC,\n        0xAF,\n        0x27,\n        0x1C\n    ])) {\n        return {\n            ext: \"7z\",\n            mime: \"application/x-7z-compressed\"\n        };\n    }\n    if (check1([\n        0x52,\n        0x61,\n        0x72,\n        0x21,\n        0x1A,\n        0x7\n    ]) && (buffer1[6] === 0x0 || buffer1[6] === 0x1)) {\n        return {\n            ext: \"rar\",\n            mime: \"application/x-rar-compressed\"\n        };\n    }\n    if (checkString1(\"solid \")) {\n        return {\n            ext: \"stl\",\n            mime: \"model/stl\"\n        };\n    }\n    // -- 7-byte signatures --\n    if (checkString1(\"BLENDER\")) {\n        return {\n            ext: \"blend\",\n            mime: \"application/x-blender\"\n        };\n    }\n    if (checkString1(\"!<arch>\")) {\n        await tokenizer1.ignore(8);\n        const str1 = await tokenizer1.readToken(new Token.StringType(13, \"ascii\"));\n        if (str1 === \"debian-binary\") {\n            return {\n                ext: \"deb\",\n                mime: \"application/x-deb\"\n            };\n        }\n        return {\n            ext: \"ar\",\n            mime: \"application/x-unix-archive\"\n        };\n    }\n    // -- 8-byte signatures --\n    if (check1([\n        0x89,\n        0x50,\n        0x4E,\n        0x47,\n        0x0D,\n        0x0A,\n        0x1A,\n        0x0A\n    ])) {\n        // APNG format (https://wiki.mozilla.org/APNG_Specification)\n        // 1. Find the first IDAT (image data) chunk (49 44 41 54)\n        // 2. Check if there is an \"acTL\" chunk before the IDAT one (61 63 54 4C)\n        // Offset calculated as follows:\n        // - 8 bytes: PNG signature\n        // - 4 (length) + 4 (chunk type) + 13 (chunk data) + 4 (CRC): IHDR chunk\n        await tokenizer1.ignore(8); // ignore PNG signature\n        async function readChunkHeader1() {\n            return {\n                length: await tokenizer1.readToken(Token.INT32_BE),\n                type: await tokenizer1.readToken(new Token.StringType(4, \"binary\"))\n            };\n        }\n        do {\n            const chunk1 = await readChunkHeader1();\n            if (chunk1.length < 0) {\n                return; // Invalid chunk length\n            }\n            switch(chunk1.type){\n                case \"IDAT\":\n                    return {\n                        ext: \"png\",\n                        mime: \"image/png\"\n                    };\n                case \"acTL\":\n                    return {\n                        ext: \"apng\",\n                        mime: \"image/apng\"\n                    };\n                default:\n                    await tokenizer1.ignore(chunk1.length + 4); // Ignore chunk-data + CRC\n            }\n        }while (tokenizer1.position + 8 < tokenizer1.fileInfo.size);\n        return {\n            ext: \"png\",\n            mime: \"image/png\"\n        };\n    }\n    if (check1([\n        0x41,\n        0x52,\n        0x52,\n        0x4F,\n        0x57,\n        0x31,\n        0x00,\n        0x00\n    ])) {\n        return {\n            ext: \"arrow\",\n            mime: \"application/x-apache-arrow\"\n        };\n    }\n    if (check1([\n        0x67,\n        0x6C,\n        0x54,\n        0x46,\n        0x02,\n        0x00,\n        0x00,\n        0x00\n    ])) {\n        return {\n            ext: \"glb\",\n            mime: \"model/gltf-binary\"\n        };\n    }\n    // `mov` format variants\n    if (check1([\n        0x66,\n        0x72,\n        0x65,\n        0x65\n    ], {\n        offset: 4\n    }) || // `free`\n    check1([\n        0x6D,\n        0x64,\n        0x61,\n        0x74\n    ], {\n        offset: 4\n    }) || // `mdat` MJPEG\n    check1([\n        0x6D,\n        0x6F,\n        0x6F,\n        0x76\n    ], {\n        offset: 4\n    }) || // `moov`\n    check1([\n        0x77,\n        0x69,\n        0x64,\n        0x65\n    ], {\n        offset: 4\n    }) // `wide`\n    ) {\n        return {\n            ext: \"mov\",\n            mime: \"video/quicktime\"\n        };\n    }\n    // -- 9-byte signatures --\n    if (check1([\n        0x49,\n        0x49,\n        0x52,\n        0x4F,\n        0x08,\n        0x00,\n        0x00,\n        0x00,\n        0x18\n    ])) {\n        return {\n            ext: \"orf\",\n            mime: \"image/x-olympus-orf\"\n        };\n    }\n    if (checkString1(\"gimp xcf \")) {\n        return {\n            ext: \"xcf\",\n            mime: \"image/x-xcf\"\n        };\n    }\n    // -- 12-byte signatures --\n    if (check1([\n        0x49,\n        0x49,\n        0x55,\n        0x00,\n        0x18,\n        0x00,\n        0x00,\n        0x00,\n        0x88,\n        0xE7,\n        0x74,\n        0xD8\n    ])) {\n        return {\n            ext: \"rw2\",\n            mime: \"image/x-panasonic-rw2\"\n        };\n    }\n    // ASF_Header_Object first 80 bytes\n    if (check1([\n        0x30,\n        0x26,\n        0xB2,\n        0x75,\n        0x8E,\n        0x66,\n        0xCF,\n        0x11,\n        0xA6,\n        0xD9\n    ])) {\n        async function readHeader1() {\n            const guid1 = Buffer.alloc(16);\n            await tokenizer1.readBuffer(guid1);\n            return {\n                id: guid1,\n                size: Number(await tokenizer1.readToken(Token.UINT64_LE))\n            };\n        }\n        await tokenizer1.ignore(30);\n        // Search for header should be in first 1KB of file.\n        while(tokenizer1.position + 24 < tokenizer1.fileInfo.size){\n            const header1 = await readHeader1();\n            let payload1 = header1.size - 24;\n            if (_check(header1.id, [\n                0x91,\n                0x07,\n                0xDC,\n                0xB7,\n                0xB7,\n                0xA9,\n                0xCF,\n                0x11,\n                0x8E,\n                0xE6,\n                0x00,\n                0xC0,\n                0x0C,\n                0x20,\n                0x53,\n                0x65\n            ])) {\n                // Sync on Stream-Properties-Object (B7DC0791-A9B7-11CF-8EE6-00C00C205365)\n                const typeId1 = Buffer.alloc(16);\n                payload1 -= await tokenizer1.readBuffer(typeId1);\n                if (_check(typeId1, [\n                    0x40,\n                    0x9E,\n                    0x69,\n                    0xF8,\n                    0x4D,\n                    0x5B,\n                    0xCF,\n                    0x11,\n                    0xA8,\n                    0xFD,\n                    0x00,\n                    0x80,\n                    0x5F,\n                    0x5C,\n                    0x44,\n                    0x2B\n                ])) {\n                    // Found audio:\n                    return {\n                        ext: \"asf\",\n                        mime: \"audio/x-ms-asf\"\n                    };\n                }\n                if (_check(typeId1, [\n                    0xC0,\n                    0xEF,\n                    0x19,\n                    0xBC,\n                    0x4D,\n                    0x5B,\n                    0xCF,\n                    0x11,\n                    0xA8,\n                    0xFD,\n                    0x00,\n                    0x80,\n                    0x5F,\n                    0x5C,\n                    0x44,\n                    0x2B\n                ])) {\n                    // Found video:\n                    return {\n                        ext: \"asf\",\n                        mime: \"video/x-ms-asf\"\n                    };\n                }\n                break;\n            }\n            await tokenizer1.ignore(payload1);\n        }\n        // Default to ASF generic extension\n        return {\n            ext: \"asf\",\n            mime: \"application/vnd.ms-asf\"\n        };\n    }\n    if (check1([\n        0xAB,\n        0x4B,\n        0x54,\n        0x58,\n        0x20,\n        0x31,\n        0x31,\n        0xBB,\n        0x0D,\n        0x0A,\n        0x1A,\n        0x0A\n    ])) {\n        return {\n            ext: \"ktx\",\n            mime: \"image/ktx\"\n        };\n    }\n    if ((check1([\n        0x7E,\n        0x10,\n        0x04\n    ]) || check1([\n        0x7E,\n        0x18,\n        0x04\n    ])) && check1([\n        0x30,\n        0x4D,\n        0x49,\n        0x45\n    ], {\n        offset: 4\n    })) {\n        return {\n            ext: \"mie\",\n            mime: \"application/x-mie\"\n        };\n    }\n    if (check1([\n        0x27,\n        0x0A,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00\n    ], {\n        offset: 2\n    })) {\n        return {\n            ext: \"shp\",\n            mime: \"application/x-esri-shape\"\n        };\n    }\n    if (check1([\n        0x00,\n        0x00,\n        0x00,\n        0x0C,\n        0x6A,\n        0x50,\n        0x20,\n        0x20,\n        0x0D,\n        0x0A,\n        0x87,\n        0x0A\n    ])) {\n        // JPEG-2000 family\n        await tokenizer1.ignore(20);\n        const type1 = await tokenizer1.readToken(new Token.StringType(4, \"ascii\"));\n        switch(type1){\n            case \"jp2 \":\n                return {\n                    ext: \"jp2\",\n                    mime: \"image/jp2\"\n                };\n            case \"jpx \":\n                return {\n                    ext: \"jpx\",\n                    mime: \"image/jpx\"\n                };\n            case \"jpm \":\n                return {\n                    ext: \"jpm\",\n                    mime: \"image/jpm\"\n                };\n            case \"mjp2\":\n                return {\n                    ext: \"mj2\",\n                    mime: \"image/mj2\"\n                };\n            default:\n                return;\n        }\n    }\n    if (check1([\n        0xFF,\n        0x0A\n    ]) || check1([\n        0x00,\n        0x00,\n        0x00,\n        0x0C,\n        0x4A,\n        0x58,\n        0x4C,\n        0x20,\n        0x0D,\n        0x0A,\n        0x87,\n        0x0A\n    ])) {\n        return {\n            ext: \"jxl\",\n            mime: \"image/jxl\"\n        };\n    }\n    // -- Unsafe signatures --\n    if (check1([\n        0x0,\n        0x0,\n        0x1,\n        0xBA\n    ]) || check1([\n        0x0,\n        0x0,\n        0x1,\n        0xB3\n    ])) {\n        return {\n            ext: \"mpg\",\n            mime: \"video/mpeg\"\n        };\n    }\n    if (check1([\n        0x00,\n        0x01,\n        0x00,\n        0x00,\n        0x00\n    ])) {\n        return {\n            ext: \"ttf\",\n            mime: \"font/ttf\"\n        };\n    }\n    if (check1([\n        0x00,\n        0x00,\n        0x01,\n        0x00\n    ])) {\n        return {\n            ext: \"ico\",\n            mime: \"image/x-icon\"\n        };\n    }\n    if (check1([\n        0x00,\n        0x00,\n        0x02,\n        0x00\n    ])) {\n        return {\n            ext: \"cur\",\n            mime: \"image/x-icon\"\n        };\n    }\n    if (check1([\n        0xD0,\n        0xCF,\n        0x11,\n        0xE0,\n        0xA1,\n        0xB1,\n        0x1A,\n        0xE1\n    ])) {\n        // Detected Microsoft Compound File Binary File (MS-CFB) Format.\n        return {\n            ext: \"cfb\",\n            mime: \"application/x-cfb\"\n        };\n    }\n    // Increase sample size from 12 to 256.\n    await tokenizer1.peekBuffer(buffer1, {\n        length: Math.min(256, tokenizer1.fileInfo.size),\n        mayBeLess: true\n    });\n    // -- 15-byte signatures --\n    if (checkString1(\"BEGIN:\")) {\n        if (checkString1(\"VCARD\", {\n            offset: 6\n        })) {\n            return {\n                ext: \"vcf\",\n                mime: \"text/vcard\"\n            };\n        }\n        if (checkString1(\"VCALENDAR\", {\n            offset: 6\n        })) {\n            return {\n                ext: \"ics\",\n                mime: \"text/calendar\"\n            };\n        }\n    }\n    // `raf` is here just to keep all the raw image detectors together.\n    if (checkString1(\"FUJIFILMCCD-RAW\")) {\n        return {\n            ext: \"raf\",\n            mime: \"image/x-fujifilm-raf\"\n        };\n    }\n    if (checkString1(\"Extended Module:\")) {\n        return {\n            ext: \"xm\",\n            mime: \"audio/x-xm\"\n        };\n    }\n    if (checkString1(\"Creative Voice File\")) {\n        return {\n            ext: \"voc\",\n            mime: \"audio/x-voc\"\n        };\n    }\n    if (check1([\n        0x04,\n        0x00,\n        0x00,\n        0x00\n    ]) && buffer1.length >= 16) {\n        const jsonSize1 = buffer1.readUInt32LE(12);\n        if (jsonSize1 > 12 && buffer1.length >= jsonSize1 + 16) {\n            try {\n                const header1 = buffer1.slice(16, jsonSize1 + 16).toString();\n                const json1 = JSON.parse(header1);\n                // Check if Pickle is ASAR\n                if (json1.files) {\n                    return {\n                        ext: \"asar\",\n                        mime: \"application/x-asar\"\n                    };\n                }\n            } catch (_1) {}\n        }\n    }\n    if (check1([\n        0x06,\n        0x0E,\n        0x2B,\n        0x34,\n        0x02,\n        0x05,\n        0x01,\n        0x01,\n        0x0D,\n        0x01,\n        0x02,\n        0x01,\n        0x01,\n        0x02\n    ])) {\n        return {\n            ext: \"mxf\",\n            mime: \"application/mxf\"\n        };\n    }\n    if (checkString1(\"SCRM\", {\n        offset: 44\n    })) {\n        return {\n            ext: \"s3m\",\n            mime: \"audio/x-s3m\"\n        };\n    }\n    if (check1([\n        0x47\n    ], {\n        offset: 4\n    }) && (check1([\n        0x47\n    ], {\n        offset: 192\n    }) || check1([\n        0x47\n    ], {\n        offset: 196\n    }))) {\n        return {\n            ext: \"mts\",\n            mime: \"video/mp2t\"\n        };\n    }\n    if (check1([\n        0x42,\n        0x4F,\n        0x4F,\n        0x4B,\n        0x4D,\n        0x4F,\n        0x42,\n        0x49\n    ], {\n        offset: 60\n    })) {\n        return {\n            ext: \"mobi\",\n            mime: \"application/x-mobipocket-ebook\"\n        };\n    }\n    if (check1([\n        0x44,\n        0x49,\n        0x43,\n        0x4D\n    ], {\n        offset: 128\n    })) {\n        return {\n            ext: \"dcm\",\n            mime: \"application/dicom\"\n        };\n    }\n    if (check1([\n        0x4C,\n        0x00,\n        0x00,\n        0x00,\n        0x01,\n        0x14,\n        0x02,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0xC0,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x46\n    ])) {\n        return {\n            ext: \"lnk\",\n            mime: \"application/x.ms.shortcut\" // Invented by us\n        };\n    }\n    if (check1([\n        0x62,\n        0x6F,\n        0x6F,\n        0x6B,\n        0x00,\n        0x00,\n        0x00,\n        0x00,\n        0x6D,\n        0x61,\n        0x72,\n        0x6B,\n        0x00,\n        0x00,\n        0x00,\n        0x00\n    ])) {\n        return {\n            ext: \"alias\",\n            mime: \"application/x.apple.alias\" // Invented by us\n        };\n    }\n    if (check1([\n        0x4C,\n        0x50\n    ], {\n        offset: 34\n    }) && (check1([\n        0x00,\n        0x00,\n        0x01\n    ], {\n        offset: 8\n    }) || check1([\n        0x01,\n        0x00,\n        0x02\n    ], {\n        offset: 8\n    }) || check1([\n        0x02,\n        0x00,\n        0x02\n    ], {\n        offset: 8\n    }))) {\n        return {\n            ext: \"eot\",\n            mime: \"application/vnd.ms-fontobject\"\n        };\n    }\n    if (check1([\n        0x06,\n        0x06,\n        0xED,\n        0xF5,\n        0xD8,\n        0x1D,\n        0x46,\n        0xE5,\n        0xBD,\n        0x31,\n        0xEF,\n        0xE7,\n        0xFE,\n        0x74,\n        0xB7,\n        0x1D\n    ])) {\n        return {\n            ext: \"indd\",\n            mime: \"application/x-indesign\"\n        };\n    }\n    // Increase sample size from 256 to 512\n    await tokenizer1.peekBuffer(buffer1, {\n        length: Math.min(512, tokenizer1.fileInfo.size),\n        mayBeLess: true\n    });\n    // Requires a buffer size of 512 bytes\n    if (tarHeaderChecksumMatches(buffer1)) {\n        return {\n            ext: \"tar\",\n            mime: \"application/x-tar\"\n        };\n    }\n    if (check1([\n        0xFF,\n        0xFE,\n        0xFF,\n        0x0E,\n        0x53,\n        0x00,\n        0x6B,\n        0x00,\n        0x65,\n        0x00,\n        0x74,\n        0x00,\n        0x63,\n        0x00,\n        0x68,\n        0x00,\n        0x55,\n        0x00,\n        0x70,\n        0x00,\n        0x20,\n        0x00,\n        0x4D,\n        0x00,\n        0x6F,\n        0x00,\n        0x64,\n        0x00,\n        0x65,\n        0x00,\n        0x6C,\n        0x00\n    ])) {\n        return {\n            ext: \"skp\",\n            mime: \"application/vnd.sketchup.skp\"\n        };\n    }\n    if (checkString1(\"-----BEGIN PGP MESSAGE-----\")) {\n        return {\n            ext: \"pgp\",\n            mime: \"application/pgp-encrypted\"\n        };\n    }\n    // Check MPEG 1 or 2 Layer 3 header, or 'layer 0' for ADTS (MPEG sync-word 0xFFE)\n    if (buffer1.length >= 2 && check1([\n        0xFF,\n        0xE0\n    ], {\n        offset: 0,\n        mask: [\n            0xFF,\n            0xE0\n        ]\n    })) {\n        if (check1([\n            0x10\n        ], {\n            offset: 1,\n            mask: [\n                0x16\n            ]\n        })) {\n            // Check for (ADTS) MPEG-2\n            if (check1([\n                0x08\n            ], {\n                offset: 1,\n                mask: [\n                    0x08\n                ]\n            })) {\n                return {\n                    ext: \"aac\",\n                    mime: \"audio/aac\"\n                };\n            }\n            // Must be (ADTS) MPEG-4\n            return {\n                ext: \"aac\",\n                mime: \"audio/aac\"\n            };\n        }\n        // MPEG 1 or 2 Layer 3 header\n        // Check for MPEG layer 3\n        if (check1([\n            0x02\n        ], {\n            offset: 1,\n            mask: [\n                0x06\n            ]\n        })) {\n            return {\n                ext: \"mp3\",\n                mime: \"audio/mpeg\"\n            };\n        }\n        // Check for MPEG layer 2\n        if (check1([\n            0x04\n        ], {\n            offset: 1,\n            mask: [\n                0x06\n            ]\n        })) {\n            return {\n                ext: \"mp2\",\n                mime: \"audio/mpeg\"\n            };\n        }\n        // Check for MPEG layer 1\n        if (check1([\n            0x06\n        ], {\n            offset: 1,\n            mask: [\n                0x06\n            ]\n        })) {\n            return {\n                ext: \"mp1\",\n                mime: \"audio/mpeg\"\n            };\n        }\n    }\n}\nconst stream = (readableStream)=>new Promise((resolve, reject)=>{\n        // Using `eval` to work around issues when bundling with Webpack\n        const stream = eval(\"require\")(\"stream\"); // eslint-disable-line no-eval\n        readableStream.on(\"error\", reject);\n        readableStream.once(\"readable\", async ()=>{\n            // Set up output stream\n            const pass1 = new stream.PassThrough();\n            let outputStream1;\n            if (stream.pipeline) {\n                outputStream1 = stream.pipeline(readableStream, pass1, ()=>{});\n            } else {\n                outputStream1 = readableStream.pipe(pass1);\n            }\n            // Read the input stream and detect the filetype\n            const chunk1 = readableStream.read(minimumBytes) || readableStream.read() || Buffer.alloc(0);\n            try {\n                const fileType1 = await fromBuffer(chunk1);\n                pass1.fileType = fileType1;\n            } catch (error1) {\n                reject(error1);\n            }\n            resolve(outputStream1);\n        });\n    });\nconst fileType = {\n    fromStream,\n    fromTokenizer,\n    fromBuffer,\n    stream\n};\nObject.defineProperty(fileType, \"extensions\", {\n    get () {\n        return new Set(supported.extensions);\n    }\n});\nObject.defineProperty(fileType, \"mimeTypes\", {\n    get () {\n        return new Set(supported.mimeTypes);\n    }\n});\nmodule.exports = fileType;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/file-type/index.js":
/*!*****************************************!*\
  !*** ./node_modules/file-type/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst strtok3 = __webpack_require__(/*! strtok3 */ \"(rsc)/./node_modules/strtok3/lib/index.js\");\nconst core = __webpack_require__(/*! ./core */ \"(rsc)/./node_modules/file-type/core.js\");\nasync function fromFile(path) {\n    const tokenizer = await strtok3.fromFile(path);\n    try {\n        return await core.fromTokenizer(tokenizer);\n    } finally{\n        await tokenizer.close();\n    }\n}\nconst fileType = {\n    fromFile\n};\nObject.assign(fileType, core);\nObject.defineProperty(fileType, \"extensions\", {\n    get () {\n        return core.extensions;\n    }\n});\nObject.defineProperty(fileType, \"mimeTypes\", {\n    get () {\n        return core.mimeTypes;\n    }\n});\nmodule.exports = fileType;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmlsZS10eXBlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EsTUFBTUEsVUFBVUMsbUJBQU9BLENBQUM7QUFDeEIsTUFBTUMsT0FBT0QsbUJBQU9BLENBQUM7QUFFckIsZUFBZUUsU0FBU0MsSUFBSTtJQUMzQixNQUFNQyxZQUFZLE1BQU1MLFFBQVFHLFFBQVEsQ0FBQ0M7SUFDekMsSUFBSTtRQUNILE9BQU8sTUFBTUYsS0FBS0ksYUFBYSxDQUFDRDtJQUNqQyxTQUFVO1FBQ1QsTUFBTUEsVUFBVUUsS0FBSztJQUN0QjtBQUNEO0FBRUEsTUFBTUMsV0FBVztJQUNoQkw7QUFDRDtBQUVBTSxPQUFPQyxNQUFNLENBQUNGLFVBQVVOO0FBRXhCTyxPQUFPRSxjQUFjLENBQUNILFVBQVUsY0FBYztJQUM3Q0k7UUFDQyxPQUFPVixLQUFLVyxVQUFVO0lBQ3ZCO0FBQ0Q7QUFFQUosT0FBT0UsY0FBYyxDQUFDSCxVQUFVLGFBQWE7SUFDNUNJO1FBQ0MsT0FBT1YsS0FBS1ksU0FBUztJQUN0QjtBQUNEO0FBRUFDLE9BQU9DLE9BQU8sR0FBR1IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly94YXV1c2QtdHJhZGluZy1hbmFseXplci8uL25vZGVfbW9kdWxlcy9maWxlLXR5cGUvaW5kZXguanM/NjczNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5jb25zdCBzdHJ0b2szID0gcmVxdWlyZSgnc3RydG9rMycpO1xuY29uc3QgY29yZSA9IHJlcXVpcmUoJy4vY29yZScpO1xuXG5hc3luYyBmdW5jdGlvbiBmcm9tRmlsZShwYXRoKSB7XG5cdGNvbnN0IHRva2VuaXplciA9IGF3YWl0IHN0cnRvazMuZnJvbUZpbGUocGF0aCk7XG5cdHRyeSB7XG5cdFx0cmV0dXJuIGF3YWl0IGNvcmUuZnJvbVRva2VuaXplcih0b2tlbml6ZXIpO1xuXHR9IGZpbmFsbHkge1xuXHRcdGF3YWl0IHRva2VuaXplci5jbG9zZSgpO1xuXHR9XG59XG5cbmNvbnN0IGZpbGVUeXBlID0ge1xuXHRmcm9tRmlsZVxufTtcblxuT2JqZWN0LmFzc2lnbihmaWxlVHlwZSwgY29yZSk7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShmaWxlVHlwZSwgJ2V4dGVuc2lvbnMnLCB7XG5cdGdldCgpIHtcblx0XHRyZXR1cm4gY29yZS5leHRlbnNpb25zO1xuXHR9XG59KTtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGZpbGVUeXBlLCAnbWltZVR5cGVzJywge1xuXHRnZXQoKSB7XG5cdFx0cmV0dXJuIGNvcmUubWltZVR5cGVzO1xuXHR9XG59KTtcblxubW9kdWxlLmV4cG9ydHMgPSBmaWxlVHlwZTtcbiJdLCJuYW1lcyI6WyJzdHJ0b2szIiwicmVxdWlyZSIsImNvcmUiLCJmcm9tRmlsZSIsInBhdGgiLCJ0b2tlbml6ZXIiLCJmcm9tVG9rZW5pemVyIiwiY2xvc2UiLCJmaWxlVHlwZSIsIk9iamVjdCIsImFzc2lnbiIsImRlZmluZVByb3BlcnR5IiwiZ2V0IiwiZXh0ZW5zaW9ucyIsIm1pbWVUeXBlcyIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/file-type/supported.js":
/*!*********************************************!*\
  !*** ./node_modules/file-type/supported.js ***!
  \*********************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    extensions: [\n        \"jpg\",\n        \"png\",\n        \"apng\",\n        \"gif\",\n        \"webp\",\n        \"flif\",\n        \"xcf\",\n        \"cr2\",\n        \"cr3\",\n        \"orf\",\n        \"arw\",\n        \"dng\",\n        \"nef\",\n        \"rw2\",\n        \"raf\",\n        \"tif\",\n        \"bmp\",\n        \"icns\",\n        \"jxr\",\n        \"psd\",\n        \"indd\",\n        \"zip\",\n        \"tar\",\n        \"rar\",\n        \"gz\",\n        \"bz2\",\n        \"7z\",\n        \"dmg\",\n        \"mp4\",\n        \"mid\",\n        \"mkv\",\n        \"webm\",\n        \"mov\",\n        \"avi\",\n        \"mpg\",\n        \"mp2\",\n        \"mp3\",\n        \"m4a\",\n        \"oga\",\n        \"ogg\",\n        \"ogv\",\n        \"opus\",\n        \"flac\",\n        \"wav\",\n        \"spx\",\n        \"amr\",\n        \"pdf\",\n        \"epub\",\n        \"exe\",\n        \"swf\",\n        \"rtf\",\n        \"wasm\",\n        \"woff\",\n        \"woff2\",\n        \"eot\",\n        \"ttf\",\n        \"otf\",\n        \"ico\",\n        \"flv\",\n        \"ps\",\n        \"xz\",\n        \"sqlite\",\n        \"nes\",\n        \"crx\",\n        \"xpi\",\n        \"cab\",\n        \"deb\",\n        \"ar\",\n        \"rpm\",\n        \"Z\",\n        \"lz\",\n        \"cfb\",\n        \"mxf\",\n        \"mts\",\n        \"blend\",\n        \"bpg\",\n        \"docx\",\n        \"pptx\",\n        \"xlsx\",\n        \"3gp\",\n        \"3g2\",\n        \"jp2\",\n        \"jpm\",\n        \"jpx\",\n        \"mj2\",\n        \"aif\",\n        \"qcp\",\n        \"odt\",\n        \"ods\",\n        \"odp\",\n        \"xml\",\n        \"mobi\",\n        \"heic\",\n        \"cur\",\n        \"ktx\",\n        \"ape\",\n        \"wv\",\n        \"dcm\",\n        \"ics\",\n        \"glb\",\n        \"pcap\",\n        \"dsf\",\n        \"lnk\",\n        \"alias\",\n        \"voc\",\n        \"ac3\",\n        \"m4v\",\n        \"m4p\",\n        \"m4b\",\n        \"f4v\",\n        \"f4p\",\n        \"f4b\",\n        \"f4a\",\n        \"mie\",\n        \"asf\",\n        \"ogm\",\n        \"ogx\",\n        \"mpc\",\n        \"arrow\",\n        \"shp\",\n        \"aac\",\n        \"mp1\",\n        \"it\",\n        \"s3m\",\n        \"xm\",\n        \"ai\",\n        \"skp\",\n        \"avif\",\n        \"eps\",\n        \"lzh\",\n        \"pgp\",\n        \"asar\",\n        \"stl\",\n        \"chm\",\n        \"3mf\",\n        \"zst\",\n        \"jxl\",\n        \"vcf\"\n    ],\n    mimeTypes: [\n        \"image/jpeg\",\n        \"image/png\",\n        \"image/gif\",\n        \"image/webp\",\n        \"image/flif\",\n        \"image/x-xcf\",\n        \"image/x-canon-cr2\",\n        \"image/x-canon-cr3\",\n        \"image/tiff\",\n        \"image/bmp\",\n        \"image/vnd.ms-photo\",\n        \"image/vnd.adobe.photoshop\",\n        \"application/x-indesign\",\n        \"application/epub+zip\",\n        \"application/x-xpinstall\",\n        \"application/vnd.oasis.opendocument.text\",\n        \"application/vnd.oasis.opendocument.spreadsheet\",\n        \"application/vnd.oasis.opendocument.presentation\",\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n        \"application/zip\",\n        \"application/x-tar\",\n        \"application/x-rar-compressed\",\n        \"application/gzip\",\n        \"application/x-bzip2\",\n        \"application/x-7z-compressed\",\n        \"application/x-apple-diskimage\",\n        \"application/x-apache-arrow\",\n        \"video/mp4\",\n        \"audio/midi\",\n        \"video/x-matroska\",\n        \"video/webm\",\n        \"video/quicktime\",\n        \"video/vnd.avi\",\n        \"audio/vnd.wave\",\n        \"audio/qcelp\",\n        \"audio/x-ms-asf\",\n        \"video/x-ms-asf\",\n        \"application/vnd.ms-asf\",\n        \"video/mpeg\",\n        \"video/3gpp\",\n        \"audio/mpeg\",\n        \"audio/mp4\",\n        \"audio/opus\",\n        \"video/ogg\",\n        \"audio/ogg\",\n        \"application/ogg\",\n        \"audio/x-flac\",\n        \"audio/ape\",\n        \"audio/wavpack\",\n        \"audio/amr\",\n        \"application/pdf\",\n        \"application/x-msdownload\",\n        \"application/x-shockwave-flash\",\n        \"application/rtf\",\n        \"application/wasm\",\n        \"font/woff\",\n        \"font/woff2\",\n        \"application/vnd.ms-fontobject\",\n        \"font/ttf\",\n        \"font/otf\",\n        \"image/x-icon\",\n        \"video/x-flv\",\n        \"application/postscript\",\n        \"application/eps\",\n        \"application/x-xz\",\n        \"application/x-sqlite3\",\n        \"application/x-nintendo-nes-rom\",\n        \"application/x-google-chrome-extension\",\n        \"application/vnd.ms-cab-compressed\",\n        \"application/x-deb\",\n        \"application/x-unix-archive\",\n        \"application/x-rpm\",\n        \"application/x-compress\",\n        \"application/x-lzip\",\n        \"application/x-cfb\",\n        \"application/x-mie\",\n        \"application/mxf\",\n        \"video/mp2t\",\n        \"application/x-blender\",\n        \"image/bpg\",\n        \"image/jp2\",\n        \"image/jpx\",\n        \"image/jpm\",\n        \"image/mj2\",\n        \"audio/aiff\",\n        \"application/xml\",\n        \"application/x-mobipocket-ebook\",\n        \"image/heif\",\n        \"image/heif-sequence\",\n        \"image/heic\",\n        \"image/heic-sequence\",\n        \"image/icns\",\n        \"image/ktx\",\n        \"application/dicom\",\n        \"audio/x-musepack\",\n        \"text/calendar\",\n        \"text/vcard\",\n        \"model/gltf-binary\",\n        \"application/vnd.tcpdump.pcap\",\n        \"audio/x-dsf\",\n        \"application/x.ms.shortcut\",\n        \"application/x.apple.alias\",\n        \"audio/x-voc\",\n        \"audio/vnd.dolby.dd-raw\",\n        \"audio/x-m4a\",\n        \"image/apng\",\n        \"image/x-olympus-orf\",\n        \"image/x-sony-arw\",\n        \"image/x-adobe-dng\",\n        \"image/x-nikon-nef\",\n        \"image/x-panasonic-rw2\",\n        \"image/x-fujifilm-raf\",\n        \"video/x-m4v\",\n        \"video/3gpp2\",\n        \"application/x-esri-shape\",\n        \"audio/aac\",\n        \"audio/x-it\",\n        \"audio/x-s3m\",\n        \"audio/x-xm\",\n        \"video/MP1S\",\n        \"video/MP2P\",\n        \"application/vnd.sketchup.skp\",\n        \"image/avif\",\n        \"application/x-lzh-compressed\",\n        \"application/pgp-encrypted\",\n        \"application/x-asar\",\n        \"model/stl\",\n        \"application/vnd.ms-htmlhelp\",\n        \"model/3mf\",\n        \"image/jxl\",\n        \"application/zstd\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/supported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/file-type/util.js":
/*!****************************************!*\
  !*** ./node_modules/file-type/util.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.stringToBytes = (string)=>[\n        ...string\n    ].map((character)=>character.charCodeAt(0));\n/**\nChecks whether the TAR checksum is valid.\n\n@param {Buffer} buffer - The TAR header `[offset ... offset + 512]`.\n@param {number} offset - TAR header offset.\n@returns {boolean} `true` if the TAR checksum is valid, otherwise `false`.\n*/ exports.tarHeaderChecksumMatches = (buffer, offset = 0)=>{\n    const readSum = parseInt(buffer.toString(\"utf8\", 148, 154).replace(/\\0.*$/, \"\").trim(), 8); // Read sum in header\n    if (isNaN(readSum)) {\n        return false;\n    }\n    let sum = 8 * 0x20; // Initialize signed bit sum\n    for(let i = offset; i < offset + 148; i++){\n        sum += buffer[i];\n    }\n    for(let i = offset + 156; i < offset + 512; i++){\n        sum += buffer[i];\n    }\n    return readSum === sum;\n};\n/**\nID3 UINT32 sync-safe tokenizer token.\n28 bits (representing up to 256MB) integer, the msb is 0 to avoid \"false syncsignals\".\n*/ exports.uint32SyncSafeToken = {\n    get: (buffer, offset)=>{\n        return buffer[offset + 3] & 0x7F | buffer[offset + 2] << 7 | buffer[offset + 1] << 14 | buffer[offset] << 21;\n    },\n    len: 4\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/util.js\n");

/***/ })

};
;