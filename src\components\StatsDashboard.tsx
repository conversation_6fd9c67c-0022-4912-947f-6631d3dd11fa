'use client'

import { useState, useEffect } from 'react'
import { BarChart3, TrendingUp, Target, Users, Star, Activity } from 'lucide-react'

interface StatsData {
  totalAnalyses: number
  strategyBreakdown: { [key: string]: number }
  averageConfidence: number
  directionBreakdown: { BUY: number; SELL: number; NEUTRAL: number }
}

export default function StatsDashboard() {
  const [stats, setStats] = useState<StatsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/analyze')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-black/30 backdrop-blur-sm rounded-xl p-6 border border-gold-500/20">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="bg-black/30 backdrop-blur-sm rounded-xl p-6 border border-gold-500/20">
        <div className="text-center text-gray-400">
          <BarChart3 className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No analysis data available yet</p>
          <p className="text-sm">Upload your first chart to see statistics</p>
        </div>
      </div>
    )
  }

  const getDirectionColor = (direction: string) => {
    switch (direction) {
      case 'BUY': return 'text-green-500'
      case 'SELL': return 'text-red-500'
      case 'NEUTRAL': return 'text-yellow-500'
      default: return 'text-gray-500'
    }
  }

  const getDirectionBg = (direction: string) => {
    switch (direction) {
      case 'BUY': return 'bg-green-500/20 border-green-500/30'
      case 'SELL': return 'bg-red-500/20 border-red-500/30'
      case 'NEUTRAL': return 'bg-yellow-500/20 border-yellow-500/30'
      default: return 'bg-gray-500/20 border-gray-500/30'
    }
  }

  return (
    <div className="bg-black/30 backdrop-blur-sm rounded-xl p-6 border border-gold-500/20 space-y-6">
      <div className="flex items-center space-x-3">
        <BarChart3 className="w-6 h-6 text-gold-400" />
        <h2 className="text-xl font-bold text-white">Analysis Statistics</h2>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-black/50 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center space-x-2 mb-2">
            <Activity className="w-5 h-5 text-blue-400" />
            <span className="text-gray-400 text-sm">Total Analyses</span>
          </div>
          <div className="text-2xl font-bold text-white">{stats.totalAnalyses}</div>
        </div>

        <div className="bg-black/50 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center space-x-2 mb-2">
            <Target className="w-5 h-5 text-gold-400" />
            <span className="text-gray-400 text-sm">Avg Confidence</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {stats.averageConfidence.toFixed(1)}%
          </div>
        </div>

        <div className="bg-black/50 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center space-x-2 mb-2">
            <TrendingUp className="w-5 h-5 text-green-400" />
            <span className="text-gray-400 text-sm">Buy Signals</span>
          </div>
          <div className="text-2xl font-bold text-green-400">
            {stats.directionBreakdown.BUY || 0}
          </div>
        </div>

        <div className="bg-black/50 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center space-x-2 mb-2">
            <TrendingUp className="w-5 h-5 text-red-400 rotate-180" />
            <span className="text-gray-400 text-sm">Sell Signals</span>
          </div>
          <div className="text-2xl font-bold text-red-400">
            {stats.directionBreakdown.SELL || 0}
          </div>
        </div>
      </div>

      {/* Strategy Breakdown */}
      {Object.keys(stats.strategyBreakdown).length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-white">Strategy Breakdown</h3>
          <div className="grid md:grid-cols-2 gap-4">
            {Object.entries(stats.strategyBreakdown).map(([strategy, count]) => (
              <div key={strategy} className="bg-black/50 rounded-lg p-4 border border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 capitalize">{strategy}</span>
                  <span className="text-white font-semibold">{count}</span>
                </div>
                <div className="mt-2 bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gold-500 h-2 rounded-full transition-all duration-500"
                    style={{ 
                      width: `${stats.totalAnalyses > 0 ? (count / stats.totalAnalyses) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Direction Distribution */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-white">Signal Distribution</h3>
        <div className="grid grid-cols-3 gap-4">
          {Object.entries(stats.directionBreakdown).map(([direction, count]) => (
            <div 
              key={direction} 
              className={`rounded-lg p-4 border ${getDirectionBg(direction)}`}
            >
              <div className="text-center">
                <div className={`text-2xl font-bold ${getDirectionColor(direction)}`}>
                  {count}
                </div>
                <div className="text-sm text-gray-300 mt-1">{direction}</div>
                <div className="text-xs text-gray-400 mt-1">
                  {stats.totalAnalyses > 0 
                    ? `${((count / stats.totalAnalyses) * 100).toFixed(1)}%`
                    : '0%'
                  }
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-black/50 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-3">Performance Insights</h3>
        <div className="space-y-2 text-sm">
          {stats.averageConfidence > 75 && (
            <div className="flex items-center space-x-2 text-green-400">
              <Star className="w-4 h-4" />
              <span>High confidence analysis - Strong signal quality</span>
            </div>
          )}
          
          {stats.totalAnalyses > 10 && (
            <div className="flex items-center space-x-2 text-blue-400">
              <Users className="w-4 h-4" />
              <span>Sufficient data for pattern recognition</span>
            </div>
          )}
          
          {stats.directionBreakdown.BUY > stats.directionBreakdown.SELL && (
            <div className="flex items-center space-x-2 text-gold-400">
              <TrendingUp className="w-4 h-4" />
              <span>Bullish market bias detected</span>
            </div>
          )}
          
          {stats.directionBreakdown.SELL > stats.directionBreakdown.BUY && (
            <div className="flex items-center space-x-2 text-gold-400">
              <TrendingUp className="w-4 h-4 rotate-180" />
              <span>Bearish market bias detected</span>
            </div>
          )}
          
          {stats.totalAnalyses < 5 && (
            <div className="flex items-center space-x-2 text-gray-400">
              <Activity className="w-4 h-4" />
              <span>Upload more charts to improve analysis accuracy</span>
            </div>
          )}
        </div>
      </div>

      <div className="text-center">
        <button
          onClick={fetchStats}
          className="bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-semibold py-2 px-4 rounded-lg transition-all duration-200"
        >
          Refresh Statistics
        </button>
      </div>
    </div>
  )
}
