{"version": 3, "file": "index.js", "names": ["configure", "types", "plugins"], "sources": ["../src/index.js"], "sourcesContent": ["import configure from \"@jimp/custom\";\n\nimport types from \"@jimp/types\";\nimport plugins from \"@jimp/plugins\";\n\nexport default configure({\n  types: [types],\n  plugins: [plugins],\n});\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AAAoC;AAAA,eAErB,IAAAA,eAAS,EAAC;EACvBC,KAAK,EAAE,CAACA,cAAK,CAAC;EACdC,OAAO,EAAE,CAACA,gBAAO;AACnB,CAAC,CAAC;AAAA;AAAA;AAAA"}