{"version": 3, "file": "index.js", "names": ["Path", "b<PERSON>ont", "isNodePattern", "throwError", "measureText", "measureTextHeight", "splitLines", "xOffsetBasedOnAlignment", "constants", "font", "line", "max<PERSON><PERSON><PERSON>", "alignment", "HORIZONTAL_ALIGN_LEFT", "HORIZONTAL_ALIGN_CENTER", "<PERSON><PERSON><PERSON><PERSON>", "image", "x", "y", "char", "width", "height", "characterPage", "pages", "page", "blit", "xoffset", "yoffset", "printText", "text", "defaultCharWidth", "i", "length", "chars", "test", "fontChar", "fontKerning", "kernings", "kerning", "xadvance", "loadPages", "<PERSON><PERSON>", "dir", "newPages", "map", "read", "Promise", "all", "process", "env", "DIRNAME", "__dirname", "FONT_SANS_8_BLACK", "join", "FONT_SANS_10_BLACK", "FONT_SANS_12_BLACK", "FONT_SANS_14_BLACK", "FONT_SANS_16_BLACK", "FONT_SANS_32_BLACK", "FONT_SANS_64_BLACK", "FONT_SANS_128_BLACK", "FONT_SANS_8_WHITE", "FONT_SANS_16_WHITE", "FONT_SANS_32_WHITE", "FONT_SANS_64_WHITE", "FONT_SANS_128_WHITE", "loadFont", "file", "cb", "call", "resolve", "reject", "err", "String", "fromCharCode", "id", "firstString", "first", "second", "amount", "dirname", "then", "common", "info", "class", "print", "maxHeight", "Infinity", "alignmentX", "alignmentY", "undefined", "constructor", "VERTICAL_ALIGN_TOP", "toString", "VERTICAL_ALIGN_BOTTOM", "VERTICAL_ALIGN_MIDDLE", "Object", "entries", "lines", "longestLine", "for<PERSON>ach", "lineString", "alignmentWidth", "lineHeight"], "sources": ["../src/index.js"], "sourcesContent": ["import Path from \"path\";\nimport bMFont from \"load-bmfont\";\nimport { isNodePattern, throwError } from \"@jimp/utils\";\nimport { measureText, measureTextHeight, splitLines } from \"./measure-text\";\n\nfunction xOffsetBasedOnAlignment(constants, font, line, maxWidth, alignment) {\n  if (alignment === constants.HORIZONTAL_ALIGN_LEFT) {\n    return 0;\n  }\n\n  if (alignment === constants.HORIZONTAL_ALIGN_CENTER) {\n    return (maxWidth - measureText(font, line)) / 2;\n  }\n\n  return maxWidth - measureText(font, line);\n}\n\nfunction drawCharacter(image, font, x, y, char) {\n  if (char.width > 0 && char.height > 0) {\n    const characterPage = font.pages[char.page];\n\n    image.blit(\n      characterPage,\n      x + char.xoffset,\n      y + char.yoffset,\n      char.x,\n      char.y,\n      char.width,\n      char.height\n    );\n  }\n\n  return image;\n}\n\nfunction printText(font, x, y, text, defaultCharWidth) {\n  for (let i = 0; i < text.length; i++) {\n    let char;\n\n    if (font.chars[text[i]]) {\n      char = text[i];\n    } else if (/\\s/.test(text[i])) {\n      char = \"\";\n    } else {\n      char = \"?\";\n    }\n\n    const fontChar = font.chars[char] || {};\n    const fontKerning = font.kernings[char];\n\n    drawCharacter(this, font, x, y, fontChar || {});\n\n    const kerning =\n      fontKerning && fontKerning[text[i + 1]] ? fontKerning[text[i + 1]] : 0;\n\n    x += kerning + (fontChar.xadvance || defaultCharWidth);\n  }\n}\n\nfunction loadPages(Jimp, dir, pages) {\n  const newPages = pages.map((page) => {\n    return Jimp.read(dir + \"/\" + page);\n  });\n\n  return Promise.all(newPages);\n}\n\nconst dir = process.env.DIRNAME || `${__dirname}/../`;\n\nexport default () => ({\n  constants: {\n    measureText,\n    measureTextHeight,\n    FONT_SANS_8_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt\"\n    ),\n    FONT_SANS_10_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt\"\n    ),\n    FONT_SANS_12_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt\"\n    ),\n    FONT_SANS_14_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt\"\n    ),\n    FONT_SANS_16_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt\"\n    ),\n    FONT_SANS_32_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt\"\n    ),\n    FONT_SANS_64_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt\"\n    ),\n    FONT_SANS_128_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt\"\n    ),\n\n    FONT_SANS_8_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt\"\n    ),\n    FONT_SANS_16_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt\"\n    ),\n    FONT_SANS_32_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt\"\n    ),\n    FONT_SANS_64_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt\"\n    ),\n    FONT_SANS_128_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt\"\n    ),\n\n    /**\n     * Loads a bitmap font from a file\n     * @param {string} file the file path of a .fnt file\n     * @param {function(Error, Jimp)} cb (optional) a function to call when the font is loaded\n     * @returns {Promise} a promise\n     */\n    loadFont(file, cb) {\n      if (typeof file !== \"string\")\n        return throwError.call(this, \"file must be a string\", cb);\n\n      return new Promise((resolve, reject) => {\n        cb =\n          cb ||\n          function (err, font) {\n            if (err) reject(err);\n            else resolve(font);\n          };\n\n        bMFont(file, (err, font) => {\n          const chars = {};\n          const kernings = {};\n\n          if (err) {\n            return throwError.call(this, err, cb);\n          }\n\n          for (let i = 0; i < font.chars.length; i++) {\n            chars[String.fromCharCode(font.chars[i].id)] = font.chars[i];\n          }\n\n          for (let i = 0; i < font.kernings.length; i++) {\n            const firstString = String.fromCharCode(font.kernings[i].first);\n            kernings[firstString] = kernings[firstString] || {};\n            kernings[firstString][\n              String.fromCharCode(font.kernings[i].second)\n            ] = font.kernings[i].amount;\n          }\n\n          loadPages(this, Path.dirname(file), font.pages).then((pages) => {\n            cb(null, {\n              chars,\n              kernings,\n              pages,\n              common: font.common,\n              info: font.info,\n            });\n          });\n        });\n      });\n    },\n  },\n\n  class: {\n    /**\n     * Draws a text on a image on a given boundary\n     * @param {Jimp} font a bitmap font loaded from `Jimp.loadFont` command\n     * @param {number} x the x position to start drawing the text\n     * @param {number} y the y position to start drawing the text\n     * @param {any} text the text to draw (string or object with `text`, `alignmentX`, and/or `alignmentY`)\n     * @param {number} maxWidth (optional) the boundary width to draw in\n     * @param {number} maxHeight (optional) the boundary height to draw in\n     * @param {function(Error, Jimp)} cb (optional) a function to call when the text is written\n     * @returns {Jimp} this for chaining of methods\n     */\n    print(font, x, y, text, maxWidth, maxHeight, cb) {\n      if (typeof maxWidth === \"function\" && typeof cb === \"undefined\") {\n        cb = maxWidth;\n        maxWidth = Infinity;\n      }\n\n      if (typeof maxWidth === \"undefined\") {\n        maxWidth = Infinity;\n      }\n\n      if (typeof maxHeight === \"function\" && typeof cb === \"undefined\") {\n        cb = maxHeight;\n        maxHeight = Infinity;\n      }\n\n      if (typeof maxHeight === \"undefined\") {\n        maxHeight = Infinity;\n      }\n\n      if (typeof font !== \"object\") {\n        return throwError.call(this, \"font must be a Jimp loadFont\", cb);\n      }\n\n      if (\n        typeof x !== \"number\" ||\n        typeof y !== \"number\" ||\n        typeof maxWidth !== \"number\"\n      ) {\n        return throwError.call(this, \"x, y and maxWidth must be numbers\", cb);\n      }\n\n      if (typeof maxWidth !== \"number\") {\n        return throwError.call(this, \"maxWidth must be a number\", cb);\n      }\n\n      if (typeof maxHeight !== \"number\") {\n        return throwError.call(this, \"maxHeight must be a number\", cb);\n      }\n\n      let alignmentX;\n      let alignmentY;\n\n      if (\n        typeof text === \"object\" &&\n        text.text !== null &&\n        text.text !== undefined\n      ) {\n        alignmentX = text.alignmentX || this.constructor.HORIZONTAL_ALIGN_LEFT;\n        alignmentY = text.alignmentY || this.constructor.VERTICAL_ALIGN_TOP;\n        ({ text } = text);\n      } else {\n        alignmentX = this.constructor.HORIZONTAL_ALIGN_LEFT;\n        alignmentY = this.constructor.VERTICAL_ALIGN_TOP;\n        text = text.toString();\n      }\n\n      if (\n        maxHeight !== Infinity &&\n        alignmentY === this.constructor.VERTICAL_ALIGN_BOTTOM\n      ) {\n        y += maxHeight - measureTextHeight(font, text, maxWidth);\n      } else if (\n        maxHeight !== Infinity &&\n        alignmentY === this.constructor.VERTICAL_ALIGN_MIDDLE\n      ) {\n        y += maxHeight / 2 - measureTextHeight(font, text, maxWidth) / 2;\n      }\n\n      const defaultCharWidth = Object.entries(font.chars)[0][1].xadvance;\n      const { lines, longestLine } = splitLines(font, text, maxWidth);\n\n      lines.forEach((line) => {\n        const lineString = line.join(\" \");\n        const alignmentWidth = xOffsetBasedOnAlignment(\n          this.constructor,\n          font,\n          lineString,\n          maxWidth,\n          alignmentX\n        );\n\n        printText.call(\n          this,\n          font,\n          x + alignmentWidth,\n          y,\n          lineString,\n          defaultCharWidth\n        );\n\n        y += font.common.lineHeight;\n      });\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this, { x: x + longestLine, y });\n      }\n\n      return this;\n    },\n  },\n});\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,MAAM;AACvB,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,aAAa,EAAEC,UAAU,QAAQ,aAAa;AACvD,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,gBAAgB;AAE3E,SAASC,uBAAuB,CAACC,SAAS,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EAC3E,IAAIA,SAAS,KAAKJ,SAAS,CAACK,qBAAqB,EAAE;IACjD,OAAO,CAAC;EACV;EAEA,IAAID,SAAS,KAAKJ,SAAS,CAACM,uBAAuB,EAAE;IACnD,OAAO,CAACH,QAAQ,GAAGP,WAAW,CAACK,IAAI,EAAEC,IAAI,CAAC,IAAI,CAAC;EACjD;EAEA,OAAOC,QAAQ,GAAGP,WAAW,CAACK,IAAI,EAAEC,IAAI,CAAC;AAC3C;AAEA,SAASK,aAAa,CAACC,KAAK,EAAEP,IAAI,EAAEQ,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAE;EAC9C,IAAIA,IAAI,CAACC,KAAK,GAAG,CAAC,IAAID,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;IACrC,MAAMC,aAAa,GAAGb,IAAI,CAACc,KAAK,CAACJ,IAAI,CAACK,IAAI,CAAC;IAE3CR,KAAK,CAACS,IAAI,CACRH,aAAa,EACbL,CAAC,GAAGE,IAAI,CAACO,OAAO,EAChBR,CAAC,GAAGC,IAAI,CAACQ,OAAO,EAChBR,IAAI,CAACF,CAAC,EACNE,IAAI,CAACD,CAAC,EACNC,IAAI,CAACC,KAAK,EACVD,IAAI,CAACE,MAAM,CACZ;EACH;EAEA,OAAOL,KAAK;AACd;AAEA,SAASY,SAAS,CAACnB,IAAI,EAAEQ,CAAC,EAAEC,CAAC,EAAEW,IAAI,EAAEC,gBAAgB,EAAE;EACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIZ,IAAI;IAER,IAAIV,IAAI,CAACwB,KAAK,CAACJ,IAAI,CAACE,CAAC,CAAC,CAAC,EAAE;MACvBZ,IAAI,GAAGU,IAAI,CAACE,CAAC,CAAC;IAChB,CAAC,MAAM,IAAI,IAAI,CAACG,IAAI,CAACL,IAAI,CAACE,CAAC,CAAC,CAAC,EAAE;MAC7BZ,IAAI,GAAG,EAAE;IACX,CAAC,MAAM;MACLA,IAAI,GAAG,GAAG;IACZ;IAEA,MAAMgB,QAAQ,GAAG1B,IAAI,CAACwB,KAAK,CAACd,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,MAAMiB,WAAW,GAAG3B,IAAI,CAAC4B,QAAQ,CAAClB,IAAI,CAAC;IAEvCJ,aAAa,CAAC,IAAI,EAAEN,IAAI,EAAEQ,CAAC,EAAEC,CAAC,EAAEiB,QAAQ,IAAI,CAAC,CAAC,CAAC;IAE/C,MAAMG,OAAO,GACXF,WAAW,IAAIA,WAAW,CAACP,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGK,WAAW,CAACP,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAExEd,CAAC,IAAIqB,OAAO,IAAIH,QAAQ,CAACI,QAAQ,IAAIT,gBAAgB,CAAC;EACxD;AACF;AAEA,SAASU,SAAS,CAACC,IAAI,EAAEC,GAAG,EAAEnB,KAAK,EAAE;EACnC,MAAMoB,QAAQ,GAAGpB,KAAK,CAACqB,GAAG,CAAEpB,IAAI,IAAK;IACnC,OAAOiB,IAAI,CAACI,IAAI,CAACH,GAAG,GAAG,GAAG,GAAGlB,IAAI,CAAC;EACpC,CAAC,CAAC;EAEF,OAAOsB,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;AAC9B;AAEA,MAAMD,GAAG,GAAGM,OAAO,CAACC,GAAG,CAACC,OAAO,IAAK,GAAEC,SAAU,MAAK;AAErD,gBAAe,OAAO;EACpB3C,SAAS,EAAE;IACTJ,WAAW;IACXC,iBAAiB;IACjB+C,iBAAiB,EAAEpD,IAAI,CAACqD,IAAI,CAC1BX,GAAG,EACH,yDAAyD,CAC1D;IACDY,kBAAkB,EAAEtD,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDa,kBAAkB,EAAEvD,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDc,kBAAkB,EAAExD,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDe,kBAAkB,EAAEzD,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDgB,kBAAkB,EAAE1D,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDiB,kBAAkB,EAAE3D,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDkB,mBAAmB,EAAE5D,IAAI,CAACqD,IAAI,CAC5BX,GAAG,EACH,6DAA6D,CAC9D;IAEDmB,iBAAiB,EAAE7D,IAAI,CAACqD,IAAI,CAC1BX,GAAG,EACH,yDAAyD,CAC1D;IACDoB,kBAAkB,EAAE9D,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDqB,kBAAkB,EAAE/D,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDsB,kBAAkB,EAAEhE,IAAI,CAACqD,IAAI,CAC3BX,GAAG,EACH,2DAA2D,CAC5D;IACDuB,mBAAmB,EAAEjE,IAAI,CAACqD,IAAI,CAC5BX,GAAG,EACH,6DAA6D,CAC9D;IAED;AACJ;AACA;AACA;AACA;AACA;IACIwB,QAAQ,CAACC,IAAI,EAAEC,EAAE,EAAE;MACjB,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAC1B,OAAOhE,UAAU,CAACkE,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAED,EAAE,CAAC;MAE3D,OAAO,IAAItB,OAAO,CAAC,CAACwB,OAAO,EAAEC,MAAM,KAAK;QACtCH,EAAE,GACAA,EAAE,IACF,UAAUI,GAAG,EAAE/D,IAAI,EAAE;UACnB,IAAI+D,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC,KAChBF,OAAO,CAAC7D,IAAI,CAAC;QACpB,CAAC;QAEHR,MAAM,CAACkE,IAAI,EAAE,CAACK,GAAG,EAAE/D,IAAI,KAAK;UAC1B,MAAMwB,KAAK,GAAG,CAAC,CAAC;UAChB,MAAMI,QAAQ,GAAG,CAAC,CAAC;UAEnB,IAAImC,GAAG,EAAE;YACP,OAAOrE,UAAU,CAACkE,IAAI,CAAC,IAAI,EAAEG,GAAG,EAAEJ,EAAE,CAAC;UACvC;UAEA,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,IAAI,CAACwB,KAAK,CAACD,MAAM,EAAED,CAAC,EAAE,EAAE;YAC1CE,KAAK,CAACwC,MAAM,CAACC,YAAY,CAACjE,IAAI,CAACwB,KAAK,CAACF,CAAC,CAAC,CAAC4C,EAAE,CAAC,CAAC,GAAGlE,IAAI,CAACwB,KAAK,CAACF,CAAC,CAAC;UAC9D;UAEA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,IAAI,CAAC4B,QAAQ,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;YAC7C,MAAM6C,WAAW,GAAGH,MAAM,CAACC,YAAY,CAACjE,IAAI,CAAC4B,QAAQ,CAACN,CAAC,CAAC,CAAC8C,KAAK,CAAC;YAC/DxC,QAAQ,CAACuC,WAAW,CAAC,GAAGvC,QAAQ,CAACuC,WAAW,CAAC,IAAI,CAAC,CAAC;YACnDvC,QAAQ,CAACuC,WAAW,CAAC,CACnBH,MAAM,CAACC,YAAY,CAACjE,IAAI,CAAC4B,QAAQ,CAACN,CAAC,CAAC,CAAC+C,MAAM,CAAC,CAC7C,GAAGrE,IAAI,CAAC4B,QAAQ,CAACN,CAAC,CAAC,CAACgD,MAAM;UAC7B;UAEAvC,SAAS,CAAC,IAAI,EAAExC,IAAI,CAACgF,OAAO,CAACb,IAAI,CAAC,EAAE1D,IAAI,CAACc,KAAK,CAAC,CAAC0D,IAAI,CAAE1D,KAAK,IAAK;YAC9D6C,EAAE,CAAC,IAAI,EAAE;cACPnC,KAAK;cACLI,QAAQ;cACRd,KAAK;cACL2D,MAAM,EAAEzE,IAAI,CAACyE,MAAM;cACnBC,IAAI,EAAE1E,IAAI,CAAC0E;YACb,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EAEDC,KAAK,EAAE;IACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,KAAK,CAAC5E,IAAI,EAAEQ,CAAC,EAAEC,CAAC,EAAEW,IAAI,EAAElB,QAAQ,EAAE2E,SAAS,EAAElB,EAAE,EAAE;MAC/C,IAAI,OAAOzD,QAAQ,KAAK,UAAU,IAAI,OAAOyD,EAAE,KAAK,WAAW,EAAE;QAC/DA,EAAE,GAAGzD,QAAQ;QACbA,QAAQ,GAAG4E,QAAQ;MACrB;MAEA,IAAI,OAAO5E,QAAQ,KAAK,WAAW,EAAE;QACnCA,QAAQ,GAAG4E,QAAQ;MACrB;MAEA,IAAI,OAAOD,SAAS,KAAK,UAAU,IAAI,OAAOlB,EAAE,KAAK,WAAW,EAAE;QAChEA,EAAE,GAAGkB,SAAS;QACdA,SAAS,GAAGC,QAAQ;MACtB;MAEA,IAAI,OAAOD,SAAS,KAAK,WAAW,EAAE;QACpCA,SAAS,GAAGC,QAAQ;MACtB;MAEA,IAAI,OAAO9E,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAON,UAAU,CAACkE,IAAI,CAAC,IAAI,EAAE,8BAA8B,EAAED,EAAE,CAAC;MAClE;MAEA,IACE,OAAOnD,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,IACrB,OAAOP,QAAQ,KAAK,QAAQ,EAC5B;QACA,OAAOR,UAAU,CAACkE,IAAI,CAAC,IAAI,EAAE,mCAAmC,EAAED,EAAE,CAAC;MACvE;MAEA,IAAI,OAAOzD,QAAQ,KAAK,QAAQ,EAAE;QAChC,OAAOR,UAAU,CAACkE,IAAI,CAAC,IAAI,EAAE,2BAA2B,EAAED,EAAE,CAAC;MAC/D;MAEA,IAAI,OAAOkB,SAAS,KAAK,QAAQ,EAAE;QACjC,OAAOnF,UAAU,CAACkE,IAAI,CAAC,IAAI,EAAE,4BAA4B,EAAED,EAAE,CAAC;MAChE;MAEA,IAAIoB,UAAU;MACd,IAAIC,UAAU;MAEd,IACE,OAAO5D,IAAI,KAAK,QAAQ,IACxBA,IAAI,CAACA,IAAI,KAAK,IAAI,IAClBA,IAAI,CAACA,IAAI,KAAK6D,SAAS,EACvB;QACAF,UAAU,GAAG3D,IAAI,CAAC2D,UAAU,IAAI,IAAI,CAACG,WAAW,CAAC9E,qBAAqB;QACtE4E,UAAU,GAAG5D,IAAI,CAAC4D,UAAU,IAAI,IAAI,CAACE,WAAW,CAACC,kBAAkB;QACnE,CAAC;UAAE/D;QAAK,CAAC,GAAGA,IAAI;MAClB,CAAC,MAAM;QACL2D,UAAU,GAAG,IAAI,CAACG,WAAW,CAAC9E,qBAAqB;QACnD4E,UAAU,GAAG,IAAI,CAACE,WAAW,CAACC,kBAAkB;QAChD/D,IAAI,GAAGA,IAAI,CAACgE,QAAQ,EAAE;MACxB;MAEA,IACEP,SAAS,KAAKC,QAAQ,IACtBE,UAAU,KAAK,IAAI,CAACE,WAAW,CAACG,qBAAqB,EACrD;QACA5E,CAAC,IAAIoE,SAAS,GAAGjF,iBAAiB,CAACI,IAAI,EAAEoB,IAAI,EAAElB,QAAQ,CAAC;MAC1D,CAAC,MAAM,IACL2E,SAAS,KAAKC,QAAQ,IACtBE,UAAU,KAAK,IAAI,CAACE,WAAW,CAACI,qBAAqB,EACrD;QACA7E,CAAC,IAAIoE,SAAS,GAAG,CAAC,GAAGjF,iBAAiB,CAACI,IAAI,EAAEoB,IAAI,EAAElB,QAAQ,CAAC,GAAG,CAAC;MAClE;MAEA,MAAMmB,gBAAgB,GAAGkE,MAAM,CAACC,OAAO,CAACxF,IAAI,CAACwB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACM,QAAQ;MAClE,MAAM;QAAE2D,KAAK;QAAEC;MAAY,CAAC,GAAG7F,UAAU,CAACG,IAAI,EAAEoB,IAAI,EAAElB,QAAQ,CAAC;MAE/DuF,KAAK,CAACE,OAAO,CAAE1F,IAAI,IAAK;QACtB,MAAM2F,UAAU,GAAG3F,IAAI,CAAC2C,IAAI,CAAC,GAAG,CAAC;QACjC,MAAMiD,cAAc,GAAG/F,uBAAuB,CAC5C,IAAI,CAACoF,WAAW,EAChBlF,IAAI,EACJ4F,UAAU,EACV1F,QAAQ,EACR6E,UAAU,CACX;QAED5D,SAAS,CAACyC,IAAI,CACZ,IAAI,EACJ5D,IAAI,EACJQ,CAAC,GAAGqF,cAAc,EAClBpF,CAAC,EACDmF,UAAU,EACVvE,gBAAgB,CACjB;QAEDZ,CAAC,IAAIT,IAAI,CAACyE,MAAM,CAACqB,UAAU;MAC7B,CAAC,CAAC;MAEF,IAAIrG,aAAa,CAACkE,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;UAAEpD,CAAC,EAAEA,CAAC,GAAGkF,WAAW;UAAEjF;QAAE,CAAC,CAAC;MACtD;MAEA,OAAO,IAAI;IACb;EACF;AACF,CAAC,CAAC"}