{"version": 3, "file": "index.js", "names": ["mergeDeep", "jpeg", "png", "bmp", "tiff", "gif"], "sources": ["../src/index.js"], "sourcesContent": ["import { mergeDeep } from \"timm\";\n\nimport jpeg from \"@jimp/jpeg\";\nimport png from \"@jimp/png\";\nimport bmp from \"@jimp/bmp\";\nimport tiff from \"@jimp/tiff\";\nimport gif from \"@jimp/gif\";\n\nexport default () => mergeDeep(jpeg(), png(), bmp(), tiff(), gif());\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AAA4B;AAAA,eAEb,MAAM,IAAAA,eAAS,EAAC,IAAAC,aAAI,GAAE,EAAE,IAAAC,YAAG,GAAE,EAAE,IAAAC,YAAG,GAAE,EAAE,IAAAC,aAAI,GAAE,EAAE,IAAAC,YAAG,GAAE,CAAC;AAAA;AAAA;AAAA"}