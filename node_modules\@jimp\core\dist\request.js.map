{"version": 3, "file": "request.js", "names": ["cb", "url", "options", "fetch", "then", "response", "ok", "arrayBuffer", "catch", "error", "Error", "message", "status", "data"], "sources": ["../src/request.js"], "sourcesContent": ["import \"isomorphic-fetch\";\n\nexport default ({ url, ...options }, cb) => {\n  fetch(url, options)\n    .then((response) => {\n      if (response.ok) {\n        return response.arrayBuffer().catch((error) => {\n          throw new Error(\n            `Response is not a buffer for url ${url}. Error: ${error.message}`\n          );\n        });\n      }\n\n      throw new Error(`HTTP Status ${response.status} for url ${url}`);\n    })\n    .then((data) => cb(null, data))\n    .catch((error) => cb(error));\n};\n"], "mappings": ";;;;;;AAAA;AAA0B,eAEX,OAAsBA,EAAE,KAAK;EAAA,IAA5B;IAAEC,GAAG;IAAE,GAAGC;EAAQ,CAAC;EACjCC,KAAK,CAACF,GAAG,EAAEC,OAAO,CAAC,CAChBE,IAAI,CAAEC,QAAQ,IAAK;IAClB,IAAIA,QAAQ,CAACC,EAAE,EAAE;MACf,OAAOD,QAAQ,CAACE,WAAW,EAAE,CAACC,KAAK,CAAEC,KAAK,IAAK;QAC7C,MAAM,IAAIC,KAAK,CACZ,oCAAmCT,GAAI,YAAWQ,KAAK,CAACE,OAAQ,EAAC,CACnE;MACH,CAAC,CAAC;IACJ;IAEA,MAAM,IAAID,KAAK,CAAE,eAAcL,QAAQ,CAACO,MAAO,YAAWX,GAAI,EAAC,CAAC;EAClE,CAAC,CAAC,CACDG,IAAI,CAAES,IAAI,IAAKb,EAAE,CAAC,IAAI,EAAEa,IAAI,CAAC,CAAC,CAC9BL,KAAK,CAAEC,KAAK,IAAKT,EAAE,CAACS,KAAK,CAAC,CAAC;AAChC,CAAC;AAAA;AAAA;AAAA"}