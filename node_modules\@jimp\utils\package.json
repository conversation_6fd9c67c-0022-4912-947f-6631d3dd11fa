{"name": "@jimp/utils", "version": "0.22.12", "description": "Utils for jimp extensions.", "main": "dist/index.js", "module": "es/index.js", "types": "index.d.ts", "repository": "jimp-dev/jimp", "scripts": {"build": "npm run build:node:production && npm run build:module", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node"}, "author": "", "license": "MIT", "publishConfig": {"access": "public"}, "dependencies": {"regenerator-runtime": "^0.13.3"}, "gitHead": "a4a8d6364bbf97629749e196f3b0a4c94c9a7abc"}