{"version": 3, "file": "index.js", "names": ["isNodePattern", "circle", "options", "cb", "radius", "bitmap", "width", "height", "center", "x", "y", "scanQuiet", "idx", "curR", "Math", "sqrt", "pow", "data", "call"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Creates a circle out of an image.\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} options (optional) radius, x, y\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  circle(options = {}, cb) {\n    if (typeof options === \"function\") {\n      cb = options;\n      options = {};\n    }\n\n    const radius =\n      options.radius ||\n      (this.bitmap.width > this.bitmap.height\n        ? this.bitmap.height\n        : this.bitmap.width) / 2;\n\n    const center = {\n      x: typeof options.x === \"number\" ? options.x : this.bitmap.width / 2,\n      y: typeof options.y === \"number\" ? options.y : this.bitmap.height / 2,\n    };\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        const curR = Math.sqrt(\n          Math.pow(x - center.x, 2) + Math.pow(y - center.y, 2)\n        );\n\n        if (radius - curR <= 0.0) {\n          this.bitmap.data[idx + 3] = 0;\n        } else if (radius - curR < 1.0) {\n          this.bitmap.data[idx + 3] = 255 * (radius - curR);\n        }\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,MAAM,GAAmB;IAAA,IAAlBC,OAAO,uEAAG,CAAC,CAAC;IAAA,IAAEC,EAAE;IACrB,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MACjCC,EAAE,GAAGD,OAAO;MACZA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAME,MAAM,GACVF,OAAO,CAACE,MAAM,IACd,CAAC,IAAI,CAACC,MAAM,CAACC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACE,MAAM,GACnC,IAAI,CAACF,MAAM,CAACE,MAAM,GAClB,IAAI,CAACF,MAAM,CAACC,KAAK,IAAI,CAAC;IAE5B,MAAME,MAAM,GAAG;MACbC,CAAC,EAAE,OAAOP,OAAO,CAACO,CAAC,KAAK,QAAQ,GAAGP,OAAO,CAACO,CAAC,GAAG,IAAI,CAACJ,MAAM,CAACC,KAAK,GAAG,CAAC;MACpEI,CAAC,EAAE,OAAOR,OAAO,CAACQ,CAAC,KAAK,QAAQ,GAAGR,OAAO,CAACQ,CAAC,GAAG,IAAI,CAACL,MAAM,CAACE,MAAM,GAAG;IACtE,CAAC;IAED,IAAI,CAACI,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUE,CAAC,EAAEC,CAAC,EAAEE,GAAG,EAAE;MACnB,MAAMC,IAAI,GAAGC,IAAI,CAACC,IAAI,CACpBD,IAAI,CAACE,GAAG,CAACP,CAAC,GAAGD,MAAM,CAACC,CAAC,EAAE,CAAC,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACN,CAAC,GAAGF,MAAM,CAACE,CAAC,EAAE,CAAC,CAAC,CACtD;MAED,IAAIN,MAAM,GAAGS,IAAI,IAAI,GAAG,EAAE;QACxB,IAAI,CAACR,MAAM,CAACY,IAAI,CAACL,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;MAC/B,CAAC,MAAM,IAAIR,MAAM,GAAGS,IAAI,GAAG,GAAG,EAAE;QAC9B,IAAI,CAACR,MAAM,CAACY,IAAI,CAACL,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAIR,MAAM,GAAGS,IAAI,CAAC;MACnD;IACF,CAAC,CACF;IAED,IAAIb,aAAa,CAACG,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACe,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}