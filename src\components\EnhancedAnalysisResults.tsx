'use client'

import { useState } from 'react'
import { TrendingUp, TrendingDown, Target, Shield, DollarSign, Clock, AlertTriangle, CheckCircle, MessageSquare, Activity, Zap, BarChart3 } from 'lucide-react'
import FeedbackModal from './FeedbackModal'

interface EnhancedAnalysisResultsProps {
  results: {
    direction: 'BUY' | 'SELL' | 'NEUTRAL'
    confidence: number
    entry: number
    stopLoss: number
    takeProfits: {
      tp1: number
      tp2: number
      tp3: number
    }
    riskReward: {
      tp1: number
      tp2: number
      tp3: number
    }
    timeframe: string
    strategy: string
    reasoning: {
      marketStructure: string
      orderBlocks: string[]
      fairValueGaps: string[]
      liquidityLevels: string[]
      ictConcepts: string[]
      riskAssessment: string
    }
    timestamp: string
    analysisId?: string
    marketConditions?: {
      volatility: 'high' | 'medium' | 'low'
      sentiment: 'bullish' | 'bearish' | 'neutral'
      session: string
    }
  }
}

export default function EnhancedAnalysisResults({ results }: EnhancedAnalysisResultsProps) {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'technical' | 'risk'>('overview')

  const getDirectionColor = (direction: string) => {
    switch (direction) {
      case 'BUY': return 'text-green-500'
      case 'SELL': return 'text-red-500'
      default: return 'text-yellow-500'
    }
  }

  const getDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'BUY': return <TrendingUp className="w-6 h-6" />
      case 'SELL': return <TrendingDown className="w-6 h-6" />
      default: return <AlertTriangle className="w-6 h-6" />
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-500'
    if (confidence >= 60) return 'text-yellow-500'
    return 'text-red-500'
  }

  const formatPrice = (price: number) => {
    return price.toFixed(2)
  }

  const calculateRiskAmount = () => {
    return Math.abs(results.entry - results.stopLoss)
  }

  const calculateRewardAmount = (tp: number) => {
    return Math.abs(tp - results.entry)
  }

  return (
    <div className="bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold text-white">Enhanced Analysis Results</h2>
        <div className="flex items-center justify-center space-x-6">
          <div className={`flex items-center space-x-2 ${getDirectionColor(results.direction)}`}>
            {getDirectionIcon(results.direction)}
            <span className="text-2xl font-bold">{results.direction}</span>
          </div>
          <div className="text-gray-400">•</div>
          <div className="text-white">
            <span className={`text-lg font-semibold ${getConfidenceColor(results.confidence)}`}>
              {results.confidence}%
            </span>
            <span className="text-gray-400 ml-1">confidence</span>
          </div>
          <div className="text-gray-400">•</div>
          <div className="text-white">
            <span className="text-lg font-semibold text-gold-400">{results.strategy}</span>
            <span className="text-gray-400 ml-1">strategy</span>
          </div>
        </div>
      </div>

      {/* Market Conditions */}
      {results.marketConditions && (
        <div className="bg-black/50 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
            <Activity className="w-5 h-5 mr-2 text-blue-400" />
            Current Market Conditions
          </h3>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-sm text-gray-400">Volatility</div>
              <div className={`text-lg font-semibold ${
                results.marketConditions.volatility === 'high' ? 'text-red-400' :
                results.marketConditions.volatility === 'medium' ? 'text-yellow-400' : 'text-green-400'
              }`}>
                {results.marketConditions.volatility.toUpperCase()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-400">Sentiment</div>
              <div className={`text-lg font-semibold ${
                results.marketConditions.sentiment === 'bullish' ? 'text-green-400' :
                results.marketConditions.sentiment === 'bearish' ? 'text-red-400' : 'text-yellow-400'
              }`}>
                {results.marketConditions.sentiment.toUpperCase()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-400">Session</div>
              <div className="text-lg font-semibold text-blue-400">
                {results.marketConditions.session}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-black/50 rounded-lg p-1">
        {[
          { id: 'overview', label: 'Overview', icon: Target },
          { id: 'technical', label: 'Technical', icon: BarChart3 },
          { id: 'risk', label: 'Risk Management', icon: Shield }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all ${
              activeTab === tab.id
                ? 'bg-gold-500 text-black font-semibold'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2 text-gold-400" />
              Trade Setup
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Entry Price:</span>
                <span className="text-white font-mono text-lg">${formatPrice(results.entry)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Stop Loss:</span>
                <span className="text-red-400 font-mono text-lg">${formatPrice(results.stopLoss)}</span>
              </div>
              <div className="border-t border-gray-700 pt-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">TP1:</span>
                    <span className="text-green-400 font-mono">${formatPrice(results.takeProfits.tp1)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">TP2:</span>
                    <span className="text-green-400 font-mono">${formatPrice(results.takeProfits.tp2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">TP3:</span>
                    <span className="text-green-400 font-mono">${formatPrice(results.takeProfits.tp3)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <DollarSign className="w-5 h-5 mr-2 text-gold-400" />
              Risk/Reward Analysis
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Risk Amount:</span>
                <span className="text-red-400 font-semibold">${calculateRiskAmount().toFixed(2)}</span>
              </div>
              <div className="border-t border-gray-700 pt-4 space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">TP1 R:R:</span>
                  <span className="text-gold-400 font-semibold">1:{results.riskReward.tp1}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">TP2 R:R:</span>
                  <span className="text-gold-400 font-semibold">1:{results.riskReward.tp2}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">TP3 R:R:</span>
                  <span className="text-gold-400 font-semibold">1:{results.riskReward.tp3}</span>
                </div>
              </div>
              <div className="border-t border-gray-700 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Timeframe:</span>
                  <span className="text-white">{results.timeframe}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'technical' && (
        <div className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
              <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
                Market Structure
              </h4>
              <p className="text-gray-300">{results.reasoning.marketStructure}</p>
            </div>

            <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
              <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                <Shield className="w-5 h-5 mr-2 text-blue-400" />
                Risk Assessment
              </h4>
              <p className="text-gray-300">{results.reasoning.riskAssessment}</p>
            </div>
          </div>

          {results.reasoning.orderBlocks.length > 0 && (
            <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
              <h4 className="text-lg font-semibold text-white mb-3">Order Blocks Identified</h4>
              <ul className="space-y-2">
                {results.reasoning.orderBlocks.map((block, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <span className="text-gold-400 mr-2">•</span>
                    {block}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {results.reasoning.ictConcepts.length > 0 && (
            <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
              <h4 className="text-lg font-semibold text-white mb-3">ICT Concepts Applied</h4>
              <ul className="space-y-2">
                {results.reasoning.ictConcepts.map((concept, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <span className="text-gold-400 mr-2">•</span>
                    {concept}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {activeTab === 'risk' && (
        <div className="space-y-6">
          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">Position Sizing Calculator</h4>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-800 rounded-lg">
                <div className="text-sm text-gray-400 mb-1">1% Risk</div>
                <div className="text-lg font-semibold text-white">
                  ${(1000 / calculateRiskAmount()).toFixed(0)} lots
                </div>
              </div>
              <div className="text-center p-4 bg-gray-800 rounded-lg">
                <div className="text-sm text-gray-400 mb-1">2% Risk</div>
                <div className="text-lg font-semibold text-white">
                  ${(2000 / calculateRiskAmount()).toFixed(0)} lots
                </div>
              </div>
              <div className="text-center p-4 bg-gray-800 rounded-lg">
                <div className="text-sm text-gray-400 mb-1">3% Risk</div>
                <div className="text-lg font-semibold text-white">
                  ${(3000 / calculateRiskAmount()).toFixed(0)} lots
                </div>
              </div>
            </div>
          </div>

          <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
            <h4 className="text-lg font-semibold text-white mb-4">Trade Management Plan</h4>
            <div className="space-y-3 text-gray-300">
              <div className="flex items-start">
                <Zap className="w-4 h-4 mr-2 mt-1 text-yellow-400" />
                <span>Move stop loss to breakeven after TP1 is hit</span>
              </div>
              <div className="flex items-start">
                <Target className="w-4 h-4 mr-2 mt-1 text-green-400" />
                <span>Close 50% of position at TP1, 30% at TP2, 20% at TP3</span>
              </div>
              <div className="flex items-start">
                <Shield className="w-4 h-4 mr-2 mt-1 text-blue-400" />
                <span>Trail stop loss by 50% of profit after TP2</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Feedback Section */}
      <div className="bg-black/50 rounded-lg p-6 border border-gray-700">
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-white">Help Improve Our Analysis</h3>
          <p className="text-gray-400">
            Your feedback helps our AI learn and provide better trading analysis
          </p>
          <button
            onClick={() => setShowFeedbackModal(true)}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2 mx-auto"
          >
            <MessageSquare className="w-5 h-5" />
            <span>Provide Feedback</span>
          </button>
        </div>
      </div>

      {/* Timestamp */}
      <div className="text-center text-gray-500 text-sm flex items-center justify-center">
        <Clock className="w-4 h-4 mr-2" />
        Analysis completed: {new Date(results.timestamp).toLocaleString()}
      </div>

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        analysisId={results.analysisId || 'unknown'}
      />
    </div>
  )
}
