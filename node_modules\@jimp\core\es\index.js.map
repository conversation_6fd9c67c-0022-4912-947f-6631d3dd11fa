{"version": 3, "file": "index.js", "names": ["fs", "Path", "EventEmitter", "isNodePattern", "throwError", "scan", "scanIterator", "anyBase", "pixelMatch", "tinyColor", "ImagePHash", "request", "composite", "promisify", "MIME", "parseBitmap", "<PERSON><PERSON><PERSON><PERSON>", "getBufferAsync", "constants", "alphabet", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "NaN", "i", "maxHash", "BIN", "slice", "Array", "join", "push", "length", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test", "Object", "prototype", "toString", "call", "toLowerCase", "indexOf", "bufferFromArrayBuffer", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "alloc", "byteLength", "view", "Uint8Array", "loadFromURL", "options", "cb", "err", "data", "<PERSON><PERSON><PERSON><PERSON>", "Error", "url", "loadBufferFromPath", "src", "readFile", "match", "isRawRGBAData", "obj", "width", "height", "Uint8ClampedArray", "makeRGBABufferFromRGB", "rgbaBuffer", "allocUnsafe", "j", "emptyBitmap", "<PERSON><PERSON>", "constructor", "args", "MIME_PNG", "path", "write", "mime", "getBase64", "getPixelColor", "setPixelColor", "jimpInstance", "finish", "evData", "methodName", "setTimeout", "emitError", "emitMulti", "parseInt", "w", "h", "_background", "cssColorToHex", "bitmap", "writeUInt32BE", "original", "from", "_quality", "_deflateLevel", "_deflateStrategy", "_filterType", "_rgba", "_originalMime", "imageData", "isRGBA", "extraConstructor", "__extraConstructors", "find", "c", "Promise", "resolve", "reject", "run", "then", "catch", "rgba", "bool", "eventName", "assign", "emit", "getHeight", "getWidth", "inspect", "getMIME", "getExtension", "createWriteStream", "getType", "pathObj", "parse", "dir", "mkdirSync", "recursive", "stream", "on", "end", "AUTO", "hash", "base", "pHash", "getHash", "distanceFromHash", "compareHash", "currentHash", "distance", "getPixelIndex", "x", "y", "edgeHandling", "xi", "yi", "EDGE_EXTEND", "Math", "round", "EDGE_WRAP", "idx", "hex", "readUInt32BE", "has<PERSON><PERSON><PERSON>", "byteLen", "addConstants", "entries", "for<PERSON>ach", "name", "value", "addJimpMethods", "methods", "appendConstructorOption", "read", "image", "create", "rgbaToInt", "r", "g", "b", "a", "intToRGBA", "floor", "pow", "cssColor", "Number", "toHex8", "limit255", "n", "max", "min", "diff", "img1", "img2", "threshold", "bmp1", "bmp2", "clone<PERSON>uiet", "resize", "numDiffPixels", "percent", "phash", "hash1", "hash2", "compareHashes", "colorDiff", "rgba1", "rgba2", "maxVal", "jimpEvMethod", "evName", "method", "evNameBefore", "evNameAfter", "replace", "wrappedCb", "apply", "result", "error", "clone", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "f", "process", "env", "ENVIRONMENT", "gl", "window", "self", "addType"], "sources": ["../src/index.js"], "sourcesContent": ["import fs from \"fs\";\nimport Path from \"path\";\nimport EventEmitter from \"events\";\n\nimport { isNodePattern, throwError, scan, scanIterator } from \"@jimp/utils\";\nimport anyBase from \"any-base\";\nimport pixelMatch from \"pixelmatch\";\nimport tinyColor from \"tinycolor2\";\n\nimport ImagePHash from \"./modules/phash\";\nimport request from \"./request\";\n\nimport composite from \"./composite\";\nimport promisify from \"./utils/promisify\";\nimport * as MIME from \"./utils/mime\";\nimport { parseBitmap, getBuffer, getBufferAsync } from \"./utils/image-bitmap\";\nimport * as constants from \"./constants\";\n\nconst alphabet =\n  \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_\";\n\n// an array storing the maximum string length of hashes at various bases\n// 0 and 1 do not exist as possible hash lengths\nconst maxHashLength = [NaN, NaN];\n\nfor (let i = 2; i < 65; i++) {\n  const maxHash = anyBase(\n    anyBase.BIN,\n    alphabet.slice(0, i)\n  )(new Array(64 + 1).join(\"1\"));\n  maxHashLength.push(maxHash.length);\n}\n\n// no operation\nfunction noop() {}\n\n// error checking methods\n\nfunction isArrayBuffer(test) {\n  return (\n    Object.prototype.toString.call(test).toLowerCase().indexOf(\"arraybuffer\") >\n    -1\n  );\n}\n\n// Prepare a Buffer object from the arrayBuffer. Necessary in the browser > node conversion,\n// But this function is not useful when running in node directly\nfunction bufferFromArrayBuffer(arrayBuffer) {\n  const buffer = Buffer.alloc(arrayBuffer.byteLength);\n  const view = new Uint8Array(arrayBuffer);\n\n  for (let i = 0; i < buffer.length; ++i) {\n    buffer[i] = view[i];\n  }\n\n  return buffer;\n}\n\nfunction loadFromURL(options, cb) {\n  request(options, (err, data) => {\n    if (err) {\n      return cb(err);\n    }\n\n    if (typeof data === \"object\" && Buffer.isBuffer(data)) {\n      return cb(null, data);\n    }\n\n    if (typeof data === \"object\" && isArrayBuffer(data)) {\n      return cb(null, bufferFromArrayBuffer(data));\n    }\n\n    return new Error(`Could not load Buffer from <${options.url}>`);\n  });\n}\n\nfunction loadBufferFromPath(src, cb) {\n  if (\n    fs &&\n    typeof fs.readFile === \"function\" &&\n    !src.match(/^(http|ftp)s?:\\/\\/./)\n  ) {\n    fs.readFile(src, cb);\n  } else {\n    loadFromURL({ url: src }, cb);\n  }\n}\n\nfunction isRawRGBAData(obj) {\n  return (\n    obj &&\n    typeof obj === \"object\" &&\n    typeof obj.width === \"number\" &&\n    typeof obj.height === \"number\" &&\n    (Buffer.isBuffer(obj.data) ||\n      obj.data instanceof Uint8Array ||\n      (typeof Uint8ClampedArray === \"function\" &&\n        obj.data instanceof Uint8ClampedArray)) &&\n    (obj.data.length === obj.width * obj.height * 4 ||\n      obj.data.length === obj.width * obj.height * 3)\n  );\n}\n\nfunction makeRGBABufferFromRGB(buffer) {\n  if (buffer.length % 3 !== 0) {\n    throw new Error(\"Buffer length is incorrect\");\n  }\n\n  const rgbaBuffer = Buffer.allocUnsafe((buffer.length / 3) * 4);\n  let j = 0;\n\n  for (let i = 0; i < buffer.length; i++) {\n    rgbaBuffer[j] = buffer[i];\n\n    if ((i + 1) % 3 === 0) {\n      rgbaBuffer[++j] = 255;\n    }\n\n    j++;\n  }\n\n  return rgbaBuffer;\n}\n\nconst emptyBitmap = {\n  data: null,\n  width: null,\n  height: null,\n};\n\n/**\n * Jimp constructor (from a file)\n * @param path a path to the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a url with options)\n * @param options { url, otherOptions}\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from another Jimp image or raw image data)\n * @param image a Jimp image to clone\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a Buffer)\n * @param data a Buffer containing the image data\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param background color to fill the image with\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\nclass Jimp extends EventEmitter {\n  // An object representing a bitmap in memory, comprising:\n  //  - data: a buffer of the bitmap data\n  //  - width: the width of the image in pixels\n  //  - height: the height of the image in pixels\n  bitmap = emptyBitmap;\n\n  // Default colour to use for new pixels\n  _background = 0x00000000;\n\n  // Default MIME is PNG\n  _originalMime = Jimp.MIME_PNG;\n\n  // Exif data for the image\n  _exif = null;\n\n  // Whether Transparency supporting formats will be exported as RGB or RGBA\n  _rgba = true;\n\n  constructor(...args) {\n    super();\n\n    const jimpInstance = this;\n    let cb = noop;\n\n    if (isArrayBuffer(args[0])) {\n      args[0] = bufferFromArrayBuffer(args[0]);\n    }\n\n    function finish(...args) {\n      const [err] = args;\n      const evData = err || {};\n      evData.methodName = \"constructor\";\n\n      setTimeout(() => {\n        // run on next tick.\n        if (err && cb === noop) {\n          jimpInstance.emitError(\"constructor\", err);\n        } else if (!err) {\n          jimpInstance.emitMulti(\"constructor\", \"initialized\");\n        }\n\n        cb.call(jimpInstance, ...args);\n      }, 1);\n    }\n\n    if (\n      (typeof args[0] === \"number\" && typeof args[1] === \"number\") ||\n      (parseInt(args[0], 10) && parseInt(args[1], 10))\n    ) {\n      // create a new image\n      const w = parseInt(args[0], 10);\n      const h = parseInt(args[1], 10);\n      cb = args[2];\n\n      // with a hex color\n      if (typeof args[2] === \"number\") {\n        this._background = args[2];\n        cb = args[3];\n      }\n\n      // with a css color\n      if (typeof args[2] === \"string\") {\n        this._background = Jimp.cssColorToHex(args[2]);\n        cb = args[3];\n      }\n\n      if (typeof cb === \"undefined\") {\n        cb = noop;\n      }\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      this.bitmap = {\n        data: Buffer.alloc(w * h * 4),\n        width: w,\n        height: h,\n      };\n\n      for (let i = 0; i < this.bitmap.data.length; i += 4) {\n        this.bitmap.data.writeUInt32BE(this._background, i);\n      }\n\n      finish(null, this);\n    } else if (typeof args[0] === \"object\" && args[0].url) {\n      cb = args[1] || noop;\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      loadFromURL(args[0], (err, data) => {\n        if (err) {\n          return throwError.call(this, err, finish);\n        }\n\n        this.parseBitmap(data, args[0].url, finish);\n      });\n    } else if (args[0] instanceof Jimp) {\n      // clone an existing Jimp\n      const [original] = args;\n      cb = args[1];\n\n      if (typeof cb === \"undefined\") {\n        cb = noop;\n      }\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      this.bitmap = {\n        data: Buffer.from(original.bitmap.data),\n        width: original.bitmap.width,\n        height: original.bitmap.height,\n      };\n\n      this._quality = original._quality;\n      this._deflateLevel = original._deflateLevel;\n      this._deflateStrategy = original._deflateStrategy;\n      this._filterType = original._filterType;\n      this._rgba = original._rgba;\n      this._background = original._background;\n      this._originalMime = original._originalMime;\n\n      finish(null, this);\n    } else if (isRawRGBAData(args[0])) {\n      const [imageData] = args;\n      cb = args[1] || noop;\n\n      const isRGBA =\n        imageData.width * imageData.height * 4 === imageData.data.length;\n      const buffer = isRGBA\n        ? Buffer.from(imageData.data)\n        : makeRGBABufferFromRGB(imageData.data);\n\n      this.bitmap = {\n        data: buffer,\n        width: imageData.width,\n        height: imageData.height,\n      };\n\n      finish(null, this);\n    } else if (typeof args[0] === \"string\") {\n      // read from a path\n      const path = args[0];\n      cb = args[1];\n\n      if (typeof cb === \"undefined\") {\n        cb = noop;\n      }\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      loadBufferFromPath(path, (err, data) => {\n        if (err) {\n          return throwError.call(this, err, finish);\n        }\n\n        this.parseBitmap(data, path, finish);\n      });\n    } else if (typeof args[0] === \"object\" && Buffer.isBuffer(args[0])) {\n      // read from a buffer\n      const data = args[0];\n      cb = args[1];\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      this.parseBitmap(data, null, finish);\n    } else {\n      // Allow client libs to add new ways to build a Jimp object.\n      // Extra constructors must be added by `Jimp.appendConstructorOption()`\n      cb = args[args.length - 1];\n\n      if (typeof cb !== \"function\") {\n        // TODO: try to solve the args after cb problem.\n        cb = args[args.length - 2];\n\n        if (typeof cb !== \"function\") {\n          cb = noop;\n        }\n      }\n\n      const extraConstructor = Jimp.__extraConstructors.find((c) =>\n        c.test(...args)\n      );\n\n      if (extraConstructor) {\n        new Promise((resolve, reject) => {\n          extraConstructor.run.call(this, resolve, reject, ...args);\n        })\n          .then(() => finish(null, this))\n          .catch(finish);\n      } else {\n        return throwError.call(\n          this,\n          \"No matching constructor overloading was found. \" +\n            \"Please see the docs for how to call the Jimp constructor.\",\n          finish\n        );\n      }\n    }\n  }\n\n  /**\n   * Parse a bitmap with the loaded image types.\n   *\n   * @param {Buffer} data raw image data\n   * @param {string} path optional path to file\n   * @param {function(Error, Jimp)} finish (optional) a callback for when complete\n   * @memberof Jimp\n   */\n  parseBitmap(data, path, finish) {\n    parseBitmap.call(this, data, null, finish);\n  }\n\n  /**\n   * Sets the type of the image (RGB or RGBA) when saving in a format that supports transparency (default is RGBA)\n   * @param {boolean} bool A Boolean, true to use RGBA or false to use RGB\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  rgba(bool, cb) {\n    if (typeof bool !== \"boolean\") {\n      return throwError.call(\n        this,\n        \"bool must be a boolean, true for RGBA or false for RGB\",\n        cb\n      );\n    }\n\n    this._rgba = bool;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n\n  /**\n   * Emit for multiple listeners\n   * @param {string} methodName name of the method to emit an error for\n   * @param {string} eventName name of the eventName to emit an error for\n   * @param {object} data to emit\n   */\n  emitMulti(methodName, eventName, data = {}) {\n    data = Object.assign(data, { methodName, eventName });\n    this.emit(\"any\", data);\n\n    if (methodName) {\n      this.emit(methodName, data);\n    }\n\n    this.emit(eventName, data);\n  }\n\n  emitError(methodName, err) {\n    this.emitMulti(methodName, \"error\", err);\n  }\n\n  /**\n   * Get the current height of the image\n   * @return {number} height of the image\n   */\n  getHeight() {\n    return this.bitmap.height;\n  }\n\n  /**\n   * Get the current width of the image\n   * @return {number} width of the image\n   */\n  getWidth() {\n    return this.bitmap.width;\n  }\n\n  /**\n   * Nicely format Jimp object when sent to the console e.g. console.log(image)\n   * @returns {string} pretty printed\n   */\n  inspect() {\n    return (\n      \"<Jimp \" +\n      (this.bitmap === emptyBitmap\n        ? \"pending...\"\n        : this.bitmap.width + \"x\" + this.bitmap.height) +\n      \">\"\n    );\n  }\n\n  /**\n   * Nicely format Jimp object when converted to a string\n   * @returns {string} pretty printed\n   */\n  toString() {\n    return \"[object Jimp]\";\n  }\n\n  /**\n   * Returns the original MIME of the image (default: \"image/png\")\n   * @returns {string} the MIME\n   */\n  getMIME() {\n    const mime = this._originalMime || Jimp.MIME_PNG;\n\n    return mime;\n  }\n\n  /**\n   * Returns the appropriate file extension for the original MIME of the image (default: \"png\")\n   * @returns {string} the file extension\n   */\n  getExtension() {\n    const mime = this.getMIME();\n\n    return MIME.getExtension(mime);\n  }\n\n  /**\n   * Writes the image to a file\n   * @param {string} path a path to the destination file\n   * @param {function(Error, Jimp)} cb (optional) a function to call when the image is saved to disk\n   * @returns {Jimp} this for chaining of methods\n   */\n  write(path, cb) {\n    if (!fs || !fs.createWriteStream) {\n      throw new Error(\n        \"Cant access the filesystem. You can use the getBase64 method.\"\n      );\n    }\n\n    if (typeof path !== \"string\") {\n      return throwError.call(this, \"path must be a string\", cb);\n    }\n\n    if (typeof cb === \"undefined\") {\n      cb = noop;\n    }\n\n    if (typeof cb !== \"function\") {\n      return throwError.call(this, \"cb must be a function\", cb);\n    }\n\n    const mime = MIME.getType(path) || this.getMIME();\n    const pathObj = Path.parse(path);\n\n    if (pathObj.dir) {\n      fs.mkdirSync(pathObj.dir, { recursive: true });\n    }\n\n    this.getBuffer(mime, (err, buffer) => {\n      if (err) {\n        return throwError.call(this, err, cb);\n      }\n\n      const stream = fs.createWriteStream(path);\n\n      stream\n        .on(\"open\", () => {\n          stream.write(buffer);\n          stream.end();\n        })\n        .on(\"error\", (err) => {\n          return throwError.call(this, err, cb);\n        });\n      stream.on(\"finish\", () => {\n        cb.call(this, null, this);\n      });\n    });\n\n    return this;\n  }\n\n  writeAsync = (path) => promisify(this.write, this, path);\n\n  /**\n   * Converts the image to a base 64 string\n   * @param {string} mime the mime type of the image data to be created\n   * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n   * @returns {Jimp} this for chaining of methods\n   */\n  getBase64(mime, cb) {\n    if (mime === Jimp.AUTO) {\n      // allow auto MIME detection\n      mime = this.getMIME();\n    }\n\n    if (typeof mime !== \"string\") {\n      return throwError.call(this, \"mime must be a string\", cb);\n    }\n\n    if (typeof cb !== \"function\") {\n      return throwError.call(this, \"cb must be a function\", cb);\n    }\n\n    this.getBuffer(mime, function (err, data) {\n      if (err) {\n        return throwError.call(this, err, cb);\n      }\n\n      const src = \"data:\" + mime + \";base64,\" + data.toString(\"base64\");\n      cb.call(this, null, src);\n    });\n\n    return this;\n  }\n\n  getBase64Async = (mime) => promisify(this.getBase64, this, mime);\n\n  /**\n   * Generates a perceptual hash of the image <https://en.wikipedia.org/wiki/Perceptual_hashing>. And pads the string. Can configure base.\n   * @param {number} base (optional) a number between 2 and 64 representing the base for the hash (e.g. 2 is binary, 10 is decimal, 16 is hex, 64 is base 64). Defaults to 64.\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {string} a string representing the hash\n   */\n  hash(base, cb) {\n    base = base || 64;\n\n    if (typeof base === \"function\") {\n      cb = base;\n      base = 64;\n    }\n\n    if (typeof base !== \"number\") {\n      return throwError.call(this, \"base must be a number\", cb);\n    }\n\n    if (base < 2 || base > 64) {\n      return throwError.call(\n        this,\n        \"base must be a number between 2 and 64\",\n        cb\n      );\n    }\n\n    let hash = this.pHash();\n    hash = anyBase(anyBase.BIN, alphabet.slice(0, base))(hash);\n\n    while (hash.length < maxHashLength[base]) {\n      hash = \"0\" + hash; // pad out with leading zeros\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, hash);\n    }\n\n    return hash;\n  }\n\n  /**\n   * Calculates the perceptual hash\n   * @returns {number} the perceptual hash\n   */\n  pHash() {\n    const pHash = new ImagePHash();\n    return pHash.getHash(this);\n  }\n\n  /**\n   * Calculates the hamming distance of the current image and a hash based on their perceptual hash\n   * @param {hash} compareHash hash to compare to\n   * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n   */\n  distanceFromHash(compareHash) {\n    const pHash = new ImagePHash();\n    const currentHash = pHash.getHash(this);\n\n    return pHash.distance(currentHash, compareHash);\n  }\n\n  /**\n   * Converts the image to a buffer\n   * @param {string} mime the mime type of the image buffer to be created\n   * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n   * @returns {Jimp} this for chaining of methods\n   */\n  getBuffer = getBuffer;\n\n  getBufferAsync = getBufferAsync;\n\n  /**\n   * Returns the offset of a pixel in the bitmap buffer\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {number} edgeHandling (optional) define how to sum pixels from outside the border\n   * @param {number} cb (optional) a callback for when complete\n   * @returns {number} the index of the pixel or -1 if not found\n   */\n  getPixelIndex(x, y, edgeHandling, cb) {\n    let xi;\n    let yi;\n\n    if (typeof edgeHandling === \"function\" && typeof cb === \"undefined\") {\n      cb = edgeHandling;\n      edgeHandling = null;\n    }\n\n    if (!edgeHandling) {\n      edgeHandling = Jimp.EDGE_EXTEND;\n    }\n\n    if (typeof x !== \"number\" || typeof y !== \"number\") {\n      return throwError.call(this, \"x and y must be numbers\", cb);\n    }\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n    xi = x;\n    yi = y;\n\n    if (edgeHandling === Jimp.EDGE_EXTEND) {\n      if (x < 0) xi = 0;\n      if (x >= this.bitmap.width) xi = this.bitmap.width - 1;\n      if (y < 0) yi = 0;\n      if (y >= this.bitmap.height) yi = this.bitmap.height - 1;\n    }\n\n    if (edgeHandling === Jimp.EDGE_WRAP) {\n      if (x < 0) {\n        xi = this.bitmap.width + x;\n      }\n\n      if (x >= this.bitmap.width) {\n        xi = x % this.bitmap.width;\n      }\n\n      if (y < 0) {\n        yi = this.bitmap.height + y;\n      }\n\n      if (y >= this.bitmap.height) {\n        yi = y % this.bitmap.height;\n      }\n    }\n\n    let i = (this.bitmap.width * yi + xi) << 2;\n\n    // if out of bounds index is -1\n    if (xi < 0 || xi >= this.bitmap.width) {\n      i = -1;\n    }\n\n    if (yi < 0 || yi >= this.bitmap.height) {\n      i = -1;\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, i);\n    }\n\n    return i;\n  }\n\n  /**\n   * Returns the hex colour value of a pixel\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {number} the color of the pixel\n   */\n  getPixelColor(x, y, cb) {\n    if (typeof x !== \"number\" || typeof y !== \"number\")\n      return throwError.call(this, \"x and y must be numbers\", cb);\n\n    const idx = this.getPixelIndex(x, y);\n    const hex = this.bitmap.data.readUInt32BE(idx);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, hex);\n    }\n\n    return hex;\n  }\n\n  getPixelColour = this.getPixelColor;\n\n  /**\n   * Returns the hex colour value of a pixel\n   * @param {number} hex color to set\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {number} the index of the pixel or -1 if not found\n   */\n  setPixelColor(hex, x, y, cb) {\n    if (\n      typeof hex !== \"number\" ||\n      typeof x !== \"number\" ||\n      typeof y !== \"number\"\n    )\n      return throwError.call(this, \"hex, x and y must be numbers\", cb);\n\n    const idx = this.getPixelIndex(x, y);\n    this.bitmap.data.writeUInt32BE(hex, idx);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n\n  setPixelColour = this.setPixelColor;\n\n  /**\n   * Determine if the image contains opaque pixels.\n   * @return {boolean} hasAlpha whether the image contains opaque pixels\n   */\n  hasAlpha() {\n    const {width, height, data} = this.bitmap;\n    const byteLen = (width * height) << 2;\n\n    for (let idx = 3; idx < byteLen; idx += 4) {\n      if (data[idx] !== 0xff) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Iterate scan through a region of the bitmap\n   * @param {number} x the x coordinate to begin the scan at\n   * @param {number} y the y coordinate to begin the scan at\n   * @param w the width of the scan region\n   * @param h the height of the scan region\n   * @returns {IterableIterator<{x: number, y: number, idx: number, image: Jimp}>}\n   */\n  scanIterator(x, y, w, h) {\n    if (typeof x !== \"number\" || typeof y !== \"number\") {\n      return throwError.call(this, \"x and y must be numbers\");\n    }\n\n    if (typeof w !== \"number\" || typeof h !== \"number\") {\n      return throwError.call(this, \"w and h must be numbers\");\n    }\n\n    return scanIterator(this, x, y, w, h);\n  }\n}\n\nexport function addConstants(constants, jimpInstance = Jimp) {\n  Object.entries(constants).forEach(([name, value]) => {\n    jimpInstance[name] = value;\n  });\n}\n\nexport function addJimpMethods(methods, jimpInstance = Jimp) {\n  Object.entries(methods).forEach(([name, value]) => {\n    jimpInstance.prototype[name] = value;\n  });\n}\n\naddConstants(constants);\naddJimpMethods({ composite });\n\nJimp.__extraConstructors = [];\n\n/**\n * Allow client libs to add new ways to build a Jimp object.\n * @param {string} name identify the extra constructor.\n * @param {function} test a function that returns true when it accepts the arguments passed to the main constructor.\n * @param {function} run where the magic happens.\n */\nJimp.appendConstructorOption = function (name, test, run) {\n  Jimp.__extraConstructors.push({ name, test, run });\n};\n\n/**\n * Read an image from a file or a Buffer. Takes the same args as the constructor\n * @returns {Promise} a promise\n */\nJimp.read = function (...args) {\n  return new Promise((resolve, reject) => {\n    // eslint-disable-next-line no-new\n    new Jimp(...args, (err, image) => {\n      if (err) reject(err);\n      else resolve(image);\n    });\n  });\n};\n\nJimp.create = Jimp.read;\n\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} r the red value (0-255)\n * @param {number} g the green value (0-255)\n * @param {number} b the blue value (0-255)\n * @param {number} a the alpha value (0-255)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {number} an single integer colour value\n */\nJimp.rgbaToInt = function (r, g, b, a, cb) {\n  if (\n    typeof r !== \"number\" ||\n    typeof g !== \"number\" ||\n    typeof b !== \"number\" ||\n    typeof a !== \"number\"\n  ) {\n    return throwError.call(this, \"r, g, b and a must be numbers\", cb);\n  }\n\n  if (r < 0 || r > 255) {\n    return throwError.call(this, \"r must be between 0 and 255\", cb);\n  }\n\n  if (g < 0 || g > 255) {\n    throwError.call(this, \"g must be between 0 and 255\", cb);\n  }\n\n  if (b < 0 || b > 255) {\n    return throwError.call(this, \"b must be between 0 and 255\", cb);\n  }\n\n  if (a < 0 || a > 255) {\n    return throwError.call(this, \"a must be between 0 and 255\", cb);\n  }\n\n  let i = (r & 0xff);\n  i <<= 8;\n  i |= (g & 0xff)\n  i <<= 8;\n  i |= (b & 0xff)\n  i <<= 8;\n  i |= (a & 0xff);\n\n  // Ensure sign is correct\n  i >>>= 0;\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, i);\n  }\n\n  return i;\n};\n\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} i a single integer value representing an RGBA colour (e.g. 0xFF0000FF for red)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {object} an object with the properties r, g, b and a representing RGBA values\n */\nJimp.intToRGBA = function (i, cb) {\n  if (typeof i !== \"number\") {\n    return throwError.call(this, \"i must be a number\", cb);\n  }\n\n  const rgba = {};\n\n  rgba.r = Math.floor(i / Math.pow(256, 3));\n  rgba.g = Math.floor((i - rgba.r * Math.pow(256, 3)) / Math.pow(256, 2));\n  rgba.b = Math.floor(\n    (i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2)) /\n      Math.pow(256, 1)\n  );\n  rgba.a = Math.floor(\n    (i -\n      rgba.r * Math.pow(256, 3) -\n      rgba.g * Math.pow(256, 2) -\n      rgba.b * Math.pow(256, 1)) /\n      Math.pow(256, 0)\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, rgba);\n  }\n\n  return rgba;\n};\n\n/**\n * Converts a css color (Hex, 8-digit (RGBA) Hex, RGB, RGBA, HSL, HSLA, HSV, HSVA, Named) to a hex number\n * @param {string} cssColor a number\n * @returns {number} a hex number representing a color\n */\nJimp.cssColorToHex = function (cssColor) {\n  cssColor = cssColor || 0; // 0, null, undefined, NaN\n\n  if (typeof cssColor === \"number\") return Number(cssColor);\n\n  return parseInt(tinyColor(cssColor).toHex8(), 16);\n};\n\n/**\n * Limits a number to between 0 or 255\n * @param {number} n a number\n * @returns {number} the number limited to between 0 or 255\n */\nJimp.limit255 = function (n) {\n  n = Math.max(n, 0);\n  n = Math.min(n, 255);\n\n  return n;\n};\n\n/**\n * Diffs two images and returns\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @param {number} threshold (optional) a number, 0 to 1, the smaller the value the more sensitive the comparison (default: 0.1)\n * @returns {object} an object { percent: percent similar, diff: a Jimp image highlighting differences }\n */\nJimp.diff = function (img1, img2, threshold = 0.1) {\n  if (!(img1 instanceof Jimp) || !(img2 instanceof Jimp))\n    return throwError.call(this, \"img1 and img2 must be an Jimp images\");\n\n  const bmp1 = img1.bitmap;\n  const bmp2 = img2.bitmap;\n\n  if (bmp1.width !== bmp2.width || bmp1.height !== bmp2.height) {\n    if (bmp1.width * bmp1.height > bmp2.width * bmp2.height) {\n      // img1 is bigger\n      img1 = img1.cloneQuiet().resize(bmp2.width, bmp2.height);\n    } else {\n      // img2 is bigger (or they are the same in area)\n      img2 = img2.cloneQuiet().resize(bmp1.width, bmp1.height);\n    }\n  }\n\n  if (typeof threshold !== \"number\" || threshold < 0 || threshold > 1) {\n    return throwError.call(this, \"threshold must be a number between 0 and 1\");\n  }\n\n  const diff = new Jimp(bmp1.width, bmp1.height, 0xffffffff);\n\n  const numDiffPixels = pixelMatch(\n    bmp1.data,\n    bmp2.data,\n    diff.bitmap.data,\n    diff.bitmap.width,\n    diff.bitmap.height,\n    { threshold }\n  );\n\n  return {\n    percent: numDiffPixels / (diff.bitmap.width * diff.bitmap.height),\n    image: diff,\n  };\n};\n\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\nJimp.distance = function (img1, img2) {\n  const phash = new ImagePHash();\n  const hash1 = phash.getHash(img1);\n  const hash2 = phash.getHash(img2);\n\n  return phash.distance(hash1, hash2);\n};\n\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {hash} hash1 a pHash\n * @param {hash} hash2 a pHash\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\nJimp.compareHashes = function (hash1, hash2) {\n  const phash = new ImagePHash();\n\n  return phash.distance(hash1, hash2);\n};\n\n/**\n * Compute color difference\n * 0 means no difference, 1 means maximum difference.\n * @param {number} rgba1:    first color to compare.\n * @param {number} rgba2:    second color to compare.\n * Both parameters must be an color object {r:val, g:val, b:val, a:val}\n * Where `a` is optional and `val` is an integer between 0 and 255.\n * @returns {number} float between 0 and 1.\n */\nJimp.colorDiff = function (rgba1, rgba2) {\n  const pow = (n) => Math.pow(n, 2);\n  const { max } = Math;\n  const maxVal = 255 * 255 * 3;\n\n  if (rgba1.a !== 0 && !rgba1.a) {\n    rgba1.a = 255;\n  }\n\n  if (rgba2.a !== 0 && !rgba2.a) {\n    rgba2.a = 255;\n  }\n\n  return (\n    (max(pow(rgba1.r - rgba2.r), pow(rgba1.r - rgba2.r - rgba1.a + rgba2.a)) +\n      max(pow(rgba1.g - rgba2.g), pow(rgba1.g - rgba2.g - rgba1.a + rgba2.a)) +\n      max(pow(rgba1.b - rgba2.b), pow(rgba1.b - rgba2.b - rgba1.a + rgba2.a))) /\n    maxVal\n  );\n};\n\n/**\n * Helper to create Jimp methods that emit events before and after its execution.\n * @param {string} methodName   The name to be appended to Jimp prototype.\n * @param {string} evName       The event name to be called.\n *                     It will be prefixed by `before-` and emitted when on method call.\n *                     It will be appended by `ed` and emitted after the method run.\n * @param {function} method       A function implementing the method itself.\n * It will also create a quiet version that will not emit events, to not\n * mess the user code with many `changed` event calls. You can call with\n * `methodName + \"Quiet\"`.\n *\n * The emitted event comes with a object parameter to the listener with the\n * `methodName` as one attribute.\n */\nexport function jimpEvMethod(methodName, evName, method) {\n  const evNameBefore = \"before-\" + evName;\n  const evNameAfter = evName.replace(/e$/, \"\") + \"ed\";\n\n  Jimp.prototype[methodName] = function (...args) {\n    let wrappedCb;\n    const cb = args[method.length - 1];\n    const jimpInstance = this;\n\n    if (typeof cb === \"function\") {\n      wrappedCb = function (...args) {\n        const [err, data] = args;\n\n        if (err) {\n          jimpInstance.emitError(methodName, err);\n        } else {\n          jimpInstance.emitMulti(methodName, evNameAfter, {\n            [methodName]: data,\n          });\n        }\n\n        cb.apply(this, args);\n      };\n\n      args[args.length - 1] = wrappedCb;\n    } else {\n      wrappedCb = false;\n    }\n\n    this.emitMulti(methodName, evNameBefore);\n\n    let result;\n\n    try {\n      result = method.apply(this, args);\n\n      if (!wrappedCb) {\n        this.emitMulti(methodName, evNameAfter, {\n          [methodName]: result,\n        });\n      }\n    } catch (error) {\n      error.methodName = methodName;\n      this.emitError(methodName, error);\n    }\n\n    return result;\n  };\n\n  Jimp.prototype[methodName + \"Quiet\"] = method;\n}\n\n/**\n * Creates a new image that is a clone of this one.\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns the new image\n */\njimpEvMethod(\"clone\", \"clone\", function (cb) {\n  const clone = new Jimp(this);\n\n  if (isNodePattern(cb)) {\n    cb.call(clone, null, clone);\n  }\n\n  return clone;\n});\n\n/**\n * Simplify jimpEvMethod call for the common `change` evName.\n * @param {string} methodName name of the method\n * @param {function} method to watch changes for\n */\nexport function jimpEvChange(methodName, method) {\n  jimpEvMethod(methodName, \"change\", method);\n}\n\n/**\n * Sets the type of the image (RGB or RGBA) when saving as PNG format (default is RGBA)\n * @param b A Boolean, true to use RGBA or false to use RGB\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\njimpEvChange(\"background\", function (hex, cb) {\n  if (typeof hex !== \"number\") {\n    return throwError.call(this, \"hex must be a hexadecimal rgba value\", cb);\n  }\n\n  this._background = hex;\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n});\n\n/**\n * Scans through a region of the bitmap, calling a function for each pixel.\n * @param {number} x the x coordinate to begin the scan at\n * @param {number} y the y coordinate to begin the scan at\n * @param w the width of the scan region\n * @param h the height of the scan region\n * @param f a function to call on even pixel; the (x, y) position of the pixel\n * and the index of the pixel in the bitmap buffer are passed to the function\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\njimpEvChange(\"scan\", function (x, y, w, h, f, cb) {\n  if (typeof x !== \"number\" || typeof y !== \"number\") {\n    return throwError.call(this, \"x and y must be numbers\", cb);\n  }\n\n  if (typeof w !== \"number\" || typeof h !== \"number\") {\n    return throwError.call(this, \"w and h must be numbers\", cb);\n  }\n\n  if (typeof f !== \"function\") {\n    return throwError.call(this, \"f must be a function\", cb);\n  }\n\n  const result = scan(this, x, y, w, h, f);\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, result);\n  }\n\n  return result;\n});\n\nif (process.env.ENVIRONMENT === \"BROWSER\") {\n  // For use in a web browser or web worker\n  /* global self */\n  let gl;\n\n  if (typeof window !== \"undefined\" && typeof window === \"object\") {\n    gl = window;\n  }\n\n  if (typeof self !== \"undefined\" && typeof self === \"object\") {\n    gl = self;\n  }\n\n  gl.Jimp = Jimp;\n  gl.Buffer = Buffer;\n}\n\nexport { addType } from \"./utils/mime\";\n\nexport default Jimp;\n"], "mappings": ";;;AAAA,OAAOA,EAAE,MAAM,IAAI;AACnB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,YAAY,MAAM,QAAQ;AAEjC,SAASC,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,YAAY,QAAQ,aAAa;AAC3E,OAAOC,OAAO,MAAM,UAAU;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,YAAY;AAElC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,OAAO,MAAM,WAAW;AAE/B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,cAAc;AACpC,SAASC,WAAW,EAAEC,SAAS,EAAEC,cAAc,QAAQ,sBAAsB;AAC7E,OAAO,KAAKC,SAAS,MAAM,aAAa;AAExC,MAAMC,QAAQ,GACZ,kEAAkE;;AAEpE;AACA;AACA,MAAMC,aAAa,GAAG,CAACC,GAAG,EAAEA,GAAG,CAAC;AAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;EAC3B,MAAMC,OAAO,GAAGhB,OAAO,CACrBA,OAAO,CAACiB,GAAG,EACXL,QAAQ,CAACM,KAAK,CAAC,CAAC,EAAEH,CAAC,CAAC,CACrB,CAAC,IAAII,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC9BP,aAAa,CAACQ,IAAI,CAACL,OAAO,CAACM,MAAM,CAAC;AACpC;;AAEA;AACA,SAASC,IAAI,GAAG,CAAC;;AAEjB;;AAEA,SAASC,aAAa,CAACC,IAAI,EAAE;EAC3B,OACEC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,WAAW,EAAE,CAACC,OAAO,CAAC,aAAa,CAAC,GACzE,CAAC,CAAC;AAEN;;AAEA;AACA;AACA,SAASC,qBAAqB,CAACC,WAAW,EAAE;EAC1C,MAAMC,MAAM,GAAGC,MAAM,CAACC,KAAK,CAACH,WAAW,CAACI,UAAU,CAAC;EACnD,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACN,WAAW,CAAC;EAExC,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,MAAM,CAACZ,MAAM,EAAE,EAAEP,CAAC,EAAE;IACtCmB,MAAM,CAACnB,CAAC,CAAC,GAAGuB,IAAI,CAACvB,CAAC,CAAC;EACrB;EAEA,OAAOmB,MAAM;AACf;AAEA,SAASM,WAAW,CAACC,OAAO,EAAEC,EAAE,EAAE;EAChCtC,OAAO,CAACqC,OAAO,EAAE,CAACE,GAAG,EAAEC,IAAI,KAAK;IAC9B,IAAID,GAAG,EAAE;MACP,OAAOD,EAAE,CAACC,GAAG,CAAC;IAChB;IAEA,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIT,MAAM,CAACU,QAAQ,CAACD,IAAI,CAAC,EAAE;MACrD,OAAOF,EAAE,CAAC,IAAI,EAAEE,IAAI,CAAC;IACvB;IAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIpB,aAAa,CAACoB,IAAI,CAAC,EAAE;MACnD,OAAOF,EAAE,CAAC,IAAI,EAAEV,qBAAqB,CAACY,IAAI,CAAC,CAAC;IAC9C;IAEA,OAAO,IAAIE,KAAK,CAAE,+BAA8BL,OAAO,CAACM,GAAI,GAAE,CAAC;EACjE,CAAC,CAAC;AACJ;AAEA,SAASC,kBAAkB,CAACC,GAAG,EAAEP,EAAE,EAAE;EACnC,IACEjD,EAAE,IACF,OAAOA,EAAE,CAACyD,QAAQ,KAAK,UAAU,IACjC,CAACD,GAAG,CAACE,KAAK,CAAC,qBAAqB,CAAC,EACjC;IACA1D,EAAE,CAACyD,QAAQ,CAACD,GAAG,EAAEP,EAAE,CAAC;EACtB,CAAC,MAAM;IACLF,WAAW,CAAC;MAAEO,GAAG,EAAEE;IAAI,CAAC,EAAEP,EAAE,CAAC;EAC/B;AACF;AAEA,SAASU,aAAa,CAACC,GAAG,EAAE;EAC1B,OACEA,GAAG,IACH,OAAOA,GAAG,KAAK,QAAQ,IACvB,OAAOA,GAAG,CAACC,KAAK,KAAK,QAAQ,IAC7B,OAAOD,GAAG,CAACE,MAAM,KAAK,QAAQ,KAC7BpB,MAAM,CAACU,QAAQ,CAACQ,GAAG,CAACT,IAAI,CAAC,IACxBS,GAAG,CAACT,IAAI,YAAYL,UAAU,IAC7B,OAAOiB,iBAAiB,KAAK,UAAU,IACtCH,GAAG,CAACT,IAAI,YAAYY,iBAAkB,CAAC,KAC1CH,GAAG,CAACT,IAAI,CAACtB,MAAM,KAAK+B,GAAG,CAACC,KAAK,GAAGD,GAAG,CAACE,MAAM,GAAG,CAAC,IAC7CF,GAAG,CAACT,IAAI,CAACtB,MAAM,KAAK+B,GAAG,CAACC,KAAK,GAAGD,GAAG,CAACE,MAAM,GAAG,CAAC,CAAC;AAErD;AAEA,SAASE,qBAAqB,CAACvB,MAAM,EAAE;EACrC,IAAIA,MAAM,CAACZ,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IAC3B,MAAM,IAAIwB,KAAK,CAAC,4BAA4B,CAAC;EAC/C;EAEA,MAAMY,UAAU,GAAGvB,MAAM,CAACwB,WAAW,CAAEzB,MAAM,CAACZ,MAAM,GAAG,CAAC,GAAI,CAAC,CAAC;EAC9D,IAAIsC,CAAC,GAAG,CAAC;EAET,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,MAAM,CAACZ,MAAM,EAAEP,CAAC,EAAE,EAAE;IACtC2C,UAAU,CAACE,CAAC,CAAC,GAAG1B,MAAM,CAACnB,CAAC,CAAC;IAEzB,IAAI,CAACA,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MACrB2C,UAAU,CAAC,EAAEE,CAAC,CAAC,GAAG,GAAG;IACvB;IAEAA,CAAC,EAAE;EACL;EAEA,OAAOF,UAAU;AACnB;AAEA,MAAMG,WAAW,GAAG;EAClBjB,IAAI,EAAE,IAAI;EACVU,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMO,IAAI,SAASnE,YAAY,CAAC;EAC9B;EACA;EACA;EACA;;EAGA;;EAGA;;EAGA;;EAGA;;EAGAoE,WAAW,GAAU;IAAA,kCAANC,IAAI;MAAJA,IAAI;IAAA;IACjB,KAAK,EAAE;IAAC,gCAfDH,WAAW;IAAA,qCAGN,UAAU;IAAA,uCAGRC,IAAI,CAACG,QAAQ;IAAA,+BAGrB,IAAI;IAAA,+BAGJ,IAAI;IAAA,oCA2WEC,IAAI,IAAK5D,SAAS,CAAC,IAAI,CAAC6D,KAAK,EAAE,IAAI,EAAED,IAAI,CAAC;IAAA,wCAkCtCE,IAAI,IAAK9D,SAAS,CAAC,IAAI,CAAC+D,SAAS,EAAE,IAAI,EAAED,IAAI,CAAC;IAAA,mCAqEpD3D,SAAS;IAAA,wCAEJC,cAAc;IAAA,wCAiGd,IAAI,CAAC4D,aAAa;IAAA,wCA4BlB,IAAI,CAACC,aAAa;IA5kBjC,MAAMC,YAAY,GAAG,IAAI;IACzB,IAAI9B,EAAE,GAAGnB,IAAI;IAEb,IAAIC,aAAa,CAACwC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1BA,IAAI,CAAC,CAAC,CAAC,GAAGhC,qBAAqB,CAACgC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1C;IAEA,SAASS,MAAM,GAAU;MAAA,mCAANT,IAAI;QAAJA,IAAI;MAAA;MACrB,MAAM,CAACrB,GAAG,CAAC,GAAGqB,IAAI;MAClB,MAAMU,MAAM,GAAG/B,GAAG,IAAI,CAAC,CAAC;MACxB+B,MAAM,CAACC,UAAU,GAAG,aAAa;MAEjCC,UAAU,CAAC,MAAM;QACf;QACA,IAAIjC,GAAG,IAAID,EAAE,KAAKnB,IAAI,EAAE;UACtBiD,YAAY,CAACK,SAAS,CAAC,aAAa,EAAElC,GAAG,CAAC;QAC5C,CAAC,MAAM,IAAI,CAACA,GAAG,EAAE;UACf6B,YAAY,CAACM,SAAS,CAAC,aAAa,EAAE,aAAa,CAAC;QACtD;QAEApC,EAAE,CAACb,IAAI,CAAC2C,YAAY,EAAE,GAAGR,IAAI,CAAC;MAChC,CAAC,EAAE,CAAC,CAAC;IACP;IAEA,IACG,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC1De,QAAQ,CAACf,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIe,QAAQ,CAACf,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAE,EAChD;MACA;MACA,MAAMgB,CAAC,GAAGD,QAAQ,CAACf,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC/B,MAAMiB,CAAC,GAAGF,QAAQ,CAACf,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC/BtB,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC;;MAEZ;MACA,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAC/B,IAAI,CAACkB,WAAW,GAAGlB,IAAI,CAAC,CAAC,CAAC;QAC1BtB,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC;MACd;;MAEA;MACA,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAC/B,IAAI,CAACkB,WAAW,GAAGpB,IAAI,CAACqB,aAAa,CAACnB,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9CtB,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC;MACd;MAEA,IAAI,OAAOtB,EAAE,KAAK,WAAW,EAAE;QAC7BA,EAAE,GAAGnB,IAAI;MACX;MAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAO7C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAE4C,MAAM,CAAC;MAC/D;MAEA,IAAI,CAACW,MAAM,GAAG;QACZxC,IAAI,EAAET,MAAM,CAACC,KAAK,CAAC4C,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC;QAC7B3B,KAAK,EAAE0B,CAAC;QACRzB,MAAM,EAAE0B;MACV,CAAC;MAED,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACqE,MAAM,CAACxC,IAAI,CAACtB,MAAM,EAAEP,CAAC,IAAI,CAAC,EAAE;QACnD,IAAI,CAACqE,MAAM,CAACxC,IAAI,CAACyC,aAAa,CAAC,IAAI,CAACH,WAAW,EAAEnE,CAAC,CAAC;MACrD;MAEA0D,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;IACpB,CAAC,MAAM,IAAI,OAAOT,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACjB,GAAG,EAAE;MACrDL,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC,IAAIzC,IAAI;MAEpB,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAO7C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAE4C,MAAM,CAAC;MAC/D;MAEAjC,WAAW,CAACwB,IAAI,CAAC,CAAC,CAAC,EAAE,CAACrB,GAAG,EAAEC,IAAI,KAAK;QAClC,IAAID,GAAG,EAAE;UACP,OAAO9C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAEc,GAAG,EAAE8B,MAAM,CAAC;QAC3C;QAEA,IAAI,CAACjE,WAAW,CAACoC,IAAI,EAAEoB,IAAI,CAAC,CAAC,CAAC,CAACjB,GAAG,EAAE0B,MAAM,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIT,IAAI,CAAC,CAAC,CAAC,YAAYF,IAAI,EAAE;MAClC;MACA,MAAM,CAACwB,QAAQ,CAAC,GAAGtB,IAAI;MACvBtB,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC;MAEZ,IAAI,OAAOtB,EAAE,KAAK,WAAW,EAAE;QAC7BA,EAAE,GAAGnB,IAAI;MACX;MAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAO7C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAE4C,MAAM,CAAC;MAC/D;MAEA,IAAI,CAACW,MAAM,GAAG;QACZxC,IAAI,EAAET,MAAM,CAACoD,IAAI,CAACD,QAAQ,CAACF,MAAM,CAACxC,IAAI,CAAC;QACvCU,KAAK,EAAEgC,QAAQ,CAACF,MAAM,CAAC9B,KAAK;QAC5BC,MAAM,EAAE+B,QAAQ,CAACF,MAAM,CAAC7B;MAC1B,CAAC;MAED,IAAI,CAACiC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;MACjC,IAAI,CAACC,aAAa,GAAGH,QAAQ,CAACG,aAAa;MAC3C,IAAI,CAACC,gBAAgB,GAAGJ,QAAQ,CAACI,gBAAgB;MACjD,IAAI,CAACC,WAAW,GAAGL,QAAQ,CAACK,WAAW;MACvC,IAAI,CAACC,KAAK,GAAGN,QAAQ,CAACM,KAAK;MAC3B,IAAI,CAACV,WAAW,GAAGI,QAAQ,CAACJ,WAAW;MACvC,IAAI,CAACW,aAAa,GAAGP,QAAQ,CAACO,aAAa;MAE3CpB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;IACpB,CAAC,MAAM,IAAIrB,aAAa,CAACY,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,MAAM,CAAC8B,SAAS,CAAC,GAAG9B,IAAI;MACxBtB,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC,IAAIzC,IAAI;MAEpB,MAAMwE,MAAM,GACVD,SAAS,CAACxC,KAAK,GAAGwC,SAAS,CAACvC,MAAM,GAAG,CAAC,KAAKuC,SAAS,CAAClD,IAAI,CAACtB,MAAM;MAClE,MAAMY,MAAM,GAAG6D,MAAM,GACjB5D,MAAM,CAACoD,IAAI,CAACO,SAAS,CAAClD,IAAI,CAAC,GAC3Ba,qBAAqB,CAACqC,SAAS,CAAClD,IAAI,CAAC;MAEzC,IAAI,CAACwC,MAAM,GAAG;QACZxC,IAAI,EAAEV,MAAM;QACZoB,KAAK,EAAEwC,SAAS,CAACxC,KAAK;QACtBC,MAAM,EAAEuC,SAAS,CAACvC;MACpB,CAAC;MAEDkB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;IACpB,CAAC,MAAM,IAAI,OAAOT,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACtC;MACA,MAAME,IAAI,GAAGF,IAAI,CAAC,CAAC,CAAC;MACpBtB,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC;MAEZ,IAAI,OAAOtB,EAAE,KAAK,WAAW,EAAE;QAC7BA,EAAE,GAAGnB,IAAI;MACX;MAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAO7C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAE4C,MAAM,CAAC;MAC/D;MAEAzB,kBAAkB,CAACkB,IAAI,EAAE,CAACvB,GAAG,EAAEC,IAAI,KAAK;QACtC,IAAID,GAAG,EAAE;UACP,OAAO9C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAEc,GAAG,EAAE8B,MAAM,CAAC;QAC3C;QAEA,IAAI,CAACjE,WAAW,CAACoC,IAAI,EAAEsB,IAAI,EAAEO,MAAM,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOT,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI7B,MAAM,CAACU,QAAQ,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MAClE;MACA,MAAMpB,IAAI,GAAGoB,IAAI,CAAC,CAAC,CAAC;MACpBtB,EAAE,GAAGsB,IAAI,CAAC,CAAC,CAAC;MAEZ,IAAI,OAAOtB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAO7C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAE4C,MAAM,CAAC;MAC/D;MAEA,IAAI,CAACjE,WAAW,CAACoC,IAAI,EAAE,IAAI,EAAE6B,MAAM,CAAC;IACtC,CAAC,MAAM;MACL;MACA;MACA/B,EAAE,GAAGsB,IAAI,CAACA,IAAI,CAAC1C,MAAM,GAAG,CAAC,CAAC;MAE1B,IAAI,OAAOoB,EAAE,KAAK,UAAU,EAAE;QAC5B;QACAA,EAAE,GAAGsB,IAAI,CAACA,IAAI,CAAC1C,MAAM,GAAG,CAAC,CAAC;QAE1B,IAAI,OAAOoB,EAAE,KAAK,UAAU,EAAE;UAC5BA,EAAE,GAAGnB,IAAI;QACX;MACF;MAEA,MAAMyE,gBAAgB,GAAGlC,IAAI,CAACmC,mBAAmB,CAACC,IAAI,CAAEC,CAAC,IACvDA,CAAC,CAAC1E,IAAI,CAAC,GAAGuC,IAAI,CAAC,CAChB;MAED,IAAIgC,gBAAgB,EAAE;QACpB,IAAII,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UAC/BN,gBAAgB,CAACO,GAAG,CAAC1E,IAAI,CAAC,IAAI,EAAEwE,OAAO,EAAEC,MAAM,EAAE,GAAGtC,IAAI,CAAC;QAC3D,CAAC,CAAC,CACCwC,IAAI,CAAC,MAAM/B,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAC9BgC,KAAK,CAAChC,MAAM,CAAC;MAClB,CAAC,MAAM;QACL,OAAO5E,UAAU,CAACgC,IAAI,CACpB,IAAI,EACJ,iDAAiD,GAC/C,2DAA2D,EAC7D4C,MAAM,CACP;MACH;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEjE,WAAW,CAACoC,IAAI,EAAEsB,IAAI,EAAEO,MAAM,EAAE;IAC9BjE,WAAW,CAACqB,IAAI,CAAC,IAAI,EAAEe,IAAI,EAAE,IAAI,EAAE6B,MAAM,CAAC;EAC5C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEiC,IAAI,CAACC,IAAI,EAAEjE,EAAE,EAAE;IACb,IAAI,OAAOiE,IAAI,KAAK,SAAS,EAAE;MAC7B,OAAO9G,UAAU,CAACgC,IAAI,CACpB,IAAI,EACJ,wDAAwD,EACxDa,EAAE,CACH;IACH;IAEA,IAAI,CAACkD,KAAK,GAAGe,IAAI;IAEjB,IAAI/G,aAAa,CAAC8C,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEiD,SAAS,CAACH,UAAU,EAAEiC,SAAS,EAAa;IAAA,IAAXhE,IAAI,uEAAG,CAAC,CAAC;IACxCA,IAAI,GAAGlB,MAAM,CAACmF,MAAM,CAACjE,IAAI,EAAE;MAAE+B,UAAU;MAAEiC;IAAU,CAAC,CAAC;IACrD,IAAI,CAACE,IAAI,CAAC,KAAK,EAAElE,IAAI,CAAC;IAEtB,IAAI+B,UAAU,EAAE;MACd,IAAI,CAACmC,IAAI,CAACnC,UAAU,EAAE/B,IAAI,CAAC;IAC7B;IAEA,IAAI,CAACkE,IAAI,CAACF,SAAS,EAAEhE,IAAI,CAAC;EAC5B;EAEAiC,SAAS,CAACF,UAAU,EAAEhC,GAAG,EAAE;IACzB,IAAI,CAACmC,SAAS,CAACH,UAAU,EAAE,OAAO,EAAEhC,GAAG,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;EACEoE,SAAS,GAAG;IACV,OAAO,IAAI,CAAC3B,MAAM,CAAC7B,MAAM;EAC3B;;EAEA;AACF;AACA;AACA;EACEyD,QAAQ,GAAG;IACT,OAAO,IAAI,CAAC5B,MAAM,CAAC9B,KAAK;EAC1B;;EAEA;AACF;AACA;AACA;EACE2D,OAAO,GAAG;IACR,OACE,QAAQ,IACP,IAAI,CAAC7B,MAAM,KAAKvB,WAAW,GACxB,YAAY,GACZ,IAAI,CAACuB,MAAM,CAAC9B,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC8B,MAAM,CAAC7B,MAAM,CAAC,GACjD,GAAG;EAEP;;EAEA;AACF;AACA;AACA;EACE3B,QAAQ,GAAG;IACT,OAAO,eAAe;EACxB;;EAEA;AACF;AACA;AACA;EACEsF,OAAO,GAAG;IACR,MAAM9C,IAAI,GAAG,IAAI,CAACyB,aAAa,IAAI/B,IAAI,CAACG,QAAQ;IAEhD,OAAOG,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACE+C,YAAY,GAAG;IACb,MAAM/C,IAAI,GAAG,IAAI,CAAC8C,OAAO,EAAE;IAE3B,OAAO3G,IAAI,CAAC4G,YAAY,CAAC/C,IAAI,CAAC;EAChC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACED,KAAK,CAACD,IAAI,EAAExB,EAAE,EAAE;IACd,IAAI,CAACjD,EAAE,IAAI,CAACA,EAAE,CAAC2H,iBAAiB,EAAE;MAChC,MAAM,IAAItE,KAAK,CACb,+DAA+D,CAChE;IACH;IAEA,IAAI,OAAOoB,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOrE,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAI,OAAOA,EAAE,KAAK,WAAW,EAAE;MAC7BA,EAAE,GAAGnB,IAAI;IACX;IAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;MAC5B,OAAO7C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,MAAM0B,IAAI,GAAG7D,IAAI,CAAC8G,OAAO,CAACnD,IAAI,CAAC,IAAI,IAAI,CAACgD,OAAO,EAAE;IACjD,MAAMI,OAAO,GAAG5H,IAAI,CAAC6H,KAAK,CAACrD,IAAI,CAAC;IAEhC,IAAIoD,OAAO,CAACE,GAAG,EAAE;MACf/H,EAAE,CAACgI,SAAS,CAACH,OAAO,CAACE,GAAG,EAAE;QAAEE,SAAS,EAAE;MAAK,CAAC,CAAC;IAChD;IAEA,IAAI,CAACjH,SAAS,CAAC2D,IAAI,EAAE,CAACzB,GAAG,EAAET,MAAM,KAAK;MACpC,IAAIS,GAAG,EAAE;QACP,OAAO9C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAEc,GAAG,EAAED,EAAE,CAAC;MACvC;MAEA,MAAMiF,MAAM,GAAGlI,EAAE,CAAC2H,iBAAiB,CAAClD,IAAI,CAAC;MAEzCyD,MAAM,CACHC,EAAE,CAAC,MAAM,EAAE,MAAM;QAChBD,MAAM,CAACxD,KAAK,CAACjC,MAAM,CAAC;QACpByF,MAAM,CAACE,GAAG,EAAE;MACd,CAAC,CAAC,CACDD,EAAE,CAAC,OAAO,EAAGjF,GAAG,IAAK;QACpB,OAAO9C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAEc,GAAG,EAAED,EAAE,CAAC;MACvC,CAAC,CAAC;MACJiF,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,MAAM;QACxBlF,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,IAAI;EACb;EAIA;AACF;AACA;AACA;AACA;AACA;EACEwC,SAAS,CAACD,IAAI,EAAE1B,EAAE,EAAE;IAClB,IAAI0B,IAAI,KAAKN,IAAI,CAACgE,IAAI,EAAE;MACtB;MACA1D,IAAI,GAAG,IAAI,CAAC8C,OAAO,EAAE;IACvB;IAEA,IAAI,OAAO9C,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOvE,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;MAC5B,OAAO7C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAI,CAACjC,SAAS,CAAC2D,IAAI,EAAE,UAAUzB,GAAG,EAAEC,IAAI,EAAE;MACxC,IAAID,GAAG,EAAE;QACP,OAAO9C,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAEc,GAAG,EAAED,EAAE,CAAC;MACvC;MAEA,MAAMO,GAAG,GAAG,OAAO,GAAGmB,IAAI,GAAG,UAAU,GAAGxB,IAAI,CAAChB,QAAQ,CAAC,QAAQ,CAAC;MACjEc,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEoB,GAAG,CAAC;IAC1B,CAAC,CAAC;IAEF,OAAO,IAAI;EACb;EAIA;AACF;AACA;AACA;AACA;AACA;EACE8E,IAAI,CAACC,IAAI,EAAEtF,EAAE,EAAE;IACbsF,IAAI,GAAGA,IAAI,IAAI,EAAE;IAEjB,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MAC9BtF,EAAE,GAAGsF,IAAI;MACTA,IAAI,GAAG,EAAE;IACX;IAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOnI,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAIsF,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE;MACzB,OAAOnI,UAAU,CAACgC,IAAI,CACpB,IAAI,EACJ,wCAAwC,EACxCa,EAAE,CACH;IACH;IAEA,IAAIqF,IAAI,GAAG,IAAI,CAACE,KAAK,EAAE;IACvBF,IAAI,GAAG/H,OAAO,CAACA,OAAO,CAACiB,GAAG,EAAEL,QAAQ,CAACM,KAAK,CAAC,CAAC,EAAE8G,IAAI,CAAC,CAAC,CAACD,IAAI,CAAC;IAE1D,OAAOA,IAAI,CAACzG,MAAM,GAAGT,aAAa,CAACmH,IAAI,CAAC,EAAE;MACxCD,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC;IACrB;;IAEA,IAAInI,aAAa,CAAC8C,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEkG,IAAI,CAAC;IAC3B;IAEA,OAAOA,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEE,KAAK,GAAG;IACN,MAAMA,KAAK,GAAG,IAAI9H,UAAU,EAAE;IAC9B,OAAO8H,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;AACA;EACEC,gBAAgB,CAACC,WAAW,EAAE;IAC5B,MAAMH,KAAK,GAAG,IAAI9H,UAAU,EAAE;IAC9B,MAAMkI,WAAW,GAAGJ,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC;IAEvC,OAAOD,KAAK,CAACK,QAAQ,CAACD,WAAW,EAAED,WAAW,CAAC;EACjD;;EAEA;AACF;AACA;AACA;AACA;AACA;;EAKE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,aAAa,CAACC,CAAC,EAAEC,CAAC,EAAEC,YAAY,EAAEhG,EAAE,EAAE;IACpC,IAAIiG,EAAE;IACN,IAAIC,EAAE;IAEN,IAAI,OAAOF,YAAY,KAAK,UAAU,IAAI,OAAOhG,EAAE,KAAK,WAAW,EAAE;MACnEA,EAAE,GAAGgG,YAAY;MACjBA,YAAY,GAAG,IAAI;IACrB;IAEA,IAAI,CAACA,YAAY,EAAE;MACjBA,YAAY,GAAG5E,IAAI,CAAC+E,WAAW;IACjC;IAEA,IAAI,OAAOL,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAO5I,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;IAC7D;;IAEA;IACA8F,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC;IACjBC,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;IACjBE,EAAE,GAAGH,CAAC;IACNI,EAAE,GAAGH,CAAC;IAEN,IAAIC,YAAY,KAAK5E,IAAI,CAAC+E,WAAW,EAAE;MACrC,IAAIL,CAAC,GAAG,CAAC,EAAEG,EAAE,GAAG,CAAC;MACjB,IAAIH,CAAC,IAAI,IAAI,CAACpD,MAAM,CAAC9B,KAAK,EAAEqF,EAAE,GAAG,IAAI,CAACvD,MAAM,CAAC9B,KAAK,GAAG,CAAC;MACtD,IAAImF,CAAC,GAAG,CAAC,EAAEG,EAAE,GAAG,CAAC;MACjB,IAAIH,CAAC,IAAI,IAAI,CAACrD,MAAM,CAAC7B,MAAM,EAAEqF,EAAE,GAAG,IAAI,CAACxD,MAAM,CAAC7B,MAAM,GAAG,CAAC;IAC1D;IAEA,IAAImF,YAAY,KAAK5E,IAAI,CAACkF,SAAS,EAAE;MACnC,IAAIR,CAAC,GAAG,CAAC,EAAE;QACTG,EAAE,GAAG,IAAI,CAACvD,MAAM,CAAC9B,KAAK,GAAGkF,CAAC;MAC5B;MAEA,IAAIA,CAAC,IAAI,IAAI,CAACpD,MAAM,CAAC9B,KAAK,EAAE;QAC1BqF,EAAE,GAAGH,CAAC,GAAG,IAAI,CAACpD,MAAM,CAAC9B,KAAK;MAC5B;MAEA,IAAImF,CAAC,GAAG,CAAC,EAAE;QACTG,EAAE,GAAG,IAAI,CAACxD,MAAM,CAAC7B,MAAM,GAAGkF,CAAC;MAC7B;MAEA,IAAIA,CAAC,IAAI,IAAI,CAACrD,MAAM,CAAC7B,MAAM,EAAE;QAC3BqF,EAAE,GAAGH,CAAC,GAAG,IAAI,CAACrD,MAAM,CAAC7B,MAAM;MAC7B;IACF;IAEA,IAAIxC,CAAC,GAAI,IAAI,CAACqE,MAAM,CAAC9B,KAAK,GAAGsF,EAAE,GAAGD,EAAE,IAAK,CAAC;;IAE1C;IACA,IAAIA,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,IAAI,CAACvD,MAAM,CAAC9B,KAAK,EAAE;MACrCvC,CAAC,GAAG,CAAC,CAAC;IACR;IAEA,IAAI6H,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,IAAI,CAACxD,MAAM,CAAC7B,MAAM,EAAE;MACtCxC,CAAC,GAAG,CAAC,CAAC;IACR;IAEA,IAAInB,aAAa,CAAC8C,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEd,CAAC,CAAC;IACxB;IAEA,OAAOA,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEuD,aAAa,CAACkE,CAAC,EAAEC,CAAC,EAAE/F,EAAE,EAAE;IACtB,IAAI,OAAO8F,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAChD,OAAO5I,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;IAE7D,MAAMuG,GAAG,GAAG,IAAI,CAACV,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACpC,MAAMS,GAAG,GAAG,IAAI,CAAC9D,MAAM,CAACxC,IAAI,CAACuG,YAAY,CAACF,GAAG,CAAC;IAE9C,IAAIrJ,aAAa,CAAC8C,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEqH,GAAG,CAAC;IAC1B;IAEA,OAAOA,GAAG;EACZ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE3E,aAAa,CAAC2E,GAAG,EAAEV,CAAC,EAAEC,CAAC,EAAE/F,EAAE,EAAE;IAC3B,IACE,OAAOwG,GAAG,KAAK,QAAQ,IACvB,OAAOV,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,EAErB,OAAO5I,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,8BAA8B,EAAEa,EAAE,CAAC;IAElE,MAAMuG,GAAG,GAAG,IAAI,CAACV,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACpC,IAAI,CAACrD,MAAM,CAACxC,IAAI,CAACyC,aAAa,CAAC6D,GAAG,EAAED,GAAG,CAAC;IAExC,IAAIrJ,aAAa,CAAC8C,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;EAIA;AACF;AACA;AACA;EACEuH,QAAQ,GAAG;IACT,MAAM;MAAC9F,KAAK;MAAEC,MAAM;MAAEX;IAAI,CAAC,GAAG,IAAI,CAACwC,MAAM;IACzC,MAAMiE,OAAO,GAAI/F,KAAK,GAAGC,MAAM,IAAK,CAAC;IAErC,KAAK,IAAI0F,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGI,OAAO,EAAEJ,GAAG,IAAI,CAAC,EAAE;MACzC,IAAIrG,IAAI,CAACqG,GAAG,CAAC,KAAK,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACElJ,YAAY,CAACyI,CAAC,EAAEC,CAAC,EAAEzD,CAAC,EAAEC,CAAC,EAAE;IACvB,IAAI,OAAOuD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAO5I,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;IACzD;IAEA,IAAI,OAAOmD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAOpF,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;IACzD;IAEA,OAAO9B,YAAY,CAAC,IAAI,EAAEyI,CAAC,EAAEC,CAAC,EAAEzD,CAAC,EAAEC,CAAC,CAAC;EACvC;AACF;AAEA,OAAO,SAASqE,YAAY,CAAC3I,SAAS,EAAuB;EAAA,IAArB6D,YAAY,uEAAGV,IAAI;EACzDpC,MAAM,CAAC6H,OAAO,CAAC5I,SAAS,CAAC,CAAC6I,OAAO,CAAC,QAAmB;IAAA,IAAlB,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC9ClF,YAAY,CAACiF,IAAI,CAAC,GAAGC,KAAK;EAC5B,CAAC,CAAC;AACJ;AAEA,OAAO,SAASC,cAAc,CAACC,OAAO,EAAuB;EAAA,IAArBpF,YAAY,uEAAGV,IAAI;EACzDpC,MAAM,CAAC6H,OAAO,CAACK,OAAO,CAAC,CAACJ,OAAO,CAAC,SAAmB;IAAA,IAAlB,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC5ClF,YAAY,CAAC7C,SAAS,CAAC8H,IAAI,CAAC,GAAGC,KAAK;EACtC,CAAC,CAAC;AACJ;AAEAJ,YAAY,CAAC3I,SAAS,CAAC;AACvBgJ,cAAc,CAAC;EAAEtJ;AAAU,CAAC,CAAC;AAE7ByD,IAAI,CAACmC,mBAAmB,GAAG,EAAE;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACAnC,IAAI,CAAC+F,uBAAuB,GAAG,UAAUJ,IAAI,EAAEhI,IAAI,EAAE8E,GAAG,EAAE;EACxDzC,IAAI,CAACmC,mBAAmB,CAAC5E,IAAI,CAAC;IAAEoI,IAAI;IAAEhI,IAAI;IAAE8E;EAAI,CAAC,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACAzC,IAAI,CAACgG,IAAI,GAAG,YAAmB;EAAA,mCAAN9F,IAAI;IAAJA,IAAI;EAAA;EAC3B,OAAO,IAAIoC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC;IACA,IAAIxC,IAAI,CAAC,GAAGE,IAAI,EAAE,CAACrB,GAAG,EAAEoH,KAAK,KAAK;MAChC,IAAIpH,GAAG,EAAE2D,MAAM,CAAC3D,GAAG,CAAC,CAAC,KAChB0D,OAAO,CAAC0D,KAAK,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAEDjG,IAAI,CAACkG,MAAM,GAAGlG,IAAI,CAACgG,IAAI;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAhG,IAAI,CAACmG,SAAS,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE3H,EAAE,EAAE;EACzC,IACE,OAAOwH,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,EACrB;IACA,OAAOxK,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,+BAA+B,EAAEa,EAAE,CAAC;EACnE;EAEA,IAAIwH,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpB,OAAOrK,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EACjE;EAEA,IAAIyH,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpBtK,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EAC1D;EAEA,IAAI0H,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpB,OAAOvK,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EACjE;EAEA,IAAI2H,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpB,OAAOxK,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EACjE;EAEA,IAAI3B,CAAC,GAAImJ,CAAC,GAAG,IAAK;EAClBnJ,CAAC,KAAK,CAAC;EACPA,CAAC,IAAKoJ,CAAC,GAAG,IAAK;EACfpJ,CAAC,KAAK,CAAC;EACPA,CAAC,IAAKqJ,CAAC,GAAG,IAAK;EACfrJ,CAAC,KAAK,CAAC;EACPA,CAAC,IAAKsJ,CAAC,GAAG,IAAK;;EAEf;EACAtJ,CAAC,MAAM,CAAC;EAER,IAAInB,aAAa,CAAC8C,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEd,CAAC,CAAC;EACxB;EAEA,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA+C,IAAI,CAACwG,SAAS,GAAG,UAAUvJ,CAAC,EAAE2B,EAAE,EAAE;EAChC,IAAI,OAAO3B,CAAC,KAAK,QAAQ,EAAE;IACzB,OAAOlB,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEa,EAAE,CAAC;EACxD;EAEA,MAAMgE,IAAI,GAAG,CAAC,CAAC;EAEfA,IAAI,CAACwD,CAAC,GAAGpB,IAAI,CAACyB,KAAK,CAACxJ,CAAC,GAAG+H,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACzC9D,IAAI,CAACyD,CAAC,GAAGrB,IAAI,CAACyB,KAAK,CAAC,CAACxJ,CAAC,GAAG2F,IAAI,CAACwD,CAAC,GAAGpB,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI1B,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACvE9D,IAAI,CAAC0D,CAAC,GAAGtB,IAAI,CAACyB,KAAK,CACjB,CAACxJ,CAAC,GAAG2F,IAAI,CAACwD,CAAC,GAAGpB,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG9D,IAAI,CAACyD,CAAC,GAAGrB,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,IACxD1B,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CACnB;EACD9D,IAAI,CAAC2D,CAAC,GAAGvB,IAAI,CAACyB,KAAK,CACjB,CAACxJ,CAAC,GACA2F,IAAI,CAACwD,CAAC,GAAGpB,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GACzB9D,IAAI,CAACyD,CAAC,GAAGrB,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GACzB9D,IAAI,CAAC0D,CAAC,GAAGtB,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,IACzB1B,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CACnB;EAED,IAAI5K,aAAa,CAAC8C,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE6E,IAAI,CAAC;EAC3B;EAEA,OAAOA,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA5C,IAAI,CAACqB,aAAa,GAAG,UAAUsF,QAAQ,EAAE;EACvCA,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC,CAAC;;EAE1B,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE,OAAOC,MAAM,CAACD,QAAQ,CAAC;EAEzD,OAAO1F,QAAQ,CAAC7E,SAAS,CAACuK,QAAQ,CAAC,CAACE,MAAM,EAAE,EAAE,EAAE,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA7G,IAAI,CAAC8G,QAAQ,GAAG,UAAUC,CAAC,EAAE;EAC3BA,CAAC,GAAG/B,IAAI,CAACgC,GAAG,CAACD,CAAC,EAAE,CAAC,CAAC;EAClBA,CAAC,GAAG/B,IAAI,CAACiC,GAAG,CAACF,CAAC,EAAE,GAAG,CAAC;EAEpB,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA/G,IAAI,CAACkH,IAAI,GAAG,UAAUC,IAAI,EAAEC,IAAI,EAAmB;EAAA,IAAjBC,SAAS,uEAAG,GAAG;EAC/C,IAAI,EAAEF,IAAI,YAAYnH,IAAI,CAAC,IAAI,EAAEoH,IAAI,YAAYpH,IAAI,CAAC,EACpD,OAAOjE,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,sCAAsC,CAAC;EAEtE,MAAMuJ,IAAI,GAAGH,IAAI,CAAC7F,MAAM;EACxB,MAAMiG,IAAI,GAAGH,IAAI,CAAC9F,MAAM;EAExB,IAAIgG,IAAI,CAAC9H,KAAK,KAAK+H,IAAI,CAAC/H,KAAK,IAAI8H,IAAI,CAAC7H,MAAM,KAAK8H,IAAI,CAAC9H,MAAM,EAAE;IAC5D,IAAI6H,IAAI,CAAC9H,KAAK,GAAG8H,IAAI,CAAC7H,MAAM,GAAG8H,IAAI,CAAC/H,KAAK,GAAG+H,IAAI,CAAC9H,MAAM,EAAE;MACvD;MACA0H,IAAI,GAAGA,IAAI,CAACK,UAAU,EAAE,CAACC,MAAM,CAACF,IAAI,CAAC/H,KAAK,EAAE+H,IAAI,CAAC9H,MAAM,CAAC;IAC1D,CAAC,MAAM;MACL;MACA2H,IAAI,GAAGA,IAAI,CAACI,UAAU,EAAE,CAACC,MAAM,CAACH,IAAI,CAAC9H,KAAK,EAAE8H,IAAI,CAAC7H,MAAM,CAAC;IAC1D;EACF;EAEA,IAAI,OAAO4H,SAAS,KAAK,QAAQ,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,CAAC,EAAE;IACnE,OAAOtL,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,4CAA4C,CAAC;EAC5E;EAEA,MAAMmJ,IAAI,GAAG,IAAIlH,IAAI,CAACsH,IAAI,CAAC9H,KAAK,EAAE8H,IAAI,CAAC7H,MAAM,EAAE,UAAU,CAAC;EAE1D,MAAMiI,aAAa,GAAGvL,UAAU,CAC9BmL,IAAI,CAACxI,IAAI,EACTyI,IAAI,CAACzI,IAAI,EACToI,IAAI,CAAC5F,MAAM,CAACxC,IAAI,EAChBoI,IAAI,CAAC5F,MAAM,CAAC9B,KAAK,EACjB0H,IAAI,CAAC5F,MAAM,CAAC7B,MAAM,EAClB;IAAE4H;EAAU,CAAC,CACd;EAED,OAAO;IACLM,OAAO,EAAED,aAAa,IAAIR,IAAI,CAAC5F,MAAM,CAAC9B,KAAK,GAAG0H,IAAI,CAAC5F,MAAM,CAAC7B,MAAM,CAAC;IACjEwG,KAAK,EAAEiB;EACT,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAlH,IAAI,CAACwE,QAAQ,GAAG,UAAU2C,IAAI,EAAEC,IAAI,EAAE;EACpC,MAAMQ,KAAK,GAAG,IAAIvL,UAAU,EAAE;EAC9B,MAAMwL,KAAK,GAAGD,KAAK,CAACxD,OAAO,CAAC+C,IAAI,CAAC;EACjC,MAAMW,KAAK,GAAGF,KAAK,CAACxD,OAAO,CAACgD,IAAI,CAAC;EAEjC,OAAOQ,KAAK,CAACpD,QAAQ,CAACqD,KAAK,EAAEC,KAAK,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA9H,IAAI,CAAC+H,aAAa,GAAG,UAAUF,KAAK,EAAEC,KAAK,EAAE;EAC3C,MAAMF,KAAK,GAAG,IAAIvL,UAAU,EAAE;EAE9B,OAAOuL,KAAK,CAACpD,QAAQ,CAACqD,KAAK,EAAEC,KAAK,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9H,IAAI,CAACgI,SAAS,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;EACvC,MAAMxB,GAAG,GAAIK,CAAC,IAAK/B,IAAI,CAAC0B,GAAG,CAACK,CAAC,EAAE,CAAC,CAAC;EACjC,MAAM;IAAEC;EAAI,CAAC,GAAGhC,IAAI;EACpB,MAAMmD,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAE5B,IAAIF,KAAK,CAAC1B,CAAC,KAAK,CAAC,IAAI,CAAC0B,KAAK,CAAC1B,CAAC,EAAE;IAC7B0B,KAAK,CAAC1B,CAAC,GAAG,GAAG;EACf;EAEA,IAAI2B,KAAK,CAAC3B,CAAC,KAAK,CAAC,IAAI,CAAC2B,KAAK,CAAC3B,CAAC,EAAE;IAC7B2B,KAAK,CAAC3B,CAAC,GAAG,GAAG;EACf;EAEA,OACE,CAACS,GAAG,CAACN,GAAG,CAACuB,KAAK,CAAC7B,CAAC,GAAG8B,KAAK,CAAC9B,CAAC,CAAC,EAAEM,GAAG,CAACuB,KAAK,CAAC7B,CAAC,GAAG8B,KAAK,CAAC9B,CAAC,GAAG6B,KAAK,CAAC1B,CAAC,GAAG2B,KAAK,CAAC3B,CAAC,CAAC,CAAC,GACtES,GAAG,CAACN,GAAG,CAACuB,KAAK,CAAC5B,CAAC,GAAG6B,KAAK,CAAC7B,CAAC,CAAC,EAAEK,GAAG,CAACuB,KAAK,CAAC5B,CAAC,GAAG6B,KAAK,CAAC7B,CAAC,GAAG4B,KAAK,CAAC1B,CAAC,GAAG2B,KAAK,CAAC3B,CAAC,CAAC,CAAC,GACvES,GAAG,CAACN,GAAG,CAACuB,KAAK,CAAC3B,CAAC,GAAG4B,KAAK,CAAC5B,CAAC,CAAC,EAAEI,GAAG,CAACuB,KAAK,CAAC3B,CAAC,GAAG4B,KAAK,CAAC5B,CAAC,GAAG2B,KAAK,CAAC1B,CAAC,GAAG2B,KAAK,CAAC3B,CAAC,CAAC,CAAC,IACzE4B,MAAM;AAEV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAY,CAACvH,UAAU,EAAEwH,MAAM,EAAEC,MAAM,EAAE;EACvD,MAAMC,YAAY,GAAG,SAAS,GAAGF,MAAM;EACvC,MAAMG,WAAW,GAAGH,MAAM,CAACI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI;EAEnDzI,IAAI,CAACnC,SAAS,CAACgD,UAAU,CAAC,GAAG,YAAmB;IAC9C,IAAI6H,SAAS;IAAC,mCAD0BxI,IAAI;MAAJA,IAAI;IAAA;IAE5C,MAAMtB,EAAE,GAAGsB,IAAI,CAACoI,MAAM,CAAC9K,MAAM,GAAG,CAAC,CAAC;IAClC,MAAMkD,YAAY,GAAG,IAAI;IAEzB,IAAI,OAAO9B,EAAE,KAAK,UAAU,EAAE;MAC5B8J,SAAS,GAAG,YAAmB;QAAA,mCAANxI,IAAI;UAAJA,IAAI;QAAA;QAC3B,MAAM,CAACrB,GAAG,EAAEC,IAAI,CAAC,GAAGoB,IAAI;QAExB,IAAIrB,GAAG,EAAE;UACP6B,YAAY,CAACK,SAAS,CAACF,UAAU,EAAEhC,GAAG,CAAC;QACzC,CAAC,MAAM;UACL6B,YAAY,CAACM,SAAS,CAACH,UAAU,EAAE2H,WAAW,EAAE;YAC9C,CAAC3H,UAAU,GAAG/B;UAChB,CAAC,CAAC;QACJ;QAEAF,EAAE,CAAC+J,KAAK,CAAC,IAAI,EAAEzI,IAAI,CAAC;MACtB,CAAC;MAEDA,IAAI,CAACA,IAAI,CAAC1C,MAAM,GAAG,CAAC,CAAC,GAAGkL,SAAS;IACnC,CAAC,MAAM;MACLA,SAAS,GAAG,KAAK;IACnB;IAEA,IAAI,CAAC1H,SAAS,CAACH,UAAU,EAAE0H,YAAY,CAAC;IAExC,IAAIK,MAAM;IAEV,IAAI;MACFA,MAAM,GAAGN,MAAM,CAACK,KAAK,CAAC,IAAI,EAAEzI,IAAI,CAAC;MAEjC,IAAI,CAACwI,SAAS,EAAE;QACd,IAAI,CAAC1H,SAAS,CAACH,UAAU,EAAE2H,WAAW,EAAE;UACtC,CAAC3H,UAAU,GAAG+H;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdA,KAAK,CAAChI,UAAU,GAAGA,UAAU;MAC7B,IAAI,CAACE,SAAS,CAACF,UAAU,EAAEgI,KAAK,CAAC;IACnC;IAEA,OAAOD,MAAM;EACf,CAAC;EAED5I,IAAI,CAACnC,SAAS,CAACgD,UAAU,GAAG,OAAO,CAAC,GAAGyH,MAAM;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACAF,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,UAAUxJ,EAAE,EAAE;EAC3C,MAAMkK,KAAK,GAAG,IAAI9I,IAAI,CAAC,IAAI,CAAC;EAE5B,IAAIlE,aAAa,CAAC8C,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC+K,KAAK,EAAE,IAAI,EAAEA,KAAK,CAAC;EAC7B;EAEA,OAAOA,KAAK;AACd,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAY,CAAClI,UAAU,EAAEyH,MAAM,EAAE;EAC/CF,YAAY,CAACvH,UAAU,EAAE,QAAQ,EAAEyH,MAAM,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAS,YAAY,CAAC,YAAY,EAAE,UAAU3D,GAAG,EAAExG,EAAE,EAAE;EAC5C,IAAI,OAAOwG,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOrJ,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,sCAAsC,EAAEa,EAAE,CAAC;EAC1E;EAEA,IAAI,CAACwC,WAAW,GAAGgE,GAAG;EAEtB,IAAItJ,aAAa,CAAC8C,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAgL,YAAY,CAAC,MAAM,EAAE,UAAUrE,CAAC,EAAEC,CAAC,EAAEzD,CAAC,EAAEC,CAAC,EAAE6H,CAAC,EAAEpK,EAAE,EAAE;EAChD,IAAI,OAAO8F,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,OAAO5I,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;EAC7D;EAEA,IAAI,OAAOsC,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,OAAOpF,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;EAC7D;EAEA,IAAI,OAAOoK,CAAC,KAAK,UAAU,EAAE;IAC3B,OAAOjN,UAAU,CAACgC,IAAI,CAAC,IAAI,EAAE,sBAAsB,EAAEa,EAAE,CAAC;EAC1D;EAEA,MAAMgK,MAAM,GAAG5M,IAAI,CAAC,IAAI,EAAE0I,CAAC,EAAEC,CAAC,EAAEzD,CAAC,EAAEC,CAAC,EAAE6H,CAAC,CAAC;EAExC,IAAIlN,aAAa,CAAC8C,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE6K,MAAM,CAAC;EAC7B;EAEA,OAAOA,MAAM;AACf,CAAC,CAAC;AAEF,IAAIK,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,SAAS,EAAE;EACzC;EACA;EACA,IAAIC,EAAE;EAEN,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC/DD,EAAE,GAAGC,MAAM;EACb;EAEA,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC3DF,EAAE,GAAGE,IAAI;EACX;EAEAF,EAAE,CAACpJ,IAAI,GAAGA,IAAI;EACdoJ,EAAE,CAAC/K,MAAM,GAAGA,MAAM;AACpB;AAEA,SAASkL,OAAO,QAAQ,cAAc;AAEtC,eAAevJ,IAAI"}