/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sax";
exports.ids = ["vendor-chunks/sax"];
exports.modules = {

/***/ "(rsc)/./node_modules/sax/lib/sax.js":
/*!*************************************!*\
  !*** ./node_modules/sax/lib/sax.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(";\n(function(sax) {\n    sax.parser = function(strict, opt) {\n        return new SAXParser(strict, opt);\n    };\n    sax.SAXParser = SAXParser;\n    sax.SAXStream = SAXStream;\n    sax.createStream = createStream;\n    // When we pass the MAX_BUFFER_LENGTH position, start checking for buffer overruns.\n    // When we check, schedule the next check for MAX_BUFFER_LENGTH - (max(buffer lengths)),\n    // since that's the earliest that a buffer overrun could occur.  This way, checks are\n    // as rare as required, but as often as necessary to ensure never crossing this bound.\n    // Furthermore, buffers are only tested at most once per write(), so passing a very\n    // large string into write() might have undesirable effects, but this is manageable by\n    // the caller, so it is assumed to be safe.  Thus, a call to write() may, in the extreme\n    // edge case, result in creating at most one complete copy of the string passed in.\n    // Set to Infinity to have unlimited buffers.\n    sax.MAX_BUFFER_LENGTH = 64 * 1024;\n    var buffers = [\n        \"comment\",\n        \"sgmlDecl\",\n        \"textNode\",\n        \"tagName\",\n        \"doctype\",\n        \"procInstName\",\n        \"procInstBody\",\n        \"entity\",\n        \"attribName\",\n        \"attribValue\",\n        \"cdata\",\n        \"script\"\n    ];\n    sax.EVENTS = [\n        \"text\",\n        \"processinginstruction\",\n        \"sgmldeclaration\",\n        \"doctype\",\n        \"comment\",\n        \"opentagstart\",\n        \"attribute\",\n        \"opentag\",\n        \"closetag\",\n        \"opencdata\",\n        \"cdata\",\n        \"closecdata\",\n        \"error\",\n        \"end\",\n        \"ready\",\n        \"script\",\n        \"opennamespace\",\n        \"closenamespace\"\n    ];\n    function SAXParser(strict, opt) {\n        if (!(this instanceof SAXParser)) {\n            return new SAXParser(strict, opt);\n        }\n        var parser = this;\n        clearBuffers(parser);\n        parser.q = parser.c = \"\";\n        parser.bufferCheckPosition = sax.MAX_BUFFER_LENGTH;\n        parser.opt = opt || {};\n        parser.opt.lowercase = parser.opt.lowercase || parser.opt.lowercasetags;\n        parser.looseCase = parser.opt.lowercase ? \"toLowerCase\" : \"toUpperCase\";\n        parser.tags = [];\n        parser.closed = parser.closedRoot = parser.sawRoot = false;\n        parser.tag = parser.error = null;\n        parser.strict = !!strict;\n        parser.noscript = !!(strict || parser.opt.noscript);\n        parser.state = S.BEGIN;\n        parser.strictEntities = parser.opt.strictEntities;\n        parser.ENTITIES = parser.strictEntities ? Object.create(sax.XML_ENTITIES) : Object.create(sax.ENTITIES);\n        parser.attribList = [];\n        // namespaces form a prototype chain.\n        // it always points at the current tag,\n        // which protos to its parent tag.\n        if (parser.opt.xmlns) {\n            parser.ns = Object.create(rootNS);\n        }\n        // disallow unquoted attribute values if not otherwise configured\n        // and strict mode is true\n        if (parser.opt.unquotedAttributeValues === undefined) {\n            parser.opt.unquotedAttributeValues = !strict;\n        }\n        // mostly just for error reporting\n        parser.trackPosition = parser.opt.position !== false;\n        if (parser.trackPosition) {\n            parser.position = parser.line = parser.column = 0;\n        }\n        emit(parser, \"onready\");\n    }\n    if (!Object.create) {\n        Object.create = function(o) {\n            function F() {}\n            F.prototype = o;\n            var newf = new F();\n            return newf;\n        };\n    }\n    if (!Object.keys) {\n        Object.keys = function(o) {\n            var a = [];\n            for(var i in o)if (o.hasOwnProperty(i)) a.push(i);\n            return a;\n        };\n    }\n    function checkBufferLength(parser) {\n        var maxAllowed = Math.max(sax.MAX_BUFFER_LENGTH, 10);\n        var maxActual = 0;\n        for(var i = 0, l = buffers.length; i < l; i++){\n            var len = parser[buffers[i]].length;\n            if (len > maxAllowed) {\n                // Text/cdata nodes can get big, and since they're buffered,\n                // we can get here under normal conditions.\n                // Avoid issues by emitting the text node now,\n                // so at least it won't get any bigger.\n                switch(buffers[i]){\n                    case \"textNode\":\n                        closeText(parser);\n                        break;\n                    case \"cdata\":\n                        emitNode(parser, \"oncdata\", parser.cdata);\n                        parser.cdata = \"\";\n                        break;\n                    case \"script\":\n                        emitNode(parser, \"onscript\", parser.script);\n                        parser.script = \"\";\n                        break;\n                    default:\n                        error(parser, \"Max buffer length exceeded: \" + buffers[i]);\n                }\n            }\n            maxActual = Math.max(maxActual, len);\n        }\n        // schedule the next check for the earliest possible buffer overrun.\n        var m = sax.MAX_BUFFER_LENGTH - maxActual;\n        parser.bufferCheckPosition = m + parser.position;\n    }\n    function clearBuffers(parser) {\n        for(var i = 0, l = buffers.length; i < l; i++){\n            parser[buffers[i]] = \"\";\n        }\n    }\n    function flushBuffers(parser) {\n        closeText(parser);\n        if (parser.cdata !== \"\") {\n            emitNode(parser, \"oncdata\", parser.cdata);\n            parser.cdata = \"\";\n        }\n        if (parser.script !== \"\") {\n            emitNode(parser, \"onscript\", parser.script);\n            parser.script = \"\";\n        }\n    }\n    SAXParser.prototype = {\n        end: function() {\n            end(this);\n        },\n        write: write,\n        resume: function() {\n            this.error = null;\n            return this;\n        },\n        close: function() {\n            return this.write(null);\n        },\n        flush: function() {\n            flushBuffers(this);\n        }\n    };\n    var Stream;\n    try {\n        Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\n    } catch (ex) {\n        Stream = function() {};\n    }\n    if (!Stream) Stream = function() {};\n    var streamWraps = sax.EVENTS.filter(function(ev) {\n        return ev !== \"error\" && ev !== \"end\";\n    });\n    function createStream(strict, opt) {\n        return new SAXStream(strict, opt);\n    }\n    function SAXStream(strict, opt) {\n        if (!(this instanceof SAXStream)) {\n            return new SAXStream(strict, opt);\n        }\n        Stream.apply(this);\n        this._parser = new SAXParser(strict, opt);\n        this.writable = true;\n        this.readable = true;\n        var me = this;\n        this._parser.onend = function() {\n            me.emit(\"end\");\n        };\n        this._parser.onerror = function(er) {\n            me.emit(\"error\", er);\n            // if didn't throw, then means error was handled.\n            // go ahead and clear error, so we can write again.\n            me._parser.error = null;\n        };\n        this._decoder = null;\n        streamWraps.forEach(function(ev) {\n            Object.defineProperty(me, \"on\" + ev, {\n                get: function() {\n                    return me._parser[\"on\" + ev];\n                },\n                set: function(h) {\n                    if (!h) {\n                        me.removeAllListeners(ev);\n                        me._parser[\"on\" + ev] = h;\n                        return h;\n                    }\n                    me.on(ev, h);\n                },\n                enumerable: true,\n                configurable: false\n            });\n        });\n    }\n    SAXStream.prototype = Object.create(Stream.prototype, {\n        constructor: {\n            value: SAXStream\n        }\n    });\n    SAXStream.prototype.write = function(data) {\n        if (typeof Buffer === \"function\" && typeof Buffer.isBuffer === \"function\" && Buffer.isBuffer(data)) {\n            if (!this._decoder) {\n                var SD = (__webpack_require__(/*! string_decoder */ \"string_decoder\").StringDecoder);\n                this._decoder = new SD(\"utf8\");\n            }\n            data = this._decoder.write(data);\n        }\n        this._parser.write(data.toString());\n        this.emit(\"data\", data);\n        return true;\n    };\n    SAXStream.prototype.end = function(chunk) {\n        if (chunk && chunk.length) {\n            this.write(chunk);\n        }\n        this._parser.end();\n        return true;\n    };\n    SAXStream.prototype.on = function(ev, handler) {\n        var me = this;\n        if (!me._parser[\"on\" + ev] && streamWraps.indexOf(ev) !== -1) {\n            me._parser[\"on\" + ev] = function() {\n                var args = arguments.length === 1 ? [\n                    arguments[0]\n                ] : Array.apply(null, arguments);\n                args.splice(0, 0, ev);\n                me.emit.apply(me, args);\n            };\n        }\n        return Stream.prototype.on.call(me, ev, handler);\n    };\n    // this really needs to be replaced with character classes.\n    // XML allows all manner of ridiculous numbers and digits.\n    var CDATA = \"[CDATA[\";\n    var DOCTYPE = \"DOCTYPE\";\n    var XML_NAMESPACE = \"http://www.w3.org/XML/1998/namespace\";\n    var XMLNS_NAMESPACE = \"http://www.w3.org/2000/xmlns/\";\n    var rootNS = {\n        xml: XML_NAMESPACE,\n        xmlns: XMLNS_NAMESPACE\n    };\n    // http://www.w3.org/TR/REC-xml/#NT-NameStartChar\n    // This implementation works on strings, a single character at a time\n    // as such, it cannot ever support astral-plane characters (10000-EFFFF)\n    // without a significant breaking change to either this  parser, or the\n    // JavaScript language.  Implementation of an emoji-capable xml parser\n    // is left as an exercise for the reader.\n    var nameStart = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/;\n    var nameBody = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/;\n    var entityStart = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/;\n    var entityBody = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/;\n    function isWhitespace(c) {\n        return c === \" \" || c === \"\\n\" || c === \"\\r\" || c === \"\t\";\n    }\n    function isQuote(c) {\n        return c === '\"' || c === \"'\";\n    }\n    function isAttribEnd(c) {\n        return c === \">\" || isWhitespace(c);\n    }\n    function isMatch(regex, c) {\n        return regex.test(c);\n    }\n    function notMatch(regex, c) {\n        return !isMatch(regex, c);\n    }\n    var S = 0;\n    sax.STATE = {\n        BEGIN: S++,\n        BEGIN_WHITESPACE: S++,\n        TEXT: S++,\n        TEXT_ENTITY: S++,\n        OPEN_WAKA: S++,\n        SGML_DECL: S++,\n        SGML_DECL_QUOTED: S++,\n        DOCTYPE: S++,\n        DOCTYPE_QUOTED: S++,\n        DOCTYPE_DTD: S++,\n        DOCTYPE_DTD_QUOTED: S++,\n        COMMENT_STARTING: S++,\n        COMMENT: S++,\n        COMMENT_ENDING: S++,\n        COMMENT_ENDED: S++,\n        CDATA: S++,\n        CDATA_ENDING: S++,\n        CDATA_ENDING_2: S++,\n        PROC_INST: S++,\n        PROC_INST_BODY: S++,\n        PROC_INST_ENDING: S++,\n        OPEN_TAG: S++,\n        OPEN_TAG_SLASH: S++,\n        ATTRIB: S++,\n        ATTRIB_NAME: S++,\n        ATTRIB_NAME_SAW_WHITE: S++,\n        ATTRIB_VALUE: S++,\n        ATTRIB_VALUE_QUOTED: S++,\n        ATTRIB_VALUE_CLOSED: S++,\n        ATTRIB_VALUE_UNQUOTED: S++,\n        ATTRIB_VALUE_ENTITY_Q: S++,\n        ATTRIB_VALUE_ENTITY_U: S++,\n        CLOSE_TAG: S++,\n        CLOSE_TAG_SAW_WHITE: S++,\n        SCRIPT: S++,\n        SCRIPT_ENDING: S++ // <script> ... <\n    };\n    sax.XML_ENTITIES = {\n        \"amp\": \"&\",\n        \"gt\": \">\",\n        \"lt\": \"<\",\n        \"quot\": '\"',\n        \"apos\": \"'\"\n    };\n    sax.ENTITIES = {\n        \"amp\": \"&\",\n        \"gt\": \">\",\n        \"lt\": \"<\",\n        \"quot\": '\"',\n        \"apos\": \"'\",\n        \"AElig\": 198,\n        \"Aacute\": 193,\n        \"Acirc\": 194,\n        \"Agrave\": 192,\n        \"Aring\": 197,\n        \"Atilde\": 195,\n        \"Auml\": 196,\n        \"Ccedil\": 199,\n        \"ETH\": 208,\n        \"Eacute\": 201,\n        \"Ecirc\": 202,\n        \"Egrave\": 200,\n        \"Euml\": 203,\n        \"Iacute\": 205,\n        \"Icirc\": 206,\n        \"Igrave\": 204,\n        \"Iuml\": 207,\n        \"Ntilde\": 209,\n        \"Oacute\": 211,\n        \"Ocirc\": 212,\n        \"Ograve\": 210,\n        \"Oslash\": 216,\n        \"Otilde\": 213,\n        \"Ouml\": 214,\n        \"THORN\": 222,\n        \"Uacute\": 218,\n        \"Ucirc\": 219,\n        \"Ugrave\": 217,\n        \"Uuml\": 220,\n        \"Yacute\": 221,\n        \"aacute\": 225,\n        \"acirc\": 226,\n        \"aelig\": 230,\n        \"agrave\": 224,\n        \"aring\": 229,\n        \"atilde\": 227,\n        \"auml\": 228,\n        \"ccedil\": 231,\n        \"eacute\": 233,\n        \"ecirc\": 234,\n        \"egrave\": 232,\n        \"eth\": 240,\n        \"euml\": 235,\n        \"iacute\": 237,\n        \"icirc\": 238,\n        \"igrave\": 236,\n        \"iuml\": 239,\n        \"ntilde\": 241,\n        \"oacute\": 243,\n        \"ocirc\": 244,\n        \"ograve\": 242,\n        \"oslash\": 248,\n        \"otilde\": 245,\n        \"ouml\": 246,\n        \"szlig\": 223,\n        \"thorn\": 254,\n        \"uacute\": 250,\n        \"ucirc\": 251,\n        \"ugrave\": 249,\n        \"uuml\": 252,\n        \"yacute\": 253,\n        \"yuml\": 255,\n        \"copy\": 169,\n        \"reg\": 174,\n        \"nbsp\": 160,\n        \"iexcl\": 161,\n        \"cent\": 162,\n        \"pound\": 163,\n        \"curren\": 164,\n        \"yen\": 165,\n        \"brvbar\": 166,\n        \"sect\": 167,\n        \"uml\": 168,\n        \"ordf\": 170,\n        \"laquo\": 171,\n        \"not\": 172,\n        \"shy\": 173,\n        \"macr\": 175,\n        \"deg\": 176,\n        \"plusmn\": 177,\n        \"sup1\": 185,\n        \"sup2\": 178,\n        \"sup3\": 179,\n        \"acute\": 180,\n        \"micro\": 181,\n        \"para\": 182,\n        \"middot\": 183,\n        \"cedil\": 184,\n        \"ordm\": 186,\n        \"raquo\": 187,\n        \"frac14\": 188,\n        \"frac12\": 189,\n        \"frac34\": 190,\n        \"iquest\": 191,\n        \"times\": 215,\n        \"divide\": 247,\n        \"OElig\": 338,\n        \"oelig\": 339,\n        \"Scaron\": 352,\n        \"scaron\": 353,\n        \"Yuml\": 376,\n        \"fnof\": 402,\n        \"circ\": 710,\n        \"tilde\": 732,\n        \"Alpha\": 913,\n        \"Beta\": 914,\n        \"Gamma\": 915,\n        \"Delta\": 916,\n        \"Epsilon\": 917,\n        \"Zeta\": 918,\n        \"Eta\": 919,\n        \"Theta\": 920,\n        \"Iota\": 921,\n        \"Kappa\": 922,\n        \"Lambda\": 923,\n        \"Mu\": 924,\n        \"Nu\": 925,\n        \"Xi\": 926,\n        \"Omicron\": 927,\n        \"Pi\": 928,\n        \"Rho\": 929,\n        \"Sigma\": 931,\n        \"Tau\": 932,\n        \"Upsilon\": 933,\n        \"Phi\": 934,\n        \"Chi\": 935,\n        \"Psi\": 936,\n        \"Omega\": 937,\n        \"alpha\": 945,\n        \"beta\": 946,\n        \"gamma\": 947,\n        \"delta\": 948,\n        \"epsilon\": 949,\n        \"zeta\": 950,\n        \"eta\": 951,\n        \"theta\": 952,\n        \"iota\": 953,\n        \"kappa\": 954,\n        \"lambda\": 955,\n        \"mu\": 956,\n        \"nu\": 957,\n        \"xi\": 958,\n        \"omicron\": 959,\n        \"pi\": 960,\n        \"rho\": 961,\n        \"sigmaf\": 962,\n        \"sigma\": 963,\n        \"tau\": 964,\n        \"upsilon\": 965,\n        \"phi\": 966,\n        \"chi\": 967,\n        \"psi\": 968,\n        \"omega\": 969,\n        \"thetasym\": 977,\n        \"upsih\": 978,\n        \"piv\": 982,\n        \"ensp\": 8194,\n        \"emsp\": 8195,\n        \"thinsp\": 8201,\n        \"zwnj\": 8204,\n        \"zwj\": 8205,\n        \"lrm\": 8206,\n        \"rlm\": 8207,\n        \"ndash\": 8211,\n        \"mdash\": 8212,\n        \"lsquo\": 8216,\n        \"rsquo\": 8217,\n        \"sbquo\": 8218,\n        \"ldquo\": 8220,\n        \"rdquo\": 8221,\n        \"bdquo\": 8222,\n        \"dagger\": 8224,\n        \"Dagger\": 8225,\n        \"bull\": 8226,\n        \"hellip\": 8230,\n        \"permil\": 8240,\n        \"prime\": 8242,\n        \"Prime\": 8243,\n        \"lsaquo\": 8249,\n        \"rsaquo\": 8250,\n        \"oline\": 8254,\n        \"frasl\": 8260,\n        \"euro\": 8364,\n        \"image\": 8465,\n        \"weierp\": 8472,\n        \"real\": 8476,\n        \"trade\": 8482,\n        \"alefsym\": 8501,\n        \"larr\": 8592,\n        \"uarr\": 8593,\n        \"rarr\": 8594,\n        \"darr\": 8595,\n        \"harr\": 8596,\n        \"crarr\": 8629,\n        \"lArr\": 8656,\n        \"uArr\": 8657,\n        \"rArr\": 8658,\n        \"dArr\": 8659,\n        \"hArr\": 8660,\n        \"forall\": 8704,\n        \"part\": 8706,\n        \"exist\": 8707,\n        \"empty\": 8709,\n        \"nabla\": 8711,\n        \"isin\": 8712,\n        \"notin\": 8713,\n        \"ni\": 8715,\n        \"prod\": 8719,\n        \"sum\": 8721,\n        \"minus\": 8722,\n        \"lowast\": 8727,\n        \"radic\": 8730,\n        \"prop\": 8733,\n        \"infin\": 8734,\n        \"ang\": 8736,\n        \"and\": 8743,\n        \"or\": 8744,\n        \"cap\": 8745,\n        \"cup\": 8746,\n        \"int\": 8747,\n        \"there4\": 8756,\n        \"sim\": 8764,\n        \"cong\": 8773,\n        \"asymp\": 8776,\n        \"ne\": 8800,\n        \"equiv\": 8801,\n        \"le\": 8804,\n        \"ge\": 8805,\n        \"sub\": 8834,\n        \"sup\": 8835,\n        \"nsub\": 8836,\n        \"sube\": 8838,\n        \"supe\": 8839,\n        \"oplus\": 8853,\n        \"otimes\": 8855,\n        \"perp\": 8869,\n        \"sdot\": 8901,\n        \"lceil\": 8968,\n        \"rceil\": 8969,\n        \"lfloor\": 8970,\n        \"rfloor\": 8971,\n        \"lang\": 9001,\n        \"rang\": 9002,\n        \"loz\": 9674,\n        \"spades\": 9824,\n        \"clubs\": 9827,\n        \"hearts\": 9829,\n        \"diams\": 9830\n    };\n    Object.keys(sax.ENTITIES).forEach(function(key) {\n        var e = sax.ENTITIES[key];\n        var s = typeof e === \"number\" ? String.fromCharCode(e) : e;\n        sax.ENTITIES[key] = s;\n    });\n    for(var s in sax.STATE){\n        sax.STATE[sax.STATE[s]] = s;\n    }\n    // shorthand\n    S = sax.STATE;\n    function emit(parser, event, data) {\n        parser[event] && parser[event](data);\n    }\n    function emitNode(parser, nodeType, data) {\n        if (parser.textNode) closeText(parser);\n        emit(parser, nodeType, data);\n    }\n    function closeText(parser) {\n        parser.textNode = textopts(parser.opt, parser.textNode);\n        if (parser.textNode) emit(parser, \"ontext\", parser.textNode);\n        parser.textNode = \"\";\n    }\n    function textopts(opt, text) {\n        if (opt.trim) text = text.trim();\n        if (opt.normalize) text = text.replace(/\\s+/g, \" \");\n        return text;\n    }\n    function error(parser, er) {\n        closeText(parser);\n        if (parser.trackPosition) {\n            er += \"\\nLine: \" + parser.line + \"\\nColumn: \" + parser.column + \"\\nChar: \" + parser.c;\n        }\n        er = new Error(er);\n        parser.error = er;\n        emit(parser, \"onerror\", er);\n        return parser;\n    }\n    function end(parser) {\n        if (parser.sawRoot && !parser.closedRoot) strictFail(parser, \"Unclosed root tag\");\n        if (parser.state !== S.BEGIN && parser.state !== S.BEGIN_WHITESPACE && parser.state !== S.TEXT) {\n            error(parser, \"Unexpected end\");\n        }\n        closeText(parser);\n        parser.c = \"\";\n        parser.closed = true;\n        emit(parser, \"onend\");\n        SAXParser.call(parser, parser.strict, parser.opt);\n        return parser;\n    }\n    function strictFail(parser, message) {\n        if (typeof parser !== \"object\" || !(parser instanceof SAXParser)) {\n            throw new Error(\"bad call to strictFail\");\n        }\n        if (parser.strict) {\n            error(parser, message);\n        }\n    }\n    function newTag(parser) {\n        if (!parser.strict) parser.tagName = parser.tagName[parser.looseCase]();\n        var parent = parser.tags[parser.tags.length - 1] || parser;\n        var tag = parser.tag = {\n            name: parser.tagName,\n            attributes: {}\n        };\n        // will be overridden if tag contails an xmlns=\"foo\" or xmlns:foo=\"bar\"\n        if (parser.opt.xmlns) {\n            tag.ns = parent.ns;\n        }\n        parser.attribList.length = 0;\n        emitNode(parser, \"onopentagstart\", tag);\n    }\n    function qname(name, attribute) {\n        var i = name.indexOf(\":\");\n        var qualName = i < 0 ? [\n            \"\",\n            name\n        ] : name.split(\":\");\n        var prefix = qualName[0];\n        var local = qualName[1];\n        // <x \"xmlns\"=\"http://foo\">\n        if (attribute && name === \"xmlns\") {\n            prefix = \"xmlns\";\n            local = \"\";\n        }\n        return {\n            prefix: prefix,\n            local: local\n        };\n    }\n    function attrib(parser) {\n        if (!parser.strict) {\n            parser.attribName = parser.attribName[parser.looseCase]();\n        }\n        if (parser.attribList.indexOf(parser.attribName) !== -1 || parser.tag.attributes.hasOwnProperty(parser.attribName)) {\n            parser.attribName = parser.attribValue = \"\";\n            return;\n        }\n        if (parser.opt.xmlns) {\n            var qn = qname(parser.attribName, true);\n            var prefix = qn.prefix;\n            var local = qn.local;\n            if (prefix === \"xmlns\") {\n                // namespace binding attribute. push the binding into scope\n                if (local === \"xml\" && parser.attribValue !== XML_NAMESPACE) {\n                    strictFail(parser, \"xml: prefix must be bound to \" + XML_NAMESPACE + \"\\n\" + \"Actual: \" + parser.attribValue);\n                } else if (local === \"xmlns\" && parser.attribValue !== XMLNS_NAMESPACE) {\n                    strictFail(parser, \"xmlns: prefix must be bound to \" + XMLNS_NAMESPACE + \"\\n\" + \"Actual: \" + parser.attribValue);\n                } else {\n                    var tag = parser.tag;\n                    var parent = parser.tags[parser.tags.length - 1] || parser;\n                    if (tag.ns === parent.ns) {\n                        tag.ns = Object.create(parent.ns);\n                    }\n                    tag.ns[local] = parser.attribValue;\n                }\n            }\n            // defer onattribute events until all attributes have been seen\n            // so any new bindings can take effect. preserve attribute order\n            // so deferred events can be emitted in document order\n            parser.attribList.push([\n                parser.attribName,\n                parser.attribValue\n            ]);\n        } else {\n            // in non-xmlns mode, we can emit the event right away\n            parser.tag.attributes[parser.attribName] = parser.attribValue;\n            emitNode(parser, \"onattribute\", {\n                name: parser.attribName,\n                value: parser.attribValue\n            });\n        }\n        parser.attribName = parser.attribValue = \"\";\n    }\n    function openTag(parser, selfClosing) {\n        if (parser.opt.xmlns) {\n            // emit namespace binding events\n            var tag = parser.tag;\n            // add namespace info to tag\n            var qn = qname(parser.tagName);\n            tag.prefix = qn.prefix;\n            tag.local = qn.local;\n            tag.uri = tag.ns[qn.prefix] || \"\";\n            if (tag.prefix && !tag.uri) {\n                strictFail(parser, \"Unbound namespace prefix: \" + JSON.stringify(parser.tagName));\n                tag.uri = qn.prefix;\n            }\n            var parent = parser.tags[parser.tags.length - 1] || parser;\n            if (tag.ns && parent.ns !== tag.ns) {\n                Object.keys(tag.ns).forEach(function(p) {\n                    emitNode(parser, \"onopennamespace\", {\n                        prefix: p,\n                        uri: tag.ns[p]\n                    });\n                });\n            }\n            // handle deferred onattribute events\n            // Note: do not apply default ns to attributes:\n            //   http://www.w3.org/TR/REC-xml-names/#defaulting\n            for(var i = 0, l = parser.attribList.length; i < l; i++){\n                var nv = parser.attribList[i];\n                var name = nv[0];\n                var value = nv[1];\n                var qualName = qname(name, true);\n                var prefix = qualName.prefix;\n                var local = qualName.local;\n                var uri = prefix === \"\" ? \"\" : tag.ns[prefix] || \"\";\n                var a = {\n                    name: name,\n                    value: value,\n                    prefix: prefix,\n                    local: local,\n                    uri: uri\n                };\n                // if there's any attributes with an undefined namespace,\n                // then fail on them now.\n                if (prefix && prefix !== \"xmlns\" && !uri) {\n                    strictFail(parser, \"Unbound namespace prefix: \" + JSON.stringify(prefix));\n                    a.uri = prefix;\n                }\n                parser.tag.attributes[name] = a;\n                emitNode(parser, \"onattribute\", a);\n            }\n            parser.attribList.length = 0;\n        }\n        parser.tag.isSelfClosing = !!selfClosing;\n        // process the tag\n        parser.sawRoot = true;\n        parser.tags.push(parser.tag);\n        emitNode(parser, \"onopentag\", parser.tag);\n        if (!selfClosing) {\n            // special case for <script> in non-strict mode.\n            if (!parser.noscript && parser.tagName.toLowerCase() === \"script\") {\n                parser.state = S.SCRIPT;\n            } else {\n                parser.state = S.TEXT;\n            }\n            parser.tag = null;\n            parser.tagName = \"\";\n        }\n        parser.attribName = parser.attribValue = \"\";\n        parser.attribList.length = 0;\n    }\n    function closeTag(parser) {\n        if (!parser.tagName) {\n            strictFail(parser, \"Weird empty close tag.\");\n            parser.textNode += \"</>\";\n            parser.state = S.TEXT;\n            return;\n        }\n        if (parser.script) {\n            if (parser.tagName !== \"script\") {\n                parser.script += \"</\" + parser.tagName + \">\";\n                parser.tagName = \"\";\n                parser.state = S.SCRIPT;\n                return;\n            }\n            emitNode(parser, \"onscript\", parser.script);\n            parser.script = \"\";\n        }\n        // first make sure that the closing tag actually exists.\n        // <a><b></c></b></a> will close everything, otherwise.\n        var t = parser.tags.length;\n        var tagName = parser.tagName;\n        if (!parser.strict) {\n            tagName = tagName[parser.looseCase]();\n        }\n        var closeTo = tagName;\n        while(t--){\n            var close = parser.tags[t];\n            if (close.name !== closeTo) {\n                // fail the first time in strict mode\n                strictFail(parser, \"Unexpected close tag\");\n            } else {\n                break;\n            }\n        }\n        // didn't find it.  we already failed for strict, so just abort.\n        if (t < 0) {\n            strictFail(parser, \"Unmatched closing tag: \" + parser.tagName);\n            parser.textNode += \"</\" + parser.tagName + \">\";\n            parser.state = S.TEXT;\n            return;\n        }\n        parser.tagName = tagName;\n        var s = parser.tags.length;\n        while(s-- > t){\n            var tag = parser.tag = parser.tags.pop();\n            parser.tagName = parser.tag.name;\n            emitNode(parser, \"onclosetag\", parser.tagName);\n            var x = {};\n            for(var i in tag.ns){\n                x[i] = tag.ns[i];\n            }\n            var parent = parser.tags[parser.tags.length - 1] || parser;\n            if (parser.opt.xmlns && tag.ns !== parent.ns) {\n                // remove namespace bindings introduced by tag\n                Object.keys(tag.ns).forEach(function(p) {\n                    var n = tag.ns[p];\n                    emitNode(parser, \"onclosenamespace\", {\n                        prefix: p,\n                        uri: n\n                    });\n                });\n            }\n        }\n        if (t === 0) parser.closedRoot = true;\n        parser.tagName = parser.attribValue = parser.attribName = \"\";\n        parser.attribList.length = 0;\n        parser.state = S.TEXT;\n    }\n    function parseEntity(parser) {\n        var entity = parser.entity;\n        var entityLC = entity.toLowerCase();\n        var num;\n        var numStr = \"\";\n        if (parser.ENTITIES[entity]) {\n            return parser.ENTITIES[entity];\n        }\n        if (parser.ENTITIES[entityLC]) {\n            return parser.ENTITIES[entityLC];\n        }\n        entity = entityLC;\n        if (entity.charAt(0) === \"#\") {\n            if (entity.charAt(1) === \"x\") {\n                entity = entity.slice(2);\n                num = parseInt(entity, 16);\n                numStr = num.toString(16);\n            } else {\n                entity = entity.slice(1);\n                num = parseInt(entity, 10);\n                numStr = num.toString(10);\n            }\n        }\n        entity = entity.replace(/^0+/, \"\");\n        if (isNaN(num) || numStr.toLowerCase() !== entity) {\n            strictFail(parser, \"Invalid character entity\");\n            return \"&\" + parser.entity + \";\";\n        }\n        return String.fromCodePoint(num);\n    }\n    function beginWhiteSpace(parser, c) {\n        if (c === \"<\") {\n            parser.state = S.OPEN_WAKA;\n            parser.startTagPosition = parser.position;\n        } else if (!isWhitespace(c)) {\n            // have to process this as a text node.\n            // weird, but happens.\n            strictFail(parser, \"Non-whitespace before first tag.\");\n            parser.textNode = c;\n            parser.state = S.TEXT;\n        }\n    }\n    function charAt(chunk, i) {\n        var result = \"\";\n        if (i < chunk.length) {\n            result = chunk.charAt(i);\n        }\n        return result;\n    }\n    function write(chunk) {\n        var parser = this;\n        if (this.error) {\n            throw this.error;\n        }\n        if (parser.closed) {\n            return error(parser, \"Cannot write after close. Assign an onready handler.\");\n        }\n        if (chunk === null) {\n            return end(parser);\n        }\n        if (typeof chunk === \"object\") {\n            chunk = chunk.toString();\n        }\n        var i = 0;\n        var c = \"\";\n        while(true){\n            c = charAt(chunk, i++);\n            parser.c = c;\n            if (!c) {\n                break;\n            }\n            if (parser.trackPosition) {\n                parser.position++;\n                if (c === \"\\n\") {\n                    parser.line++;\n                    parser.column = 0;\n                } else {\n                    parser.column++;\n                }\n            }\n            switch(parser.state){\n                case S.BEGIN:\n                    parser.state = S.BEGIN_WHITESPACE;\n                    if (c === \"\\uFEFF\") {\n                        continue;\n                    }\n                    beginWhiteSpace(parser, c);\n                    continue;\n                case S.BEGIN_WHITESPACE:\n                    beginWhiteSpace(parser, c);\n                    continue;\n                case S.TEXT:\n                    if (parser.sawRoot && !parser.closedRoot) {\n                        var starti = i - 1;\n                        while(c && c !== \"<\" && c !== \"&\"){\n                            c = charAt(chunk, i++);\n                            if (c && parser.trackPosition) {\n                                parser.position++;\n                                if (c === \"\\n\") {\n                                    parser.line++;\n                                    parser.column = 0;\n                                } else {\n                                    parser.column++;\n                                }\n                            }\n                        }\n                        parser.textNode += chunk.substring(starti, i - 1);\n                    }\n                    if (c === \"<\" && !(parser.sawRoot && parser.closedRoot && !parser.strict)) {\n                        parser.state = S.OPEN_WAKA;\n                        parser.startTagPosition = parser.position;\n                    } else {\n                        if (!isWhitespace(c) && (!parser.sawRoot || parser.closedRoot)) {\n                            strictFail(parser, \"Text data outside of root node.\");\n                        }\n                        if (c === \"&\") {\n                            parser.state = S.TEXT_ENTITY;\n                        } else {\n                            parser.textNode += c;\n                        }\n                    }\n                    continue;\n                case S.SCRIPT:\n                    // only non-strict\n                    if (c === \"<\") {\n                        parser.state = S.SCRIPT_ENDING;\n                    } else {\n                        parser.script += c;\n                    }\n                    continue;\n                case S.SCRIPT_ENDING:\n                    if (c === \"/\") {\n                        parser.state = S.CLOSE_TAG;\n                    } else {\n                        parser.script += \"<\" + c;\n                        parser.state = S.SCRIPT;\n                    }\n                    continue;\n                case S.OPEN_WAKA:\n                    // either a /, ?, !, or text is coming next.\n                    if (c === \"!\") {\n                        parser.state = S.SGML_DECL;\n                        parser.sgmlDecl = \"\";\n                    } else if (isWhitespace(c)) {\n                    // wait for it...\n                    } else if (isMatch(nameStart, c)) {\n                        parser.state = S.OPEN_TAG;\n                        parser.tagName = c;\n                    } else if (c === \"/\") {\n                        parser.state = S.CLOSE_TAG;\n                        parser.tagName = \"\";\n                    } else if (c === \"?\") {\n                        parser.state = S.PROC_INST;\n                        parser.procInstName = parser.procInstBody = \"\";\n                    } else {\n                        strictFail(parser, \"Unencoded <\");\n                        // if there was some whitespace, then add that in.\n                        if (parser.startTagPosition + 1 < parser.position) {\n                            var pad = parser.position - parser.startTagPosition;\n                            c = new Array(pad).join(\" \") + c;\n                        }\n                        parser.textNode += \"<\" + c;\n                        parser.state = S.TEXT;\n                    }\n                    continue;\n                case S.SGML_DECL:\n                    if (parser.sgmlDecl + c === \"--\") {\n                        parser.state = S.COMMENT;\n                        parser.comment = \"\";\n                        parser.sgmlDecl = \"\";\n                        continue;\n                    }\n                    if (parser.doctype && parser.doctype !== true && parser.sgmlDecl) {\n                        parser.state = S.DOCTYPE_DTD;\n                        parser.doctype += \"<!\" + parser.sgmlDecl + c;\n                        parser.sgmlDecl = \"\";\n                    } else if ((parser.sgmlDecl + c).toUpperCase() === CDATA) {\n                        emitNode(parser, \"onopencdata\");\n                        parser.state = S.CDATA;\n                        parser.sgmlDecl = \"\";\n                        parser.cdata = \"\";\n                    } else if ((parser.sgmlDecl + c).toUpperCase() === DOCTYPE) {\n                        parser.state = S.DOCTYPE;\n                        if (parser.doctype || parser.sawRoot) {\n                            strictFail(parser, \"Inappropriately located doctype declaration\");\n                        }\n                        parser.doctype = \"\";\n                        parser.sgmlDecl = \"\";\n                    } else if (c === \">\") {\n                        emitNode(parser, \"onsgmldeclaration\", parser.sgmlDecl);\n                        parser.sgmlDecl = \"\";\n                        parser.state = S.TEXT;\n                    } else if (isQuote(c)) {\n                        parser.state = S.SGML_DECL_QUOTED;\n                        parser.sgmlDecl += c;\n                    } else {\n                        parser.sgmlDecl += c;\n                    }\n                    continue;\n                case S.SGML_DECL_QUOTED:\n                    if (c === parser.q) {\n                        parser.state = S.SGML_DECL;\n                        parser.q = \"\";\n                    }\n                    parser.sgmlDecl += c;\n                    continue;\n                case S.DOCTYPE:\n                    if (c === \">\") {\n                        parser.state = S.TEXT;\n                        emitNode(parser, \"ondoctype\", parser.doctype);\n                        parser.doctype = true // just remember that we saw it.\n                        ;\n                    } else {\n                        parser.doctype += c;\n                        if (c === \"[\") {\n                            parser.state = S.DOCTYPE_DTD;\n                        } else if (isQuote(c)) {\n                            parser.state = S.DOCTYPE_QUOTED;\n                            parser.q = c;\n                        }\n                    }\n                    continue;\n                case S.DOCTYPE_QUOTED:\n                    parser.doctype += c;\n                    if (c === parser.q) {\n                        parser.q = \"\";\n                        parser.state = S.DOCTYPE;\n                    }\n                    continue;\n                case S.DOCTYPE_DTD:\n                    if (c === \"]\") {\n                        parser.doctype += c;\n                        parser.state = S.DOCTYPE;\n                    } else if (c === \"<\") {\n                        parser.state = S.OPEN_WAKA;\n                        parser.startTagPosition = parser.position;\n                    } else if (isQuote(c)) {\n                        parser.doctype += c;\n                        parser.state = S.DOCTYPE_DTD_QUOTED;\n                        parser.q = c;\n                    } else {\n                        parser.doctype += c;\n                    }\n                    continue;\n                case S.DOCTYPE_DTD_QUOTED:\n                    parser.doctype += c;\n                    if (c === parser.q) {\n                        parser.state = S.DOCTYPE_DTD;\n                        parser.q = \"\";\n                    }\n                    continue;\n                case S.COMMENT:\n                    if (c === \"-\") {\n                        parser.state = S.COMMENT_ENDING;\n                    } else {\n                        parser.comment += c;\n                    }\n                    continue;\n                case S.COMMENT_ENDING:\n                    if (c === \"-\") {\n                        parser.state = S.COMMENT_ENDED;\n                        parser.comment = textopts(parser.opt, parser.comment);\n                        if (parser.comment) {\n                            emitNode(parser, \"oncomment\", parser.comment);\n                        }\n                        parser.comment = \"\";\n                    } else {\n                        parser.comment += \"-\" + c;\n                        parser.state = S.COMMENT;\n                    }\n                    continue;\n                case S.COMMENT_ENDED:\n                    if (c !== \">\") {\n                        strictFail(parser, \"Malformed comment\");\n                        // allow <!-- blah -- bloo --> in non-strict mode,\n                        // which is a comment of \" blah -- bloo \"\n                        parser.comment += \"--\" + c;\n                        parser.state = S.COMMENT;\n                    } else if (parser.doctype && parser.doctype !== true) {\n                        parser.state = S.DOCTYPE_DTD;\n                    } else {\n                        parser.state = S.TEXT;\n                    }\n                    continue;\n                case S.CDATA:\n                    if (c === \"]\") {\n                        parser.state = S.CDATA_ENDING;\n                    } else {\n                        parser.cdata += c;\n                    }\n                    continue;\n                case S.CDATA_ENDING:\n                    if (c === \"]\") {\n                        parser.state = S.CDATA_ENDING_2;\n                    } else {\n                        parser.cdata += \"]\" + c;\n                        parser.state = S.CDATA;\n                    }\n                    continue;\n                case S.CDATA_ENDING_2:\n                    if (c === \">\") {\n                        if (parser.cdata) {\n                            emitNode(parser, \"oncdata\", parser.cdata);\n                        }\n                        emitNode(parser, \"onclosecdata\");\n                        parser.cdata = \"\";\n                        parser.state = S.TEXT;\n                    } else if (c === \"]\") {\n                        parser.cdata += \"]\";\n                    } else {\n                        parser.cdata += \"]]\" + c;\n                        parser.state = S.CDATA;\n                    }\n                    continue;\n                case S.PROC_INST:\n                    if (c === \"?\") {\n                        parser.state = S.PROC_INST_ENDING;\n                    } else if (isWhitespace(c)) {\n                        parser.state = S.PROC_INST_BODY;\n                    } else {\n                        parser.procInstName += c;\n                    }\n                    continue;\n                case S.PROC_INST_BODY:\n                    if (!parser.procInstBody && isWhitespace(c)) {\n                        continue;\n                    } else if (c === \"?\") {\n                        parser.state = S.PROC_INST_ENDING;\n                    } else {\n                        parser.procInstBody += c;\n                    }\n                    continue;\n                case S.PROC_INST_ENDING:\n                    if (c === \">\") {\n                        emitNode(parser, \"onprocessinginstruction\", {\n                            name: parser.procInstName,\n                            body: parser.procInstBody\n                        });\n                        parser.procInstName = parser.procInstBody = \"\";\n                        parser.state = S.TEXT;\n                    } else {\n                        parser.procInstBody += \"?\" + c;\n                        parser.state = S.PROC_INST_BODY;\n                    }\n                    continue;\n                case S.OPEN_TAG:\n                    if (isMatch(nameBody, c)) {\n                        parser.tagName += c;\n                    } else {\n                        newTag(parser);\n                        if (c === \">\") {\n                            openTag(parser);\n                        } else if (c === \"/\") {\n                            parser.state = S.OPEN_TAG_SLASH;\n                        } else {\n                            if (!isWhitespace(c)) {\n                                strictFail(parser, \"Invalid character in tag name\");\n                            }\n                            parser.state = S.ATTRIB;\n                        }\n                    }\n                    continue;\n                case S.OPEN_TAG_SLASH:\n                    if (c === \">\") {\n                        openTag(parser, true);\n                        closeTag(parser);\n                    } else {\n                        strictFail(parser, \"Forward-slash in opening tag not followed by >\");\n                        parser.state = S.ATTRIB;\n                    }\n                    continue;\n                case S.ATTRIB:\n                    // haven't read the attribute name yet.\n                    if (isWhitespace(c)) {\n                        continue;\n                    } else if (c === \">\") {\n                        openTag(parser);\n                    } else if (c === \"/\") {\n                        parser.state = S.OPEN_TAG_SLASH;\n                    } else if (isMatch(nameStart, c)) {\n                        parser.attribName = c;\n                        parser.attribValue = \"\";\n                        parser.state = S.ATTRIB_NAME;\n                    } else {\n                        strictFail(parser, \"Invalid attribute name\");\n                    }\n                    continue;\n                case S.ATTRIB_NAME:\n                    if (c === \"=\") {\n                        parser.state = S.ATTRIB_VALUE;\n                    } else if (c === \">\") {\n                        strictFail(parser, \"Attribute without value\");\n                        parser.attribValue = parser.attribName;\n                        attrib(parser);\n                        openTag(parser);\n                    } else if (isWhitespace(c)) {\n                        parser.state = S.ATTRIB_NAME_SAW_WHITE;\n                    } else if (isMatch(nameBody, c)) {\n                        parser.attribName += c;\n                    } else {\n                        strictFail(parser, \"Invalid attribute name\");\n                    }\n                    continue;\n                case S.ATTRIB_NAME_SAW_WHITE:\n                    if (c === \"=\") {\n                        parser.state = S.ATTRIB_VALUE;\n                    } else if (isWhitespace(c)) {\n                        continue;\n                    } else {\n                        strictFail(parser, \"Attribute without value\");\n                        parser.tag.attributes[parser.attribName] = \"\";\n                        parser.attribValue = \"\";\n                        emitNode(parser, \"onattribute\", {\n                            name: parser.attribName,\n                            value: \"\"\n                        });\n                        parser.attribName = \"\";\n                        if (c === \">\") {\n                            openTag(parser);\n                        } else if (isMatch(nameStart, c)) {\n                            parser.attribName = c;\n                            parser.state = S.ATTRIB_NAME;\n                        } else {\n                            strictFail(parser, \"Invalid attribute name\");\n                            parser.state = S.ATTRIB;\n                        }\n                    }\n                    continue;\n                case S.ATTRIB_VALUE:\n                    if (isWhitespace(c)) {\n                        continue;\n                    } else if (isQuote(c)) {\n                        parser.q = c;\n                        parser.state = S.ATTRIB_VALUE_QUOTED;\n                    } else {\n                        if (!parser.opt.unquotedAttributeValues) {\n                            error(parser, \"Unquoted attribute value\");\n                        }\n                        parser.state = S.ATTRIB_VALUE_UNQUOTED;\n                        parser.attribValue = c;\n                    }\n                    continue;\n                case S.ATTRIB_VALUE_QUOTED:\n                    if (c !== parser.q) {\n                        if (c === \"&\") {\n                            parser.state = S.ATTRIB_VALUE_ENTITY_Q;\n                        } else {\n                            parser.attribValue += c;\n                        }\n                        continue;\n                    }\n                    attrib(parser);\n                    parser.q = \"\";\n                    parser.state = S.ATTRIB_VALUE_CLOSED;\n                    continue;\n                case S.ATTRIB_VALUE_CLOSED:\n                    if (isWhitespace(c)) {\n                        parser.state = S.ATTRIB;\n                    } else if (c === \">\") {\n                        openTag(parser);\n                    } else if (c === \"/\") {\n                        parser.state = S.OPEN_TAG_SLASH;\n                    } else if (isMatch(nameStart, c)) {\n                        strictFail(parser, \"No whitespace between attributes\");\n                        parser.attribName = c;\n                        parser.attribValue = \"\";\n                        parser.state = S.ATTRIB_NAME;\n                    } else {\n                        strictFail(parser, \"Invalid attribute name\");\n                    }\n                    continue;\n                case S.ATTRIB_VALUE_UNQUOTED:\n                    if (!isAttribEnd(c)) {\n                        if (c === \"&\") {\n                            parser.state = S.ATTRIB_VALUE_ENTITY_U;\n                        } else {\n                            parser.attribValue += c;\n                        }\n                        continue;\n                    }\n                    attrib(parser);\n                    if (c === \">\") {\n                        openTag(parser);\n                    } else {\n                        parser.state = S.ATTRIB;\n                    }\n                    continue;\n                case S.CLOSE_TAG:\n                    if (!parser.tagName) {\n                        if (isWhitespace(c)) {\n                            continue;\n                        } else if (notMatch(nameStart, c)) {\n                            if (parser.script) {\n                                parser.script += \"</\" + c;\n                                parser.state = S.SCRIPT;\n                            } else {\n                                strictFail(parser, \"Invalid tagname in closing tag.\");\n                            }\n                        } else {\n                            parser.tagName = c;\n                        }\n                    } else if (c === \">\") {\n                        closeTag(parser);\n                    } else if (isMatch(nameBody, c)) {\n                        parser.tagName += c;\n                    } else if (parser.script) {\n                        parser.script += \"</\" + parser.tagName;\n                        parser.tagName = \"\";\n                        parser.state = S.SCRIPT;\n                    } else {\n                        if (!isWhitespace(c)) {\n                            strictFail(parser, \"Invalid tagname in closing tag\");\n                        }\n                        parser.state = S.CLOSE_TAG_SAW_WHITE;\n                    }\n                    continue;\n                case S.CLOSE_TAG_SAW_WHITE:\n                    if (isWhitespace(c)) {\n                        continue;\n                    }\n                    if (c === \">\") {\n                        closeTag(parser);\n                    } else {\n                        strictFail(parser, \"Invalid characters in closing tag\");\n                    }\n                    continue;\n                case S.TEXT_ENTITY:\n                case S.ATTRIB_VALUE_ENTITY_Q:\n                case S.ATTRIB_VALUE_ENTITY_U:\n                    var returnState;\n                    var buffer;\n                    switch(parser.state){\n                        case S.TEXT_ENTITY:\n                            returnState = S.TEXT;\n                            buffer = \"textNode\";\n                            break;\n                        case S.ATTRIB_VALUE_ENTITY_Q:\n                            returnState = S.ATTRIB_VALUE_QUOTED;\n                            buffer = \"attribValue\";\n                            break;\n                        case S.ATTRIB_VALUE_ENTITY_U:\n                            returnState = S.ATTRIB_VALUE_UNQUOTED;\n                            buffer = \"attribValue\";\n                            break;\n                    }\n                    if (c === \";\") {\n                        var parsedEntity = parseEntity(parser);\n                        if (parser.opt.unparsedEntities && !Object.values(sax.XML_ENTITIES).includes(parsedEntity)) {\n                            parser.entity = \"\";\n                            parser.state = returnState;\n                            parser.write(parsedEntity);\n                        } else {\n                            parser[buffer] += parsedEntity;\n                            parser.entity = \"\";\n                            parser.state = returnState;\n                        }\n                    } else if (isMatch(parser.entity.length ? entityBody : entityStart, c)) {\n                        parser.entity += c;\n                    } else {\n                        strictFail(parser, \"Invalid character in entity name\");\n                        parser[buffer] += \"&\" + parser.entity + c;\n                        parser.entity = \"\";\n                        parser.state = returnState;\n                    }\n                    continue;\n                default:\n                    /* istanbul ignore next */ {\n                        throw new Error(parser, \"Unknown state: \" + parser.state);\n                    }\n            }\n        } // while\n        if (parser.position >= parser.bufferCheckPosition) {\n            checkBufferLength(parser);\n        }\n        return parser;\n    }\n    /*! http://mths.be/fromcodepoint v0.1.0 by @mathias */ /* istanbul ignore next */ if (!String.fromCodePoint) {\n        (function() {\n            var stringFromCharCode = String.fromCharCode;\n            var floor = Math.floor;\n            var fromCodePoint = function() {\n                var MAX_SIZE = 0x4000;\n                var codeUnits = [];\n                var highSurrogate;\n                var lowSurrogate;\n                var index = -1;\n                var length = arguments.length;\n                if (!length) {\n                    return \"\";\n                }\n                var result = \"\";\n                while(++index < length){\n                    var codePoint = Number(arguments[index]);\n                    if (!isFinite(codePoint) || // `NaN`, `+Infinity`, or `-Infinity`\n                    codePoint < 0 || // not a valid Unicode code point\n                    codePoint > 0x10FFFF || // not a valid Unicode code point\n                    floor(codePoint) !== codePoint // not an integer\n                    ) {\n                        throw RangeError(\"Invalid code point: \" + codePoint);\n                    }\n                    if (codePoint <= 0xFFFF) {\n                        codeUnits.push(codePoint);\n                    } else {\n                        // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n                        codePoint -= 0x10000;\n                        highSurrogate = (codePoint >> 10) + 0xD800;\n                        lowSurrogate = codePoint % 0x400 + 0xDC00;\n                        codeUnits.push(highSurrogate, lowSurrogate);\n                    }\n                    if (index + 1 === length || codeUnits.length > MAX_SIZE) {\n                        result += stringFromCharCode.apply(null, codeUnits);\n                        codeUnits.length = 0;\n                    }\n                }\n                return result;\n            };\n            /* istanbul ignore next */ if (Object.defineProperty) {\n                Object.defineProperty(String, \"fromCodePoint\", {\n                    value: fromCodePoint,\n                    configurable: true,\n                    writable: true\n                });\n            } else {\n                String.fromCodePoint = fromCodePoint;\n            }\n        })();\n    }\n})( false ? 0 : exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/sax/lib/sax.js\n");

/***/ })

};
;