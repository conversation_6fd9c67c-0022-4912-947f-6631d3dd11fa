"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _jpegJs = _interopRequireDefault(require("jpeg-js"));
var _utils = require("@jimp/utils");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const MIME_TYPE = "image/jpeg";
var _default = () => ({
  mime: {
    [MIME_TYPE]: ["jpeg", "jpg", "jpe"]
  },
  constants: {
    MIME_JPEG: MIME_TYPE
  },
  decoders: {
    [MIME_TYPE]: _jpegJs.default.decode
  },
  encoders: {
    [MIME_TYPE]: image => _jpegJs.default.encode(image.bitmap, image._quality).data
  },
  class: {
    // The quality to be used when saving JPEG images
    _quality: 100,
    /**
     * Sets the quality of the image when saving as JPEG format (default is 100)
     * @param {number} n The quality to use 0-100
     * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete
     * @returns {Jimp} this for chaining of methods
     */
    quality(n, cb) {
      if (typeof n !== "number") {
        return _utils.throwError.call(this, "n must be a number", cb);
      }
      if (n < 0 || n > 100) {
        return _utils.throwError.call(this, "n must be a number 0 - 100", cb);
      }
      this._quality = Math.round(n);
      if ((0, _utils.isNodePattern)(cb)) {
        cb.call(this, null, this);
      }
      return this;
    }
  }
});
exports.default = _default;
module.exports = exports.default;
module.exports.default = exports.default;
//# sourceMappingURL=index.js.map