{"name": "jimp", "version": "0.22.12", "description": "An image processing library written entirely in JavaScript (i.e. zero external or native dependencies)", "main": "dist/index.js", "module": "es/index.js", "types": "types/index.d.ts", "browser": "browser/lib/jimp.js", "tonicExampleFilename": "example.js", "files": ["browser", "dist", "es", "index.d.ts", "fonts", "types"], "repository": "jimp-dev/jimp", "scripts": {"test": "cross-env BABEL_ENV=test mocha --require @babel/register --recursive test --extension js", "test:watch": "npm run test -- --reporter min --watch", "test:coverage": "nyc npm run test", "build": "npm run build:browser && npm run build:node:production && npm run build:module", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "build:debug": "npm run build:browser:debug && npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node", "build:browser": "cross-env NODE_ENV=production webpack", "build:browser:debug": "cross-env NODE_ENV=development ENV=browser webpack"}, "keywords": ["image", "image processing", "image manipulation", "png", "jpg", "jpeg", "bmp", "resize", "scale", "crop"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@jimp/custom": "^0.22.12", "@jimp/plugins": "^0.22.12", "@jimp/types": "^0.22.12", "regenerator-runtime": "^0.13.3"}, "devDependencies": {"@jimp/test-utils": "^0.22.12", "empty-module": "^0.0.2", "express": "^4.17.1", "path-browserify": "^1.0.1", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "nyc": {"sourceMap": false, "instrument": false, "reporter": ["text", "text-summary", "lcov", "html"], "exclude": ["src/modules/*.js", "test/*.js"]}, "gitHead": "a4a8d6364bbf97629749e196f3b0a4c94c9a7abc"}