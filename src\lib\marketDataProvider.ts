import { CandlestickData } from '@/types/trading'

// Real-time market data provider for XAUUSD
export class MarketDataProvider {
  private static instance: MarketDataProvider
  private currentPrice: number = 2000
  private priceHistory: CandlestickData[] = []
  private subscribers: ((data: CandlestickData) => void)[] = []
  private intervalId: NodeJS.Timeout | null = null

  private constructor() {
    this.initializeMarketData()
  }

  static getInstance(): MarketDataProvider {
    if (!MarketDataProvider.instance) {
      MarketDataProvider.instance = new MarketDataProvider()
    }
    return MarketDataProvider.instance
  }

  private initializeMarketData() {
    // Initialize with realistic XAUUSD data
    this.currentPrice = 2000 + Math.random() * 100
    this.generateInitialHistory()
    this.startRealTimeUpdates()
  }

  private generateInitialHistory() {
    const now = Date.now()
    const candleCount = 100
    
    for (let i = 0; i < candleCount; i++) {
      const timestamp = now - (candleCount - i) * 60000 // 1-minute candles
      const candle = this.generateRealisticCandle(timestamp, i === 0 ? this.currentPrice : this.priceHistory[i - 1].close)
      this.priceHistory.push(candle)
    }
  }

  private generateRealisticCandle(timestamp: number, previousClose: number): CandlestickData {
    // Simulate realistic XAUUSD price movement
    const volatility = 0.5 + Math.random() * 1.5 // 0.5 to 2.0 volatility
    const trend = this.calculateMarketTrend()
    
    // Market hours affect volatility
    const hour = new Date(timestamp).getUTCHours()
    const isActiveHours = (hour >= 8 && hour <= 11) || (hour >= 13 && hour <= 16) // London/NY sessions
    const volatilityMultiplier = isActiveHours ? 1.5 : 0.7
    
    const adjustedVolatility = volatility * volatilityMultiplier
    
    // Generate price movement with trend bias
    const direction = Math.random() < (0.5 + trend * 0.1) ? 1 : -1
    const bodySize = Math.random() * adjustedVolatility
    
    const open = previousClose
    const close = open + (direction * bodySize)
    
    // Generate wicks
    const upperWick = Math.random() * adjustedVolatility * 0.5
    const lowerWick = Math.random() * adjustedVolatility * 0.5
    
    const high = Math.max(open, close) + upperWick
    const low = Math.min(open, close) - lowerWick
    
    // Volume based on volatility and time
    const baseVolume = 100
    const volume = Math.floor(baseVolume + Math.random() * 500 * volatilityMultiplier)

    return {
      timestamp,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
      volume
    }
  }

  private calculateMarketTrend(): number {
    // Analyze recent price history to determine trend
    if (this.priceHistory.length < 10) return 0
    
    const recent = this.priceHistory.slice(-10)
    const firstPrice = recent[0].close
    const lastPrice = recent[recent.length - 1].close
    
    const change = (lastPrice - firstPrice) / firstPrice
    return Math.max(-1, Math.min(1, change * 10)) // Normalize to -1 to 1
  }

  private startRealTimeUpdates() {
    // Update every minute with new candle data
    this.intervalId = setInterval(() => {
      const newCandle = this.generateRealisticCandle(
        Date.now(),
        this.priceHistory[this.priceHistory.length - 1].close
      )
      
      this.priceHistory.push(newCandle)
      this.currentPrice = newCandle.close
      
      // Keep only last 200 candles
      if (this.priceHistory.length > 200) {
        this.priceHistory.shift()
      }
      
      // Notify subscribers
      this.notifySubscribers(newCandle)
    }, 60000) // 1 minute
  }

  private notifySubscribers(candle: CandlestickData) {
    this.subscribers.forEach(callback => {
      try {
        callback(candle)
      } catch (error) {
        console.error('Error notifying subscriber:', error)
      }
    })
  }

  // Public methods
  getCurrentPrice(): number {
    return this.currentPrice
  }

  getPriceHistory(count: number = 100): CandlestickData[] {
    return this.priceHistory.slice(-count)
  }

  getLatestCandle(): CandlestickData | null {
    return this.priceHistory.length > 0 ? this.priceHistory[this.priceHistory.length - 1] : null
  }

  subscribe(callback: (data: CandlestickData) => void): () => void {
    this.subscribers.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  // Market analysis helpers
  getMarketSentiment(): 'bullish' | 'bearish' | 'neutral' {
    const trend = this.calculateMarketTrend()
    if (trend > 0.3) return 'bullish'
    if (trend < -0.3) return 'bearish'
    return 'neutral'
  }

  getVolatility(): 'high' | 'medium' | 'low' {
    if (this.priceHistory.length < 20) return 'medium'
    
    const recent = this.priceHistory.slice(-20)
    const ranges = recent.map(candle => candle.high - candle.low)
    const avgRange = ranges.reduce((sum, range) => sum + range, 0) / ranges.length
    
    // Classify based on average range
    if (avgRange > 3) return 'high'
    if (avgRange > 1.5) return 'medium'
    return 'low'
  }

  getSupportResistanceLevels(): { support: number[]; resistance: number[] } {
    if (this.priceHistory.length < 50) return { support: [], resistance: [] }
    
    const recent = this.priceHistory.slice(-50)
    const highs = recent.map(c => c.high)
    const lows = recent.map(c => c.low)
    
    // Find significant levels
    const support = this.findSignificantLevels(lows, 'support')
    const resistance = this.findSignificantLevels(highs, 'resistance')
    
    return { support, resistance }
  }

  private findSignificantLevels(prices: number[], type: 'support' | 'resistance'): number[] {
    const levels: number[] = []
    const tolerance = 0.5 // $0.50 tolerance
    
    // Group similar price levels
    const groupedLevels: { [key: string]: number[] } = {}
    
    prices.forEach(price => {
      const key = Math.round(price / tolerance) * tolerance
      if (!groupedLevels[key]) groupedLevels[key] = []
      groupedLevels[key].push(price)
    })
    
    // Find levels that were tested multiple times
    Object.entries(groupedLevels).forEach(([key, group]) => {
      if (group.length >= 3) { // Level tested at least 3 times
        levels.push(Number(key))
      }
    })
    
    return levels.sort((a, b) => type === 'support' ? b - a : a - b).slice(0, 5)
  }

  // Economic calendar simulation
  getUpcomingEvents(): Array<{ time: string; event: string; impact: 'high' | 'medium' | 'low' }> {
    const events = [
      { event: 'US Non-Farm Payrolls', impact: 'high' as const },
      { event: 'Federal Reserve Interest Rate Decision', impact: 'high' as const },
      { event: 'US CPI Data', impact: 'high' as const },
      { event: 'Gold ETF Holdings', impact: 'medium' as const },
      { event: 'US Dollar Index', impact: 'medium' as const },
      { event: 'Mining Production Data', impact: 'low' as const }
    ]
    
    return events.map(event => ({
      ...event,
      time: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    })).sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime()).slice(0, 3)
  }

  // Market session information
  getCurrentSession(): { name: string; active: boolean; openTime: string; closeTime: string } {
    const now = new Date()
    const utcHour = now.getUTCHours()
    
    const sessions = [
      { name: 'Asian', active: utcHour >= 23 || utcHour <= 8, openTime: '23:00', closeTime: '08:00' },
      { name: 'London', active: utcHour >= 8 && utcHour <= 17, openTime: '08:00', closeTime: '17:00' },
      { name: 'New York', active: utcHour >= 13 && utcHour <= 22, openTime: '13:00', closeTime: '22:00' }
    ]
    
    return sessions.find(session => session.active) || sessions[0]
  }

  destroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    this.subscribers = []
  }
}
