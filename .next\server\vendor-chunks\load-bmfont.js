"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/load-bmfont";
exports.ids = ["vendor-chunks/load-bmfont"];
exports.modules = {

/***/ "(rsc)/./node_modules/load-bmfont/index.js":
/*!*******************************************!*\
  !*** ./node_modules/load-bmfont/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar url = __webpack_require__(/*! url */ \"url\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar request = __webpack_require__(/*! phin */ \"(rsc)/./node_modules/phin/lib/phin.js\");\nvar parseASCII = __webpack_require__(/*! parse-bmfont-ascii */ \"(rsc)/./node_modules/parse-bmfont-ascii/index.js\");\nvar parseXML = __webpack_require__(/*! parse-bmfont-xml */ \"(rsc)/./node_modules/parse-bmfont-xml/lib/index.js\");\nvar readBinary = __webpack_require__(/*! parse-bmfont-binary */ \"(rsc)/./node_modules/parse-bmfont-binary/index.js\");\nvar mime = __webpack_require__(/*! mime */ \"(rsc)/./node_modules/mime/mime.js\");\nvar noop = function() {};\nvar isBinary = __webpack_require__(/*! ./lib/is-binary */ \"(rsc)/./node_modules/load-bmfont/lib/is-binary.js\");\nfunction parseFont(file, data, cb) {\n    var result, binary;\n    if (isBinary(data)) {\n        if (typeof data === \"string\") data = Buffer.from(data, \"binary\");\n        binary = true;\n    } else data = data.toString().trim();\n    try {\n        if (binary) result = readBinary(data);\n        else if (/json/.test(mime.lookup(file)) || data.charAt(0) === \"{\") result = JSON.parse(data);\n        else if (/xml/.test(mime.lookup(file)) || data.charAt(0) === \"<\") result = parseXML(data);\n        else result = parseASCII(data);\n    } catch (e) {\n        cb(e);\n        cb = noop;\n    }\n    cb(null, result);\n}\nmodule.exports = function loadFont(opt, cb) {\n    cb = typeof cb === \"function\" ? cb : noop;\n    if (typeof opt === \"string\") opt = {\n        uri: opt,\n        url: opt\n    };\n    else if (!opt) opt = {};\n    var file = opt.uri || opt.url;\n    function handleData(err, data) {\n        if (err) return cb(err);\n        parseFont(file, data.body || data, cb);\n    }\n    if (url.parse(file).host) {\n        request(opt).then(function(res) {\n            handleData(null, res);\n        }).catch(function(err) {\n            handleData(err);\n        });\n    } else {\n        fs.readFile(file, opt, handleData);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/load-bmfont/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/load-bmfont/lib/is-binary.js":
/*!***************************************************!*\
  !*** ./node_modules/load-bmfont/lib/is-binary.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar equal = __webpack_require__(/*! buffer-equal */ \"(rsc)/./node_modules/buffer-equal/index.js\");\nvar HEADER = Buffer.from([\n    66,\n    77,\n    70,\n    3\n]);\nmodule.exports = function(buf) {\n    if (typeof buf === \"string\") return buf.substring(0, 3) === \"BMF\";\n    return buf.length > 4 && equal(buf.slice(0, 4), HEADER);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbG9hZC1ibWZvbnQvbGliL2lzLWJpbmFyeS5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsUUFBUUMsbUJBQU9BLENBQUM7QUFDcEIsSUFBSUMsU0FBU0MsT0FBT0MsSUFBSSxDQUFDO0lBQUM7SUFBSTtJQUFJO0lBQUk7Q0FBRTtBQUV4Q0MsT0FBT0MsT0FBTyxHQUFHLFNBQVNDLEdBQUc7SUFDM0IsSUFBSSxPQUFPQSxRQUFRLFVBQ2pCLE9BQU9BLElBQUlDLFNBQVMsQ0FBQyxHQUFHLE9BQU87SUFDakMsT0FBT0QsSUFBSUUsTUFBTSxHQUFHLEtBQUtULE1BQU1PLElBQUlHLEtBQUssQ0FBQyxHQUFHLElBQUlSO0FBQ2xEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8veGF1dXNkLXRyYWRpbmctYW5hbHl6ZXIvLi9ub2RlX21vZHVsZXMvbG9hZC1ibWZvbnQvbGliL2lzLWJpbmFyeS5qcz80ZDI0Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBlcXVhbCA9IHJlcXVpcmUoJ2J1ZmZlci1lcXVhbCcpXG52YXIgSEVBREVSID0gQnVmZmVyLmZyb20oWzY2LCA3NywgNzAsIDNdKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uKGJ1Zikge1xuICBpZiAodHlwZW9mIGJ1ZiA9PT0gJ3N0cmluZycpXG4gICAgcmV0dXJuIGJ1Zi5zdWJzdHJpbmcoMCwgMykgPT09ICdCTUYnXG4gIHJldHVybiBidWYubGVuZ3RoID4gNCAmJiBlcXVhbChidWYuc2xpY2UoMCwgNCksIEhFQURFUilcbn0iXSwibmFtZXMiOlsiZXF1YWwiLCJyZXF1aXJlIiwiSEVBREVSIiwiQnVmZmVyIiwiZnJvbSIsIm1vZHVsZSIsImV4cG9ydHMiLCJidWYiLCJzdWJzdHJpbmciLCJsZW5ndGgiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/load-bmfont/lib/is-binary.js\n");

/***/ })

};
;