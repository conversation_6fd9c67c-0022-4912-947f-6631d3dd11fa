'use client'

import { useState } from 'react'
import { Upload, TrendingUp, TrendingDown, Clock, Target, Shield, DollarSign } from 'lucide-react'
import ImageUpload from '@/components/ImageUpload'
import AnalysisResults from '@/components/AnalysisResults'
import TradingStrategySelector from '@/components/TradingStrategySelector'
import StatsDashboard from '@/components/StatsDashboard'

export default function Home() {
  const [selectedStrategy, setSelectedStrategy] = useState<'scalping' | 'swing'>('scalping')
  const [analysisResults, setAnalysisResults] = useState(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  const handleImageUpload = async (file: File) => {
    setIsAnalyzing(true)
    try {
      const formData = new FormData()
      formData.append('image', file)
      formData.append('strategy', selectedStrategy)

      const response = await fetch('/api/analyze', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Analysis failed')
      }

      const results = await response.json()
      setAnalysisResults(results)
    } catch (error) {
      console.error('Analysis error:', error)
      alert('Analysis failed. Please try again.')
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
          Professional <span className="text-gold-400">XAUUSD</span> Analysis
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          Upload your trading chart screenshots and receive detailed analysis using 
          Smart Money Concepts and Inner Circle Trader methodologies
        </p>
        
        {/* Key Features */}
        <div className="grid md:grid-cols-4 gap-6 mt-12">
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20">
            <TrendingUp className="w-8 h-8 text-gold-400 mx-auto mb-3" />
            <h3 className="font-semibold text-white mb-2">Smart Money Concepts</h3>
            <p className="text-sm text-gray-400">Market structure, order blocks, and liquidity analysis</p>
          </div>
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20">
            <Clock className="w-8 h-8 text-gold-400 mx-auto mb-3" />
            <h3 className="font-semibold text-white mb-2">ICT Methodology</h3>
            <p className="text-sm text-gray-400">Institutional order flow and time-based analysis</p>
          </div>
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20">
            <Target className="w-8 h-8 text-gold-400 mx-auto mb-3" />
            <h3 className="font-semibold text-white mb-2">Precise Entries</h3>
            <p className="text-sm text-gray-400">Exact entry points with detailed reasoning</p>
          </div>
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20">
            <Shield className="w-8 h-8 text-gold-400 mx-auto mb-3" />
            <h3 className="font-semibold text-white mb-2">Risk Management</h3>
            <p className="text-sm text-gray-400">Stop loss and take profit calculations</p>
          </div>
        </div>
      </div>

      {/* Trading Strategy Selection */}
      <TradingStrategySelector 
        selectedStrategy={selectedStrategy}
        onStrategyChange={setSelectedStrategy}
      />

      {/* Image Upload Section */}
      <div className="bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">
          Upload Your Chart Screenshot
        </h2>
        <ImageUpload 
          onImageUpload={handleImageUpload}
          isAnalyzing={isAnalyzing}
        />
      </div>

      {/* Analysis Results */}
      {analysisResults && (
        <AnalysisResults results={analysisResults} />
      )}

      {/* Statistics Dashboard */}
      <StatsDashboard />

      {/* Information Section */}
      <div className="bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20">
        <h2 className="text-2xl font-bold text-white mb-6">How It Works</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="space-y-3">
            <div className="w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl">
              1
            </div>
            <h3 className="text-lg font-semibold text-white">Upload Chart</h3>
            <p className="text-gray-400">
              Upload a screenshot of your XAUUSD chart from any platform (MT4, MT5, TradingView)
            </p>
          </div>
          <div className="space-y-3">
            <div className="w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl">
              2
            </div>
            <h3 className="text-lg font-semibold text-white">AI Analysis</h3>
            <p className="text-gray-400">
              Our advanced algorithms analyze market structure, patterns, and institutional behavior
            </p>
          </div>
          <div className="space-y-3">
            <div className="w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl">
              3
            </div>
            <h3 className="text-lg font-semibold text-white">Get Recommendations</h3>
            <p className="text-gray-400">
              Receive detailed trade setups with entry, stop loss, and take profit levels
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
