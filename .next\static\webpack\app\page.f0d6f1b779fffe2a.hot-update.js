"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AnalysisResults.tsx":
/*!********************************************!*\
  !*** ./src/components/AnalysisResults.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnalysisResults; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,DollarSign,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AnalysisResults(param) {\n    let { results } = param;\n    const getDirectionColor = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return \"text-green-500\";\n            case \"SELL\":\n                return \"text-red-500\";\n            default:\n                return \"text-yellow-500\";\n        }\n    };\n    const getDirectionIcon = (direction)=>{\n        switch(direction){\n            case \"BUY\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 26\n                }, this);\n            case \"SELL\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatPrice = (price)=>{\n        return price.toFixed(2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-white\",\n                        children: \"Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 \".concat(getDirectionColor(results.direction)),\n                                children: [\n                                    getDirectionIcon(results.direction),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: results.direction\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            results.confidence,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 ml-1\",\n                                        children: \"confidence\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Trade Setup\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Entry Price:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.entry)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Stop Loss:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-mono text-lg\",\n                                                children: [\n                                                    \"$\",\n                                                    formatPrice(results.stopLoss)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP1:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP2:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"TP3:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-mono\",\n                                                            children: [\n                                                                \"$\",\n                                                                formatPrice(results.takeProfits.tp3)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2 text-gold-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Risk/Reward Ratios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP1 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp1\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP2 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp2\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"TP3 R:R:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 font-semibold\",\n                                                children: [\n                                                    \"1:\",\n                                                    results.riskReward.tp3\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Strategy:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white capitalize\",\n                                                        children: results.strategy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Timeframe:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: results.timeframe\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold text-white\",\n                        children: \"Detailed Analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Market Structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.marketStructure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Risk Assessment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300\",\n                                        children: results.reasoning.riskAssessment\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    results.reasoning.orderBlocks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Order Blocks Identified\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.orderBlocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this),\n                                            block\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.fairValueGaps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"Fair Value Gaps\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.fairValueGaps.map((gap, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this),\n                                            gap\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    results.reasoning.ictConcepts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 rounded-lg p-6 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"ICT Concepts Applied\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: results.reasoning.ictConcepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-300 flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-400 mr-2\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this),\n                                            concept\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500 text-sm flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_DollarSign_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    \"Analysis completed: \",\n                    new Date(results.timestamp).toLocaleString()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\components\\\\AnalysisResults.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_c = AnalysisResults;\nvar _c;\n$RefreshReg$(_c, \"AnalysisResults\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AnalysisResults.tsx\n"));

/***/ })

});