"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jpeg-js";
exports.ids = ["vendor-chunks/jpeg-js"];
exports.modules = {

/***/ "(rsc)/./node_modules/jpeg-js/index.js":
/*!***************************************!*\
  !*** ./node_modules/jpeg-js/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar encode = __webpack_require__(/*! ./lib/encoder */ \"(rsc)/./node_modules/jpeg-js/lib/encoder.js\"), decode = __webpack_require__(/*! ./lib/decoder */ \"(rsc)/./node_modules/jpeg-js/lib/decoder.js\");\nmodule.exports = {\n    encode: encode,\n    decode: decode\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanBlZy1qcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsU0FBU0MsbUJBQU9BLENBQUMscUVBQ2pCQyxTQUFTRCxtQkFBT0EsQ0FBQztBQUVyQkUsT0FBT0MsT0FBTyxHQUFHO0lBQ2ZKLFFBQVFBO0lBQ1JFLFFBQVFBO0FBQ1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly94YXV1c2QtdHJhZGluZy1hbmFseXplci8uL25vZGVfbW9kdWxlcy9qcGVnLWpzL2luZGV4LmpzPzExNzMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGVuY29kZSA9IHJlcXVpcmUoJy4vbGliL2VuY29kZXInKSxcbiAgICBkZWNvZGUgPSByZXF1aXJlKCcuL2xpYi9kZWNvZGVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBlbmNvZGU6IGVuY29kZSxcbiAgZGVjb2RlOiBkZWNvZGVcbn07XG4iXSwibmFtZXMiOlsiZW5jb2RlIiwicmVxdWlyZSIsImRlY29kZSIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jpeg-js/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jpeg-js/lib/decoder.js":
/*!*********************************************!*\
  !*** ./node_modules/jpeg-js/lib/decoder.js ***!
  \*********************************************/
/***/ ((module) => {

eval("/* -*- tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- /\n/* vim: set shiftwidth=2 tabstop=2 autoindent cindent expandtab: */ /*\n   Copyright 2011 notmasteryet\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/ // - The JPEG specification can be found in the ITU CCITT Recommendation T.81\n//   (www.w3.org/Graphics/JPEG/itu-t81.pdf)\n// - The JFIF specification can be found in the JPEG File Interchange Format\n//   (www.w3.org/Graphics/JPEG/jfif3.pdf)\n// - The Adobe Application-Specific JPEG markers in the Supporting the DCT Filters\n//   in PostScript Level 2, Technical Note #5116\n//   (partners.adobe.com/public/developer/en/ps/sdk/5116.DCT_Filter.pdf)\n\nvar JpegImage = function jpegImage() {\n    \"use strict\";\n    var dctZigZag = new Int32Array([\n        0,\n        1,\n        8,\n        16,\n        9,\n        2,\n        3,\n        10,\n        17,\n        24,\n        32,\n        25,\n        18,\n        11,\n        4,\n        5,\n        12,\n        19,\n        26,\n        33,\n        40,\n        48,\n        41,\n        34,\n        27,\n        20,\n        13,\n        6,\n        7,\n        14,\n        21,\n        28,\n        35,\n        42,\n        49,\n        56,\n        57,\n        50,\n        43,\n        36,\n        29,\n        22,\n        15,\n        23,\n        30,\n        37,\n        44,\n        51,\n        58,\n        59,\n        52,\n        45,\n        38,\n        31,\n        39,\n        46,\n        53,\n        60,\n        61,\n        54,\n        47,\n        55,\n        62,\n        63\n    ]);\n    var dctCos1 = 4017 // cos(pi/16)\n    ;\n    var dctSin1 = 799 // sin(pi/16)\n    ;\n    var dctCos3 = 3406 // cos(3*pi/16)\n    ;\n    var dctSin3 = 2276 // sin(3*pi/16)\n    ;\n    var dctCos6 = 1567 // cos(6*pi/16)\n    ;\n    var dctSin6 = 3784 // sin(6*pi/16)\n    ;\n    var dctSqrt2 = 5793 // sqrt(2)\n    ;\n    var dctSqrt1d2 = 2896 // sqrt(2) / 2\n    ;\n    function constructor() {}\n    function buildHuffmanTable(codeLengths, values) {\n        var k = 0, code = [], i, j, length = 16;\n        while(length > 0 && !codeLengths[length - 1])length--;\n        code.push({\n            children: [],\n            index: 0\n        });\n        var p = code[0], q;\n        for(i = 0; i < length; i++){\n            for(j = 0; j < codeLengths[i]; j++){\n                p = code.pop();\n                p.children[p.index] = values[k];\n                while(p.index > 0){\n                    if (code.length === 0) throw new Error(\"Could not recreate Huffman Table\");\n                    p = code.pop();\n                }\n                p.index++;\n                code.push(p);\n                while(code.length <= i){\n                    code.push(q = {\n                        children: [],\n                        index: 0\n                    });\n                    p.children[p.index] = q.children;\n                    p = q;\n                }\n                k++;\n            }\n            if (i + 1 < length) {\n                // p here points to last code\n                code.push(q = {\n                    children: [],\n                    index: 0\n                });\n                p.children[p.index] = q.children;\n                p = q;\n            }\n        }\n        return code[0].children;\n    }\n    function decodeScan(data, offset, frame, components, resetInterval, spectralStart, spectralEnd, successivePrev, successive, opts) {\n        var precision = frame.precision;\n        var samplesPerLine = frame.samplesPerLine;\n        var scanLines = frame.scanLines;\n        var mcusPerLine = frame.mcusPerLine;\n        var progressive = frame.progressive;\n        var maxH = frame.maxH, maxV = frame.maxV;\n        var startOffset = offset, bitsData = 0, bitsCount = 0;\n        function readBit() {\n            if (bitsCount > 0) {\n                bitsCount--;\n                return bitsData >> bitsCount & 1;\n            }\n            bitsData = data[offset++];\n            if (bitsData == 0xFF) {\n                var nextByte = data[offset++];\n                if (nextByte) {\n                    throw new Error(\"unexpected marker: \" + (bitsData << 8 | nextByte).toString(16));\n                }\n            // unstuff 0\n            }\n            bitsCount = 7;\n            return bitsData >>> 7;\n        }\n        function decodeHuffman(tree) {\n            var node = tree, bit;\n            while((bit = readBit()) !== null){\n                node = node[bit];\n                if (typeof node === \"number\") return node;\n                if (typeof node !== \"object\") throw new Error(\"invalid huffman sequence\");\n            }\n            return null;\n        }\n        function receive(length) {\n            var n = 0;\n            while(length > 0){\n                var bit = readBit();\n                if (bit === null) return;\n                n = n << 1 | bit;\n                length--;\n            }\n            return n;\n        }\n        function receiveAndExtend(length) {\n            var n = receive(length);\n            if (n >= 1 << length - 1) return n;\n            return n + (-1 << length) + 1;\n        }\n        function decodeBaseline(component, zz) {\n            var t = decodeHuffman(component.huffmanTableDC);\n            var diff = t === 0 ? 0 : receiveAndExtend(t);\n            zz[0] = component.pred += diff;\n            var k = 1;\n            while(k < 64){\n                var rs = decodeHuffman(component.huffmanTableAC);\n                var s = rs & 15, r = rs >> 4;\n                if (s === 0) {\n                    if (r < 15) break;\n                    k += 16;\n                    continue;\n                }\n                k += r;\n                var z = dctZigZag[k];\n                zz[z] = receiveAndExtend(s);\n                k++;\n            }\n        }\n        function decodeDCFirst(component, zz) {\n            var t = decodeHuffman(component.huffmanTableDC);\n            var diff = t === 0 ? 0 : receiveAndExtend(t) << successive;\n            zz[0] = component.pred += diff;\n        }\n        function decodeDCSuccessive(component, zz) {\n            zz[0] |= readBit() << successive;\n        }\n        var eobrun = 0;\n        function decodeACFirst(component, zz) {\n            if (eobrun > 0) {\n                eobrun--;\n                return;\n            }\n            var k = spectralStart, e = spectralEnd;\n            while(k <= e){\n                var rs = decodeHuffman(component.huffmanTableAC);\n                var s = rs & 15, r = rs >> 4;\n                if (s === 0) {\n                    if (r < 15) {\n                        eobrun = receive(r) + (1 << r) - 1;\n                        break;\n                    }\n                    k += 16;\n                    continue;\n                }\n                k += r;\n                var z = dctZigZag[k];\n                zz[z] = receiveAndExtend(s) * (1 << successive);\n                k++;\n            }\n        }\n        var successiveACState = 0, successiveACNextValue;\n        function decodeACSuccessive(component, zz) {\n            var k = spectralStart, e = spectralEnd, r = 0;\n            while(k <= e){\n                var z = dctZigZag[k];\n                var direction = zz[z] < 0 ? -1 : 1;\n                switch(successiveACState){\n                    case 0:\n                        var rs = decodeHuffman(component.huffmanTableAC);\n                        var s = rs & 15, r = rs >> 4;\n                        if (s === 0) {\n                            if (r < 15) {\n                                eobrun = receive(r) + (1 << r);\n                                successiveACState = 4;\n                            } else {\n                                r = 16;\n                                successiveACState = 1;\n                            }\n                        } else {\n                            if (s !== 1) throw new Error(\"invalid ACn encoding\");\n                            successiveACNextValue = receiveAndExtend(s);\n                            successiveACState = r ? 2 : 3;\n                        }\n                        continue;\n                    case 1:\n                    case 2:\n                        if (zz[z]) zz[z] += (readBit() << successive) * direction;\n                        else {\n                            r--;\n                            if (r === 0) successiveACState = successiveACState == 2 ? 3 : 0;\n                        }\n                        break;\n                    case 3:\n                        if (zz[z]) zz[z] += (readBit() << successive) * direction;\n                        else {\n                            zz[z] = successiveACNextValue << successive;\n                            successiveACState = 0;\n                        }\n                        break;\n                    case 4:\n                        if (zz[z]) zz[z] += (readBit() << successive) * direction;\n                        break;\n                }\n                k++;\n            }\n            if (successiveACState === 4) {\n                eobrun--;\n                if (eobrun === 0) successiveACState = 0;\n            }\n        }\n        function decodeMcu(component, decode, mcu, row, col) {\n            var mcuRow = mcu / mcusPerLine | 0;\n            var mcuCol = mcu % mcusPerLine;\n            var blockRow = mcuRow * component.v + row;\n            var blockCol = mcuCol * component.h + col;\n            // If the block is missing and we're in tolerant mode, just skip it.\n            if (component.blocks[blockRow] === undefined && opts.tolerantDecoding) return;\n            decode(component, component.blocks[blockRow][blockCol]);\n        }\n        function decodeBlock(component, decode, mcu) {\n            var blockRow = mcu / component.blocksPerLine | 0;\n            var blockCol = mcu % component.blocksPerLine;\n            // If the block is missing and we're in tolerant mode, just skip it.\n            if (component.blocks[blockRow] === undefined && opts.tolerantDecoding) return;\n            decode(component, component.blocks[blockRow][blockCol]);\n        }\n        var componentsLength = components.length;\n        var component, i, j, k, n;\n        var decodeFn;\n        if (progressive) {\n            if (spectralStart === 0) decodeFn = successivePrev === 0 ? decodeDCFirst : decodeDCSuccessive;\n            else decodeFn = successivePrev === 0 ? decodeACFirst : decodeACSuccessive;\n        } else {\n            decodeFn = decodeBaseline;\n        }\n        var mcu = 0, marker;\n        var mcuExpected;\n        if (componentsLength == 1) {\n            mcuExpected = components[0].blocksPerLine * components[0].blocksPerColumn;\n        } else {\n            mcuExpected = mcusPerLine * frame.mcusPerColumn;\n        }\n        if (!resetInterval) resetInterval = mcuExpected;\n        var h, v;\n        while(mcu < mcuExpected){\n            // reset interval stuff\n            for(i = 0; i < componentsLength; i++)components[i].pred = 0;\n            eobrun = 0;\n            if (componentsLength == 1) {\n                component = components[0];\n                for(n = 0; n < resetInterval; n++){\n                    decodeBlock(component, decodeFn, mcu);\n                    mcu++;\n                }\n            } else {\n                for(n = 0; n < resetInterval; n++){\n                    for(i = 0; i < componentsLength; i++){\n                        component = components[i];\n                        h = component.h;\n                        v = component.v;\n                        for(j = 0; j < v; j++){\n                            for(k = 0; k < h; k++){\n                                decodeMcu(component, decodeFn, mcu, j, k);\n                            }\n                        }\n                    }\n                    mcu++;\n                    // If we've reached our expected MCU's, stop decoding\n                    if (mcu === mcuExpected) break;\n                }\n            }\n            if (mcu === mcuExpected) {\n                // Skip trailing bytes at the end of the scan - until we reach the next marker\n                do {\n                    if (data[offset] === 0xFF) {\n                        if (data[offset + 1] !== 0x00) {\n                            break;\n                        }\n                    }\n                    offset += 1;\n                }while (offset < data.length - 2);\n            }\n            // find marker\n            bitsCount = 0;\n            marker = data[offset] << 8 | data[offset + 1];\n            if (marker < 0xFF00) {\n                throw new Error(\"marker was not found\");\n            }\n            if (marker >= 0xFFD0 && marker <= 0xFFD7) {\n                offset += 2;\n            } else break;\n        }\n        return offset - startOffset;\n    }\n    function buildComponentData(frame, component) {\n        var lines = [];\n        var blocksPerLine = component.blocksPerLine;\n        var blocksPerColumn = component.blocksPerColumn;\n        var samplesPerLine = blocksPerLine << 3;\n        // Only 1 used per invocation of this function and garbage collected after invocation, so no need to account for its memory footprint.\n        var R = new Int32Array(64), r = new Uint8Array(64);\n        // A port of poppler's IDCT method which in turn is taken from:\n        //   Christoph Loeffler, Adriaan Ligtenberg, George S. Moschytz,\n        //   \"Practical Fast 1-D DCT Algorithms with 11 Multiplications\",\n        //   IEEE Intl. Conf. on Acoustics, Speech & Signal Processing, 1989,\n        //   988-991.\n        function quantizeAndInverse(zz, dataOut, dataIn) {\n            var qt = component.quantizationTable;\n            var v0, v1, v2, v3, v4, v5, v6, v7, t;\n            var p = dataIn;\n            var i;\n            // dequant\n            for(i = 0; i < 64; i++)p[i] = zz[i] * qt[i];\n            // inverse DCT on rows\n            for(i = 0; i < 8; ++i){\n                var row = 8 * i;\n                // check for all-zero AC coefficients\n                if (p[1 + row] == 0 && p[2 + row] == 0 && p[3 + row] == 0 && p[4 + row] == 0 && p[5 + row] == 0 && p[6 + row] == 0 && p[7 + row] == 0) {\n                    t = dctSqrt2 * p[0 + row] + 512 >> 10;\n                    p[0 + row] = t;\n                    p[1 + row] = t;\n                    p[2 + row] = t;\n                    p[3 + row] = t;\n                    p[4 + row] = t;\n                    p[5 + row] = t;\n                    p[6 + row] = t;\n                    p[7 + row] = t;\n                    continue;\n                }\n                // stage 4\n                v0 = dctSqrt2 * p[0 + row] + 128 >> 8;\n                v1 = dctSqrt2 * p[4 + row] + 128 >> 8;\n                v2 = p[2 + row];\n                v3 = p[6 + row];\n                v4 = dctSqrt1d2 * (p[1 + row] - p[7 + row]) + 128 >> 8;\n                v7 = dctSqrt1d2 * (p[1 + row] + p[7 + row]) + 128 >> 8;\n                v5 = p[3 + row] << 4;\n                v6 = p[5 + row] << 4;\n                // stage 3\n                t = v0 - v1 + 1 >> 1;\n                v0 = v0 + v1 + 1 >> 1;\n                v1 = t;\n                t = v2 * dctSin6 + v3 * dctCos6 + 128 >> 8;\n                v2 = v2 * dctCos6 - v3 * dctSin6 + 128 >> 8;\n                v3 = t;\n                t = v4 - v6 + 1 >> 1;\n                v4 = v4 + v6 + 1 >> 1;\n                v6 = t;\n                t = v7 + v5 + 1 >> 1;\n                v5 = v7 - v5 + 1 >> 1;\n                v7 = t;\n                // stage 2\n                t = v0 - v3 + 1 >> 1;\n                v0 = v0 + v3 + 1 >> 1;\n                v3 = t;\n                t = v1 - v2 + 1 >> 1;\n                v1 = v1 + v2 + 1 >> 1;\n                v2 = t;\n                t = v4 * dctSin3 + v7 * dctCos3 + 2048 >> 12;\n                v4 = v4 * dctCos3 - v7 * dctSin3 + 2048 >> 12;\n                v7 = t;\n                t = v5 * dctSin1 + v6 * dctCos1 + 2048 >> 12;\n                v5 = v5 * dctCos1 - v6 * dctSin1 + 2048 >> 12;\n                v6 = t;\n                // stage 1\n                p[0 + row] = v0 + v7;\n                p[7 + row] = v0 - v7;\n                p[1 + row] = v1 + v6;\n                p[6 + row] = v1 - v6;\n                p[2 + row] = v2 + v5;\n                p[5 + row] = v2 - v5;\n                p[3 + row] = v3 + v4;\n                p[4 + row] = v3 - v4;\n            }\n            // inverse DCT on columns\n            for(i = 0; i < 8; ++i){\n                var col = i;\n                // check for all-zero AC coefficients\n                if (p[1 * 8 + col] == 0 && p[2 * 8 + col] == 0 && p[3 * 8 + col] == 0 && p[4 * 8 + col] == 0 && p[5 * 8 + col] == 0 && p[6 * 8 + col] == 0 && p[7 * 8 + col] == 0) {\n                    t = dctSqrt2 * dataIn[i + 0] + 8192 >> 14;\n                    p[0 * 8 + col] = t;\n                    p[1 * 8 + col] = t;\n                    p[2 * 8 + col] = t;\n                    p[3 * 8 + col] = t;\n                    p[4 * 8 + col] = t;\n                    p[5 * 8 + col] = t;\n                    p[6 * 8 + col] = t;\n                    p[7 * 8 + col] = t;\n                    continue;\n                }\n                // stage 4\n                v0 = dctSqrt2 * p[0 * 8 + col] + 2048 >> 12;\n                v1 = dctSqrt2 * p[4 * 8 + col] + 2048 >> 12;\n                v2 = p[2 * 8 + col];\n                v3 = p[6 * 8 + col];\n                v4 = dctSqrt1d2 * (p[1 * 8 + col] - p[7 * 8 + col]) + 2048 >> 12;\n                v7 = dctSqrt1d2 * (p[1 * 8 + col] + p[7 * 8 + col]) + 2048 >> 12;\n                v5 = p[3 * 8 + col];\n                v6 = p[5 * 8 + col];\n                // stage 3\n                t = v0 - v1 + 1 >> 1;\n                v0 = v0 + v1 + 1 >> 1;\n                v1 = t;\n                t = v2 * dctSin6 + v3 * dctCos6 + 2048 >> 12;\n                v2 = v2 * dctCos6 - v3 * dctSin6 + 2048 >> 12;\n                v3 = t;\n                t = v4 - v6 + 1 >> 1;\n                v4 = v4 + v6 + 1 >> 1;\n                v6 = t;\n                t = v7 + v5 + 1 >> 1;\n                v5 = v7 - v5 + 1 >> 1;\n                v7 = t;\n                // stage 2\n                t = v0 - v3 + 1 >> 1;\n                v0 = v0 + v3 + 1 >> 1;\n                v3 = t;\n                t = v1 - v2 + 1 >> 1;\n                v1 = v1 + v2 + 1 >> 1;\n                v2 = t;\n                t = v4 * dctSin3 + v7 * dctCos3 + 2048 >> 12;\n                v4 = v4 * dctCos3 - v7 * dctSin3 + 2048 >> 12;\n                v7 = t;\n                t = v5 * dctSin1 + v6 * dctCos1 + 2048 >> 12;\n                v5 = v5 * dctCos1 - v6 * dctSin1 + 2048 >> 12;\n                v6 = t;\n                // stage 1\n                p[0 * 8 + col] = v0 + v7;\n                p[7 * 8 + col] = v0 - v7;\n                p[1 * 8 + col] = v1 + v6;\n                p[6 * 8 + col] = v1 - v6;\n                p[2 * 8 + col] = v2 + v5;\n                p[5 * 8 + col] = v2 - v5;\n                p[3 * 8 + col] = v3 + v4;\n                p[4 * 8 + col] = v3 - v4;\n            }\n            // convert to 8-bit integers\n            for(i = 0; i < 64; ++i){\n                var sample = 128 + (p[i] + 8 >> 4);\n                dataOut[i] = sample < 0 ? 0 : sample > 0xFF ? 0xFF : sample;\n            }\n        }\n        requestMemoryAllocation(samplesPerLine * blocksPerColumn * 8);\n        var i, j;\n        for(var blockRow = 0; blockRow < blocksPerColumn; blockRow++){\n            var scanLine = blockRow << 3;\n            for(i = 0; i < 8; i++)lines.push(new Uint8Array(samplesPerLine));\n            for(var blockCol = 0; blockCol < blocksPerLine; blockCol++){\n                quantizeAndInverse(component.blocks[blockRow][blockCol], r, R);\n                var offset = 0, sample = blockCol << 3;\n                for(j = 0; j < 8; j++){\n                    var line = lines[scanLine + j];\n                    for(i = 0; i < 8; i++)line[sample + i] = r[offset++];\n                }\n            }\n        }\n        return lines;\n    }\n    function clampTo8bit(a) {\n        return a < 0 ? 0 : a > 255 ? 255 : a;\n    }\n    constructor.prototype = {\n        load: function load(path) {\n            var xhr = new XMLHttpRequest();\n            xhr.open(\"GET\", path, true);\n            xhr.responseType = \"arraybuffer\";\n            xhr.onload = (function() {\n                // TODO catch parse error\n                var data = new Uint8Array(xhr.response || xhr.mozResponseArrayBuffer);\n                this.parse(data);\n                if (this.onload) this.onload();\n            }).bind(this);\n            xhr.send(null);\n        },\n        parse: function parse(data) {\n            var maxResolutionInPixels = this.opts.maxResolutionInMP * 1000 * 1000;\n            var offset = 0, length = data.length;\n            function readUint16() {\n                var value = data[offset] << 8 | data[offset + 1];\n                offset += 2;\n                return value;\n            }\n            function readDataBlock() {\n                var length = readUint16();\n                var array = data.subarray(offset, offset + length - 2);\n                offset += array.length;\n                return array;\n            }\n            function prepareComponents(frame) {\n                // According to the JPEG standard, the sampling factor must be between 1 and 4\n                // See https://github.com/libjpeg-turbo/libjpeg-turbo/blob/9abeff46d87bd201a952e276f3e4339556a403a3/libjpeg.txt#L1138-L1146\n                var maxH = 1, maxV = 1;\n                var component, componentId;\n                for(componentId in frame.components){\n                    if (frame.components.hasOwnProperty(componentId)) {\n                        component = frame.components[componentId];\n                        if (maxH < component.h) maxH = component.h;\n                        if (maxV < component.v) maxV = component.v;\n                    }\n                }\n                var mcusPerLine = Math.ceil(frame.samplesPerLine / 8 / maxH);\n                var mcusPerColumn = Math.ceil(frame.scanLines / 8 / maxV);\n                for(componentId in frame.components){\n                    if (frame.components.hasOwnProperty(componentId)) {\n                        component = frame.components[componentId];\n                        var blocksPerLine = Math.ceil(Math.ceil(frame.samplesPerLine / 8) * component.h / maxH);\n                        var blocksPerColumn = Math.ceil(Math.ceil(frame.scanLines / 8) * component.v / maxV);\n                        var blocksPerLineForMcu = mcusPerLine * component.h;\n                        var blocksPerColumnForMcu = mcusPerColumn * component.v;\n                        var blocksToAllocate = blocksPerColumnForMcu * blocksPerLineForMcu;\n                        var blocks = [];\n                        // Each block is a Int32Array of length 64 (4 x 64 = 256 bytes)\n                        requestMemoryAllocation(blocksToAllocate * 256);\n                        for(var i = 0; i < blocksPerColumnForMcu; i++){\n                            var row = [];\n                            for(var j = 0; j < blocksPerLineForMcu; j++)row.push(new Int32Array(64));\n                            blocks.push(row);\n                        }\n                        component.blocksPerLine = blocksPerLine;\n                        component.blocksPerColumn = blocksPerColumn;\n                        component.blocks = blocks;\n                    }\n                }\n                frame.maxH = maxH;\n                frame.maxV = maxV;\n                frame.mcusPerLine = mcusPerLine;\n                frame.mcusPerColumn = mcusPerColumn;\n            }\n            var jfif = null;\n            var adobe = null;\n            var pixels = null;\n            var frame, resetInterval;\n            var quantizationTables = [], frames = [];\n            var huffmanTablesAC = [], huffmanTablesDC = [];\n            var fileMarker = readUint16();\n            var malformedDataOffset = -1;\n            this.comments = [];\n            if (fileMarker != 0xFFD8) {\n                throw new Error(\"SOI not found\");\n            }\n            fileMarker = readUint16();\n            while(fileMarker != 0xFFD9){\n                var i, j, l;\n                switch(fileMarker){\n                    case 0xFF00:\n                        break;\n                    case 0xFFE0:\n                    case 0xFFE1:\n                    case 0xFFE2:\n                    case 0xFFE3:\n                    case 0xFFE4:\n                    case 0xFFE5:\n                    case 0xFFE6:\n                    case 0xFFE7:\n                    case 0xFFE8:\n                    case 0xFFE9:\n                    case 0xFFEA:\n                    case 0xFFEB:\n                    case 0xFFEC:\n                    case 0xFFED:\n                    case 0xFFEE:\n                    case 0xFFEF:\n                    case 0xFFFE:\n                        var appData = readDataBlock();\n                        if (fileMarker === 0xFFFE) {\n                            var comment = String.fromCharCode.apply(null, appData);\n                            this.comments.push(comment);\n                        }\n                        if (fileMarker === 0xFFE0) {\n                            if (appData[0] === 0x4A && appData[1] === 0x46 && appData[2] === 0x49 && appData[3] === 0x46 && appData[4] === 0) {\n                                jfif = {\n                                    version: {\n                                        major: appData[5],\n                                        minor: appData[6]\n                                    },\n                                    densityUnits: appData[7],\n                                    xDensity: appData[8] << 8 | appData[9],\n                                    yDensity: appData[10] << 8 | appData[11],\n                                    thumbWidth: appData[12],\n                                    thumbHeight: appData[13],\n                                    thumbData: appData.subarray(14, 14 + 3 * appData[12] * appData[13])\n                                };\n                            }\n                        }\n                        // TODO APP1 - Exif\n                        if (fileMarker === 0xFFE1) {\n                            if (appData[0] === 0x45 && appData[1] === 0x78 && appData[2] === 0x69 && appData[3] === 0x66 && appData[4] === 0) {\n                                this.exifBuffer = appData.subarray(5, appData.length);\n                            }\n                        }\n                        if (fileMarker === 0xFFEE) {\n                            if (appData[0] === 0x41 && appData[1] === 0x64 && appData[2] === 0x6F && appData[3] === 0x62 && appData[4] === 0x65 && appData[5] === 0) {\n                                adobe = {\n                                    version: appData[6],\n                                    flags0: appData[7] << 8 | appData[8],\n                                    flags1: appData[9] << 8 | appData[10],\n                                    transformCode: appData[11]\n                                };\n                            }\n                        }\n                        break;\n                    case 0xFFDB:\n                        var quantizationTablesLength = readUint16();\n                        var quantizationTablesEnd = quantizationTablesLength + offset - 2;\n                        while(offset < quantizationTablesEnd){\n                            var quantizationTableSpec = data[offset++];\n                            requestMemoryAllocation(64 * 4);\n                            var tableData = new Int32Array(64);\n                            if (quantizationTableSpec >> 4 === 0) {\n                                for(j = 0; j < 64; j++){\n                                    var z = dctZigZag[j];\n                                    tableData[z] = data[offset++];\n                                }\n                            } else if (quantizationTableSpec >> 4 === 1) {\n                                for(j = 0; j < 64; j++){\n                                    var z = dctZigZag[j];\n                                    tableData[z] = readUint16();\n                                }\n                            } else throw new Error(\"DQT: invalid table spec\");\n                            quantizationTables[quantizationTableSpec & 15] = tableData;\n                        }\n                        break;\n                    case 0xFFC0:\n                    case 0xFFC1:\n                    case 0xFFC2:\n                        readUint16(); // skip data length\n                        frame = {};\n                        frame.extended = fileMarker === 0xFFC1;\n                        frame.progressive = fileMarker === 0xFFC2;\n                        frame.precision = data[offset++];\n                        frame.scanLines = readUint16();\n                        frame.samplesPerLine = readUint16();\n                        frame.components = {};\n                        frame.componentsOrder = [];\n                        var pixelsInFrame = frame.scanLines * frame.samplesPerLine;\n                        if (pixelsInFrame > maxResolutionInPixels) {\n                            var exceededAmount = Math.ceil((pixelsInFrame - maxResolutionInPixels) / 1e6);\n                            throw new Error(`maxResolutionInMP limit exceeded by ${exceededAmount}MP`);\n                        }\n                        var componentsCount = data[offset++], componentId;\n                        var maxH = 0, maxV = 0;\n                        for(i = 0; i < componentsCount; i++){\n                            componentId = data[offset];\n                            var h = data[offset + 1] >> 4;\n                            var v = data[offset + 1] & 15;\n                            var qId = data[offset + 2];\n                            if (h <= 0 || v <= 0) {\n                                throw new Error(\"Invalid sampling factor, expected values above 0\");\n                            }\n                            frame.componentsOrder.push(componentId);\n                            frame.components[componentId] = {\n                                h: h,\n                                v: v,\n                                quantizationIdx: qId\n                            };\n                            offset += 3;\n                        }\n                        prepareComponents(frame);\n                        frames.push(frame);\n                        break;\n                    case 0xFFC4:\n                        var huffmanLength = readUint16();\n                        for(i = 2; i < huffmanLength;){\n                            var huffmanTableSpec = data[offset++];\n                            var codeLengths = new Uint8Array(16);\n                            var codeLengthSum = 0;\n                            for(j = 0; j < 16; j++, offset++){\n                                codeLengthSum += codeLengths[j] = data[offset];\n                            }\n                            requestMemoryAllocation(16 + codeLengthSum);\n                            var huffmanValues = new Uint8Array(codeLengthSum);\n                            for(j = 0; j < codeLengthSum; j++, offset++)huffmanValues[j] = data[offset];\n                            i += 17 + codeLengthSum;\n                            (huffmanTableSpec >> 4 === 0 ? huffmanTablesDC : huffmanTablesAC)[huffmanTableSpec & 15] = buildHuffmanTable(codeLengths, huffmanValues);\n                        }\n                        break;\n                    case 0xFFDD:\n                        readUint16(); // skip data length\n                        resetInterval = readUint16();\n                        break;\n                    case 0xFFDC:\n                        readUint16() // skip data length\n                        ;\n                        readUint16() // Ignore this data since it represents the image height\n                        ;\n                        break;\n                    case 0xFFDA:\n                        var scanLength = readUint16();\n                        var selectorsCount = data[offset++];\n                        var components = [], component;\n                        for(i = 0; i < selectorsCount; i++){\n                            component = frame.components[data[offset++]];\n                            var tableSpec = data[offset++];\n                            component.huffmanTableDC = huffmanTablesDC[tableSpec >> 4];\n                            component.huffmanTableAC = huffmanTablesAC[tableSpec & 15];\n                            components.push(component);\n                        }\n                        var spectralStart = data[offset++];\n                        var spectralEnd = data[offset++];\n                        var successiveApproximation = data[offset++];\n                        var processed = decodeScan(data, offset, frame, components, resetInterval, spectralStart, spectralEnd, successiveApproximation >> 4, successiveApproximation & 15, this.opts);\n                        offset += processed;\n                        break;\n                    case 0xFFFF:\n                        if (data[offset] !== 0xFF) {\n                            offset--;\n                        }\n                        break;\n                    default:\n                        if (data[offset - 3] == 0xFF && data[offset - 2] >= 0xC0 && data[offset - 2] <= 0xFE) {\n                            // could be incorrect encoding -- last 0xFF byte of the previous\n                            // block was eaten by the encoder\n                            offset -= 3;\n                            break;\n                        } else if (fileMarker === 0xE0 || fileMarker == 0xE1) {\n                            // Recover from malformed APP1 markers popular in some phone models.\n                            // See https://github.com/eugeneware/jpeg-js/issues/82\n                            if (malformedDataOffset !== -1) {\n                                throw new Error(`first unknown JPEG marker at offset ${malformedDataOffset.toString(16)}, second unknown JPEG marker ${fileMarker.toString(16)} at offset ${(offset - 1).toString(16)}`);\n                            }\n                            malformedDataOffset = offset - 1;\n                            const nextOffset = readUint16();\n                            if (data[offset + nextOffset - 2] === 0xFF) {\n                                offset += nextOffset - 2;\n                                break;\n                            }\n                        }\n                        throw new Error(\"unknown JPEG marker \" + fileMarker.toString(16));\n                }\n                fileMarker = readUint16();\n            }\n            if (frames.length != 1) throw new Error(\"only single frame JPEGs supported\");\n            // set each frame's components quantization table\n            for(var i = 0; i < frames.length; i++){\n                var cp = frames[i].components;\n                for(var j in cp){\n                    cp[j].quantizationTable = quantizationTables[cp[j].quantizationIdx];\n                    delete cp[j].quantizationIdx;\n                }\n            }\n            this.width = frame.samplesPerLine;\n            this.height = frame.scanLines;\n            this.jfif = jfif;\n            this.adobe = adobe;\n            this.components = [];\n            for(var i = 0; i < frame.componentsOrder.length; i++){\n                var component = frame.components[frame.componentsOrder[i]];\n                this.components.push({\n                    lines: buildComponentData(frame, component),\n                    scaleX: component.h / frame.maxH,\n                    scaleY: component.v / frame.maxV\n                });\n            }\n        },\n        getData: function getData(width, height) {\n            var scaleX = this.width / width, scaleY = this.height / height;\n            var component1, component2, component3, component4;\n            var component1Line, component2Line, component3Line, component4Line;\n            var x, y;\n            var offset = 0;\n            var Y, Cb, Cr, K, C, M, Ye, R, G, B;\n            var colorTransform;\n            var dataLength = width * height * this.components.length;\n            requestMemoryAllocation(dataLength);\n            var data = new Uint8Array(dataLength);\n            switch(this.components.length){\n                case 1:\n                    component1 = this.components[0];\n                    for(y = 0; y < height; y++){\n                        component1Line = component1.lines[0 | y * component1.scaleY * scaleY];\n                        for(x = 0; x < width; x++){\n                            Y = component1Line[0 | x * component1.scaleX * scaleX];\n                            data[offset++] = Y;\n                        }\n                    }\n                    break;\n                case 2:\n                    // PDF might compress two component data in custom colorspace\n                    component1 = this.components[0];\n                    component2 = this.components[1];\n                    for(y = 0; y < height; y++){\n                        component1Line = component1.lines[0 | y * component1.scaleY * scaleY];\n                        component2Line = component2.lines[0 | y * component2.scaleY * scaleY];\n                        for(x = 0; x < width; x++){\n                            Y = component1Line[0 | x * component1.scaleX * scaleX];\n                            data[offset++] = Y;\n                            Y = component2Line[0 | x * component2.scaleX * scaleX];\n                            data[offset++] = Y;\n                        }\n                    }\n                    break;\n                case 3:\n                    // The default transform for three components is true\n                    colorTransform = true;\n                    // The adobe transform marker overrides any previous setting\n                    if (this.adobe && this.adobe.transformCode) colorTransform = true;\n                    else if (typeof this.opts.colorTransform !== \"undefined\") colorTransform = !!this.opts.colorTransform;\n                    component1 = this.components[0];\n                    component2 = this.components[1];\n                    component3 = this.components[2];\n                    for(y = 0; y < height; y++){\n                        component1Line = component1.lines[0 | y * component1.scaleY * scaleY];\n                        component2Line = component2.lines[0 | y * component2.scaleY * scaleY];\n                        component3Line = component3.lines[0 | y * component3.scaleY * scaleY];\n                        for(x = 0; x < width; x++){\n                            if (!colorTransform) {\n                                R = component1Line[0 | x * component1.scaleX * scaleX];\n                                G = component2Line[0 | x * component2.scaleX * scaleX];\n                                B = component3Line[0 | x * component3.scaleX * scaleX];\n                            } else {\n                                Y = component1Line[0 | x * component1.scaleX * scaleX];\n                                Cb = component2Line[0 | x * component2.scaleX * scaleX];\n                                Cr = component3Line[0 | x * component3.scaleX * scaleX];\n                                R = clampTo8bit(Y + 1.402 * (Cr - 128));\n                                G = clampTo8bit(Y - 0.3441363 * (Cb - 128) - 0.71413636 * (Cr - 128));\n                                B = clampTo8bit(Y + 1.772 * (Cb - 128));\n                            }\n                            data[offset++] = R;\n                            data[offset++] = G;\n                            data[offset++] = B;\n                        }\n                    }\n                    break;\n                case 4:\n                    if (!this.adobe) throw new Error(\"Unsupported color mode (4 components)\");\n                    // The default transform for four components is false\n                    colorTransform = false;\n                    // The adobe transform marker overrides any previous setting\n                    if (this.adobe && this.adobe.transformCode) colorTransform = true;\n                    else if (typeof this.opts.colorTransform !== \"undefined\") colorTransform = !!this.opts.colorTransform;\n                    component1 = this.components[0];\n                    component2 = this.components[1];\n                    component3 = this.components[2];\n                    component4 = this.components[3];\n                    for(y = 0; y < height; y++){\n                        component1Line = component1.lines[0 | y * component1.scaleY * scaleY];\n                        component2Line = component2.lines[0 | y * component2.scaleY * scaleY];\n                        component3Line = component3.lines[0 | y * component3.scaleY * scaleY];\n                        component4Line = component4.lines[0 | y * component4.scaleY * scaleY];\n                        for(x = 0; x < width; x++){\n                            if (!colorTransform) {\n                                C = component1Line[0 | x * component1.scaleX * scaleX];\n                                M = component2Line[0 | x * component2.scaleX * scaleX];\n                                Ye = component3Line[0 | x * component3.scaleX * scaleX];\n                                K = component4Line[0 | x * component4.scaleX * scaleX];\n                            } else {\n                                Y = component1Line[0 | x * component1.scaleX * scaleX];\n                                Cb = component2Line[0 | x * component2.scaleX * scaleX];\n                                Cr = component3Line[0 | x * component3.scaleX * scaleX];\n                                K = component4Line[0 | x * component4.scaleX * scaleX];\n                                C = 255 - clampTo8bit(Y + 1.402 * (Cr - 128));\n                                M = 255 - clampTo8bit(Y - 0.3441363 * (Cb - 128) - 0.71413636 * (Cr - 128));\n                                Ye = 255 - clampTo8bit(Y + 1.772 * (Cb - 128));\n                            }\n                            data[offset++] = 255 - C;\n                            data[offset++] = 255 - M;\n                            data[offset++] = 255 - Ye;\n                            data[offset++] = 255 - K;\n                        }\n                    }\n                    break;\n                default:\n                    throw new Error(\"Unsupported color mode\");\n            }\n            return data;\n        },\n        copyToImageData: function copyToImageData(imageData, formatAsRGBA) {\n            var width = imageData.width, height = imageData.height;\n            var imageDataArray = imageData.data;\n            var data = this.getData(width, height);\n            var i = 0, j = 0, x, y;\n            var Y, K, C, M, R, G, B;\n            switch(this.components.length){\n                case 1:\n                    for(y = 0; y < height; y++){\n                        for(x = 0; x < width; x++){\n                            Y = data[i++];\n                            imageDataArray[j++] = Y;\n                            imageDataArray[j++] = Y;\n                            imageDataArray[j++] = Y;\n                            if (formatAsRGBA) {\n                                imageDataArray[j++] = 255;\n                            }\n                        }\n                    }\n                    break;\n                case 3:\n                    for(y = 0; y < height; y++){\n                        for(x = 0; x < width; x++){\n                            R = data[i++];\n                            G = data[i++];\n                            B = data[i++];\n                            imageDataArray[j++] = R;\n                            imageDataArray[j++] = G;\n                            imageDataArray[j++] = B;\n                            if (formatAsRGBA) {\n                                imageDataArray[j++] = 255;\n                            }\n                        }\n                    }\n                    break;\n                case 4:\n                    for(y = 0; y < height; y++){\n                        for(x = 0; x < width; x++){\n                            C = data[i++];\n                            M = data[i++];\n                            Y = data[i++];\n                            K = data[i++];\n                            R = 255 - clampTo8bit(C * (1 - K / 255) + K);\n                            G = 255 - clampTo8bit(M * (1 - K / 255) + K);\n                            B = 255 - clampTo8bit(Y * (1 - K / 255) + K);\n                            imageDataArray[j++] = R;\n                            imageDataArray[j++] = G;\n                            imageDataArray[j++] = B;\n                            if (formatAsRGBA) {\n                                imageDataArray[j++] = 255;\n                            }\n                        }\n                    }\n                    break;\n                default:\n                    throw new Error(\"Unsupported color mode\");\n            }\n        }\n    };\n    // We cap the amount of memory used by jpeg-js to avoid unexpected OOMs from untrusted content.\n    var totalBytesAllocated = 0;\n    var maxMemoryUsageBytes = 0;\n    function requestMemoryAllocation(increaseAmount = 0) {\n        var totalMemoryImpactBytes = totalBytesAllocated + increaseAmount;\n        if (totalMemoryImpactBytes > maxMemoryUsageBytes) {\n            var exceededAmount = Math.ceil((totalMemoryImpactBytes - maxMemoryUsageBytes) / 1024 / 1024);\n            throw new Error(`maxMemoryUsageInMB limit exceeded by at least ${exceededAmount}MB`);\n        }\n        totalBytesAllocated = totalMemoryImpactBytes;\n    }\n    constructor.resetMaxMemoryUsage = function(maxMemoryUsageBytes_) {\n        totalBytesAllocated = 0;\n        maxMemoryUsageBytes = maxMemoryUsageBytes_;\n    };\n    constructor.getBytesAllocated = function() {\n        return totalBytesAllocated;\n    };\n    constructor.requestMemoryAllocation = requestMemoryAllocation;\n    return constructor;\n}();\nif (true) {\n    module.exports = decode;\n} else {}\nfunction decode(jpegData, userOpts = {}) {\n    var defaultOpts = {\n        // \"undefined\" means \"Choose whether to transform colors based on the image’s color model.\"\n        colorTransform: undefined,\n        useTArray: false,\n        formatAsRGBA: true,\n        tolerantDecoding: true,\n        maxResolutionInMP: 100,\n        maxMemoryUsageInMB: 512\n    };\n    var opts = {\n        ...defaultOpts,\n        ...userOpts\n    };\n    var arr = new Uint8Array(jpegData);\n    var decoder = new JpegImage();\n    decoder.opts = opts;\n    // If this constructor ever supports async decoding this will need to be done differently.\n    // Until then, treating as singleton limit is fine.\n    JpegImage.resetMaxMemoryUsage(opts.maxMemoryUsageInMB * 1024 * 1024);\n    decoder.parse(arr);\n    var channels = opts.formatAsRGBA ? 4 : 3;\n    var bytesNeeded = decoder.width * decoder.height * channels;\n    try {\n        JpegImage.requestMemoryAllocation(bytesNeeded);\n        var image = {\n            width: decoder.width,\n            height: decoder.height,\n            exifBuffer: decoder.exifBuffer,\n            data: opts.useTArray ? new Uint8Array(bytesNeeded) : Buffer.alloc(bytesNeeded)\n        };\n        if (decoder.comments.length > 0) {\n            image[\"comments\"] = decoder.comments;\n        }\n    } catch (err) {\n        if (err instanceof RangeError) {\n            throw new Error(\"Could not allocate enough memory for the image. \" + \"Required: \" + bytesNeeded);\n        }\n        if (err instanceof ReferenceError) {\n            if (err.message === \"Buffer is not defined\") {\n                throw new Error(\"Buffer is not globally defined in this environment. \" + \"Consider setting useTArray to true\");\n            }\n        }\n        throw err;\n    }\n    decoder.copyToImageData(image, opts.formatAsRGBA);\n    return image;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jpeg-js/lib/decoder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jpeg-js/lib/encoder.js":
/*!*********************************************!*\
  !*** ./node_modules/jpeg-js/lib/encoder.js ***!
  \*********************************************/
/***/ ((module) => {

eval("/*\n  Copyright (c) 2008, Adobe Systems Incorporated\n  All rights reserved.\n\n  Redistribution and use in source and binary forms, with or without \n  modification, are permitted provided that the following conditions are\n  met:\n\n  * Redistributions of source code must retain the above copyright notice, \n    this list of conditions and the following disclaimer.\n  \n  * Redistributions in binary form must reproduce the above copyright\n    notice, this list of conditions and the following disclaimer in the \n    documentation and/or other materials provided with the distribution.\n  \n  * Neither the name of Adobe Systems Incorporated nor the names of its \n    contributors may be used to endorse or promote products derived from \n    this software without specific prior written permission.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS\n  IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,\n  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR\n  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR \n  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\n  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\n  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\n  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/ /*\nJPEG encoder ported to JavaScript and optimized by Andreas Ritter, www.bytestrom.eu, 11/2009\n\nBasic GUI blocking jpeg encoder\n*/ \nvar btoa = btoa || function(buf) {\n    return Buffer.from(buf).toString(\"base64\");\n};\nfunction JPEGEncoder(quality) {\n    var self = this;\n    var fround = Math.round;\n    var ffloor = Math.floor;\n    var YTable = new Array(64);\n    var UVTable = new Array(64);\n    var fdtbl_Y = new Array(64);\n    var fdtbl_UV = new Array(64);\n    var YDC_HT;\n    var UVDC_HT;\n    var YAC_HT;\n    var UVAC_HT;\n    var bitcode = new Array(65535);\n    var category = new Array(65535);\n    var outputfDCTQuant = new Array(64);\n    var DU = new Array(64);\n    var byteout = [];\n    var bytenew = 0;\n    var bytepos = 7;\n    var YDU = new Array(64);\n    var UDU = new Array(64);\n    var VDU = new Array(64);\n    var clt = new Array(256);\n    var RGB_YUV_TABLE = new Array(2048);\n    var currentQuality;\n    var ZigZag = [\n        0,\n        1,\n        5,\n        6,\n        14,\n        15,\n        27,\n        28,\n        2,\n        4,\n        7,\n        13,\n        16,\n        26,\n        29,\n        42,\n        3,\n        8,\n        12,\n        17,\n        25,\n        30,\n        41,\n        43,\n        9,\n        11,\n        18,\n        24,\n        31,\n        40,\n        44,\n        53,\n        10,\n        19,\n        23,\n        32,\n        39,\n        45,\n        52,\n        54,\n        20,\n        22,\n        33,\n        38,\n        46,\n        51,\n        55,\n        60,\n        21,\n        34,\n        37,\n        47,\n        50,\n        56,\n        59,\n        61,\n        35,\n        36,\n        48,\n        49,\n        57,\n        58,\n        62,\n        63\n    ];\n    var std_dc_luminance_nrcodes = [\n        0,\n        0,\n        1,\n        5,\n        1,\n        1,\n        1,\n        1,\n        1,\n        1,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0\n    ];\n    var std_dc_luminance_values = [\n        0,\n        1,\n        2,\n        3,\n        4,\n        5,\n        6,\n        7,\n        8,\n        9,\n        10,\n        11\n    ];\n    var std_ac_luminance_nrcodes = [\n        0,\n        0,\n        2,\n        1,\n        3,\n        3,\n        2,\n        4,\n        3,\n        5,\n        5,\n        4,\n        4,\n        0,\n        0,\n        1,\n        0x7d\n    ];\n    var std_ac_luminance_values = [\n        0x01,\n        0x02,\n        0x03,\n        0x00,\n        0x04,\n        0x11,\n        0x05,\n        0x12,\n        0x21,\n        0x31,\n        0x41,\n        0x06,\n        0x13,\n        0x51,\n        0x61,\n        0x07,\n        0x22,\n        0x71,\n        0x14,\n        0x32,\n        0x81,\n        0x91,\n        0xa1,\n        0x08,\n        0x23,\n        0x42,\n        0xb1,\n        0xc1,\n        0x15,\n        0x52,\n        0xd1,\n        0xf0,\n        0x24,\n        0x33,\n        0x62,\n        0x72,\n        0x82,\n        0x09,\n        0x0a,\n        0x16,\n        0x17,\n        0x18,\n        0x19,\n        0x1a,\n        0x25,\n        0x26,\n        0x27,\n        0x28,\n        0x29,\n        0x2a,\n        0x34,\n        0x35,\n        0x36,\n        0x37,\n        0x38,\n        0x39,\n        0x3a,\n        0x43,\n        0x44,\n        0x45,\n        0x46,\n        0x47,\n        0x48,\n        0x49,\n        0x4a,\n        0x53,\n        0x54,\n        0x55,\n        0x56,\n        0x57,\n        0x58,\n        0x59,\n        0x5a,\n        0x63,\n        0x64,\n        0x65,\n        0x66,\n        0x67,\n        0x68,\n        0x69,\n        0x6a,\n        0x73,\n        0x74,\n        0x75,\n        0x76,\n        0x77,\n        0x78,\n        0x79,\n        0x7a,\n        0x83,\n        0x84,\n        0x85,\n        0x86,\n        0x87,\n        0x88,\n        0x89,\n        0x8a,\n        0x92,\n        0x93,\n        0x94,\n        0x95,\n        0x96,\n        0x97,\n        0x98,\n        0x99,\n        0x9a,\n        0xa2,\n        0xa3,\n        0xa4,\n        0xa5,\n        0xa6,\n        0xa7,\n        0xa8,\n        0xa9,\n        0xaa,\n        0xb2,\n        0xb3,\n        0xb4,\n        0xb5,\n        0xb6,\n        0xb7,\n        0xb8,\n        0xb9,\n        0xba,\n        0xc2,\n        0xc3,\n        0xc4,\n        0xc5,\n        0xc6,\n        0xc7,\n        0xc8,\n        0xc9,\n        0xca,\n        0xd2,\n        0xd3,\n        0xd4,\n        0xd5,\n        0xd6,\n        0xd7,\n        0xd8,\n        0xd9,\n        0xda,\n        0xe1,\n        0xe2,\n        0xe3,\n        0xe4,\n        0xe5,\n        0xe6,\n        0xe7,\n        0xe8,\n        0xe9,\n        0xea,\n        0xf1,\n        0xf2,\n        0xf3,\n        0xf4,\n        0xf5,\n        0xf6,\n        0xf7,\n        0xf8,\n        0xf9,\n        0xfa\n    ];\n    var std_dc_chrominance_nrcodes = [\n        0,\n        0,\n        3,\n        1,\n        1,\n        1,\n        1,\n        1,\n        1,\n        1,\n        1,\n        1,\n        0,\n        0,\n        0,\n        0,\n        0\n    ];\n    var std_dc_chrominance_values = [\n        0,\n        1,\n        2,\n        3,\n        4,\n        5,\n        6,\n        7,\n        8,\n        9,\n        10,\n        11\n    ];\n    var std_ac_chrominance_nrcodes = [\n        0,\n        0,\n        2,\n        1,\n        2,\n        4,\n        4,\n        3,\n        4,\n        7,\n        5,\n        4,\n        4,\n        0,\n        1,\n        2,\n        0x77\n    ];\n    var std_ac_chrominance_values = [\n        0x00,\n        0x01,\n        0x02,\n        0x03,\n        0x11,\n        0x04,\n        0x05,\n        0x21,\n        0x31,\n        0x06,\n        0x12,\n        0x41,\n        0x51,\n        0x07,\n        0x61,\n        0x71,\n        0x13,\n        0x22,\n        0x32,\n        0x81,\n        0x08,\n        0x14,\n        0x42,\n        0x91,\n        0xa1,\n        0xb1,\n        0xc1,\n        0x09,\n        0x23,\n        0x33,\n        0x52,\n        0xf0,\n        0x15,\n        0x62,\n        0x72,\n        0xd1,\n        0x0a,\n        0x16,\n        0x24,\n        0x34,\n        0xe1,\n        0x25,\n        0xf1,\n        0x17,\n        0x18,\n        0x19,\n        0x1a,\n        0x26,\n        0x27,\n        0x28,\n        0x29,\n        0x2a,\n        0x35,\n        0x36,\n        0x37,\n        0x38,\n        0x39,\n        0x3a,\n        0x43,\n        0x44,\n        0x45,\n        0x46,\n        0x47,\n        0x48,\n        0x49,\n        0x4a,\n        0x53,\n        0x54,\n        0x55,\n        0x56,\n        0x57,\n        0x58,\n        0x59,\n        0x5a,\n        0x63,\n        0x64,\n        0x65,\n        0x66,\n        0x67,\n        0x68,\n        0x69,\n        0x6a,\n        0x73,\n        0x74,\n        0x75,\n        0x76,\n        0x77,\n        0x78,\n        0x79,\n        0x7a,\n        0x82,\n        0x83,\n        0x84,\n        0x85,\n        0x86,\n        0x87,\n        0x88,\n        0x89,\n        0x8a,\n        0x92,\n        0x93,\n        0x94,\n        0x95,\n        0x96,\n        0x97,\n        0x98,\n        0x99,\n        0x9a,\n        0xa2,\n        0xa3,\n        0xa4,\n        0xa5,\n        0xa6,\n        0xa7,\n        0xa8,\n        0xa9,\n        0xaa,\n        0xb2,\n        0xb3,\n        0xb4,\n        0xb5,\n        0xb6,\n        0xb7,\n        0xb8,\n        0xb9,\n        0xba,\n        0xc2,\n        0xc3,\n        0xc4,\n        0xc5,\n        0xc6,\n        0xc7,\n        0xc8,\n        0xc9,\n        0xca,\n        0xd2,\n        0xd3,\n        0xd4,\n        0xd5,\n        0xd6,\n        0xd7,\n        0xd8,\n        0xd9,\n        0xda,\n        0xe2,\n        0xe3,\n        0xe4,\n        0xe5,\n        0xe6,\n        0xe7,\n        0xe8,\n        0xe9,\n        0xea,\n        0xf2,\n        0xf3,\n        0xf4,\n        0xf5,\n        0xf6,\n        0xf7,\n        0xf8,\n        0xf9,\n        0xfa\n    ];\n    function initQuantTables(sf) {\n        var YQT = [\n            16,\n            11,\n            10,\n            16,\n            24,\n            40,\n            51,\n            61,\n            12,\n            12,\n            14,\n            19,\n            26,\n            58,\n            60,\n            55,\n            14,\n            13,\n            16,\n            24,\n            40,\n            57,\n            69,\n            56,\n            14,\n            17,\n            22,\n            29,\n            51,\n            87,\n            80,\n            62,\n            18,\n            22,\n            37,\n            56,\n            68,\n            109,\n            103,\n            77,\n            24,\n            35,\n            55,\n            64,\n            81,\n            104,\n            113,\n            92,\n            49,\n            64,\n            78,\n            87,\n            103,\n            121,\n            120,\n            101,\n            72,\n            92,\n            95,\n            98,\n            112,\n            100,\n            103,\n            99\n        ];\n        for(var i = 0; i < 64; i++){\n            var t = ffloor((YQT[i] * sf + 50) / 100);\n            if (t < 1) {\n                t = 1;\n            } else if (t > 255) {\n                t = 255;\n            }\n            YTable[ZigZag[i]] = t;\n        }\n        var UVQT = [\n            17,\n            18,\n            24,\n            47,\n            99,\n            99,\n            99,\n            99,\n            18,\n            21,\n            26,\n            66,\n            99,\n            99,\n            99,\n            99,\n            24,\n            26,\n            56,\n            99,\n            99,\n            99,\n            99,\n            99,\n            47,\n            66,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99,\n            99\n        ];\n        for(var j = 0; j < 64; j++){\n            var u = ffloor((UVQT[j] * sf + 50) / 100);\n            if (u < 1) {\n                u = 1;\n            } else if (u > 255) {\n                u = 255;\n            }\n            UVTable[ZigZag[j]] = u;\n        }\n        var aasf = [\n            1.0,\n            1.387039845,\n            1.306562965,\n            1.175875602,\n            1.0,\n            0.785694958,\n            0.541196100,\n            0.275899379\n        ];\n        var k = 0;\n        for(var row = 0; row < 8; row++){\n            for(var col = 0; col < 8; col++){\n                fdtbl_Y[k] = 1.0 / (YTable[ZigZag[k]] * aasf[row] * aasf[col] * 8.0);\n                fdtbl_UV[k] = 1.0 / (UVTable[ZigZag[k]] * aasf[row] * aasf[col] * 8.0);\n                k++;\n            }\n        }\n    }\n    function computeHuffmanTbl(nrcodes, std_table) {\n        var codevalue = 0;\n        var pos_in_table = 0;\n        var HT = new Array();\n        for(var k = 1; k <= 16; k++){\n            for(var j = 1; j <= nrcodes[k]; j++){\n                HT[std_table[pos_in_table]] = [];\n                HT[std_table[pos_in_table]][0] = codevalue;\n                HT[std_table[pos_in_table]][1] = k;\n                pos_in_table++;\n                codevalue++;\n            }\n            codevalue *= 2;\n        }\n        return HT;\n    }\n    function initHuffmanTbl() {\n        YDC_HT = computeHuffmanTbl(std_dc_luminance_nrcodes, std_dc_luminance_values);\n        UVDC_HT = computeHuffmanTbl(std_dc_chrominance_nrcodes, std_dc_chrominance_values);\n        YAC_HT = computeHuffmanTbl(std_ac_luminance_nrcodes, std_ac_luminance_values);\n        UVAC_HT = computeHuffmanTbl(std_ac_chrominance_nrcodes, std_ac_chrominance_values);\n    }\n    function initCategoryNumber() {\n        var nrlower = 1;\n        var nrupper = 2;\n        for(var cat = 1; cat <= 15; cat++){\n            //Positive numbers\n            for(var nr = nrlower; nr < nrupper; nr++){\n                category[32767 + nr] = cat;\n                bitcode[32767 + nr] = [];\n                bitcode[32767 + nr][1] = cat;\n                bitcode[32767 + nr][0] = nr;\n            }\n            //Negative numbers\n            for(var nrneg = -(nrupper - 1); nrneg <= -nrlower; nrneg++){\n                category[32767 + nrneg] = cat;\n                bitcode[32767 + nrneg] = [];\n                bitcode[32767 + nrneg][1] = cat;\n                bitcode[32767 + nrneg][0] = nrupper - 1 + nrneg;\n            }\n            nrlower <<= 1;\n            nrupper <<= 1;\n        }\n    }\n    function initRGBYUVTable() {\n        for(var i = 0; i < 256; i++){\n            RGB_YUV_TABLE[i] = 19595 * i;\n            RGB_YUV_TABLE[i + 256 >> 0] = 38470 * i;\n            RGB_YUV_TABLE[i + 512 >> 0] = 7471 * i + 0x8000;\n            RGB_YUV_TABLE[i + 768 >> 0] = -11059 * i;\n            RGB_YUV_TABLE[i + 1024 >> 0] = -21709 * i;\n            RGB_YUV_TABLE[i + 1280 >> 0] = 32768 * i + 0x807FFF;\n            RGB_YUV_TABLE[i + 1536 >> 0] = -27439 * i;\n            RGB_YUV_TABLE[i + 1792 >> 0] = -5329 * i;\n        }\n    }\n    // IO functions\n    function writeBits(bs) {\n        var value = bs[0];\n        var posval = bs[1] - 1;\n        while(posval >= 0){\n            if (value & 1 << posval) {\n                bytenew |= 1 << bytepos;\n            }\n            posval--;\n            bytepos--;\n            if (bytepos < 0) {\n                if (bytenew == 0xFF) {\n                    writeByte(0xFF);\n                    writeByte(0);\n                } else {\n                    writeByte(bytenew);\n                }\n                bytepos = 7;\n                bytenew = 0;\n            }\n        }\n    }\n    function writeByte(value) {\n        //byteout.push(clt[value]); // write char directly instead of converting later\n        byteout.push(value);\n    }\n    function writeWord(value) {\n        writeByte(value >> 8 & 0xFF);\n        writeByte(value & 0xFF);\n    }\n    // DCT & quantization core\n    function fDCTQuant(data, fdtbl) {\n        var d0, d1, d2, d3, d4, d5, d6, d7;\n        /* Pass 1: process rows. */ var dataOff = 0;\n        var i;\n        var I8 = 8;\n        var I64 = 64;\n        for(i = 0; i < I8; ++i){\n            d0 = data[dataOff];\n            d1 = data[dataOff + 1];\n            d2 = data[dataOff + 2];\n            d3 = data[dataOff + 3];\n            d4 = data[dataOff + 4];\n            d5 = data[dataOff + 5];\n            d6 = data[dataOff + 6];\n            d7 = data[dataOff + 7];\n            var tmp0 = d0 + d7;\n            var tmp7 = d0 - d7;\n            var tmp1 = d1 + d6;\n            var tmp6 = d1 - d6;\n            var tmp2 = d2 + d5;\n            var tmp5 = d2 - d5;\n            var tmp3 = d3 + d4;\n            var tmp4 = d3 - d4;\n            /* Even part */ var tmp10 = tmp0 + tmp3; /* phase 2 */ \n            var tmp13 = tmp0 - tmp3;\n            var tmp11 = tmp1 + tmp2;\n            var tmp12 = tmp1 - tmp2;\n            data[dataOff] = tmp10 + tmp11; /* phase 3 */ \n            data[dataOff + 4] = tmp10 - tmp11;\n            var z1 = (tmp12 + tmp13) * 0.707106781; /* c4 */ \n            data[dataOff + 2] = tmp13 + z1; /* phase 5 */ \n            data[dataOff + 6] = tmp13 - z1;\n            /* Odd part */ tmp10 = tmp4 + tmp5; /* phase 2 */ \n            tmp11 = tmp5 + tmp6;\n            tmp12 = tmp6 + tmp7;\n            /* The rotator is modified from fig 4-8 to avoid extra negations. */ var z5 = (tmp10 - tmp12) * 0.382683433; /* c6 */ \n            var z2 = 0.541196100 * tmp10 + z5; /* c2-c6 */ \n            var z4 = 1.306562965 * tmp12 + z5; /* c2+c6 */ \n            var z3 = tmp11 * 0.707106781; /* c4 */ \n            var z11 = tmp7 + z3; /* phase 5 */ \n            var z13 = tmp7 - z3;\n            data[dataOff + 5] = z13 + z2; /* phase 6 */ \n            data[dataOff + 3] = z13 - z2;\n            data[dataOff + 1] = z11 + z4;\n            data[dataOff + 7] = z11 - z4;\n            dataOff += 8; /* advance pointer to next row */ \n        }\n        /* Pass 2: process columns. */ dataOff = 0;\n        for(i = 0; i < I8; ++i){\n            d0 = data[dataOff];\n            d1 = data[dataOff + 8];\n            d2 = data[dataOff + 16];\n            d3 = data[dataOff + 24];\n            d4 = data[dataOff + 32];\n            d5 = data[dataOff + 40];\n            d6 = data[dataOff + 48];\n            d7 = data[dataOff + 56];\n            var tmp0p2 = d0 + d7;\n            var tmp7p2 = d0 - d7;\n            var tmp1p2 = d1 + d6;\n            var tmp6p2 = d1 - d6;\n            var tmp2p2 = d2 + d5;\n            var tmp5p2 = d2 - d5;\n            var tmp3p2 = d3 + d4;\n            var tmp4p2 = d3 - d4;\n            /* Even part */ var tmp10p2 = tmp0p2 + tmp3p2; /* phase 2 */ \n            var tmp13p2 = tmp0p2 - tmp3p2;\n            var tmp11p2 = tmp1p2 + tmp2p2;\n            var tmp12p2 = tmp1p2 - tmp2p2;\n            data[dataOff] = tmp10p2 + tmp11p2; /* phase 3 */ \n            data[dataOff + 32] = tmp10p2 - tmp11p2;\n            var z1p2 = (tmp12p2 + tmp13p2) * 0.707106781; /* c4 */ \n            data[dataOff + 16] = tmp13p2 + z1p2; /* phase 5 */ \n            data[dataOff + 48] = tmp13p2 - z1p2;\n            /* Odd part */ tmp10p2 = tmp4p2 + tmp5p2; /* phase 2 */ \n            tmp11p2 = tmp5p2 + tmp6p2;\n            tmp12p2 = tmp6p2 + tmp7p2;\n            /* The rotator is modified from fig 4-8 to avoid extra negations. */ var z5p2 = (tmp10p2 - tmp12p2) * 0.382683433; /* c6 */ \n            var z2p2 = 0.541196100 * tmp10p2 + z5p2; /* c2-c6 */ \n            var z4p2 = 1.306562965 * tmp12p2 + z5p2; /* c2+c6 */ \n            var z3p2 = tmp11p2 * 0.707106781; /* c4 */ \n            var z11p2 = tmp7p2 + z3p2; /* phase 5 */ \n            var z13p2 = tmp7p2 - z3p2;\n            data[dataOff + 40] = z13p2 + z2p2; /* phase 6 */ \n            data[dataOff + 24] = z13p2 - z2p2;\n            data[dataOff + 8] = z11p2 + z4p2;\n            data[dataOff + 56] = z11p2 - z4p2;\n            dataOff++; /* advance pointer to next column */ \n        }\n        // Quantize/descale the coefficients\n        var fDCTQuant;\n        for(i = 0; i < I64; ++i){\n            // Apply the quantization and scaling factor & Round to nearest integer\n            fDCTQuant = data[i] * fdtbl[i];\n            outputfDCTQuant[i] = fDCTQuant > 0.0 ? fDCTQuant + 0.5 | 0 : fDCTQuant - 0.5 | 0;\n        //outputfDCTQuant[i] = fround(fDCTQuant);\n        }\n        return outputfDCTQuant;\n    }\n    function writeAPP0() {\n        writeWord(0xFFE0); // marker\n        writeWord(16); // length\n        writeByte(0x4A); // J\n        writeByte(0x46); // F\n        writeByte(0x49); // I\n        writeByte(0x46); // F\n        writeByte(0); // = \"JFIF\",'\\0'\n        writeByte(1); // versionhi\n        writeByte(1); // versionlo\n        writeByte(0); // xyunits\n        writeWord(1); // xdensity\n        writeWord(1); // ydensity\n        writeByte(0); // thumbnwidth\n        writeByte(0); // thumbnheight\n    }\n    function writeAPP1(exifBuffer) {\n        if (!exifBuffer) return;\n        writeWord(0xFFE1); // APP1 marker\n        if (exifBuffer[0] === 0x45 && exifBuffer[1] === 0x78 && exifBuffer[2] === 0x69 && exifBuffer[3] === 0x66) {\n            // Buffer already starts with EXIF, just use it directly\n            writeWord(exifBuffer.length + 2); // length is buffer + length itself!\n        } else {\n            // Buffer doesn't start with EXIF, write it for them\n            writeWord(exifBuffer.length + 5 + 2); // length is buffer + EXIF\\0 + length itself!\n            writeByte(0x45); // E\n            writeByte(0x78); // X\n            writeByte(0x69); // I\n            writeByte(0x66); // F\n            writeByte(0); // = \"EXIF\",'\\0'\n        }\n        for(var i = 0; i < exifBuffer.length; i++){\n            writeByte(exifBuffer[i]);\n        }\n    }\n    function writeSOF0(width, height) {\n        writeWord(0xFFC0); // marker\n        writeWord(17); // length, truecolor YUV JPG\n        writeByte(8); // precision\n        writeWord(height);\n        writeWord(width);\n        writeByte(3); // nrofcomponents\n        writeByte(1); // IdY\n        writeByte(0x11); // HVY\n        writeByte(0); // QTY\n        writeByte(2); // IdU\n        writeByte(0x11); // HVU\n        writeByte(1); // QTU\n        writeByte(3); // IdV\n        writeByte(0x11); // HVV\n        writeByte(1); // QTV\n    }\n    function writeDQT() {\n        writeWord(0xFFDB); // marker\n        writeWord(132); // length\n        writeByte(0);\n        for(var i = 0; i < 64; i++){\n            writeByte(YTable[i]);\n        }\n        writeByte(1);\n        for(var j = 0; j < 64; j++){\n            writeByte(UVTable[j]);\n        }\n    }\n    function writeDHT() {\n        writeWord(0xFFC4); // marker\n        writeWord(0x01A2); // length\n        writeByte(0); // HTYDCinfo\n        for(var i = 0; i < 16; i++){\n            writeByte(std_dc_luminance_nrcodes[i + 1]);\n        }\n        for(var j = 0; j <= 11; j++){\n            writeByte(std_dc_luminance_values[j]);\n        }\n        writeByte(0x10); // HTYACinfo\n        for(var k = 0; k < 16; k++){\n            writeByte(std_ac_luminance_nrcodes[k + 1]);\n        }\n        for(var l = 0; l <= 161; l++){\n            writeByte(std_ac_luminance_values[l]);\n        }\n        writeByte(1); // HTUDCinfo\n        for(var m = 0; m < 16; m++){\n            writeByte(std_dc_chrominance_nrcodes[m + 1]);\n        }\n        for(var n = 0; n <= 11; n++){\n            writeByte(std_dc_chrominance_values[n]);\n        }\n        writeByte(0x11); // HTUACinfo\n        for(var o = 0; o < 16; o++){\n            writeByte(std_ac_chrominance_nrcodes[o + 1]);\n        }\n        for(var p = 0; p <= 161; p++){\n            writeByte(std_ac_chrominance_values[p]);\n        }\n    }\n    function writeCOM(comments) {\n        if (typeof comments === \"undefined\" || comments.constructor !== Array) return;\n        comments.forEach((e)=>{\n            if (typeof e !== \"string\") return;\n            writeWord(0xFFFE); // marker\n            var l = e.length;\n            writeWord(l + 2); // length itself as well\n            var i;\n            for(i = 0; i < l; i++)writeByte(e.charCodeAt(i));\n        });\n    }\n    function writeSOS() {\n        writeWord(0xFFDA); // marker\n        writeWord(12); // length\n        writeByte(3); // nrofcomponents\n        writeByte(1); // IdY\n        writeByte(0); // HTY\n        writeByte(2); // IdU\n        writeByte(0x11); // HTU\n        writeByte(3); // IdV\n        writeByte(0x11); // HTV\n        writeByte(0); // Ss\n        writeByte(0x3f); // Se\n        writeByte(0); // Bf\n    }\n    function processDU(CDU, fdtbl, DC, HTDC, HTAC) {\n        var EOB = HTAC[0x00];\n        var M16zeroes = HTAC[0xF0];\n        var pos;\n        var I16 = 16;\n        var I63 = 63;\n        var I64 = 64;\n        var DU_DCT = fDCTQuant(CDU, fdtbl);\n        //ZigZag reorder\n        for(var j = 0; j < I64; ++j){\n            DU[ZigZag[j]] = DU_DCT[j];\n        }\n        var Diff = DU[0] - DC;\n        DC = DU[0];\n        //Encode DC\n        if (Diff == 0) {\n            writeBits(HTDC[0]); // Diff might be 0\n        } else {\n            pos = 32767 + Diff;\n            writeBits(HTDC[category[pos]]);\n            writeBits(bitcode[pos]);\n        }\n        //Encode ACs\n        var end0pos = 63; // was const... which is crazy\n        for(; end0pos > 0 && DU[end0pos] == 0; end0pos--){}\n        ;\n        //end0pos = first element in reverse order !=0\n        if (end0pos == 0) {\n            writeBits(EOB);\n            return DC;\n        }\n        var i = 1;\n        var lng;\n        while(i <= end0pos){\n            var startpos = i;\n            for(; DU[i] == 0 && i <= end0pos; ++i){}\n            var nrzeroes = i - startpos;\n            if (nrzeroes >= I16) {\n                lng = nrzeroes >> 4;\n                for(var nrmarker = 1; nrmarker <= lng; ++nrmarker)writeBits(M16zeroes);\n                nrzeroes = nrzeroes & 0xF;\n            }\n            pos = 32767 + DU[i];\n            writeBits(HTAC[(nrzeroes << 4) + category[pos]]);\n            writeBits(bitcode[pos]);\n            i++;\n        }\n        if (end0pos != I63) {\n            writeBits(EOB);\n        }\n        return DC;\n    }\n    function initCharLookupTable() {\n        var sfcc = String.fromCharCode;\n        for(var i = 0; i < 256; i++){\n            clt[i] = sfcc(i);\n        }\n    }\n    this.encode = function(image, quality) {\n        var time_start = new Date().getTime();\n        if (quality) setQuality(quality);\n        // Initialize bit writer\n        byteout = new Array();\n        bytenew = 0;\n        bytepos = 7;\n        // Add JPEG headers\n        writeWord(0xFFD8); // SOI\n        writeAPP0();\n        writeCOM(image.comments);\n        writeAPP1(image.exifBuffer);\n        writeDQT();\n        writeSOF0(image.width, image.height);\n        writeDHT();\n        writeSOS();\n        // Encode 8x8 macroblocks\n        var DCY = 0;\n        var DCU = 0;\n        var DCV = 0;\n        bytenew = 0;\n        bytepos = 7;\n        this.encode.displayName = \"_encode_\";\n        var imageData = image.data;\n        var width = image.width;\n        var height = image.height;\n        var quadWidth = width * 4;\n        var tripleWidth = width * 3;\n        var x, y = 0;\n        var r, g, b;\n        var start, p, col, row, pos;\n        while(y < height){\n            x = 0;\n            while(x < quadWidth){\n                start = quadWidth * y + x;\n                p = start;\n                col = -1;\n                row = 0;\n                for(pos = 0; pos < 64; pos++){\n                    row = pos >> 3; // /8\n                    col = (pos & 7) * 4; // %8\n                    p = start + row * quadWidth + col;\n                    if (y + row >= height) {\n                        p -= quadWidth * (y + 1 + row - height);\n                    }\n                    if (x + col >= quadWidth) {\n                        p -= x + col - quadWidth + 4;\n                    }\n                    r = imageData[p++];\n                    g = imageData[p++];\n                    b = imageData[p++];\n                    /* // calculate YUV values dynamically\n\t\t\t\t\tYDU[pos]=((( 0.29900)*r+( 0.58700)*g+( 0.11400)*b))-128; //-0x80\n\t\t\t\t\tUDU[pos]=(((-0.16874)*r+(-0.33126)*g+( 0.50000)*b));\n\t\t\t\t\tVDU[pos]=((( 0.50000)*r+(-0.41869)*g+(-0.08131)*b));\n\t\t\t\t\t*/ // use lookup table (slightly faster)\n                    YDU[pos] = (RGB_YUV_TABLE[r] + RGB_YUV_TABLE[g + 256 >> 0] + RGB_YUV_TABLE[b + 512 >> 0] >> 16) - 128;\n                    UDU[pos] = (RGB_YUV_TABLE[r + 768 >> 0] + RGB_YUV_TABLE[g + 1024 >> 0] + RGB_YUV_TABLE[b + 1280 >> 0] >> 16) - 128;\n                    VDU[pos] = (RGB_YUV_TABLE[r + 1280 >> 0] + RGB_YUV_TABLE[g + 1536 >> 0] + RGB_YUV_TABLE[b + 1792 >> 0] >> 16) - 128;\n                }\n                DCY = processDU(YDU, fdtbl_Y, DCY, YDC_HT, YAC_HT);\n                DCU = processDU(UDU, fdtbl_UV, DCU, UVDC_HT, UVAC_HT);\n                DCV = processDU(VDU, fdtbl_UV, DCV, UVDC_HT, UVAC_HT);\n                x += 32;\n            }\n            y += 8;\n        }\n        ////////////////////////////////////////////////////////////////\n        // Do the bit alignment of the EOI marker\n        if (bytepos >= 0) {\n            var fillbits = [];\n            fillbits[1] = bytepos + 1;\n            fillbits[0] = (1 << bytepos + 1) - 1;\n            writeBits(fillbits);\n        }\n        writeWord(0xFFD9); //EOI\n        if (false) {}\n        return Buffer.from(byteout);\n        var jpegDataUri = \"data:image/jpeg;base64,\" + btoa(byteout.join(\"\"));\n        byteout = [];\n        // benchmarking\n        var duration = new Date().getTime() - time_start;\n        //console.log('Encoding time: '+ duration + 'ms');\n        //\n        return jpegDataUri;\n    };\n    function setQuality(quality) {\n        if (quality <= 0) {\n            quality = 1;\n        }\n        if (quality > 100) {\n            quality = 100;\n        }\n        if (currentQuality == quality) return; // don't recalc if unchanged\n        var sf = 0;\n        if (quality < 50) {\n            sf = Math.floor(5000 / quality);\n        } else {\n            sf = Math.floor(200 - quality * 2);\n        }\n        initQuantTables(sf);\n        currentQuality = quality;\n    //console.log('Quality set to: '+quality +'%');\n    }\n    function init() {\n        var time_start = new Date().getTime();\n        if (!quality) quality = 50;\n        // Create tables\n        initCharLookupTable();\n        initHuffmanTbl();\n        initCategoryNumber();\n        initRGBYUVTable();\n        setQuality(quality);\n        var duration = new Date().getTime() - time_start;\n    //console.log('Initialization '+ duration + 'ms');\n    }\n    init();\n}\nif (true) {\n    module.exports = encode;\n} else {}\nfunction encode(imgData, qu) {\n    if (typeof qu === \"undefined\") qu = 50;\n    var encoder = new JPEGEncoder(qu);\n    var data = encoder.encode(imgData, qu);\n    return {\n        data: data,\n        width: imgData.width,\n        height: imgData.height\n    };\n}\n// helper function to get the imageData of an existing image on the current page.\nfunction getImageDataFromImage(idOrElement) {\n    var theImg = typeof idOrElement == \"string\" ? document.getElementById(idOrElement) : idOrElement;\n    var cvs = document.createElement(\"canvas\");\n    cvs.width = theImg.width;\n    cvs.height = theImg.height;\n    var ctx = cvs.getContext(\"2d\");\n    ctx.drawImage(theImg, 0, 0);\n    return ctx.getImageData(0, 0, cvs.width, cvs.height);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jpeg-js/lib/encoder.js\n");

/***/ })

};
;