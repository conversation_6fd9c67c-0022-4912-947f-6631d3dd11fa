{"version": 3, "file": "index.js", "names": ["histogram", "r", "Array", "fill", "g", "b", "scanQuiet", "bitmap", "width", "height", "x", "y", "index", "data", "normalize", "value", "min", "max", "getBounds", "histogramChannel", "findIndex", "slice", "reverse", "cb", "h", "call", "bounds", "idx", "isNodePattern"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Get an image's histogram\n * @return {object} An object with an array of color occurrence counts for each channel (r,g,b)\n */\nfunction histogram() {\n  const histogram = {\n    r: new Array(256).fill(0),\n    g: new Array(256).fill(0),\n    b: new Array(256).fill(0),\n  };\n\n  this.scanQuiet(\n    0,\n    0,\n    this.bitmap.width,\n    this.bitmap.height,\n    function (x, y, index) {\n      histogram.r[this.bitmap.data[index + 0]]++;\n      histogram.g[this.bitmap.data[index + 1]]++;\n      histogram.b[this.bitmap.data[index + 2]]++;\n    }\n  );\n\n  return histogram;\n}\n\n/**\n * Normalize values\n * @param  {integer} value Pixel channel value.\n * @param  {integer} min   Minimum value for channel\n * @param  {integer} max   Maximum value for channel\n * @return {integer} normalized values\n */\nconst normalize = function (value, min, max) {\n  return ((value - min) * 255) / (max - min);\n};\n\nconst getBounds = function (histogramChannel) {\n  return [\n    histogramChannel.findIndex((value) => value > 0),\n    255 -\n      histogramChannel\n        .slice()\n        .reverse()\n        .findIndex((value) => value > 0),\n  ];\n};\n\n/**\n * Normalizes the image\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  normalize(cb) {\n    const h = histogram.call(this);\n\n    // store bounds (minimum and maximum values)\n    const bounds = {\n      r: getBounds(h.r),\n      g: getBounds(h.g),\n      b: getBounds(h.b),\n    };\n\n    // apply value transformations\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        const r = this.bitmap.data[idx + 0];\n        const g = this.bitmap.data[idx + 1];\n        const b = this.bitmap.data[idx + 2];\n\n        this.bitmap.data[idx + 0] = normalize(r, bounds.r[0], bounds.r[1]);\n        this.bitmap.data[idx + 1] = normalize(g, bounds.g[0], bounds.g[1]);\n        this.bitmap.data[idx + 2] = normalize(b, bounds.b[0], bounds.b[1]);\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;AACA;AACA;AACA,SAASA,SAAS,GAAG;EACnB,MAAMA,SAAS,GAAG;IAChBC,CAAC,EAAE,IAAIC,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IACzBC,CAAC,EAAE,IAAIF,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IACzBE,CAAC,EAAE,IAAIH,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;EAC1B,CAAC;EAED,IAAI,CAACG,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACC,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAE;IACrBZ,SAAS,CAACC,CAAC,CAAC,IAAI,CAACM,MAAM,CAACM,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;IAC1CZ,SAAS,CAACI,CAAC,CAAC,IAAI,CAACG,MAAM,CAACM,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;IAC1CZ,SAAS,CAACK,CAAC,CAAC,IAAI,CAACE,MAAM,CAACM,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;EAC5C,CAAC,CACF;EAED,OAAOZ,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC3C,OAAQ,CAACF,KAAK,GAAGC,GAAG,IAAI,GAAG,IAAKC,GAAG,GAAGD,GAAG,CAAC;AAC5C,CAAC;AAED,MAAME,SAAS,GAAG,UAAUC,gBAAgB,EAAE;EAC5C,OAAO,CACLA,gBAAgB,CAACC,SAAS,CAAEL,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC,EAChD,GAAG,GACDI,gBAAgB,CACbE,KAAK,EAAE,CACPC,OAAO,EAAE,CACTF,SAAS,CAAEL,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC,CACrC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJA,eAKe,OAAO;EACpBD,SAAS,CAACS,EAAE,EAAE;IACZ,MAAMC,CAAC,GAAGxB,SAAS,CAACyB,IAAI,CAAC,IAAI,CAAC;;IAE9B;IACA,MAAMC,MAAM,GAAG;MACbzB,CAAC,EAAEiB,SAAS,CAACM,CAAC,CAACvB,CAAC,CAAC;MACjBG,CAAC,EAAEc,SAAS,CAACM,CAAC,CAACpB,CAAC,CAAC;MACjBC,CAAC,EAAEa,SAAS,CAACM,CAAC,CAACnB,CAAC;IAClB,CAAC;;IAED;IACA,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACC,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUC,CAAC,EAAEC,CAAC,EAAEgB,GAAG,EAAE;MACnB,MAAM1B,CAAC,GAAG,IAAI,CAACM,MAAM,CAACM,IAAI,CAACc,GAAG,GAAG,CAAC,CAAC;MACnC,MAAMvB,CAAC,GAAG,IAAI,CAACG,MAAM,CAACM,IAAI,CAACc,GAAG,GAAG,CAAC,CAAC;MACnC,MAAMtB,CAAC,GAAG,IAAI,CAACE,MAAM,CAACM,IAAI,CAACc,GAAG,GAAG,CAAC,CAAC;MAEnC,IAAI,CAACpB,MAAM,CAACM,IAAI,CAACc,GAAG,GAAG,CAAC,CAAC,GAAGb,SAAS,CAACb,CAAC,EAAEyB,MAAM,CAACzB,CAAC,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAACzB,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,IAAI,CAACM,MAAM,CAACM,IAAI,CAACc,GAAG,GAAG,CAAC,CAAC,GAAGb,SAAS,CAACV,CAAC,EAAEsB,MAAM,CAACtB,CAAC,CAAC,CAAC,CAAC,EAAEsB,MAAM,CAACtB,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,IAAI,CAACG,MAAM,CAACM,IAAI,CAACc,GAAG,GAAG,CAAC,CAAC,GAAGb,SAAS,CAACT,CAAC,EAAEqB,MAAM,CAACrB,CAAC,CAAC,CAAC,CAAC,EAAEqB,MAAM,CAACrB,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,CACF;IAED,IAAI,IAAAuB,oBAAa,EAACL,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}