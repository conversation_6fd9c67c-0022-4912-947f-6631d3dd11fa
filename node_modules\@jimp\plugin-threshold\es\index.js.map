{"version": 3, "file": "index.js", "names": ["isNodePattern", "throwError", "threshold", "cb", "max", "replace", "autoGreyscale", "call", "constructor", "limit255", "greyscale", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "grey", "data"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Applies a minimum color threshold to a greyscale image.  Converts image to greyscale by default\n * @param {number} options object\n *  max: A number auto limited between 0 - 255\n *  replace: (optional) A number auto limited between 0 - 255 (default 255)\n *  autoGreyscale: (optional) A boolean whether to apply greyscale beforehand (default true)\n * @param {number} cb (optional) a callback for when complete\n * @return {this} this for chaining of methods\n */\nexport default () => ({\n  threshold({ max, replace = 255, autoGreyscale = true }, cb) {\n    if (typeof max !== \"number\") {\n      return throwError.call(this, \"max must be a number\", cb);\n    }\n\n    if (typeof replace !== \"number\") {\n      return throwError.call(this, \"replace must be a number\", cb);\n    }\n\n    if (typeof autoGreyscale !== \"boolean\") {\n      return throwError.call(this, \"autoGreyscale must be a boolean\", cb);\n    }\n\n    max = this.constructor.limit255(max);\n    replace = this.constructor.limit255(replace);\n\n    if (autoGreyscale) {\n      this.greyscale();\n    }\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, (x, y, idx) => {\n      const grey =\n        this.bitmap.data[idx] < max ? this.bitmap.data[idx] : replace;\n\n      this.bitmap.data[idx] = grey;\n      this.bitmap.data[idx + 1] = grey;\n      this.bitmap.data[idx + 2] = grey;\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,SAAS,OAA+CC,EAAE,EAAE;IAAA,IAAlD;MAAEC,GAAG;MAAEC,OAAO,GAAG,GAAG;MAAEC,aAAa,GAAG;IAAK,CAAC;IACpD,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOH,UAAU,CAACM,IAAI,CAAC,IAAI,EAAE,sBAAsB,EAAEJ,EAAE,CAAC;IAC1D;IAEA,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOJ,UAAU,CAACM,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAEJ,EAAE,CAAC;IAC9D;IAEA,IAAI,OAAOG,aAAa,KAAK,SAAS,EAAE;MACtC,OAAOL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAE,iCAAiC,EAAEJ,EAAE,CAAC;IACrE;IAEAC,GAAG,GAAG,IAAI,CAACI,WAAW,CAACC,QAAQ,CAACL,GAAG,CAAC;IACpCC,OAAO,GAAG,IAAI,CAACG,WAAW,CAACC,QAAQ,CAACJ,OAAO,CAAC;IAE5C,IAAIC,aAAa,EAAE;MACjB,IAAI,CAACI,SAAS,EAAE;IAClB;IAEA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,EAAE,IAAI,CAACD,MAAM,CAACE,MAAM,EAAE,CAACC,CAAC,EAAEC,CAAC,EAAEC,GAAG,KAAK;MACzE,MAAMC,IAAI,GACR,IAAI,CAACN,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC,GAAGb,GAAG,GAAG,IAAI,CAACQ,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC,GAAGZ,OAAO;MAE/D,IAAI,CAACO,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC,GAAGC,IAAI;MAC5B,IAAI,CAACN,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAGC,IAAI;MAChC,IAAI,CAACN,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAGC,IAAI;IAClC,CAAC,CAAC;IAEF,IAAIlB,aAAa,CAACG,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}