"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Shield,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _components_ImageUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ImageUpload */ \"(app-pages-browser)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(app-pages-browser)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_TradingStrategySelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TradingStrategySelector */ \"(app-pages-browser)/./src/components/TradingStrategySelector.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"scalping\");\n    const [analysisResults, setAnalysisResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleImageUpload = async (file)=>{\n        setIsAnalyzing(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", file);\n            formData.append(\"strategy\", selectedStrategy);\n            const response = await fetch(\"/api/analyze\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(\"Analysis failed\");\n            }\n            const results = await response.json();\n            setAnalysisResults(results);\n        } catch (error) {\n            console.error(\"Analysis error:\", error);\n            alert(\"Analysis failed. Please try again.\");\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-6xl font-bold text-white mb-4\",\n                        children: [\n                            \"Professional \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gold-400\",\n                                children: \"XAUUSD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 24\n                            }, this),\n                            \" Analysis\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                        children: \"Upload your trading chart screenshots and receive detailed analysis using Smart Money Concepts and Inner Circle Trader methodologies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-4 gap-6 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Smart Money Concepts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Market structure, order blocks, and liquidity analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"ICT Methodology\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Institutional order flow and time-based analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Precise Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Exact entry points with detailed reasoning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-gold-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Shield_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-8 h-8 text-gold-400 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Risk Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Stop loss and take profit calculations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradingStrategySelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                selectedStrategy: selectedStrategy,\n                onStrategyChange: setSelectedStrategy\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6 text-center\",\n                        children: \"Upload Your Chart Screenshot\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        onImageUpload: handleImageUpload,\n                        isAnalyzing: isAnalyzing\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            analysisResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                results: analysisResults\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-black/30 backdrop-blur-sm rounded-xl p-8 border border-gold-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"How It Works\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl\",\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Upload Chart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Upload a screenshot of your XAUUSD chart from any platform (MT4, MT5, TradingView)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"AI Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Our advanced algorithms analyze market structure, patterns, and institutional behavior\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center text-black font-bold text-xl\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Get Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Receive detailed trade setups with entry, stop loss, and take profit levels\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\Trading Agent\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"J3sAJS9s2NNGxjg8kqz6j0BsKSw=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});