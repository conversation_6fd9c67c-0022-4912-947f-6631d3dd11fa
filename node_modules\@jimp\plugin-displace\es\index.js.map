{"version": 3, "file": "index.js", "names": ["isNodePattern", "throwError", "displace", "map", "offset", "cb", "constructor", "call", "source", "clone<PERSON>uiet", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "displacement", "data", "Math", "round", "ids", "getPixelIndex"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Displaces the image based on the provided displacement map\n * @param {object} map the source Jimp instance\n * @param {number} offset the maximum displacement value\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  displace(map, offset, cb) {\n    if (typeof map !== \"object\" || map.constructor !== this.constructor) {\n      return throwError.call(this, \"The source must be a Jimp image\", cb);\n    }\n\n    if (typeof offset !== \"number\") {\n      return throwError.call(this, \"factor must be a number\", cb);\n    }\n\n    const source = this.cloneQuiet();\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        let displacement = (map.bitmap.data[idx] / 256) * offset;\n        displacement = Math.round(displacement);\n\n        const ids = this.getPixelIndex(x + displacement, y);\n        this.bitmap.data[ids] = source.bitmap.data[idx];\n        this.bitmap.data[ids + 1] = source.bitmap.data[idx + 1];\n        this.bitmap.data[ids + 2] = source.bitmap.data[idx + 2];\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,QAAQ,CAACC,GAAG,EAAEC,MAAM,EAAEC,EAAE,EAAE;IACxB,IAAI,OAAOF,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACG,WAAW,KAAK,IAAI,CAACA,WAAW,EAAE;MACnE,OAAOL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAE,iCAAiC,EAAEF,EAAE,CAAC;IACrE;IAEA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOH,UAAU,CAACM,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEF,EAAE,CAAC;IAC7D;IAEA,MAAMG,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;IAChC,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACC,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;MACnB,IAAIC,YAAY,GAAId,GAAG,CAACQ,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC,GAAG,GAAG,GAAIZ,MAAM;MACxDa,YAAY,GAAGE,IAAI,CAACC,KAAK,CAACH,YAAY,CAAC;MAEvC,MAAMI,GAAG,GAAG,IAAI,CAACC,aAAa,CAACR,CAAC,GAAGG,YAAY,EAAEF,CAAC,CAAC;MACnD,IAAI,CAACJ,MAAM,CAACO,IAAI,CAACG,GAAG,CAAC,GAAGb,MAAM,CAACG,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC;MAC/C,IAAI,CAACL,MAAM,CAACO,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC,GAAGb,MAAM,CAACG,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC;MACvD,IAAI,CAACL,MAAM,CAACO,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC,GAAGb,MAAM,CAACG,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC;IACzD,CAAC,CACF;IAED,IAAIhB,aAAa,CAACK,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}